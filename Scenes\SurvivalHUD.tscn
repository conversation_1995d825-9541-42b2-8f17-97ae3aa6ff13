[gd_scene load_steps=3 format=3 uid="uid://c7bc6jxnj7ipd"]

[ext_resource type="Script" uid="uid://cgj7l5wtnrb2k" path="res://Scripts/SurvivalHUD.cs" id="1_hud_script"]

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_Background"]
bg_color = Color(0, 0, 0, 0.7)
corner_radius_top_left = 5
corner_radius_top_right = 5
corner_radius_bottom_right = 5
corner_radius_bottom_left = 5

[node name="SurvivalHUD" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2
script = ExtResource("1_hud_script")

[node name="StatsContainer" type="VBoxContainer" parent="."]
layout_mode = 1
anchors_preset = 2
anchor_top = 1.0
anchor_bottom = 1.0
offset_left = 20.0
offset_top = -200.0
offset_right = 320.0
offset_bottom = -20.0
grow_vertical = 0

[node name="StatsBackground" type="Panel" parent="StatsContainer"]
layout_mode = 2
size_flags_vertical = 3
theme_override_styles/panel = SubResource("StyleBoxFlat_Background")

[node name="StatsVBox" type="VBoxContainer" parent="StatsContainer/StatsBackground"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 10.0
offset_top = 10.0
offset_right = -10.0
offset_bottom = -10.0
grow_horizontal = 2
grow_vertical = 2

[node name="HealthContainer" type="HBoxContainer" parent="StatsContainer/StatsBackground/StatsVBox"]
layout_mode = 2

[node name="HealthLabel" type="Label" parent="StatsContainer/StatsBackground/StatsVBox/HealthContainer"]
custom_minimum_size = Vector2(60, 0)
layout_mode = 2
size_flags_horizontal = 0
text = "Health:"

[node name="HealthBar" type="ProgressBar" parent="StatsContainer/StatsBackground/StatsVBox/HealthContainer"]
layout_mode = 2
size_flags_horizontal = 3
value = 100.0
show_percentage = false

[node name="HealthValue" type="Label" parent="StatsContainer/StatsBackground/StatsVBox/HealthContainer"]
custom_minimum_size = Vector2(60, 0)
layout_mode = 2
text = "100/100"
horizontal_alignment = 2

[node name="HungerContainer" type="HBoxContainer" parent="StatsContainer/StatsBackground/StatsVBox"]
layout_mode = 2

[node name="HungerLabel" type="Label" parent="StatsContainer/StatsBackground/StatsVBox/HungerContainer"]
custom_minimum_size = Vector2(60, 0)
layout_mode = 2
size_flags_horizontal = 0
text = "Hunger:"

[node name="HungerBar" type="ProgressBar" parent="StatsContainer/StatsBackground/StatsVBox/HungerContainer"]
layout_mode = 2
size_flags_horizontal = 3
value = 100.0
show_percentage = false

[node name="HungerValue" type="Label" parent="StatsContainer/StatsBackground/StatsVBox/HungerContainer"]
custom_minimum_size = Vector2(60, 0)
layout_mode = 2
text = "100/100"
horizontal_alignment = 2

[node name="ThirstContainer" type="HBoxContainer" parent="StatsContainer/StatsBackground/StatsVBox"]
layout_mode = 2

[node name="ThirstLabel" type="Label" parent="StatsContainer/StatsBackground/StatsVBox/ThirstContainer"]
custom_minimum_size = Vector2(60, 0)
layout_mode = 2
size_flags_horizontal = 0
text = "Thirst:"

[node name="ThirstBar" type="ProgressBar" parent="StatsContainer/StatsBackground/StatsVBox/ThirstContainer"]
layout_mode = 2
size_flags_horizontal = 3
value = 100.0
show_percentage = false

[node name="ThirstValue" type="Label" parent="StatsContainer/StatsBackground/StatsVBox/ThirstContainer"]
custom_minimum_size = Vector2(60, 0)
layout_mode = 2
text = "100/100"
horizontal_alignment = 2

[node name="StaminaContainer" type="HBoxContainer" parent="StatsContainer/StatsBackground/StatsVBox"]
layout_mode = 2

[node name="StaminaLabel" type="Label" parent="StatsContainer/StatsBackground/StatsVBox/StaminaContainer"]
custom_minimum_size = Vector2(60, 0)
layout_mode = 2
size_flags_horizontal = 0
text = "Stamina:"

[node name="StaminaBar" type="ProgressBar" parent="StatsContainer/StatsBackground/StatsVBox/StaminaContainer"]
layout_mode = 2
size_flags_horizontal = 3
value = 100.0
show_percentage = false

[node name="StaminaValue" type="Label" parent="StatsContainer/StatsBackground/StatsVBox/StaminaContainer"]
custom_minimum_size = Vector2(60, 0)
layout_mode = 2
text = "100/100"
horizontal_alignment = 2

[node name="WeaponContainer" type="VBoxContainer" parent="."]
layout_mode = 1
anchors_preset = 3
anchor_left = 1.0
anchor_top = 1.0
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = -320.0
offset_top = -120.0
offset_right = -20.0
offset_bottom = -20.0
grow_horizontal = 0
grow_vertical = 0

[node name="WeaponBackground" type="Panel" parent="WeaponContainer"]
layout_mode = 2
size_flags_vertical = 3
theme_override_styles/panel = SubResource("StyleBoxFlat_Background")

[node name="WeaponVBox" type="VBoxContainer" parent="WeaponContainer/WeaponBackground"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 10.0
offset_top = 10.0
offset_right = -10.0
offset_bottom = -10.0
grow_horizontal = 2
grow_vertical = 2

[node name="WeaponNameLabel" type="Label" parent="WeaponContainer/WeaponBackground/WeaponVBox"]
layout_mode = 2
text = "No Weapon"
horizontal_alignment = 1

[node name="AmmoContainer" type="HBoxContainer" parent="WeaponContainer/WeaponBackground/WeaponVBox"]
layout_mode = 2

[node name="AmmoLabel" type="Label" parent="WeaponContainer/WeaponBackground/WeaponVBox/AmmoContainer"]
custom_minimum_size = Vector2(50, 0)
layout_mode = 2
text = "Ammo:"

[node name="AmmoValue" type="Label" parent="WeaponContainer/WeaponBackground/WeaponVBox/AmmoContainer"]
layout_mode = 2
size_flags_horizontal = 3
text = "0/0"
horizontal_alignment = 2

[node name="ConditionContainer" type="HBoxContainer" parent="WeaponContainer/WeaponBackground/WeaponVBox"]
layout_mode = 2

[node name="ConditionLabel" type="Label" parent="WeaponContainer/WeaponBackground/WeaponVBox/ConditionContainer"]
custom_minimum_size = Vector2(70, 0)
layout_mode = 2
text = "Condition:"

[node name="ConditionBar" type="ProgressBar" parent="WeaponContainer/WeaponBackground/WeaponVBox/ConditionContainer"]
layout_mode = 2
size_flags_horizontal = 3
value = 100.0
show_percentage = false

[node name="ReloadingLabel" type="Label" parent="WeaponContainer/WeaponBackground/WeaponVBox"]
visible = false
layout_mode = 2
text = "RELOADING..."
horizontal_alignment = 1

[node name="TimeContainer" type="VBoxContainer" parent="."]
layout_mode = 1
anchors_preset = 1
anchor_left = 1.0
anchor_right = 1.0
offset_left = -200.0
offset_top = 20.0
offset_right = -20.0
offset_bottom = 100.0
grow_horizontal = 0

[node name="TimeBackground" type="Panel" parent="TimeContainer"]
layout_mode = 2
size_flags_vertical = 3
theme_override_styles/panel = SubResource("StyleBoxFlat_Background")

[node name="TimeVBox" type="VBoxContainer" parent="TimeContainer/TimeBackground"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 10.0
offset_top = 10.0
offset_right = -10.0
offset_bottom = -10.0
grow_horizontal = 2
grow_vertical = 2

[node name="TimeLabel" type="Label" parent="TimeContainer/TimeBackground/TimeVBox"]
layout_mode = 2
theme_override_font_sizes/font_size = 18
text = "12:00"
horizontal_alignment = 1

[node name="TimePeriodLabel" type="Label" parent="TimeContainer/TimeBackground/TimeVBox"]
layout_mode = 2
theme_override_font_sizes/font_size = 12
text = "Day"
horizontal_alignment = 1
