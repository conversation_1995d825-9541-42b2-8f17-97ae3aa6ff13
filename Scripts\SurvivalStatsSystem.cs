using System;
using System.Collections.Generic;
using System.Linq;
using Godot;

namespace SurvivalLooterShooter
{
    /// <summary>
    /// Manages all survival stats with automatic decay, consumable processing, and threshold effects
    /// </summary>
    public partial class SurvivalStatsSystem : Node
    {
        private static SurvivalStatsSystem _instance;
        public static SurvivalStatsSystem Instance => _instance;
        private Dictionary<string, SurvivalStat> _stats = new();

        [Signal]
        public delegate void PlayerDiedEventHandler();

        [Signal]
        public delegate void PlayerRespawnedEventHandler();

        [Signal]
        public delegate void StatThresholdReachedEventHandler(string statName, float threshold);

        [Signal]
        public delegate void ConsumableUsedEventHandler(string itemId, string effectsJson);

        // Stat references
        private SurvivalStat _health;
        private SurvivalStat _hunger;
        private SurvivalStat _thirst;
        private SurvivalStat _stamina;

        // System state
        private bool _isDead = false;
        private Timer _decayTimer;
        private Dictionary<string, float> _activeDebuffs = new Dictionary<string, float>();

        // Configuration
        private const float DECAY_UPDATE_INTERVAL = 1.0f; // Update decay every second
        private const float CRITICAL_THRESHOLD = 25f; // Below 25% is critical
        private const float DANGER_THRESHOLD = 10f; // Below 10% is dangerous

        // Default stat configurations
        private readonly Dictionary<string, StatConfig> _defaultStatConfigs = new Dictionary<string, StatConfig>
        {
            ["health"] = new StatConfig { MaxValue = 100f, DecayRate = 0f, CanDecay = false },
            ["hunger"] = new StatConfig { MaxValue = 100f, DecayRate = 2f, CanDecay = true },
            ["thirst"] = new StatConfig { MaxValue = 100f, DecayRate = 3f, CanDecay = true },
            ["stamina"] = new StatConfig { MaxValue = 100f, DecayRate = 1f, CanDecay = true }
        };

        public SurvivalStat Health => _health;
        public SurvivalStat Hunger => _hunger;
        public SurvivalStat Thirst => _thirst;
        public SurvivalStat Stamina => _stamina;
        public bool IsDead => _isDead;

        public override void _Ready()
        {
            // Singleton setup
            if (_instance == null)
            {
                _instance = this;
                AddToGroup("survival_stats_system");
                Logger.LogInfo("SurvivalStatsSystem", "SurvivalStatsSystem singleton initialized");
            }
            else
            {
                Logger.LogError("SurvivalStatsSystem", "Multiple SurvivalStatsSystem instances detected! Removing duplicate.");
                QueueFree();
                return;
            }

            InitializeStats();
            SetupDecayTimer();
            ConnectStatSignals();

            Logger.LogInfo("SurvivalStatsSystem", "Survival stats system initialized");
        }

        /// <summary>
        /// Initializes all survival stats with default values
        /// </summary>
        private void InitializeStats()
        {
            // Create health stat
            _health = new SurvivalStat();
            AddChild(_health);
            var healthConfig = _defaultStatConfigs["health"];
            _health.Initialize("Health", healthConfig.MaxValue, healthConfig.MaxValue, healthConfig.DecayRate, healthConfig.CanDecay);
            _stats["health"] = _health;

            // Create hunger stat
            _hunger = new SurvivalStat();
            AddChild(_hunger);
            var hungerConfig = _defaultStatConfigs["hunger"];
            _hunger.Initialize("Hunger", hungerConfig.MaxValue, hungerConfig.MaxValue, hungerConfig.DecayRate, hungerConfig.CanDecay);
            _stats["hunger"] = _hunger;

            // Create thirst stat
            _thirst = new SurvivalStat();
            AddChild(_thirst);
            var thirstConfig = _defaultStatConfigs["thirst"];
            _thirst.Initialize("Thirst", thirstConfig.MaxValue, thirstConfig.MaxValue, thirstConfig.DecayRate, thirstConfig.CanDecay);
            _stats["thirst"] = _thirst;

            // Create stamina stat
            _stamina = new SurvivalStat();
            AddChild(_stamina);
            var staminaConfig = _defaultStatConfigs["stamina"];
            _stamina.Initialize("Stamina", staminaConfig.MaxValue, staminaConfig.MaxValue, staminaConfig.DecayRate, staminaConfig.CanDecay);
            _stats["stamina"] = _stamina;
        }

        /// <summary>
        /// Sets up the timer for automatic stat decay
        /// </summary>
        private void SetupDecayTimer()
        {
            _decayTimer = new Timer();
            AddChild(_decayTimer);
            _decayTimer.WaitTime = DECAY_UPDATE_INTERVAL;
            _decayTimer.Autostart = true;
            _decayTimer.Timeout += OnDecayTimerTimeout;
        }

        /// <summary>
        /// Connects to stat change signals for threshold monitoring
        /// </summary>
        private void ConnectStatSignals()
        {
            _health.StatChanged += OnHealthChanged;
            _health.StatDepleted += OnHealthDepleted;

            _hunger.StatChanged += OnHungerChanged;
            _hunger.StatDepleted += OnHungerDepleted;

            _thirst.StatChanged += OnThirstChanged;
            _thirst.StatDepleted += OnThirstDepleted;

            _stamina.StatChanged += OnStaminaChanged;
        }

        /// <summary>
        /// Handles automatic stat decay
        /// </summary>
        private void OnDecayTimerTimeout()
        {
            if (_isDead) return;

            // Apply decay to all stats with skill bonuses and weather effects
            float decayMultiplier = GetDecayMultiplier();
            var weatherEffects = GetWeatherEffects();

            _hunger.ApplyDecay(DECAY_UPDATE_INTERVAL * decayMultiplier * weatherEffects.HungerDecayModifier);
            _thirst.ApplyDecay(DECAY_UPDATE_INTERVAL * decayMultiplier * weatherEffects.ThirstDecayModifier);
            _stamina.ApplyDecay(DECAY_UPDATE_INTERVAL * decayMultiplier);

            // Apply weather-based stamina regeneration
            if (_stamina.CurrentValue < _stamina.MaxValue)
            {
                float staminaRegen = 5.0f * DECAY_UPDATE_INTERVAL * weatherEffects.StaminaRegenModifier;
                _stamina.ModifyValue(staminaRegen);
            }

            // Apply debuffs from low stats
            ApplyStatDebuffs();
        }

        /// <summary>
        /// Applies debuffs based on low stat values
        /// </summary>
        private void ApplyStatDebuffs()
        {
            // Clear previous debuffs
            _activeDebuffs.Clear();

            // Check hunger debuffs
            if (_hunger.IsBelowThreshold(DANGER_THRESHOLD))
            {
                // Severe hunger: lose health over time
                _health.ModifyValue(-5f * DECAY_UPDATE_INTERVAL);
                _activeDebuffs["severe_hunger"] = 5f;
            }
            else if (_hunger.IsBelowThreshold(CRITICAL_THRESHOLD))
            {
                // Moderate hunger: slower stamina regeneration
                _activeDebuffs["moderate_hunger"] = 2f;
            }

            // Check thirst debuffs
            if (_thirst.IsBelowThreshold(DANGER_THRESHOLD))
            {
                // Severe dehydration: lose health over time
                _health.ModifyValue(-8f * DECAY_UPDATE_INTERVAL);
                _activeDebuffs["severe_dehydration"] = 8f;
            }
            else if (_thirst.IsBelowThreshold(CRITICAL_THRESHOLD))
            {
                // Moderate dehydration: faster stamina decay
                _activeDebuffs["moderate_dehydration"] = 3f;
            }

            // Check stamina effects
            if (_stamina.IsBelowThreshold(CRITICAL_THRESHOLD))
            {
                _activeDebuffs["low_stamina"] = 1f;
            }
        }

        /// <summary>
        /// Processes consumable item effects on survival stats
        /// </summary>
        public bool ConsumeItem(string itemId)
        {
            if (_isDead) return false;

            var item = ItemDatabase.Instance?.GetItem(itemId);
            if (item == null || item.Type != "consumable")
            {
                GD.PrintErr($"Item {itemId} is not a valid consumable");
                return false;
            }

            var effects = new Dictionary<string, float>();

            // Process health restoration with skill bonuses
            if (item.Metadata.ContainsKey("health_restore"))
            {
                float healthRestore = item.GetMetadata<float>("health_restore", 0f);
                if (healthRestore > 0f)
                {
                    float enhancedRestore = ApplyHealingBonus(healthRestore);
                    _health.ModifyValue(enhancedRestore);
                    effects["health"] = enhancedRestore;
                }
            }

            // Process hunger restoration
            if (item.Metadata.ContainsKey("hunger_restore"))
            {
                float hungerRestore = item.GetMetadata<float>("hunger_restore", 0f);
                if (hungerRestore > 0f)
                {
                    _hunger.ModifyValue(hungerRestore);
                    effects["hunger"] = hungerRestore;
                }
            }

            // Process thirst restoration
            if (item.Metadata.ContainsKey("thirst_restore"))
            {
                float thirstRestore = item.GetMetadata<float>("thirst_restore", 0f);
                if (thirstRestore > 0f)
                {
                    _thirst.ModifyValue(thirstRestore);
                    effects["thirst"] = thirstRestore;
                }
            }

            // Process stamina restoration with skill bonuses
            if (item.Metadata.ContainsKey("stamina_restore"))
            {
                float staminaRestore = item.GetMetadata<float>("stamina_restore", 0f);
                if (staminaRestore > 0f)
                {
                    float enhancedRestore = ApplyStaminaBonus(staminaRestore);
                    _stamina.ModifyValue(enhancedRestore);
                    effects["stamina"] = enhancedRestore;
                }
            }

            if (effects.Count > 0)
            {
                string effectsJson = System.Text.Json.JsonSerializer.Serialize(effects);
                EmitSignal(SignalName.ConsumableUsed, itemId, effectsJson);

                // Emit event bus event
                string eventBusEffectsJson = System.Text.Json.JsonSerializer.Serialize(effects);
                EventBus.Instance?.EmitSignal(EventBus.SignalName.ConsumableUsed, itemId, eventBusEffectsJson);

                GD.Print($"Consumed {item.Name}: {string.Join(", ", effects.Select(e => $"{e.Key} +{e.Value:F1}"))}");
                return true;
            }

            return false;
        }

        /// <summary>
        /// Handles player death and respawn mechanics
        /// </summary>
        private void HandlePlayerDeath()
        {
            if (_isDead) return;

            _isDead = true;
            _decayTimer.Stop();

            GD.Print("Player has died!");
            EmitSignal(SignalName.PlayerDied);

            // Emit event bus event
            EventBus.Instance?.EmitSignal(EventBus.SignalName.PlayerDied, "health_depleted");

            // Auto-respawn after a delay
            GetTree().CreateTimer(3.0f).Timeout += RespawnPlayer;
        }

        /// <summary>
        /// Respawns the player with restored stats
        /// </summary>
        private void RespawnPlayer()
        {
            _isDead = false;

            // Restore stats to reasonable levels (not full to maintain challenge)
            _health.SetValue(50f);
            _hunger.SetValue(75f);
            _thirst.SetValue(75f);
            _stamina.SetValue(100f);

            // Clear debuffs
            _activeDebuffs.Clear();

            // Restart decay timer
            _decayTimer.Start();

            GD.Print("Player respawned!");
            EmitSignal(SignalName.PlayerRespawned);

            // Emit event bus event
            EventBus.Instance?.EmitSignal(EventBus.SignalName.PlayerRespawned);
        }

        /// <summary>
        /// Gets all current survival stats data for saving
        /// </summary>
        public Dictionary<string, SurvivalStatData> GetAllStatsData()
        {
            return new Dictionary<string, SurvivalStatData>
            {
                ["health"] = _health.GetStatData(),
                ["hunger"] = _hunger.GetStatData(),
                ["thirst"] = _thirst.GetStatData(),
                ["stamina"] = _stamina.GetStatData()
            };
        }

        /// <summary>
        /// Loads all survival stats data from save
        /// </summary>
        public void LoadAllStatsData(Dictionary<string, SurvivalStatData> statsData)
        {
            if (statsData.ContainsKey("health"))
                _health.LoadStatData(statsData["health"]);

            if (statsData.ContainsKey("hunger"))
                _hunger.LoadStatData(statsData["hunger"]);

            if (statsData.ContainsKey("thirst"))
                _thirst.LoadStatData(statsData["thirst"]);

            if (statsData.ContainsKey("stamina"))
                _stamina.LoadStatData(statsData["stamina"]);
        }

        /// <summary>
        /// Gets active debuffs for UI display
        /// </summary>
        public Dictionary<string, float> GetActiveDebuffs()
        {
            return new Dictionary<string, float>(_activeDebuffs);
        }

        #region Stat Event Handlers

        private void OnHealthChanged(float currentValue, float maxValue)
        {
            // Emit event bus event for health changes
            EventBus.Instance?.EmitStatChanged("health", currentValue, maxValue, _health.PreviousValue);
        }

        private void OnHealthDepleted()
        {
            HandlePlayerDeath();
        }

        private void OnHungerChanged(float currentValue, float maxValue)
        {
            float percentage = currentValue / maxValue * 100f;

            // Emit event bus event for hunger changes
            EventBus.Instance?.EmitStatChanged("hunger", currentValue, maxValue, _hunger.PreviousValue);

            if (percentage <= DANGER_THRESHOLD)
            {
                EmitSignal(SignalName.StatThresholdReached, "hunger", DANGER_THRESHOLD);
                EventBus.Instance?.EmitSignal(EventBus.SignalName.StatThresholdReached, "hunger", DANGER_THRESHOLD, "danger");
            }
            else if (percentage <= CRITICAL_THRESHOLD)
            {
                EmitSignal(SignalName.StatThresholdReached, "hunger", CRITICAL_THRESHOLD);
                EventBus.Instance?.EmitSignal(EventBus.SignalName.StatThresholdReached, "hunger", CRITICAL_THRESHOLD, "critical");
            }
        }

        private void OnHungerDepleted()
        {
            // Hunger depletion is handled by debuff system
        }

        private void OnThirstChanged(float currentValue, float maxValue)
        {
            float percentage = currentValue / maxValue * 100f;

            // Emit event bus event for thirst changes
            EventBus.Instance?.EmitStatChanged("thirst", currentValue, maxValue, _thirst.PreviousValue);

            if (percentage <= DANGER_THRESHOLD)
            {
                EmitSignal(SignalName.StatThresholdReached, "thirst", DANGER_THRESHOLD);
                EventBus.Instance?.EmitSignal(EventBus.SignalName.StatThresholdReached, "thirst", DANGER_THRESHOLD, "danger");
            }
            else if (percentage <= CRITICAL_THRESHOLD)
            {
                EmitSignal(SignalName.StatThresholdReached, "thirst", CRITICAL_THRESHOLD);
                EventBus.Instance?.EmitSignal(EventBus.SignalName.StatThresholdReached, "thirst", CRITICAL_THRESHOLD, "critical");
            }
        }

        private void OnThirstDepleted()
        {
            // Thirst depletion is handled by debuff system
        }

        private void OnStaminaChanged(float currentValue, float maxValue)
        {
            float percentage = currentValue / maxValue * 100f;

            // Emit event bus event for stamina changes
            EventBus.Instance?.EmitStatChanged("stamina", currentValue, maxValue, _stamina.PreviousValue);

            if (percentage <= CRITICAL_THRESHOLD)
            {
                EmitSignal(SignalName.StatThresholdReached, "stamina", CRITICAL_THRESHOLD);
                EventBus.Instance?.EmitSignal(EventBus.SignalName.StatThresholdReached, "stamina", CRITICAL_THRESHOLD, "critical");
            }
        }

        #endregion

        #region Skill Bonuses and Weather Effects

        /// <summary>
        /// Gets the decay multiplier based on metabolism skill
        /// </summary>
        private float GetDecayMultiplier()
        {
            if (SkillManager.Instance == null) return 1f;

            float decayReduction = SkillManager.Instance.GetSkillBonus("metabolism", "decay_reduction");
            return 1f - decayReduction;
        }

        /// <summary>
        /// Gets current weather effects on survival stats
        /// </summary>
        private WeatherEffectData GetWeatherEffects()
        {
            if (WeatherManager.Instance != null)
            {
                return WeatherManager.Instance.GetCurrentWeatherEffects();
            }

            // Return default effects if weather system is not available
            return new WeatherEffectData();
        }

        /// <summary>
        /// Applies healing bonus from medicine skill
        /// </summary>
        private float ApplyHealingBonus(float baseHealing)
        {
            if (SkillManager.Instance == null) return baseHealing;

            float healingBonus = SkillManager.Instance.GetSkillBonus("medicine", "healing_bonus");
            return baseHealing * (1f + healingBonus);
        }

        /// <summary>
        /// Applies stamina bonus from endurance skill
        /// </summary>
        private float ApplyStaminaBonus(float baseStamina)
        {
            if (SkillManager.Instance == null) return baseStamina;

            float staminaBonus = SkillManager.Instance.GetSkillBonus("endurance", "stamina_bonus");
            return baseStamina * (1f + staminaBonus);
        }

        /// <summary>
        /// Gets the maximum stamina with skill bonuses
        /// </summary>
        public float GetMaxStaminaWithBonuses()
        {
            float baseMaxStamina = _stamina.MaxValue;

            if (SkillManager.Instance != null)
            {
                float maxStaminaBonus = SkillManager.Instance.GetSkillBonus("endurance", "max_stamina");
                baseMaxStamina += maxStaminaBonus;
            }

            return baseMaxStamina;
        }

        #endregion

        public override void _ExitTree()
        {
            if (_instance == this)
            {
                _instance = null;
            }
        }

        public void ResetToDefaults()
        {
            // Reset all stats to default values
            foreach (var stat in _stats.Values)
            {
                stat.SetValue(stat.MaxValue);
            }
            _decayMultiplier = 1f;
            GD.Print("Survival stats reset to defaults");
        }

        public void SetMaxHealthMultiplier(float multiplier)
        {
            if (_stats.ContainsKey("health"))
            {
                _stats["health"].SetMaxValue(_stats["health"].MaxValue * multiplier);
                GD.Print($"Max health multiplier applied: {multiplier}");
            }
        }

        public void SetMaxStaminaMultiplier(float multiplier)
        {
            if (_stats.ContainsKey("stamina"))
            {
                _stats["stamina"].SetMaxValue(_stats["stamina"].MaxValue * multiplier);
                GD.Print($"Max stamina multiplier applied: {multiplier}");
            }
        }

        public void EnableEnhancedRegen()
        {
            _enhancedRegenEnabled = true;
            GD.Print("Enhanced health regeneration enabled");
        }

        public void SetDecayMultiplier(float multiplier)
        {
            _decayMultiplier = multiplier;
            GD.Print($"Survival stats decay multiplier set to: {multiplier}");
        }

        private float _decayMultiplier = 1f;
        private bool _enhancedRegenEnabled = false;
    }

    /// <summary>
    /// Configuration data for survival stats
    /// </summary>
    public class StatConfig
    {
        public float MaxValue { get; set; }
        public float DecayRate { get; set; }
        public bool CanDecay { get; set; }
        private float _decayMultiplier = 1f;
        private bool _enhancedRegenEnabled = false;
    }

    /// <summary>
}