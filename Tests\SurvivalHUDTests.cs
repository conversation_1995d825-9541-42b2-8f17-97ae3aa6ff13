using Godot;

namespace SurvivalLooterShooter
{
    /// <summary>
    /// Test runner for the Survival HUD system
    /// </summary>
    public partial class SurvivalHUDTests : Node
    {
        private SurvivalHUD _hud;
        private SurvivalStatsSystem _survivalStatsSystem;
        private WeaponController _weaponController;
        private Inventory _inventory;

        public override void _Ready()
        {
            GD.Print("Starting Survival HUD Tests...");
            
            // Initialize test systems
            InitializeTestSystems();
            
            // Create and initialize HUD
            InitializeHUD();
            
            // Run tests
            RunTests();
        }

        private void InitializeTestSystems()
        {
            // Create inventory
            _inventory = new Inventory();
            AddChild(_inventory);
            
            // Create survival stats system
            _survivalStatsSystem = new SurvivalStatsSystem();
            AddChild(_survivalStatsSystem);
            
            // Create weapon controller
            _weaponController = new WeaponController();
            AddChild(_weaponController);
            _weaponController.Initialize(_inventory);
            
            // Add test items
            _inventory.AddItem("assault_rifle", 1, new System.Collections.Generic.Dictionary<string, object>
            {
                ["durability"] = 75f,
                ["current_ammo"] = 20
            });
            _inventory.AddItem("rifle_ammo", 60);
            
            // Equip weapon
            _inventory.EquipItem("assault_rifle", "weapon");
            
            GD.Print("Test systems initialized");
        }

        private void InitializeHUD()
        {
            // Load and instantiate HUD scene
            var hudScene = GD.Load<PackedScene>("res://Scenes/SurvivalHUD.tscn");
            _hud = hudScene.Instantiate<SurvivalHUD>();
            AddChild(_hud);
            
            // Initialize HUD with systems
            _hud.Initialize(_survivalStatsSystem, _weaponController);
            
            GD.Print("HUD initialized");
        }

        private void RunTests()
        {
            GD.Print("Running HUD tests...");
            
            // Test 1: Verify HUD displays initial stats
            TestInitialStatsDisplay();
            
            // Test 2: Test stat changes
            TestStatChanges();
            
            // Test 3: Test weapon display
            TestWeaponDisplay();
            
            GD.Print("All HUD tests completed successfully!");
        }

        private void TestInitialStatsDisplay()
        {
            GD.Print("Test 1: Initial stats display");
            
            // Check that survival stats are at full
            if (_survivalStatsSystem.Health.CurrentValue == 100f &&
                _survivalStatsSystem.Hunger.CurrentValue == 100f &&
                _survivalStatsSystem.Thirst.CurrentValue == 100f &&
                _survivalStatsSystem.Stamina.CurrentValue == 100f)
            {
                GD.Print("✓ Initial stats are correct");
            }
            else
            {
                GD.PrintErr("✗ Initial stats are incorrect");
            }
        }

        private void TestStatChanges()
        {
            GD.Print("Test 2: Stat changes");
            
            // Modify stats and verify HUD updates
            _survivalStatsSystem.Health.ModifyValue(-25f);
            _survivalStatsSystem.Hunger.ModifyValue(-50f);
            
            GD.Print($"Health: {_survivalStatsSystem.Health.CurrentValue}/100");
            GD.Print($"Hunger: {_survivalStatsSystem.Hunger.CurrentValue}/100");
            GD.Print("✓ Stat changes applied");
        }

        private void TestWeaponDisplay()
        {
            GD.Print("Test 3: Weapon display");
            
            var weaponInfo = _weaponController.GetWeaponInfo();
            if (weaponInfo != null)
            {
                GD.Print($"Weapon: {weaponInfo.Name}");
                GD.Print($"Ammo: {weaponInfo.CurrentAmmo}/{weaponInfo.MagazineSize}");
                GD.Print($"Condition: {weaponInfo.Condition:F1}%");
                GD.Print("✓ Weapon display working");
            }
            else
            {
                GD.PrintErr("✗ No weapon info available");
            }
        }
    }
}