using Godot;
using System;
using System.Collections.Generic;

namespace SurvivalLooterShooter
{
    /// <summary>
    /// Represents a placed structure in the world
    /// </summary>
    public partial class Structure : Node2D
    {
        [Export] public string StructureId { get; set; }
        [Export] public int CurrentLevel { get; set; } = 0;
        [Export] public int CurrentHealth { get; set; }
        [Export] public int MaxHealth { get; set; }
        [Export] public Vector2 GridPosition { get; set; }
        
        private StructureBlueprint _blueprint;
        private Sprite2D _sprite;
        private CollisionShape2D _collisionShape;
        private Area2D _interactionArea;
        private CraftingStation _craftingStation;
        private StorageContainer _storageContainer;
        private DefensiveTurret _defensiveTurret;
        private DefenseWall _defenseWall;
        private DefenseGate _defenseGate;
        private AlarmSystem _alarmSystem;
        
        // Events
        [Signal]
        public delegate void StructureDamagedEventHandler(Structure structure, int damage);
        
        [Signal]
        public delegate void StructureDestroyedEventHandler(Structure structure);
        
        [Signal]
        public delegate void StructureUpgradedEventHandler(Structure structure, int newLevel);

        public StructureBlueprint Blueprint => _blueprint;
        public bool IsDestroyed => CurrentHealth <= 0;
        public float HealthPercentage => MaxHealth > 0 ? (float)CurrentHealth / MaxHealth : 0f;
        public CraftingStation CraftingStation => _craftingStation;
        public bool IsCraftingStation => _blueprint?.Type == "crafting_station";
        public StorageContainer StorageContainer => _storageContainer;
        public bool IsStorageContainer => _blueprint?.Type == "storage";
        public DefensiveTurret DefensiveTurret => _defensiveTurret;
        public bool IsDefensiveTurret => _blueprint?.Type == "defense" && _blueprint?.DefenseStats != null;
        public DefenseWall DefenseWall => _defenseWall;
        public bool IsDefenseWall => _blueprint?.Type == "defense" && _blueprint?.DefenseStats == null;
        public DefenseGate DefenseGate => _defenseGate;
        public bool IsDefenseGate => _blueprint?.Id?.Contains("gate") == true;
        public AlarmSystem AlarmSystem => _alarmSystem;
        public bool IsAlarmSystem => _blueprint?.Id?.Contains("alarm") == true;

        public override void _Ready()
        {
            // Set up visual components
            _sprite = GetNode<Sprite2D>("Sprite2D");
            _collisionShape = GetNode<CollisionShape2D>("CollisionShape2D");
            _interactionArea = GetNode<Area2D>("InteractionArea");
            
            // Connect interaction signals
            _interactionArea.InputEvent += OnInteractionAreaInputEvent;
            
            AddToGroup("structures");
        }

        /// <summary>
        /// Initializes the structure with a blueprint
        /// </summary>
        public void Initialize(StructureBlueprint blueprint, Vector2 gridPosition, int level = 0)
        {
            _blueprint = blueprint;
            StructureId = blueprint.Id;
            CurrentLevel = level;
            GridPosition = gridPosition;
            MaxHealth = blueprint.GetHealthForLevel(level);
            CurrentHealth = MaxHealth;
            
            // Set up visual representation
            SetupVisuals();
            SetupCollision();
            
            // Set up crafting station if applicable
            if (IsCraftingStation)
            {
                SetupCraftingStation();
            }
            
            // Set up storage container if applicable
            if (IsStorageContainer)
            {
                SetupStorageContainer();
            }
            
            // Set up defensive components if applicable
            if (IsDefensiveTurret)
            {
                SetupDefensiveTurret();
            }
            else if (IsDefenseWall)
            {
                SetupDefenseWall();
            }
            else if (IsDefenseGate)
            {
                SetupDefenseGate();
            }
            else if (IsAlarmSystem)
            {
                SetupAlarmSystem();
            }
            
            Logger.LogInfo("Structure", $"Initialized structure {blueprint.Name} at {gridPosition}");
        }

        /// <summary>
        /// Applies damage to the structure
        /// </summary>
        public void TakeDamage(int damage)
        {
            if (IsDestroyed) return;
            
            CurrentHealth = Math.Max(0, CurrentHealth - damage);
            EmitSignal(SignalName.StructureDamaged, this, damage);
            
            Logger.LogInfo("Structure", $"Structure {StructureId} took {damage} damage. Health: {CurrentHealth}/{MaxHealth}");
            
            if (IsDestroyed)
            {
                EmitSignal(SignalName.StructureDestroyed, this);
                OnDestroyed();
            }
        }

        /// <summary>
        /// Repairs the structure by the specified amount
        /// </summary>
        public void Repair(int repairAmount)
        {
            if (IsDestroyed) return;
            
            CurrentHealth = Math.Min(MaxHealth, CurrentHealth + repairAmount);
            Logger.LogInfo("Structure", $"Structure {StructureId} repaired by {repairAmount}. Health: {CurrentHealth}/{MaxHealth}");
        }

        /// <summary>
        /// Upgrades the structure to the next level
        /// </summary>
        public bool TryUpgrade()
        {
            if (CurrentLevel >= _blueprint.GetMaxLevel())
                return false;
            
            CurrentLevel++;
            MaxHealth = _blueprint.GetHealthForLevel(CurrentLevel);
            CurrentHealth = MaxHealth; // Full health on upgrade
            
            SetupVisuals(); // Update visuals for new level
            
            // Update crafting station level if applicable
            if (IsCraftingStation && _craftingStation != null)
            {
                _craftingStation.StationLevel = CurrentLevel;
            }
            
            EmitSignal(SignalName.StructureUpgraded, this, CurrentLevel);
            
            Logger.LogInfo("Structure", $"Structure {StructureId} upgraded to level {CurrentLevel}");
            return true;
        }

        /// <summary>
        /// Gets the cost to upgrade to the next level
        /// </summary>
        public List<BuildCost> GetUpgradeCost()
        {
            if (CurrentLevel >= _blueprint.GetMaxLevel())
                return new List<BuildCost>();
            
            return _blueprint.GetBuildCostForLevel(CurrentLevel + 1);
        }

        /// <summary>
        /// Checks if this structure can be upgraded
        /// </summary>
        public bool CanUpgrade()
        {
            return CurrentLevel < _blueprint.GetMaxLevel() && !IsDestroyed;
        }

        /// <summary>
        /// Gets interaction options for this structure
        /// </summary>
        public List<string> GetInteractionOptions()
        {
            var options = new List<string>();
            
            if (CanUpgrade())
                options.Add("Upgrade");
            
            if (CurrentHealth < MaxHealth)
                options.Add("Repair");
            
            if (IsCraftingStation && _craftingStation != null)
                options.Add("Use Crafting Station");
            
            if (IsStorageContainer && _storageContainer != null)
                options.Add("Access Storage");
            
            options.Add("Demolish");
            
            return options;
        }

        private void SetupCraftingStation()
        {
            if (!IsCraftingStation) return;

            // Create crafting station component
            _craftingStation = new CraftingStation();
            AddChild(_craftingStation);
            
            // Initialize crafting station based on structure type and level
            string stationType = DetermineStationType();
            _craftingStation.Initialize(StructureId, stationType, CurrentLevel);
            
            Logger.LogInfo("Structure", $"Set up crafting station: {stationType} level {CurrentLevel}");
        }

        private void SetupStorageContainer()
        {
            if (!IsStorageContainer) return;

            // Create storage container using the StorageManager
            var storageManager = StorageManager.Instance;
            if (storageManager != null)
            {
                _storageContainer = storageManager.CreateContainerForStructure(this);
                Logger.LogInfo("Structure", $"Set up storage container: {_storageContainer?.ContainerName}");
            }
            else
            {
                Logger.LogError("Structure", "StorageManager not found - cannot create storage container");
            }
        }

        private string DetermineStationType()
        {
            // Map structure IDs to station types
            return StructureId switch
            {
                "workbench" => "workbench",
                "forge" => "forge",
                "chemistry_lab" => "chemistry_lab",
                "advanced_workbench" => "workbench",
                "blast_furnace" => "forge",
                _ => "workbench" // Default fallback
            };
        }

        private void SetupVisuals()
        {
            if (_sprite == null) return;
            
            // Load texture based on structure ID and level
            string texturePath = $"res://Assets/Structures/{StructureId}_level_{CurrentLevel}.png";
            
            // Fallback to base texture if level-specific doesn't exist
            if (!ResourceLoader.Exists(texturePath))
                texturePath = $"res://Assets/Structures/{StructureId}.png";
            
            if (ResourceLoader.Exists(texturePath))
            {
                _sprite.Texture = GD.Load<Texture2D>(texturePath);
            }
            else
            {
                // Create a placeholder colored rectangle
                var image = Image.CreateEmpty(_blueprint.Size.Width * 32, _blueprint.Size.Height * 32, false, Image.Format.Rgb8);
                image.Fill(GetStructureColor());
                var texture = ImageTexture.CreateFromImage(image);
                _sprite.Texture = texture;
            }
        }

        private void SetupCollision()
        {
            if (_collisionShape == null) return;
            
            // Create collision shape based on structure size
            var shape = new RectangleShape2D();
            shape.Size = new Vector2(_blueprint.Size.Width * 32, _blueprint.Size.Height * 32);
            _collisionShape.Shape = shape;
        }

        private Color GetStructureColor()
        {
            return _blueprint.Type switch
            {
                "foundation" => Colors.Gray,
                "defense" => Colors.Brown,
                "crafting_station" => Colors.Blue,
                "storage" => Colors.Green,
                _ => Colors.White
            };
        }

        private void OnInteractionAreaInputEvent(Node viewport, InputEvent @event, long shapeIdx)
        {
            if (@event is InputEventMouseButton mouseEvent && mouseEvent.Pressed && mouseEvent.ButtonIndex == MouseButton.Right)
            {
                // Right-click interaction
                if (IsCraftingStation && _craftingStation != null)
                {
                    // Open crafting station UI
                    var craftingStationUI = GetNode<CraftingStationUI>("/root/GameManager/UI/CraftingStationUI");
                    if (craftingStationUI != null)
                    {
                        craftingStationUI.OpenStation(_craftingStation);
                    }
                    else
                    {
                        Logger.LogWarning("Structure", "CraftingStationUI not found");
                    }
                }
                else if (IsStorageContainer && _storageContainer != null)
                {
                    // Open storage container UI
                    var storageUI = GetNode<StorageContainerUI>("/root/GameManager/UI/StorageContainerUI");
                    var playerInventory = GetNode<Inventory>("/root/GameManager/Inventory");
                    
                    if (storageUI != null && playerInventory != null)
                    {
                        storageUI.OpenContainer(_storageContainer, playerInventory);
                    }
                    else
                    {
                        Logger.LogWarning("Structure", "StorageContainerUI or Inventory not found");
                    }
                }
                else
                {
                    Logger.LogInfo("Structure", $"Right-clicked on structure {StructureId}");
                }
            }
        }

        private void SetupDefensiveTurret()
        {
            if (!IsDefensiveTurret) return;

            _defensiveTurret = new DefensiveTurret();
            AddChild(_defensiveTurret);
            _defensiveTurret.Initialize(this);
            
            Logger.LogInfo("Structure", $"Set up defensive turret with {_blueprint.DefenseStats.Range} range");
        }

        private void SetupDefenseWall()
        {
            if (!IsDefenseWall) return;

            _defenseWall = new DefenseWall();
            AddChild(_defenseWall);
            _defenseWall.Initialize(this);
            
            Logger.LogInfo("Structure", "Set up defense wall");
        }

        private void SetupDefenseGate()
        {
            if (!IsDefenseGate) return;

            _defenseGate = new DefenseGate();
            AddChild(_defenseGate);
            _defenseGate.Initialize(this);
            
            Logger.LogInfo("Structure", "Set up defense gate");
        }

        private void SetupAlarmSystem()
        {
            if (!IsAlarmSystem) return;

            _alarmSystem = new AlarmSystem();
            AddChild(_alarmSystem);
            _alarmSystem.Initialize(this);
            
            Logger.LogInfo("Structure", "Set up alarm system");
        }

        private void OnDestroyed()
        {
            // Drop some materials when destroyed
            var dropAmount = Math.Max(1, _blueprint.BuildCost.Count / 2);
            // TODO: Implement material dropping logic
            
            Logger.LogInfo("Structure", $"Structure {StructureId} destroyed");
            
            // Remove from world after a short delay to allow for effects
            GetTree().CreateTimer(0.1f).Timeout += QueueFree;
        }
    }
}