using Godot;
using System;
using System.Collections.Generic;
using System.Linq;

namespace SurvivalLooterShooter
{
    /// <summary>
    /// UI for interacting with storage containers
    /// </summary>
    public partial class StorageContainerUI : Control
    {
        // UI References
        private Panel _background;
        private Label _containerNameLabel;
        private Label _capacityLabel;
        private GridContainer _containerGrid;
        private GridContainer _playerInventoryGrid;
        private Button _closeButton;
        private Button _sortButton;
        private Button _lockButton;
        private LineEdit _accessCodeInput;
        private OptionButton _sortModeOption;
        private OptionButton _filterTypeOption;
        private CheckBox _autoSortCheckbox;
        private Panel _accessCodePanel;
        
        // Current container and player inventory
        private StorageContainer _currentContainer;
        private Inventory _playerInventory;
        private string _currentPlayerId = "player"; // TODO: Get from actual player system
        
        // UI state
        private bool _isVisible = false;
        private readonly Dictionary<string, Control> _containerSlots = new();
        private readonly Dictionary<string, Control> _inventorySlots = new();

        public override void _Ready()
        {
            SetupUI();
            ConnectSignals();
            Visible = false;
        }

        private void SetupUI()
        {
            // Create main background panel
            _background = new Panel();
            _background.AnchorLeft = 0.5f;
            _background.AnchorTop = 0.5f;
            _background.AnchorRight = 0.5f;
            _background.AnchorBottom = 0.5f;
            _background.OffsetLeft = -400;
            _background.OffsetTop = -300;
            _background.OffsetRight = 400;
            _background.OffsetBottom = 300;
            AddChild(_background);

            // Container name and info
            var headerContainer = new HBoxContainer();
            _background.AddChild(headerContainer);
            
            _containerNameLabel = new Label();
            _containerNameLabel.Text = "Storage Container";
            _containerNameLabel.AddThemeStyleboxOverride("normal", new StyleBoxFlat());
            headerContainer.AddChild(_containerNameLabel);
            
            _capacityLabel = new Label();
            _capacityLabel.Text = "0/50";
            _capacityLabel.HorizontalAlignment = HorizontalAlignment.Right;
            headerContainer.AddChild(_capacityLabel);

            // Control buttons
            var controlsContainer = new HBoxContainer();
            _background.AddChild(controlsContainer);
            
            _sortButton = new Button();
            _sortButton.Text = "Sort";
            controlsContainer.AddChild(_sortButton);
            
            _lockButton = new Button();
            _lockButton.Text = "Lock";
            controlsContainer.AddChild(_lockButton);
            
            _sortModeOption = new OptionButton();
            _sortModeOption.AddItem("None");
            _sortModeOption.AddItem("Name");
            _sortModeOption.AddItem("Type");
            _sortModeOption.AddItem("Quantity");
            _sortModeOption.AddItem("Value");
            controlsContainer.AddChild(_sortModeOption);
            
            _filterTypeOption = new OptionButton();
            _filterTypeOption.AddItem("All Items");
            _filterTypeOption.AddItem("Weapons");
            _filterTypeOption.AddItem("Tools");
            _filterTypeOption.AddItem("Materials");
            _filterTypeOption.AddItem("Consumables");
            controlsContainer.AddChild(_filterTypeOption);
            
            _autoSortCheckbox = new CheckBox();
            _autoSortCheckbox.Text = "Auto Sort";
            controlsContainer.AddChild(_autoSortCheckbox);

            // Main content area
            var contentContainer = new HSplitContainer();
            _background.AddChild(contentContainer);

            // Container inventory section
            var containerSection = new VBoxContainer();
            contentContainer.AddChild(containerSection);
            
            var containerLabel = new Label();
            containerLabel.Text = "Container";
            containerSection.AddChild(containerLabel);
            
            var containerScroll = new ScrollContainer();
            containerSection.AddChild(containerScroll);
            
            _containerGrid = new GridContainer();
            _containerGrid.Columns = 8;
            containerScroll.AddChild(_containerGrid);

            // Player inventory section
            var inventorySection = new VBoxContainer();
            contentContainer.AddChild(inventorySection);
            
            var inventoryLabel = new Label();
            inventoryLabel.Text = "Your Inventory";
            inventorySection.AddChild(inventoryLabel);
            
            var inventoryScroll = new ScrollContainer();
            inventorySection.AddChild(inventoryScroll);
            
            _playerInventoryGrid = new GridContainer();
            _playerInventoryGrid.Columns = 8;
            inventoryScroll.AddChild(_playerInventoryGrid);

            // Access code panel (initially hidden)
            _accessCodePanel = new Panel();
            _accessCodePanel.Visible = false;
            _background.AddChild(_accessCodePanel);
            
            var codeContainer = new VBoxContainer();
            _accessCodePanel.AddChild(codeContainer);
            
            var codeLabel = new Label();
            codeLabel.Text = "Enter Access Code:";
            codeContainer.AddChild(codeLabel);
            
            _accessCodeInput = new LineEdit();
            _accessCodeInput.PlaceholderText = "Access Code";
            _accessCodeInput.Secret = true;
            codeContainer.AddChild(_accessCodeInput);
            
            var codeButtonContainer = new HBoxContainer();
            codeContainer.AddChild(codeButtonContainer);
            
            var unlockButton = new Button();
            unlockButton.Text = "Unlock";
            unlockButton.Pressed += OnUnlockPressed;
            codeButtonContainer.AddChild(unlockButton);
            
            var cancelButton = new Button();
            cancelButton.Text = "Cancel";
            cancelButton.Pressed += OnCancelAccessPressed;
            codeButtonContainer.AddChild(cancelButton);

            // Close button
            _closeButton = new Button();
            _closeButton.Text = "Close";
            _closeButton.AnchorLeft = 1.0f;
            _closeButton.AnchorTop = 0.0f;
            _closeButton.AnchorRight = 1.0f;
            _closeButton.AnchorBottom = 0.0f;
            _closeButton.OffsetLeft = -60;
            _closeButton.OffsetTop = 0;
            _closeButton.OffsetRight = 0;
            _closeButton.OffsetBottom = 30;
            _background.AddChild(_closeButton);
        }

        private void ConnectSignals()
        {
            _closeButton.Pressed += OnClosePressed;
            _sortButton.Pressed += OnSortPressed;
            _lockButton.Pressed += OnLockPressed;
            _sortModeOption.ItemSelected += OnSortModeSelected;
            _filterTypeOption.ItemSelected += OnFilterTypeSelected;
            _autoSortCheckbox.Toggled += OnAutoSortToggled;
        }

        /// <summary>
        /// Opens the storage container UI
        /// </summary>
        public void OpenContainer(StorageContainer container, Inventory playerInventory)
        {
            if (container == null || playerInventory == null)
            {
                Logger.LogError("StorageContainerUI", "Cannot open container: container or inventory is null");
                return;
            }

            // Check access permissions
            if (container.IsLocked && !container.HasPermission(_currentPlayerId, ContainerPermission.View))
            {
                ShowAccessCodePanel();
                return;
            }

            _currentContainer = container;
            _playerInventory = playerInventory;
            
            // Connect to container events
            if (!_currentContainer.IsConnected(StorageContainer.SignalName.ContainerChanged, new Callable(this, nameof(OnContainerChanged))))
                _currentContainer.ContainerChanged += OnContainerChanged;
            
            if (!_playerInventory.IsConnected(Inventory.SignalName.InventoryChanged, new Callable(this, nameof(OnInventoryChanged))))
                _playerInventory.InventoryChanged += OnInventoryChanged;

            UpdateUI();
            Show();
            
            _currentContainer.LogAccess(_currentPlayerId, ContainerAccessType.Opened);
            Logger.LogInfo("StorageContainerUI", $"Opened container: {container.ContainerName}");
        }

        /// <summary>
        /// Closes the storage container UI
        /// </summary>
        public void CloseContainer()
        {
            if (_currentContainer != null)
            {
                // Disconnect from container events
                if (_currentContainer.IsConnected(StorageContainer.SignalName.ContainerChanged, new Callable(this, nameof(OnContainerChanged))))
                    _currentContainer.ContainerChanged -= OnContainerChanged;
                
                _currentContainer.LogAccess(_currentPlayerId, ContainerAccessType.Closed);
                _currentContainer = null;
            }

            if (_playerInventory != null)
            {
                if (_playerInventory.IsConnected(Inventory.SignalName.InventoryChanged, new Callable(this, nameof(OnInventoryChanged))))
                    _playerInventory.InventoryChanged -= OnInventoryChanged;
                
                _playerInventory = null;
            }

            Hide();
            _accessCodePanel.Visible = false;
            Logger.LogInfo("StorageContainerUI", "Closed storage container UI");
        }

        private void UpdateUI()
        {
            if (_currentContainer == null) return;

            // Update header info
            _containerNameLabel.Text = _currentContainer.ContainerName;
            _capacityLabel.Text = $"{_currentContainer.CurrentCapacity}/{_currentContainer.MaxCapacity}";
            
            // Update lock button
            _lockButton.Text = _currentContainer.IsLocked ? "Unlock" : "Lock";
            
            // Update container grid
            UpdateContainerGrid();
            
            // Update player inventory grid
            UpdatePlayerInventoryGrid();
        }

        private void UpdateContainerGrid()
        {
            // Clear existing slots
            foreach (var slot in _containerSlots.Values)
                slot.QueueFree();
            _containerSlots.Clear();

            if (_currentContainer == null) return;

            // Get filtered items
            string typeFilter = GetSelectedTypeFilter();
            var items = _currentContainer.GetItems(typeFilter);

            // Create slots for items
            foreach (var slot in items)
            {
                var slotUI = CreateItemSlot(slot, true);
                _containerGrid.AddChild(slotUI);
                _containerSlots[slot.ItemId] = slotUI;
            }
        }

        private void UpdatePlayerInventoryGrid()
        {
            // Clear existing slots
            foreach (var slot in _inventorySlots.Values)
                slot.QueueFree();
            _inventorySlots.Clear();

            if (_playerInventory == null) return;

            // Get player inventory items
            var items = _playerInventory.GetAllItems();

            // Create slots for items
            foreach (var kvp in items)
            {
                var slotUI = CreateItemSlot(kvp.Value, false);
                _playerInventoryGrid.AddChild(slotUI);
                _inventorySlots[kvp.Key] = slotUI;
            }
        }

        private Control CreateItemSlot(InventorySlot slot, bool isContainerSlot)
        {
            var slotPanel = new Panel();
            slotPanel.CustomMinimumSize = new Vector2(64, 64);
            
            var item = ItemDatabase.Instance?.GetItem(slot.ItemId);
            if (item == null) return slotPanel;

            // Item icon (placeholder)
            var icon = new ColorRect();
            icon.Color = GetItemColor(item.Type);
            icon.AnchorLeft = 0;
            icon.AnchorTop = 0;
            icon.AnchorRight = 1;
            icon.AnchorBottom = 1;
            slotPanel.AddChild(icon);

            // Quantity label
            if (slot.Quantity > 1)
            {
                var quantityLabel = new Label();
                quantityLabel.Text = slot.Quantity.ToString();
                quantityLabel.AnchorLeft = 1.0f;
                quantityLabel.AnchorTop = 1.0f;
                quantityLabel.AnchorRight = 1.0f;
                quantityLabel.AnchorBottom = 1.0f;
                slotPanel.AddChild(quantityLabel);
            }

            // Click handling
            var button = new Button();
            button.AnchorLeft = 0;
            button.AnchorTop = 0;
            button.AnchorRight = 1;
            button.AnchorBottom = 1;
            button.Flat = true;
            button.Pressed += () => OnItemSlotClicked(slot.ItemId, isContainerSlot);
            slotPanel.AddChild(button);

            // Tooltip
            button.TooltipText = $"{item.Name}\nQuantity: {slot.Quantity}\nType: {item.Type}";

            return slotPanel;
        }

        private Color GetItemColor(string itemType)
        {
            return itemType switch
            {
                "weapon" => Colors.Red,
                "tool" => Colors.Orange,
                "material" => Colors.Gray,
                "consumable" => Colors.Green,
                "armor" => Colors.Blue,
                _ => Colors.White
            };
        }

        private string GetSelectedTypeFilter()
        {
            return _filterTypeOption.Selected switch
            {
                1 => "weapon",
                2 => "tool",
                3 => "material",
                4 => "consumable",
                _ => null
            };
        }

        private void ShowAccessCodePanel()
        {
            _accessCodePanel.Visible = true;
            _accessCodeInput.Text = "";
            _accessCodeInput.GrabFocus();
        }

        // Event handlers
        private void OnClosePressed()
        {
            CloseContainer();
        }

        private void OnSortPressed()
        {
            if (_currentContainer == null) return;
            
            var sortMode = (ContainerSortMode)_sortModeOption.Selected;
            _currentContainer.SortContainer(sortMode);
        }

        private void OnLockPressed()
        {
            if (_currentContainer == null) return;
            
            if (_currentContainer.IsLocked)
            {
                ShowAccessCodePanel();
            }
            else
            {
                // TODO: Show dialog to set access code
                _currentContainer.Lock("1234"); // Placeholder
            }
        }

        private void OnSortModeSelected(long index)
        {
            if (_currentContainer == null) return;
            
            var sortMode = (ContainerSortMode)index;
            _currentContainer.SortContainer(sortMode);
        }

        private void OnFilterTypeSelected(long index)
        {
            UpdateContainerGrid();
        }

        private void OnAutoSortToggled(bool pressed)
        {
            if (_currentContainer == null) return;
            
            var sortMode = (ContainerSortMode)_sortModeOption.Selected;
            _currentContainer.SetAutoSort(pressed, sortMode);
        }

        private void OnUnlockPressed()
        {
            if (_currentContainer == null) return;
            
            string code = _accessCodeInput.Text;
            if (_currentContainer.TryUnlock(code))
            {
                _accessCodePanel.Visible = false;
                OpenContainer(_currentContainer, _playerInventory);
            }
            else
            {
                Logger.LogWarning("StorageContainerUI", "Invalid access code");
                // TODO: Show error message
            }
        }

        private void OnCancelAccessPressed()
        {
            _accessCodePanel.Visible = false;
            CloseContainer();
        }

        private void OnItemSlotClicked(string itemId, bool isContainerSlot)
        {
            if (_currentContainer == null || _playerInventory == null) return;

            // Check permissions
            if (isContainerSlot && !_currentContainer.HasPermission(_currentPlayerId, ContainerPermission.Withdraw))
            {
                Logger.LogWarning("StorageContainerUI", "No permission to withdraw from container");
                return;
            }

            if (!isContainerSlot && !_currentContainer.HasPermission(_currentPlayerId, ContainerPermission.Deposit))
            {
                Logger.LogWarning("StorageContainerUI", "No permission to deposit to container");
                return;
            }

            // Transfer item
            if (isContainerSlot)
            {
                // Move from container to player inventory
                var slot = _currentContainer.Items[itemId];
                int transferAmount = Input.IsActionPressed("ui_accept") ? slot.Quantity : 1; // Shift+click for all
                
                if (_playerInventory.CanAddItem(itemId, transferAmount))
                {
                    if (_currentContainer.TryRemoveItem(itemId, transferAmount))
                    {
                        _playerInventory.AddItem(itemId, transferAmount, slot.Metadata);
                        _currentContainer.LogAccess(_currentPlayerId, ContainerAccessType.ItemRemoved);
                    }
                }
            }
            else
            {
                // Move from player inventory to container
                var allItems = _playerInventory.GetAllItems();
                if (allItems.TryGetValue(itemId, out var slot))
                {
                    int transferAmount = Input.IsActionPressed("ui_accept") ? slot.Quantity : 1; // Shift+click for all
                    
                    if (_currentContainer.TryAddItem(itemId, transferAmount, slot.Metadata))
                    {
                        _playerInventory.RemoveItem(itemId, transferAmount);
                        _currentContainer.LogAccess(_currentPlayerId, ContainerAccessType.ItemAdded);
                    }
                }
            }
        }

        private void OnContainerChanged(string itemId, int quantityChange)
        {
            UpdateUI();
        }

        private void OnInventoryChanged(string itemId, int quantityChange)
        {
            UpdatePlayerInventoryGrid();
        }

        public override void _Input(InputEvent @event)
        {
            if (@event is InputEventKey keyEvent && keyEvent.Pressed)
            {
                if (keyEvent.Keycode == Key.Escape && Visible)
                {
                    CloseContainer();
                }
            }
        }
    }
}