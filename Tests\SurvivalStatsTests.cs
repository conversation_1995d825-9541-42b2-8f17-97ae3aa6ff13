using Godot;
using System.Collections.Generic;

namespace SurvivalLooterShooter
{
    /// <summary>
    /// Unit tests for the survival stats system
    /// </summary>
    public partial class SurvivalStatsTests : Node
    {
        private SurvivalStatsSystem _statsSystem;
        private bool _testsPassed = true;
        private int _testsRun = 0;
        private int _testsPassing = 0;

        public override void _Ready()
        {
            GD.Print("=== Starting Survival Stats System Tests ===");
            RunAllTests();
        }

        private void RunAllTests()
        {
            // Initialize test environment
            SetupTestEnvironment();
            
            // Run individual stat tests
            TestSurvivalStatBasicFunctionality();
            TestSurvivalStatDecay();
            TestSurvivalStatThresholds();
            TestSurvivalStatSerialization();
            
            // Run system tests
            TestSurvivalStatsSystemInitialization();
            TestConsumableProcessing();
            TestStatDecayAndDebuffs();
            TestDeathAndRespawnMechanics();
            TestStatThresholdEvents();
            TestSaveLoadFunctionality();
            
            // Print results
            PrintTestResults();
        }

        private void SetupTestEnvironment()
        {
            // Create stats system for testing
            _statsSystem = new SurvivalStatsSystem();
            AddChild(_statsSystem);
            
            // Wait for initialization
            GetTree().CreateTimer(0.1f).Timeout += () => { };
        }

        #region Individual Stat Tests

        private void TestSurvivalStatBasicFunctionality()
        {
            GD.Print("Testing SurvivalStat basic functionality...");
            
            var stat = new SurvivalStat();
            AddChild(stat);
            stat.Initialize("TestStat", 100f, 50f, 1f, true);
            
            // Test initialization
            AssertEqual(stat.StatName, "TestStat", "Stat name initialization");
            AssertEqual(stat.MaxValue, 100f, "Max value initialization");
            AssertEqual(stat.CurrentValue, 50f, "Current value initialization");
            AssertEqual(stat.DecayRate, 1f, "Decay rate initialization");
            AssertEqual(stat.CanDecay, true, "Can decay initialization");
            AssertEqual(stat.Percentage, 50f, "Percentage calculation");
            
            // Test value modification
            stat.ModifyValue(25f);
            AssertEqual(stat.CurrentValue, 75f, "Value modification (positive)");
            
            stat.ModifyValue(-30f);
            AssertEqual(stat.CurrentValue, 45f, "Value modification (negative)");
            
            // Test clamping
            stat.SetValue(150f);
            AssertEqual(stat.CurrentValue, 100f, "Value clamping (upper bound)");
            
            stat.SetValue(-10f);
            AssertEqual(stat.CurrentValue, 0f, "Value clamping (lower bound)");
            
            // Test state checks
            AssertEqual(stat.IsEmpty, true, "IsEmpty check");
            AssertEqual(stat.IsFull, false, "IsFull check (empty)");
            
            stat.RestoreToFull();
            AssertEqual(stat.IsFull, true, "IsFull check (full)");
            AssertEqual(stat.IsEmpty, false, "IsEmpty check (full)");
            
            stat.QueueFree();
        }

        private void TestSurvivalStatDecay()
        {
            GD.Print("Testing SurvivalStat decay functionality...");
            
            var stat = new SurvivalStat();
            AddChild(stat);
            stat.Initialize("DecayTest", 100f, 100f, 10f, true);
            
            // Test decay application
            float initialValue = stat.CurrentValue;
            stat.ApplyDecay(1f); // 1 second of decay
            AssertEqual(stat.CurrentValue, initialValue - 10f, "Decay application");
            
            // Test decay disabled
            stat.CanDecay = false;
            float valueBeforeDecay = stat.CurrentValue;
            stat.ApplyDecay(1f);
            AssertEqual(stat.CurrentValue, valueBeforeDecay, "Decay disabled");
            
            // Test decay at zero
            stat.CanDecay = true;
            stat.SetValue(0f);
            stat.ApplyDecay(1f);
            AssertEqual(stat.CurrentValue, 0f, "Decay at zero value");
            
            stat.QueueFree();
        }

        private void TestSurvivalStatThresholds()
        {
            GD.Print("Testing SurvivalStat threshold functionality...");
            
            var stat = new SurvivalStat();
            AddChild(stat);
            stat.Initialize("ThresholdTest", 100f, 100f, 0f, false);
            
            // Test threshold checks
            stat.SetValue(75f);
            AssertEqual(stat.IsBelowThreshold(80f), true, "Below threshold (true)");
            AssertEqual(stat.IsBelowThreshold(70f), false, "Below threshold (false)");
            
            stat.SetValue(25f);
            AssertEqual(stat.IsBelowThreshold(30f), true, "Below threshold (low value)");
            
            stat.QueueFree();
        }

        private void TestSurvivalStatSerialization()
        {
            GD.Print("Testing SurvivalStat serialization...");
            
            var stat = new SurvivalStat();
            AddChild(stat);
            stat.Initialize("SerializationTest", 100f, 75f, 2f, true);
            
            // Get serialization data
            var data = stat.GetStatData();
            AssertEqual(data.StatName, "SerializationTest", "Serialization - stat name");
            AssertEqual(data.MaxValue, 100f, "Serialization - max value");
            AssertEqual(data.CurrentValue, 75f, "Serialization - current value");
            AssertEqual(data.DecayRate, 2f, "Serialization - decay rate");
            AssertEqual(data.CanDecay, true, "Serialization - can decay");
            
            // Test loading from data
            var newStat = new SurvivalStat();
            AddChild(newStat);
            newStat.LoadStatData(data);
            
            AssertEqual(newStat.StatName, "SerializationTest", "Deserialization - stat name");
            AssertEqual(newStat.MaxValue, 100f, "Deserialization - max value");
            AssertEqual(newStat.CurrentValue, 75f, "Deserialization - current value");
            AssertEqual(newStat.DecayRate, 2f, "Deserialization - decay rate");
            AssertEqual(newStat.CanDecay, true, "Deserialization - can decay");
            
            stat.QueueFree();
            newStat.QueueFree();
        }

        #endregion

        #region System Tests

        private void TestSurvivalStatsSystemInitialization()
        {
            GD.Print("Testing SurvivalStatsSystem initialization...");
            
            // Test that all stats are initialized
            AssertNotNull(_statsSystem.Health, "Health stat initialization");
            AssertNotNull(_statsSystem.Hunger, "Hunger stat initialization");
            AssertNotNull(_statsSystem.Thirst, "Thirst stat initialization");
            AssertNotNull(_statsSystem.Stamina, "Stamina stat initialization");
            
            // Test initial values
            AssertEqual(_statsSystem.Health.MaxValue, 100f, "Health max value");
            AssertEqual(_statsSystem.Hunger.MaxValue, 100f, "Hunger max value");
            AssertEqual(_statsSystem.Thirst.MaxValue, 100f, "Thirst max value");
            AssertEqual(_statsSystem.Stamina.MaxValue, 100f, "Stamina max value");
            
            // Test decay settings
            AssertEqual(_statsSystem.Health.CanDecay, false, "Health decay disabled");
            AssertEqual(_statsSystem.Hunger.CanDecay, true, "Hunger decay enabled");
            AssertEqual(_statsSystem.Thirst.CanDecay, true, "Thirst decay enabled");
            AssertEqual(_statsSystem.Stamina.CanDecay, true, "Stamina decay enabled");
            
            AssertEqual(_statsSystem.IsDead, false, "Initial death state");
        }

        private void TestConsumableProcessing()
        {
            GD.Print("Testing consumable processing...");
            
            // Reset stats to known values
            _statsSystem.Health.SetValue(50f);
            _statsSystem.Hunger.SetValue(30f);
            _statsSystem.Thirst.SetValue(40f);
            _statsSystem.Stamina.SetValue(60f);
            
            // Test health potion consumption
            bool consumed = _statsSystem.ConsumeItem("health_potion");
            AssertEqual(consumed, true, "Health potion consumption");
            AssertEqual(_statsSystem.Health.CurrentValue, 100f, "Health restored by potion"); // 50 + 75 = 125, clamped to 100
            
            // Test water consumption
            consumed = _statsSystem.ConsumeItem("water");
            AssertEqual(consumed, true, "Water consumption");
            AssertEqual(_statsSystem.Thirst.CurrentValue, 70f, "Thirst restored by water"); // 40 + 30 = 70
            
            // Test MRE consumption (multiple stats)
            consumed = _statsSystem.ConsumeItem("mre");
            AssertEqual(consumed, true, "MRE consumption");
            AssertEqual(_statsSystem.Hunger.CurrentValue, 90f, "Hunger restored by MRE"); // 30 + 60 = 90
            AssertEqual(_statsSystem.Thirst.CurrentValue, 80f, "Thirst restored by MRE"); // 70 + 10 = 80
            AssertEqual(_statsSystem.Health.CurrentValue, 100f, "Health maintained by MRE"); // Already at max
            
            // Test invalid item consumption
            consumed = _statsSystem.ConsumeItem("invalid_item");
            AssertEqual(consumed, false, "Invalid item consumption");
            
            // Test non-consumable item
            consumed = _statsSystem.ConsumeItem("assault_rifle");
            AssertEqual(consumed, false, "Non-consumable item consumption");
        }

        private void TestStatDecayAndDebuffs()
        {
            GD.Print("Testing stat decay and debuff application...");
            
            // Set stats to trigger debuffs
            _statsSystem.Health.SetValue(100f);
            _statsSystem.Hunger.SetValue(5f); // Below danger threshold
            _statsSystem.Thirst.SetValue(8f); // Below danger threshold
            _statsSystem.Stamina.SetValue(20f); // Below critical threshold
            
            float initialHealth = _statsSystem.Health.CurrentValue;
            
            // Simulate decay timer (this would normally be called by the timer)
            // We need to access the private method, so we'll test the effects indirectly
            // by checking if health decreases due to low hunger/thirst
            
            // Wait a bit and check if debuffs are applied
            GetTree().CreateTimer(2f).Timeout += () => {
                // Health should have decreased due to severe hunger and dehydration
                AssertTrue(_statsSystem.Health.CurrentValue < initialHealth, "Health decreased due to debuffs");
                
                // Check active debuffs
                var debuffs = _statsSystem.GetActiveDebuffs();
                AssertTrue(debuffs.Count > 0, "Active debuffs present");
            };
        }

        private void TestDeathAndRespawnMechanics()
        {
            GD.Print("Testing death and respawn mechanics...");
            
            bool deathEventFired = false;
            bool respawnEventFired = false;
            
            // Connect to death/respawn events
            _statsSystem.PlayerDied += () => { deathEventFired = true; };
            _statsSystem.PlayerRespawned += () => { respawnEventFired = true; };
            
            // Trigger death by setting health to zero
            _statsSystem.Health.SetValue(0f);
            
            // Check death state
            GetTree().CreateTimer(0.1f).Timeout += () => {
                AssertEqual(_statsSystem.IsDead, true, "Player death state");
                AssertEqual(deathEventFired, true, "Death event fired");
                
                // Wait for respawn (3 seconds + buffer)
                GetTree().CreateTimer(3.5f).Timeout += () => {
                    AssertEqual(_statsSystem.IsDead, false, "Player respawn state");
                    AssertEqual(respawnEventFired, true, "Respawn event fired");
                    AssertTrue(_statsSystem.Health.CurrentValue > 0f, "Health restored on respawn");
                };
            };
        }

        private void TestStatThresholdEvents()
        {
            GD.Print("Testing stat threshold events...");
            
            bool thresholdEventFired = false;
            string thresholdStatName = "";
            float thresholdValue = 0f;
            
            // Connect to threshold event
            _statsSystem.StatThresholdReached += (statName, threshold) => {
                thresholdEventFired = true;
                thresholdStatName = statName;
                thresholdValue = threshold;
            };
            
            // Trigger threshold by reducing hunger
            _statsSystem.Hunger.SetValue(20f); // Should trigger critical threshold
            
            GetTree().CreateTimer(0.1f).Timeout += () => {
                AssertEqual(thresholdEventFired, true, "Threshold event fired");
                AssertEqual(thresholdStatName, "hunger", "Correct stat name in threshold event");
                AssertEqual(thresholdValue, 25f, "Correct threshold value");
            };
        }

        private void TestSaveLoadFunctionality()
        {
            GD.Print("Testing save/load functionality...");
            
            // Set specific stat values
            _statsSystem.Health.SetValue(75f);
            _statsSystem.Hunger.SetValue(60f);
            _statsSystem.Thirst.SetValue(45f);
            _statsSystem.Stamina.SetValue(80f);
            
            // Get save data
            var saveData = _statsSystem.GetAllStatsData();
            
            // Verify save data
            AssertEqual(saveData["health"].CurrentValue, 75f, "Health save data");
            AssertEqual(saveData["hunger"].CurrentValue, 60f, "Hunger save data");
            AssertEqual(saveData["thirst"].CurrentValue, 45f, "Thirst save data");
            AssertEqual(saveData["stamina"].CurrentValue, 80f, "Stamina save data");
            
            // Modify stats
            _statsSystem.Health.SetValue(100f);
            _statsSystem.Hunger.SetValue(100f);
            _statsSystem.Thirst.SetValue(100f);
            _statsSystem.Stamina.SetValue(100f);
            
            // Load save data
            _statsSystem.LoadAllStatsData(saveData);
            
            // Verify loaded values
            AssertEqual(_statsSystem.Health.CurrentValue, 75f, "Health load data");
            AssertEqual(_statsSystem.Hunger.CurrentValue, 60f, "Hunger load data");
            AssertEqual(_statsSystem.Thirst.CurrentValue, 45f, "Thirst load data");
            AssertEqual(_statsSystem.Stamina.CurrentValue, 80f, "Stamina load data");
        }

        #endregion

        #region Test Utilities

        private void AssertEqual<T>(T actual, T expected, string testName)
        {
            _testsRun++;
            if (actual.Equals(expected))
            {
                _testsPassing++;
                GD.Print($"  ✓ {testName}: PASS");
            }
            else
            {
                _testsPassed = false;
                GD.PrintErr($"  ✗ {testName}: FAIL - Expected {expected}, got {actual}");
            }
        }

        private void AssertTrue(bool condition, string testName)
        {
            _testsRun++;
            if (condition)
            {
                _testsPassing++;
                GD.Print($"  ✓ {testName}: PASS");
            }
            else
            {
                _testsPassed = false;
                GD.PrintErr($"  ✗ {testName}: FAIL - Expected true, got false");
            }
        }

        private void AssertNotNull<T>(T value, string testName) where T : class
        {
            _testsRun++;
            if (value != null)
            {
                _testsPassing++;
                GD.Print($"  ✓ {testName}: PASS");
            }
            else
            {
                _testsPassed = false;
                GD.PrintErr($"  ✗ {testName}: FAIL - Expected non-null value");
            }
        }

        private void PrintTestResults()
        {
            GD.Print("\n=== Survival Stats System Test Results ===");
            GD.Print($"Tests Run: {_testsRun}");
            GD.Print($"Tests Passing: {_testsPassing}");
            GD.Print($"Tests Failing: {_testsRun - _testsPassing}");
            
            if (_testsPassed)
            {
                GD.Print("🎉 ALL TESTS PASSED! 🎉");
            }
            else
            {
                GD.PrintErr("❌ SOME TESTS FAILED ❌");
            }
            
            GD.Print("==========================================\n");
        }

        #endregion
    }
}