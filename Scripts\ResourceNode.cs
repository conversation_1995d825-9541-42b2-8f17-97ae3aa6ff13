using Godot;
using System;
using System.Collections.Generic;

namespace SurvivalLooterShooter
{
    /// <summary>
    /// ResourceNode represents a harvestable resource in the world (trees, rocks, plants)
    /// Handles resource depletion, regeneration, tool requirements, and harvesting mechanics
    /// </summary>
    public partial class ResourceNode : Area2D
    {
        // Resource properties
        [Export] public string ResourceId { get; set; } = "";
        [Export] public string ResourceType { get; set; } = ""; // organic, mineral, etc.
        [Export] public string RequiredTool { get; set; } = "none";
        [Export] public int MaxYield { get; set; } = 5;
        [Export] public int MinYield { get; set; } = 1;
        [Export] public float HarvestTime { get; set; } = 2.0f;
        [Export] public float RegenerationTime { get; set; } = 300.0f; // 5 minutes
        [Export] public float InteractionRange { get; set; } = 50.0f;

        // Current state
        private int _currentYield;
        private bool _isDepleted = false;
        private bool _isBeingHarvested = false;
        private float _regenerationTimer = 0.0f;
        private PlayerController _harvestingPlayer = null;
        private float _harvestProgress = 0.0f;

        // Visual components
        private Sprite2D _sprite;
        private CollisionShape2D _collisionShape;
        private ProgressBar _harvestProgressBar;
        private Label _interactionLabel;

        // Harvesting effects
        private GpuParticles2D _harvestParticles;
        private AudioStreamPlayer2D _harvestSound;

        // Events
        [Signal]
        public delegate void ResourceHarvestedEventHandler(string resourceId, int amount, Vector2 position);
        
        [Signal]
        public delegate void ResourceDepletedEventHandler(string resourceId, Vector2 position);
        
        [Signal]
        public delegate void ResourceRegeneratedEventHandler(string resourceId, Vector2 position);

        public bool IsDepleted => _isDepleted;
        public bool IsBeingHarvested => _isBeingHarvested;
        public int CurrentYield => _currentYield;
        public float RegenerationProgress => _isDepleted ? (_regenerationTimer / RegenerationTime) : 1.0f;

        public override void _Ready()
        {
            // Initialize resource node
            InitializeResourceNode();
            
            // Add to resource nodes group
            AddToGroup("resource_nodes");
            
            GD.Print($"ResourceNode {ResourceId} initialized at {GlobalPosition}");
        }

        /// <summary>
        /// Initializes the resource node with default values and components
        /// </summary>
        private void InitializeResourceNode()
        {
            // Set initial yield
            _currentYield = MaxYield;
            
            // Create visual components
            CreateVisualComponents();
            
            // Create collision shape
            CreateCollisionShape();
            
            // Create UI components
            CreateUIComponents();
            
            // Create effects
            CreateHarvestingEffects();
            
            // Connect area signals
            BodyEntered += OnBodyEntered;
            BodyExited += OnBodyExited;
            
            // Update visual state
            UpdateVisualState();
        }

        /// <summary>
        /// Creates the visual components for the resource node
        /// </summary>
        private void CreateVisualComponents()
        {
            _sprite = new Sprite2D();
            AddChild(_sprite);
            
            // Set sprite based on resource type
            SetResourceSprite();
        }

        /// <summary>
        /// Sets the appropriate sprite based on resource type
        /// </summary>
        private void SetResourceSprite()
        {
            // Create a simple colored rectangle as placeholder
            var texture = new ImageTexture();
            var image = Image.CreateEmpty(32, 32, false, Image.Format.Rgb8);
            
            Color spriteColor = ResourceType switch
            {
                "organic" => Colors.Green,
                "mineral" => Colors.Gray,
                _ => Colors.White
            };
            
            image.Fill(spriteColor);
            texture.SetImage(image);
            _sprite.Texture = texture;
        }

        /// <summary>
        /// Creates the collision shape for interaction detection
        /// </summary>
        private void CreateCollisionShape()
        {
            _collisionShape = new CollisionShape2D();
            var shape = new CircleShape2D();
            shape.Radius = InteractionRange;
            _collisionShape.Shape = shape;
            AddChild(_collisionShape);
        }

        /// <summary>
        /// Creates UI components for interaction feedback
        /// </summary>
        private void CreateUIComponents()
        {
            // Create interaction label
            _interactionLabel = new Label();
            _interactionLabel.Text = $"[E] Harvest {ResourceId}";
            _interactionLabel.Position = new Vector2(-50, -60);
            _interactionLabel.Visible = false;
            AddChild(_interactionLabel);
            
            // Create harvest progress bar
            _harvestProgressBar = new ProgressBar();
            _harvestProgressBar.Size = new Vector2(80, 10);
            _harvestProgressBar.Position = new Vector2(-40, -45);
            _harvestProgressBar.Visible = false;
            _harvestProgressBar.Value = 0;
            _harvestProgressBar.MaxValue = 100;
            AddChild(_harvestProgressBar);
        }

        /// <summary>
        /// Creates harvesting effects (particles and sound)
        /// </summary>
        private void CreateHarvestingEffects()
        {
            // Create harvest particles
            _harvestParticles = new GpuParticles2D();
            _harvestParticles.Emitting = false;
            _harvestParticles.Amount = 50;
            _harvestParticles.Lifetime = 2.0f;
            
            // Configure particle material based on resource type
            var material = new ParticleProcessMaterial();
            material.Direction = Vector3.Up;
            material.InitialVelocityMin = 20.0f;
            material.InitialVelocityMax = 50.0f;
            material.AngularVelocityMin = -180.0f;
            material.AngularVelocityMax = 180.0f;
            material.Gravity = Vector3.Down * 98.0f;
            material.ScaleMin = 0.5f;
            material.ScaleMax = 1.5f;
            
            // Set particle color based on resource type
            Color particleColor = ResourceType switch
            {
                "organic" => Colors.Brown,
                "mineral" => Colors.LightGray,
                _ => Colors.White
            };
            material.Color = particleColor;
            
            _harvestParticles.ProcessMaterial = material;
            AddChild(_harvestParticles);
            
            // Create harvest sound
            _harvestSound = new AudioStreamPlayer2D();
            // Note: In a full implementation, you would load appropriate sound files
            AddChild(_harvestSound);
        }

        public override void _Process(double delta)
        {
            if (_isDepleted)
            {
                UpdateRegeneration((float)delta);
            }
            
            if (_isBeingHarvested)
            {
                UpdateHarvesting((float)delta);
            }
        }

        /// <summary>
        /// Updates resource regeneration when depleted
        /// </summary>
        private void UpdateRegeneration(float delta)
        {
            _regenerationTimer += delta;
            
            if (_regenerationTimer >= RegenerationTime)
            {
                RegenerateResource();
            }
        }

        /// <summary>
        /// Updates harvesting progress
        /// </summary>
        private void UpdateHarvesting(float delta)
        {
            if (_harvestingPlayer == null)
            {
                StopHarvesting();
                return;
            }
            
            _harvestProgress += delta;
            float progressPercent = (_harvestProgress / HarvestTime) * 100.0f;
            _harvestProgressBar.Value = progressPercent;
            
            if (_harvestProgress >= HarvestTime)
            {
                CompleteHarvesting();
            }
        }

        /// <summary>
        /// Attempts to start harvesting this resource node
        /// </summary>
        public bool TryStartHarvesting(PlayerController player)
        {
            if (_isDepleted || _isBeingHarvested || player == null)
                return false;
            
            // Check if player has required tool
            if (!HasRequiredTool(player))
            {
                ShowToolRequiredMessage();
                return false;
            }
            
            // Check if player is within range
            float distance = GlobalPosition.DistanceTo(player.GlobalPosition);
            if (distance > InteractionRange)
                return false;
            
            StartHarvesting(player);
            return true;
        }

        /// <summary>
        /// Checks if the player has the required tool for harvesting
        /// </summary>
        private bool HasRequiredTool(PlayerController player)
        {
            if (RequiredTool == "none")
                return true;
            
            // Get player's inventory through the player controller
            var inventory = GetPlayerInventory(player);
            if (inventory == null)
                return false;
            
            return inventory.HasItem(RequiredTool);
        }

        /// <summary>
        /// Gets the player's inventory (helper method)
        /// </summary>
        private Inventory GetPlayerInventory(PlayerController player)
        {
            // Access the inventory through the GameManager
            var gameManager = GetTree().GetFirstNodeInGroup("game_manager") as GameManager;
            return gameManager?.GetInventory();
        }

        /// <summary>
        /// Shows a message indicating the required tool
        /// </summary>
        private void ShowToolRequiredMessage()
        {
            if (RequiredTool != "none")
            {
                var item = ItemDatabase.Instance?.GetItem(RequiredTool);
                string toolName = item?.Name ?? RequiredTool;
                GD.Print($"Requires {toolName} to harvest {ResourceId}");
                
                // In a full implementation, this would show a UI message
                EventBus.Instance?.EmitSignal(EventBus.SignalName.PlayerActionPerformed, 
                    "tool_required", $"{toolName} required", 0.0f);
            }
        }

        /// <summary>
        /// Starts the harvesting process
        /// </summary>
        private void StartHarvesting(PlayerController player)
        {
            _isBeingHarvested = true;
            _harvestingPlayer = player;
            _harvestProgress = 0.0f;
            
            // Show progress bar
            _harvestProgressBar.Visible = true;
            _harvestProgressBar.Value = 0;
            
            // Start particle effects
            _harvestParticles.Emitting = true;
            
            // Play harvest sound
            _harvestSound.Play();
            
            GD.Print($"Started harvesting {ResourceId}");
            
            // Emit event
            EventBus.Instance?.EmitSignal(EventBus.SignalName.PlayerActionPerformed, 
                "harvest_started", ResourceId, 0.0f);
        }

        /// <summary>
        /// Stops the harvesting process
        /// </summary>
        public void StopHarvesting()
        {
            if (!_isBeingHarvested)
                return;
            
            _isBeingHarvested = false;
            _harvestingPlayer = null;
            _harvestProgress = 0.0f;
            
            // Hide progress bar
            _harvestProgressBar.Visible = false;
            
            // Stop effects
            _harvestParticles.Emitting = false;
            _harvestSound.Stop();
            
            GD.Print($"Stopped harvesting {ResourceId}");
        }

        /// <summary>
        /// Completes the harvesting process and yields resources
        /// </summary>
        private void CompleteHarvesting()
        {
            if (_harvestingPlayer == null)
                return;
            
            // Calculate yield amount with skill bonuses
            int harvestAmount = CalculateHarvestYield(_harvestingPlayer);
            
            // Add resources to player inventory
            var inventory = GetPlayerInventory(_harvestingPlayer);
            if (inventory != null && inventory.CanAddItem(ResourceId, harvestAmount))
            {
                inventory.AddItem(ResourceId, harvestAmount);
                
                // Reduce current yield
                _currentYield -= harvestAmount;
                
                // Apply tool durability damage
                ApplyToolDurability(_harvestingPlayer);
                
                // Emit harvest event
                EmitSignal(SignalName.ResourceHarvested, ResourceId, harvestAmount, GlobalPosition);
                EventBus.Instance?.EmitSignal(EventBus.SignalName.PlayerActionPerformed, 
                    "resource_harvested", ResourceId, 0.0f);
                
                GD.Print($"Harvested {harvestAmount} {ResourceId}");
                
                // Check if resource is depleted
                if (_currentYield <= 0)
                {
                    DepleteResource();
                }
                else
                {
                    UpdateVisualState();
                }
            }
            else
            {
                GD.Print("Inventory full - cannot harvest resource");
            }
            
            StopHarvesting();
        }

        /// <summary>
        /// Calculates the harvest yield with skill bonuses and tool efficiency
        /// </summary>
        private int CalculateHarvestYield(PlayerController player)
        {
            int baseYield = Math.Min(_currentYield, GD.RandRange(MinYield, MaxYield + 1));
            
            // Apply tool efficiency bonus
            float toolEfficiency = GetToolEfficiency(player);
            float skillBonus = GetSkillBonus(player);
            
            int finalYield = Mathf.RoundToInt(baseYield * toolEfficiency * skillBonus);
            return Math.Max(1, Math.Min(finalYield, _currentYield));
        }

        /// <summary>
        /// Gets the efficiency bonus from the player's tool
        /// </summary>
        private float GetToolEfficiency(PlayerController player)
        {
            if (RequiredTool == "none")
                return 1.0f;
            
            var inventory = GetPlayerInventory(player);
            if (inventory?.HasItem(RequiredTool) == true)
            {
                var toolItem = ItemDatabase.Instance?.GetItem(RequiredTool);
                if (toolItem?.Metadata.ContainsKey("efficiency") == true)
                {
                    return Convert.ToSingle(toolItem.Metadata["efficiency"]);
                }
            }
            
            return 1.0f;
        }

        /// <summary>
        /// Gets skill-based harvesting bonus (placeholder for future skill system)
        /// </summary>
        private float GetSkillBonus(PlayerController player)
        {
            // Placeholder for skill system integration
            // In a full implementation, this would check the player's harvesting skill level
            return 1.0f + (GD.RandRange(0, 20) * 0.01f); // 0-20% bonus
        }

        /// <summary>
        /// Applies durability damage to the harvesting tool
        /// </summary>
        private void ApplyToolDurability(PlayerController player)
        {
            if (RequiredTool == "none")
                return;
            
            var inventory = GetPlayerInventory(player);
            if (inventory?.HasItem(RequiredTool) == true)
            {
                // In a full implementation, this would reduce tool durability
                // For now, we'll just log it
                GD.Print($"Tool {RequiredTool} durability reduced");
            }
        }

        /// <summary>
        /// Depletes the resource node
        /// </summary>
        private void DepleteResource()
        {
            _isDepleted = true;
            _currentYield = 0;
            _regenerationTimer = 0.0f;
            
            UpdateVisualState();
            
            // Emit depletion event
            EmitSignal(SignalName.ResourceDepleted, ResourceId, GlobalPosition);
            EventBus.Instance?.EmitSignal(EventBus.SignalName.GameStateChanged, 
                "resource_depleted", ResourceId);
            
            GD.Print($"Resource {ResourceId} depleted at {GlobalPosition}");
        }

        /// <summary>
        /// Regenerates the resource node after depletion
        /// </summary>
        private void RegenerateResource()
        {
            _isDepleted = false;
            _currentYield = MaxYield;
            _regenerationTimer = 0.0f;
            
            UpdateVisualState();
            
            // Emit regeneration event
            EmitSignal(SignalName.ResourceRegenerated, ResourceId, GlobalPosition);
            EventBus.Instance?.EmitSignal(EventBus.SignalName.GameStateChanged, 
                "resource_regenerated", ResourceId);
            
            GD.Print($"Resource {ResourceId} regenerated at {GlobalPosition}");
        }

        /// <summary>
        /// Updates the visual state based on current resource status
        /// </summary>
        private void UpdateVisualState()
        {
            if (_sprite == null)
                return;
            
            if (_isDepleted)
            {
                // Make sprite darker and semi-transparent when depleted
                _sprite.Modulate = new Color(0.5f, 0.5f, 0.5f, 0.7f);
            }
            else
            {
                // Calculate alpha based on current yield
                float yieldRatio = (float)_currentYield / MaxYield;
                float alpha = Mathf.Lerp(0.6f, 1.0f, yieldRatio);
                _sprite.Modulate = new Color(1.0f, 1.0f, 1.0f, alpha);
            }
        }

        #region Area2D Event Handlers

        private void OnBodyEntered(Node2D body)
        {
            if (body is PlayerController && !_isDepleted)
            {
                ShowInteractionUI();
            }
        }

        private void OnBodyExited(Node2D body)
        {
            if (body is PlayerController)
            {
                HideInteractionUI();
                
                // Stop harvesting if player moves away
                if (_isBeingHarvested && body == _harvestingPlayer)
                {
                    StopHarvesting();
                }
            }
        }

        #endregion

        #region UI Management

        /// <summary>
        /// Shows the interaction UI when player is nearby
        /// </summary>
        private void ShowInteractionUI()
        {
            if (_interactionLabel != null && !_isDepleted)
            {
                string toolText = RequiredTool != "none" ? $" (Requires {RequiredTool})" : "";
                _interactionLabel.Text = $"[E] Harvest {ResourceId} ({_currentYield}){toolText}";
                _interactionLabel.Visible = true;
            }
        }

        /// <summary>
        /// Hides the interaction UI
        /// </summary>
        private void HideInteractionUI()
        {
            if (_interactionLabel != null)
            {
                _interactionLabel.Visible = false;
            }
        }

        #endregion

        #region Public Interface

        /// <summary>
        /// Gets resource node data for saving
        /// </summary>
        public ResourceNodeSaveData GetSaveData()
        {
            return new ResourceNodeSaveData
            {
                ResourceId = ResourceId,
                Position = GlobalPosition,
                CurrentYield = _currentYield,
                IsDepleted = _isDepleted,
                RegenerationTimer = _regenerationTimer
            };
        }

        /// <summary>
        /// Loads resource node data from save
        /// </summary>
        public void LoadSaveData(ResourceNodeSaveData saveData)
        {
            ResourceId = saveData.ResourceId;
            GlobalPosition = saveData.Position;
            _currentYield = saveData.CurrentYield;
            _isDepleted = saveData.IsDepleted;
            _regenerationTimer = saveData.RegenerationTimer;
            
            UpdateVisualState();
        }

        /// <summary>
        /// Forces resource regeneration (for testing or admin commands)
        /// </summary>
        public void ForceRegenerate()
        {
            RegenerateResource();
        }

        /// <summary>
        /// Forces resource depletion (for testing or admin commands)
        /// </summary>
        public void ForceDeplete()
        {
            DepleteResource();
        }

        #endregion
    }

    /// <summary>
    /// Data structure for saving resource node state
    /// </summary>
    [System.Serializable]
    public class ResourceNodeSaveData
    {
        public string ResourceId { get; set; }
        public Vector2 Position { get; set; }
        public int CurrentYield { get; set; }
        public bool IsDepleted { get; set; }
        public float RegenerationTimer { get; set; }
    }
}