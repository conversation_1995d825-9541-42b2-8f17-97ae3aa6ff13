using Godot;
using System;
using System.Collections.Generic;

namespace SurvivalLooterShooter
{
    /// <summary>
    /// Represents a structure blueprint with all building requirements and properties
    /// </summary>
    [System.Serializable]
    public class StructureBlueprint
    {
        public string Id { get; set; }
        public string Name { get; set; }
        public string Type { get; set; }
        public int Health { get; set; }
        public List<BuildCost> BuildCost { get; set; } = new List<BuildCost>();
        public List<UpgradeLevel> UpgradeLevels { get; set; } = new List<UpgradeLevel>();
        public StructureSize Size { get; set; } = new StructureSize();
        public PlacementRules PlacementRules { get; set; } = new PlacementRules();
        public List<string> CraftingRecipes { get; set; } = new List<string>();
        public int StorageCapacity { get; set; } = 0;
        public DefenseStats DefenseStats { get; set; }

        /// <summary>
        /// Gets the build cost for a specific level (0 = base level)
        /// </summary>
        public List<BuildCost> GetBuildCostForLevel(int level)
        {
            if (level == 0)
                return BuildCost;
            
            if (level <= UpgradeLevels.Count)
                return UpgradeLevels[level - 1].Cost;
            
            return new List<BuildCost>();
        }

        /// <summary>
        /// Gets the health for a specific level
        /// </summary>
        public int GetHealthForLevel(int level)
        {
            if (level == 0)
                return Health;
            
            if (level <= UpgradeLevels.Count)
                return UpgradeLevels[level - 1].Health;
            
            return Health;
        }

        /// <summary>
        /// Gets the maximum upgrade level available
        /// </summary>
        public int GetMaxLevel()
        {
            return UpgradeLevels.Count;
        }

        /// <summary>
        /// Validates if this blueprint can be placed at the given position
        /// </summary>
        public bool CanPlaceAt(Vector2 position, BuildingManager buildingManager)
        {
            // Check terrain placement rules
            if (!PlacementRules.CanPlaceOnTerrain && !buildingManager.HasFoundationAt(position))
                return false;

            // Check foundation requirement
            if (PlacementRules.RequiresFoundation && !buildingManager.HasFoundationAt(position))
                return false;

            // Check minimum distance from other structures
            if (PlacementRules.MinDistanceFromOther > 0)
            {
                var nearbyStructures = buildingManager.GetStructuresInRadius(position, PlacementRules.MinDistanceFromOther);
                if (nearbyStructures.Count > 0)
                    return false;
            }

            // Check if area is clear (no overlapping structures)
            return buildingManager.IsAreaClear(position, Size);
        }
    }

    [System.Serializable]
    public class BuildCost
    {
        public string Item { get; set; }
        public int Amount { get; set; }
    }

    [System.Serializable]
    public class UpgradeLevel
    {
        public int Level { get; set; }
        public int Health { get; set; }
        public List<BuildCost> Cost { get; set; } = new List<BuildCost>();
    }

    [System.Serializable]
    public class StructureSize
    {
        public int Width { get; set; } = 1;
        public int Height { get; set; } = 1;
    }

    [System.Serializable]
    public class PlacementRules
    {
        public bool RequiresFoundation { get; set; } = false;
        public int MinDistanceFromOther { get; set; } = 0;
        public bool CanPlaceOnTerrain { get; set; } = true;
        public bool BlocksMovement { get; set; } = true;
    }

    [System.Serializable]
    public class DefenseStats
    {
        public float Range { get; set; } = 0;
        public int Damage { get; set; } = 0;
        public float FireRate { get; set; } = 0;
        public int AmmoConsumption { get; set; } = 0;
    }
}