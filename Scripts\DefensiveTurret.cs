using Godot;
using System;
using System.Collections.Generic;

namespace SurvivalLooterShooter
{
    /// <summary>
    /// Automated defensive turret that targets and engages enemies
    /// Handles ammunition consumption, targeting, and firing mechanics
    /// </summary>
    public partial class DefensiveTurret : Node2D
    {
        [Export] public float Range { get; set; } = 200f;
        [Export] public int Damage { get; set; } = 25;
        [Export] public float FireRate { get; set; } = 2.0f;
        [Export] public int AmmoConsumption { get; set; } = 1;
        [Export] public int MaxAmmo { get; set; } = 100;
        [Export] public float RotationSpeed { get; set; } = 180f; // degrees per second

        private Structure _parentStructure;
        private Enemy _currentTarget;
        private Timer _fireTimer;
        private Timer _targetUpdateTimer;
        private Sprite2D _turretSprite;
        private Sprite2D _barrelSprite;
        private Area2D _detectionArea;
        private Line2D _rangeIndicator;
        
        // Turret state
        private int _currentAmmo;
        private bool _isReloading = false;
        private float _targetRotation = 0f;
        private bool _isActive = true;
        private Timer _reloadTimer;

        // Visual effects
        private PackedScene _muzzleFlashScene;
        private PackedScene _projectileScene;
        private AudioStreamPlayer2D _fireSound;
        private AudioStreamPlayer2D _reloadSound;

        public bool IsDestroyed => _parentStructure?.IsDestroyed ?? true;
        public bool HasAmmo => _currentAmmo > 0;
        public bool IsActive => _isActive && !IsDestroyed && !_isReloading;
        public Enemy CurrentTarget => _currentTarget;
        public float AmmoPercentage => MaxAmmo > 0 ? (float)_currentAmmo / MaxAmmo : 0f;

        // Events
        [Signal] public delegate void TurretFiredEventHandler(DefensiveTurret turret, Enemy target);
        [Signal] public delegate void TurretReloadingEventHandler(DefensiveTurret turret, float reloadTime);
        [Signal] public delegate void TurretAmmoDepletedEventHandler(DefensiveTurret turret);
        [Signal] public delegate void TurretDestroyedEventHandler(DefensiveTurret turret);
        [Signal] public delegate void TargetAcquiredEventHandler(DefensiveTurret turret, Enemy target);
        [Signal] public delegate void TargetLostEventHandler(DefensiveTurret turret, Enemy previousTarget);

        public override void _Ready()
        {
            SetupComponents();
            SetupTimers();
            LoadEffects();
            
            _currentAmmo = MaxAmmo;
            
            // Register with defense system
            DefenseSystem.Instance?.RegisterTurret(this);
            
            Logger.LogInfo("DefensiveTurret", $"Defensive turret initialized with {Range} range and {Damage} damage");
        }

        /// <summary>
        /// Sets up visual and collision components
        /// </summary>
        private void SetupComponents()
        {
            // Create turret base sprite
            _turretSprite = new Sprite2D();
            _turretSprite.Name = "TurretSprite";
            _turretSprite.Texture = CreateTurretTexture();
            AddChild(_turretSprite);

            // Create barrel sprite
            _barrelSprite = new Sprite2D();
            _barrelSprite.Name = "BarrelSprite";
            _barrelSprite.Texture = CreateBarrelTexture();
            _barrelSprite.Position = new Vector2(0, -8);
            AddChild(_barrelSprite);

            // Create detection area
            _detectionArea = new Area2D();
            _detectionArea.Name = "DetectionArea";
            var detectionShape = new CollisionShape2D();
            var circleShape = new CircleShape2D();
            circleShape.Radius = Range;
            detectionShape.Shape = circleShape;
            _detectionArea.AddChild(detectionShape);
            AddChild(_detectionArea);

            // Connect detection signals
            _detectionArea.BodyEntered += OnEnemyEntered;
            _detectionArea.BodyExited += OnEnemyExited;

            // Create range indicator (for debugging/UI)
            _rangeIndicator = new Line2D();
            _rangeIndicator.Name = "RangeIndicator";
            _rangeIndicator.DefaultColor = Colors.Red;
            _rangeIndicator.Width = 2f;
            _rangeIndicator.Visible = false;
            AddChild(_rangeIndicator);

            CreateRangeCircle();
        }

        /// <summary>
        /// Sets up timers for firing and targeting
        /// </summary>
        private void SetupTimers()
        {
            // Fire rate timer
            _fireTimer = new Timer();
            _fireTimer.Name = "FireTimer";
            _fireTimer.WaitTime = 1f / FireRate;
            _fireTimer.OneShot = true;
            _fireTimer.Timeout += OnFireTimerTimeout;
            AddChild(_fireTimer);

            // Target update timer
            _targetUpdateTimer = new Timer();
            _targetUpdateTimer.Name = "TargetUpdateTimer";
            _targetUpdateTimer.WaitTime = 0.5f; // Update targeting twice per second
            _targetUpdateTimer.Autostart = true;
            _targetUpdateTimer.Timeout += OnTargetUpdateTimeout;
            AddChild(_targetUpdateTimer);

            // Reload timer
            _reloadTimer = new Timer();
            _reloadTimer.Name = "ReloadTimer";
            _reloadTimer.WaitTime = 3.0f; // 3 second reload time
            _reloadTimer.OneShot = true;
            _reloadTimer.Timeout += OnReloadComplete;
            AddChild(_reloadTimer);
        }

        /// <summary>
        /// Loads visual and audio effects
        /// </summary>
        private void LoadEffects()
        {
            // Try to load muzzle flash scene
            if (ResourceLoader.Exists("res://Scenes/Effects/MuzzleFlash.tscn"))
            {
                _muzzleFlashScene = GD.Load<PackedScene>("res://Scenes/Effects/MuzzleFlash.tscn");
            }

            // Try to load projectile scene
            if (ResourceLoader.Exists("res://Scenes/Effects/TurretProjectile.tscn"))
            {
                _projectileScene = GD.Load<PackedScene>("res://Scenes/Effects/TurretProjectile.tscn");
            }

            // Create audio players
            _fireSound = new AudioStreamPlayer2D();
            _fireSound.Name = "FireSound";
            AddChild(_fireSound);

            _reloadSound = new AudioStreamPlayer2D();
            _reloadSound.Name = "ReloadSound";
            AddChild(_reloadSound);
        }

        /// <summary>
        /// Creates a simple turret base texture
        /// </summary>
        private ImageTexture CreateTurretTexture()
        {
            var image = Image.CreateEmpty(32, 32, false, Image.Format.Rgb8);
            image.Fill(Colors.DarkGray);
            
            // Draw a simple turret base shape
            for (int x = 8; x < 24; x++)
            {
                for (int y = 8; y < 24; y++)
                {
                    image.SetPixel(x, y, Colors.Gray);
                }
            }
            
            return ImageTexture.CreateFromImage(image);
        }

        /// <summary>
        /// Creates a simple barrel texture
        /// </summary>
        private ImageTexture CreateBarrelTexture()
        {
            var image = Image.CreateEmpty(24, 8, false, Image.Format.Rgb8);
            image.Fill(Colors.Black);
            
            // Draw barrel
            for (int x = 0; x < 24; x++)
            {
                for (int y = 2; y < 6; y++)
                {
                    image.SetPixel(x, y, Colors.DarkGray);
                }
            }
            
            return ImageTexture.CreateFromImage(image);
        }

        /// <summary>
        /// Creates a visual range indicator circle
        /// </summary>
        private void CreateRangeCircle()
        {
            const int segments = 32;
            for (int i = 0; i <= segments; i++)
            {
                float angle = (float)i / segments * Mathf.Tau;
                Vector2 point = new Vector2(Mathf.Cos(angle), Mathf.Sin(angle)) * Range;
                _rangeIndicator.AddPoint(point);
            }
        }

        /// <summary>
        /// Initializes the turret with a parent structure
        /// </summary>
        public void Initialize(Structure parentStructure)
        {
            _parentStructure = parentStructure;
            
            if (parentStructure?.Blueprint?.DefenseStats != null)
            {
                var stats = parentStructure.Blueprint.DefenseStats;
                Range = stats.Range;
                Damage = stats.Damage;
                FireRate = stats.FireRate;
                AmmoConsumption = stats.AmmoConsumption;
                
                // Update fire timer
                _fireTimer.WaitTime = 1f / FireRate;
                
                // Update detection area
                if (_detectionArea.GetChild(0) is CollisionShape2D shape && 
                    shape.Shape is CircleShape2D circle)
                {
                    circle.Radius = Range;
                }
            }

            // Connect to structure events
            if (_parentStructure != null)
            {
                _parentStructure.StructureDestroyed += OnParentStructureDestroyed;
                _parentStructure.StructureDamaged += OnParentStructureDamaged;
            }
        }

        public override void _Process(double delta)
        {
            if (!IsActive) return;

            UpdateRotation((float)delta);
            
            // Try to fire if we have a target and can fire
            if (_currentTarget != null && CanFire())
            {
                TryFire();
            }
        }

        /// <summary>
        /// Updates turret rotation to face current target
        /// </summary>
        private void UpdateRotation(float delta)
        {
            if (_currentTarget == null) return;

            Vector2 targetDirection = (_currentTarget.GlobalPosition - GlobalPosition).Normalized();
            float targetAngle = targetDirection.Angle();
            
            // Smooth rotation towards target
            float currentAngle = _barrelSprite.Rotation;
            float angleDiff = Mathf.AngleDifference(currentAngle, targetAngle);
            float rotationStep = RotationSpeed * Mathf.DegToRad(1) * delta;
            
            if (Mathf.Abs(angleDiff) > rotationStep)
            {
                _barrelSprite.Rotation += Mathf.Sign(angleDiff) * rotationStep;
            }
            else
            {
                _barrelSprite.Rotation = targetAngle;
            }
        }

        /// <summary>
        /// Sets the current target for the turret
        /// </summary>
        public void SetTarget(Enemy target)
        {
            if (_currentTarget == target) return;

            Enemy previousTarget = _currentTarget;
            _currentTarget = target;

            if (target != null)
            {
                EmitSignal(SignalName.TargetAcquired, this, target);
                Logger.LogInfo("DefensiveTurret", $"Target acquired: {target.EnemyName}");
            }
            else if (previousTarget != null)
            {
                EmitSignal(SignalName.TargetLost, this, previousTarget);
                Logger.LogInfo("DefensiveTurret", "Target lost");
            }
        }

        /// <summary>
        /// Checks if the turret can fire
        /// </summary>
        private bool CanFire()
        {
            return IsActive && 
                   HasAmmo && 
                   !_isReloading && 
                   _fireTimer.IsStopped() &&
                   _currentTarget != null &&
                   !_currentTarget.IsDead &&
                   IsTargetInRange(_currentTarget) &&
                   IsAimedAtTarget();
        }

        /// <summary>
        /// Checks if target is within range
        /// </summary>
        private bool IsTargetInRange(Enemy target)
        {
            return target != null && GlobalPosition.DistanceTo(target.GlobalPosition) <= Range;
        }

        /// <summary>
        /// Checks if turret is properly aimed at target
        /// </summary>
        private bool IsAimedAtTarget()
        {
            if (_currentTarget == null) return false;

            Vector2 targetDirection = (_currentTarget.GlobalPosition - GlobalPosition).Normalized();
            float targetAngle = targetDirection.Angle();
            float angleDiff = Mathf.Abs(Mathf.AngleDifference(_barrelSprite.Rotation, targetAngle));
            
            return angleDiff < Mathf.DegToRad(5); // 5 degree tolerance
        }

        /// <summary>
        /// Attempts to fire at the current target
        /// </summary>
        private void TryFire()
        {
            if (!CanFire()) return;

            Fire();
        }

        /// <summary>
        /// Fires the turret at the current target
        /// </summary>
        private void Fire()
        {
            if (_currentTarget == null || !HasAmmo) return;

            // Consume ammo
            _currentAmmo -= AmmoConsumption;
            
            // Deal damage to target
            _currentTarget.TakeDamage(Damage);
            
            // Create visual effects
            CreateMuzzleFlash();
            CreateProjectile();
            
            // Play sound
            _fireSound.Play();
            
            // Start fire rate cooldown
            _fireTimer.Start();
            
            // Emit event
            EmitSignal(SignalName.TurretFired, this, _currentTarget);
            
            Logger.LogInfo("DefensiveTurret", $"Fired at {_currentTarget.EnemyName} for {Damage} damage. Ammo: {_currentAmmo}/{MaxAmmo}");
            
            // Check if we need to reload
            if (_currentAmmo <= 0)
            {
                StartReload();
            }
        }

        /// <summary>
        /// Creates muzzle flash effect
        /// </summary>
        private void CreateMuzzleFlash()
        {
            if (_muzzleFlashScene != null)
            {
                var muzzleFlash = _muzzleFlashScene.Instantiate();
                GetTree().CurrentScene.AddChild(muzzleFlash);
                
                if (muzzleFlash is Node2D flash)
                {
                    Vector2 barrelTip = GlobalPosition + Vector2.FromAngle(_barrelSprite.Rotation) * 20f;
                    flash.GlobalPosition = barrelTip;
                    flash.Rotation = _barrelSprite.Rotation;
                }
            }
        }

        /// <summary>
        /// Creates projectile effect
        /// </summary>
        private void CreateProjectile()
        {
            if (_projectileScene != null && _currentTarget != null)
            {
                var projectile = _projectileScene.Instantiate();
                GetTree().CurrentScene.AddChild(projectile);
                
                if (projectile is Node2D proj)
                {
                    Vector2 barrelTip = GlobalPosition + Vector2.FromAngle(_barrelSprite.Rotation) * 20f;
                    proj.GlobalPosition = barrelTip;
                    
                    // Set projectile direction towards target
                    Vector2 direction = (_currentTarget.GlobalPosition - barrelTip).Normalized();
                    proj.Rotation = direction.Angle();
                }
            }
        }

        /// <summary>
        /// Starts the reload process
        /// </summary>
        private void StartReload()
        {
            if (_isReloading) return;

            _isReloading = true;
            _reloadTimer.Start();
            _reloadSound.Play();
            
            EmitSignal(SignalName.TurretReloading, this, _reloadTimer.WaitTime);
            EmitSignal(SignalName.TurretAmmoDepleted, this);
            
            Logger.LogInfo("DefensiveTurret", "Starting reload...");
        }

        /// <summary>
        /// Completes the reload process
        /// </summary>
        private void OnReloadComplete()
        {
            _isReloading = false;
            
            // Try to get ammo from nearby storage or inventory
            int ammoToReload = GetAmmoFromStorage();
            _currentAmmo = Math.Min(MaxAmmo, ammoToReload);
            
            Logger.LogInfo("DefensiveTurret", $"Reload complete. Ammo: {_currentAmmo}/{MaxAmmo}");
        }

        /// <summary>
        /// Attempts to get ammunition from nearby storage containers
        /// </summary>
        private int GetAmmoFromStorage()
        {
            // Try to find nearby storage containers with ammo
            var storageContainers = GetTree().GetNodesInGroup("storage_containers");
            
            foreach (var container in storageContainers)
            {
                if (container is Node2D storageNode && storageNode.HasMethod("HasItem"))
                {
                    float distance = GlobalPosition.DistanceTo(storageNode.GlobalPosition);
                    if (distance <= 100f)
                    {
                        // Try to get ammo from this container
                        bool hasAmmo = (bool)storageNode.Call("HasItem", "ammunition", MaxAmmo);
                        if (hasAmmo)
                        {
                            storageNode.Call("RemoveItem", "ammunition", MaxAmmo);
                            return MaxAmmo;
                        }
                    }
                }
            }
            
            // Fallback: partial reload with whatever ammo is available
            return MaxAmmo / 2; // Emergency ammo supply
        }

        /// <summary>
        /// Handles enemy entering detection range
        /// </summary>
        private void OnEnemyEntered(Node2D body)
        {
            if (body is Enemy enemy && !enemy.IsDead)
            {
                // Let the defense system handle target assignment
                // This just detects potential targets
            }
        }

        /// <summary>
        /// Handles enemy exiting detection range
        /// </summary>
        private void OnEnemyExited(Node2D body)
        {
            if (body is Enemy enemy && enemy == _currentTarget)
            {
                SetTarget(null);
            }
        }

        /// <summary>
        /// Handles fire timer timeout
        /// </summary>
        private void OnFireTimerTimeout()
        {
            // Ready to fire again
        }

        /// <summary>
        /// Handles target update timer timeout
        /// </summary>
        private void OnTargetUpdateTimeout()
        {
            // Validate current target
            if (_currentTarget != null && 
                (_currentTarget.IsDead || !IsTargetInRange(_currentTarget)))
            {
                SetTarget(null);
            }
        }

        /// <summary>
        /// Handles parent structure destruction
        /// </summary>
        private void OnParentStructureDestroyed(Structure structure)
        {
            _isActive = false;
            EmitSignal(SignalName.TurretDestroyed, this);
            
            // Unregister from defense system
            DefenseSystem.Instance?.UnregisterTurret(this);
            
            Logger.LogInfo("DefensiveTurret", "Turret destroyed with parent structure");
        }

        /// <summary>
        /// Handles parent structure damage
        /// </summary>
        private void OnParentStructureDamaged(Structure structure, int damage)
        {
            // Reduce effectiveness based on structure health
            float healthPercent = structure.HealthPercentage;
            if (healthPercent < 0.5f)
            {
                // Reduced fire rate when damaged
                _fireTimer.WaitTime = (1f / FireRate) * (2f - healthPercent);
            }
        }

        /// <summary>
        /// Gets the parent structure
        /// </summary>
        public Structure GetStructure()
        {
            return _parentStructure;
        }

        /// <summary>
        /// Toggles range indicator visibility
        /// </summary>
        public void ShowRangeIndicator(bool show)
        {
            if (_rangeIndicator != null)
            {
                _rangeIndicator.Visible = show;
            }
        }

        /// <summary>
        /// Gets turret status information
        /// </summary>
        public Dictionary<string, object> GetStatus()
        {
            return new Dictionary<string, object>
            {
                ["is_active"] = IsActive,
                ["current_ammo"] = _currentAmmo,
                ["max_ammo"] = MaxAmmo,
                ["is_reloading"] = _isReloading,
                ["has_target"] = _currentTarget != null,
                ["target_name"] = _currentTarget?.EnemyName ?? "None",
                ["range"] = Range,
                ["damage"] = Damage,
                ["fire_rate"] = FireRate
            };
        }

        public override void _ExitTree()
        {
            DefenseSystem.Instance?.UnregisterTurret(this);
        }
    }
}