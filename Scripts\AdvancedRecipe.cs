using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace SurvivalLooterShooter
{
    /// <summary>
    /// Advanced recipe that supports crafting stations, multi-step processes, and intermediate materials
    /// </summary>
    [Serializable]
    public class AdvancedRecipe : Recipe
    {
        [JsonPropertyName("required_station")]
        public string RequiredStation { get; set; }

        [JsonPropertyName("required_station_level")]
        public int RequiredStationLevel { get; set; } = 1;

        [JsonPropertyName("intermediate_steps")]
        public List<IntermediateStep> IntermediateSteps { get; set; } = new List<IntermediateStep>();

        [JsonPropertyName("efficiency_bonus")]
        public float EfficiencyBonus { get; set; } = 0f; // Bonus efficiency when crafted at higher level stations

        [JsonPropertyName("batch_size")]
        public int BatchSize { get; set; } = 1; // How many can be crafted at once

        [JsonPropertyName("skill_requirements")]
        public Dictionary<string, int> SkillRequirements { get; set; } = new Dictionary<string, int>();

        [JsonPropertyName("unlock_conditions")]
        public List<UnlockCondition> UnlockConditions { get; set; } = new List<UnlockCondition>();

        public AdvancedRecipe() : base()
        {
        }

        public AdvancedRecipe(string id, List<RecipeInput> inputs, RecipeOutput output, float craftingTime = 1.0f)
            : base(id, inputs, output, craftingTime)
        {
        }

        /// <summary>
        /// Checks if the recipe is unlocked for the player
        /// </summary>
        public bool IsUnlocked(Dictionary<string, int> playerSkills = null, List<string> discoveredRecipes = null)
        {
            // Check skill requirements
            if (playerSkills != null && SkillRequirements.Count > 0)
            {
                foreach (var skillReq in SkillRequirements)
                {
                    if (!playerSkills.ContainsKey(skillReq.Key) ||
                        playerSkills[skillReq.Key] < skillReq.Value)
                    {
                        return false;
                    }
                }
            }

            // Check unlock conditions
            if (UnlockConditions.Count > 0)
            {
                foreach (var condition in UnlockConditions)
                {
                    if (!condition.IsMet(discoveredRecipes))
                    {
                        return false;
                    }
                }
            }

            return true;
        }

        /// <summary>
        /// Gets the adjusted crafting time based on station efficiency
        /// </summary>
        public float GetAdjustedCraftingTime(float stationEfficiency = 1.0f)
        {
            float adjustedTime = CraftingTime / stationEfficiency;

            // Apply efficiency bonus if crafted at higher level stations
            if (EfficiencyBonus > 0f && stationEfficiency > 1.0f)
            {
                adjustedTime *= (1.0f - EfficiencyBonus);
            }

            return Math.Max(0.1f, adjustedTime); // Minimum 0.1 seconds
        }

        /// <summary>
        /// Gets all materials needed including intermediate steps
        /// </summary>
        public List<RecipeInput> GetAllRequiredMaterials()
        {
            var allMaterials = new List<RecipeInput>(Inputs);

            // Add materials from intermediate steps
            foreach (var step in IntermediateSteps)
            {
                foreach (var input in step.Inputs)
                {
                    // Check if we already have this material in the list
                    var existing = allMaterials.Find(m => m.Id == input.Id);
                    if (existing != null)
                    {
                        existing.Amount += input.Amount;
                    }
                    else
                    {
                        allMaterials.Add(new RecipeInput(input.Id, input.Amount));
                    }
                }
            }

            return allMaterials;
        }

        /// <summary>
        /// Checks if this is a multi-step recipe
        /// </summary>
        public bool IsMultiStep => IntermediateSteps.Count > 0;

        /// <summary>
        /// Gets the total number of steps (including final step)
        /// </summary>
        public int TotalSteps => IntermediateSteps.Count + 1;

        /// <summary>
        /// Gets the total crafting time for all steps
        /// </summary>
        public float GetTotalCraftingTime(float stationEfficiency = 1.0f)
        {
            float totalTime = GetAdjustedCraftingTime(stationEfficiency);

            foreach (var step in IntermediateSteps)
            {
                totalTime += step.ProcessingTime / stationEfficiency;
            }

            return totalTime;
        }

        public override bool IsValid()
        {
            if (!base.IsValid())
                return false;

            // Validate intermediate steps
            foreach (var step in IntermediateSteps)
            {
                if (!step.IsValid())
                    return false;
            }

            return true;
        }

        public override string ToString()
        {
            string stationInfo = !string.IsNullOrEmpty(RequiredStation)
                ? $" (requires {RequiredStation} level {RequiredStationLevel})"
                : "";

            string stepInfo = IsMultiStep ? $" ({TotalSteps} steps)" : "";

            return $"AdvancedRecipe {Id}: {Inputs.Count} inputs -> {Output.Id} x{Output.Amount}{stationInfo}{stepInfo}";
        }
    }

    /// <summary>
    /// Represents an intermediate step in a multi-step crafting process
    /// </summary>
    [Serializable]
    public class IntermediateStep
    {
        [JsonPropertyName("step_name")]
        public string StepName { get; set; }

        [JsonPropertyName("inputs")]
        public List<RecipeInput> Inputs { get; set; } = new List<RecipeInput>();

        [JsonPropertyName("output")]
        public RecipeOutput Output { get; set; }

        [JsonPropertyName("processing_time")]
        public float ProcessingTime { get; set; } = 1.0f;

        [JsonPropertyName("required_tool")]
        public string RequiredTool { get; set; }

        public bool IsValid()
        {
            return !string.IsNullOrEmpty(StepName) &&
                   Inputs != null && Inputs.Count > 0 &&
                   Output != null && !string.IsNullOrEmpty(Output.Id) &&
                   Output.Amount > 0 &&
                   ProcessingTime > 0;
        }

        public override string ToString()
        {
            return $"Step: {StepName} ({Inputs.Count} inputs -> {Output.Id} x{Output.Amount})";
        }
    }

    /// <summary>
    /// Represents a condition that must be met to unlock a recipe
    /// </summary>
    [Serializable]
    public class UnlockCondition
    {
        [JsonPropertyName("type")]
        public string Type { get; set; } // "recipe_discovered", "item_crafted", "skill_level"

        [JsonPropertyName("target")]
        public string Target { get; set; } // Recipe ID, Item ID, or Skill name

        [JsonPropertyName("value")]
        public int Value { get; set; } = 1; // Required count or level

        public bool IsMet(List<string> discoveredRecipes = null)
        {
            switch (Type?.ToLower())
            {
                case "recipe_discovered":
                    return discoveredRecipes?.Contains(Target) ?? false;

                case "item_crafted":
                    // This would need to be checked against player statistics
                    // For now, assume it's met
                    return true;

                case "skill_level":
                    // This would need to be checked against player skills
                    // For now, assume it's met
                    return true;

                default:
                    return true;
            }
        }

        public override string ToString()
        {
            return $"Unlock: {Type} {Target} >= {Value}";
        }
    }
}