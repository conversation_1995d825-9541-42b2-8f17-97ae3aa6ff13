using Godot;
using System;
using System.Collections.Generic;

namespace SurvivalLooterShooter
{
    [Serializable]
    public partial class Skill : RefCounted
{
    public string Id { get; set; }
    public string Name { get; set; }
    public string Description { get; set; }
    public SkillType Category { get; set; }
    public int Level { get; set; } = 0;
    public float Experience { get; set; } = 0f;
    public int MaxLevel { get; set; } = 100;
    public List<string> Prerequisites { get; set; } = new List<string>();
    public Dictionary<string, float> PassiveBonuses { get; set; } = new Dictionary<string, float>();
    public List<string> ActiveAbilities { get; set; } = new List<string>();
    
    public float GetExperienceRequired(int level)
    {
        // Exponential XP curve: base * (level^1.5)
        return 100f * Mathf.Pow(level, 1.5f);
    }
    
    public float GetTotalExperienceRequired(int level)
    {
        float total = 0f;
        for (int i = 1; i <= level; i++)
        {
            total += GetExperienceRequired(i);
        }
        return total;
    }
    
    public bool CanLevelUp()
    {
        if (Level >= MaxLevel) return false;
        return Experience >= GetTotalExperienceRequired(Level + 1);
    }
    
    public void AddExperience(float amount)
    {
        Experience += amount;
        
        // Check for level ups
        while (CanLevelUp() && Level < MaxLevel)
        {
            Level++;
            EventBus.Instance?.EmitSignal(EventBus.SignalName.SkillLevelUp, Id, Level);
        }
    }
    
    public float GetBonusValue(string bonusType)
    {
        if (!PassiveBonuses.ContainsKey(bonusType)) return 0f;
        return PassiveBonuses[bonusType] * Level;
    }
    
    public bool HasActiveAbility(string abilityId)
    {
        return ActiveAbilities.Contains(abilityId) && Level > 0;
    }
}
}