using Godot;
using System;
using System.Collections.Generic;

namespace SurvivalLooterShooter
{
    /// <summary>
    /// Tests for the building system functionality
    /// </summary>
    public partial class BuildingSystemTests : Node
    {
        private BuildingManager _buildingManager;
        private Inventory _inventory;
        private bool _testsCompleted = false;

        public override void _Ready()
        {
            // Wait a frame for systems to initialize
            CallDeferred(nameof(RunTests));
        }

        private void RunTests()
        {
            if (_testsCompleted) return;
            _testsCompleted = true;

            Logger.LogInfo("BuildingSystemTests", "Starting building system tests...");

            try
            {
                // Get system references
                _buildingManager = BuildingManager.Instance;
                _inventory = GetNode<Inventory>("/root/GameManager/Inventory");

                if (_buildingManager == null)
                {
                    Logger.LogError("BuildingSystemTests", "BuildingManager not found");
                    return;
                }

                if (_inventory == null)
                {
                    Logger.LogError("BuildingSystemTests", "Inventory not found");
                    return;
                }

                // Run tests
                TestBlueprintLoading();
                TestPlacementValidation();
                TestStructurePlacement();
                TestMaterialConsumption();
                TestBuildingModeToggle();

                Logger.LogInfo("BuildingSystemTests", "All building system tests completed successfully!");
            }
            catch (Exception ex)
            {
                Logger.LogException("BuildingSystemTests", ex, "Building system tests");
            }
        }

        private void TestBlueprintLoading()
        {
            Logger.LogInfo("BuildingSystemTests", "Testing blueprint loading...");

            var blueprints = _buildingManager.GetAllBlueprints();
            
            if (blueprints.Count == 0)
            {
                Logger.LogError("BuildingSystemTests", "No blueprints loaded");
                return;
            }

            Logger.LogInfo("BuildingSystemTests", $"Loaded {blueprints.Count} blueprints");

            // Test specific blueprints
            var woodenWall = _buildingManager.GetBlueprint("wooden_wall");
            if (woodenWall == null)
            {
                Logger.LogError("BuildingSystemTests", "wooden_wall blueprint not found");
                return;
            }

            if (woodenWall.Name != "Wooden Wall")
            {
                Logger.LogError("BuildingSystemTests", $"Expected 'Wooden Wall', got '{woodenWall.Name}'");
                return;
            }

            if (woodenWall.BuildCost.Count == 0)
            {
                Logger.LogError("BuildingSystemTests", "wooden_wall has no build cost");
                return;
            }

            Logger.LogInfo("BuildingSystemTests", "✓ Blueprint loading test passed");
        }

        private void TestPlacementValidation()
        {
            Logger.LogInfo("BuildingSystemTests", "Testing placement validation...");

            var testPosition = new Vector2(0, 0);

            // Test foundation placement (should be allowed on terrain)
            bool canPlaceFoundation = _buildingManager.CanPlaceStructure("wooden_foundation", testPosition);
            if (!canPlaceFoundation)
            {
                Logger.LogError("BuildingSystemTests", "Foundation should be placeable on terrain");
                return;
            }

            // Test workbench placement without foundation (should fail)
            bool canPlaceWorkbench = _buildingManager.CanPlaceStructure("workbench", testPosition);
            if (canPlaceWorkbench)
            {
                Logger.LogError("BuildingSystemTests", "Workbench should require foundation");
                return;
            }

            Logger.LogInfo("BuildingSystemTests", "✓ Placement validation test passed");
        }

        private void TestStructurePlacement()
        {
            Logger.LogInfo("BuildingSystemTests", "Testing structure placement...");

            var testPosition = new Vector2(5, 5);

            // Ensure we have materials for wooden foundation
            _inventory.AddItem("wood", 10);
            _inventory.AddItem("stone", 10);

            // Try to place a foundation
            bool placed = _buildingManager.TryPlaceStructure("wooden_foundation", testPosition);
            if (!placed)
            {
                Logger.LogError("BuildingSystemTests", "Failed to place wooden foundation");
                return;
            }

            // Check if structure was actually placed
            var placedStructure = _buildingManager.GetStructureAt(testPosition);
            if (placedStructure == null)
            {
                Logger.LogError("BuildingSystemTests", "Structure not found after placement");
                return;
            }

            if (placedStructure.StructureId != "wooden_foundation")
            {
                Logger.LogError("BuildingSystemTests", $"Expected wooden_foundation, got {placedStructure.StructureId}");
                return;
            }

            Logger.LogInfo("BuildingSystemTests", "✓ Structure placement test passed");
        }

        private void TestMaterialConsumption()
        {
            Logger.LogInfo("BuildingSystemTests", "Testing material consumption...");

            // Add materials and check initial count
            _inventory.AddItem("wood", 20);
            _inventory.AddItem("nails", 10);

            int initialWood = _inventory.GetItemQuantity("wood");
            int initialNails = _inventory.GetItemQuantity("nails");

            var testPosition = new Vector2(10, 10);

            // Place a wooden wall (costs 4 wood, 2 nails)
            bool placed = _buildingManager.TryPlaceStructure("wooden_wall", testPosition);
            if (!placed)
            {
                Logger.LogError("BuildingSystemTests", "Failed to place wooden wall");
                return;
            }

            // Check material consumption
            int finalWood = _inventory.GetItemQuantity("wood");
            int finalNails = _inventory.GetItemQuantity("nails");

            if (finalWood != initialWood - 4)
            {
                Logger.LogError("BuildingSystemTests", $"Expected wood to decrease by 4, got {initialWood - finalWood}");
                return;
            }

            if (finalNails != initialNails - 2)
            {
                Logger.LogError("BuildingSystemTests", $"Expected nails to decrease by 2, got {initialNails - finalNails}");
                return;
            }

            Logger.LogInfo("BuildingSystemTests", "✓ Material consumption test passed");
        }

        private void TestBuildingModeToggle()
        {
            Logger.LogInfo("BuildingSystemTests", "Testing building mode toggle...");

            // Initially should not be in building mode
            if (_buildingManager.IsBuildingMode)
            {
                Logger.LogError("BuildingSystemTests", "Should not be in building mode initially");
                return;
            }

            // Enable building mode
            _buildingManager.SetBuildingMode(true, "wooden_wall");

            if (!_buildingManager.IsBuildingMode)
            {
                Logger.LogError("BuildingSystemTests", "Should be in building mode after enabling");
                return;
            }

            if (_buildingManager.SelectedStructureId != "wooden_wall")
            {
                Logger.LogError("BuildingSystemTests", $"Expected wooden_wall, got {_buildingManager.SelectedStructureId}");
                return;
            }

            // Disable building mode
            _buildingManager.SetBuildingMode(false);

            if (_buildingManager.IsBuildingMode)
            {
                Logger.LogError("BuildingSystemTests", "Should not be in building mode after disabling");
                return;
            }

            Logger.LogInfo("BuildingSystemTests", "✓ Building mode toggle test passed");
        }
    }
}