using Godot;
using System;
using System.Collections.Generic;

namespace SurvivalLooterShooter
{
    /// <summary>
    /// Global event bus singleton for inter-system communication
    /// Provides centralized event management to maintain loose coupling between systems
    /// </summary>
    public partial class EventBus : Node
    {
        private static EventBus _instance;
        public static EventBus Instance => _instance;

        public override void _Ready()
        {
            if (_instance == null)
            {
                _instance = this;
                GD.Print("EventBus singleton initialized");
            }
            else
            {
                GD.PrintErr("Multiple EventBus instances detected! Removing duplicate.");
                QueueFree();
            }
        }

        #region Inventory Events

        /// <summary>
        /// Emitted when an item is added to inventory
        /// </summary>
        [Signal]
        public delegate void ItemAddedEventHandler(string itemId, int quantity);

        /// <summary>
        /// Emitted when an item is removed from inventory
        /// </summary>
        [Signal]
        public delegate void ItemRemovedEventHandler(string itemId, int quantity);

        /// <summary>
        /// Emitted when an item is equipped
        /// </summary>
        [Signal]
        public delegate void ItemEquippedEventHandler(string slotType, string itemId, string previousItemId);

        /// <summary>
        /// Emitted when an item is unequipped
        /// </summary>
        [Signal]
        public delegate void ItemUnequippedEventHandler(string slotType, string itemId);

        /// <summary>
        /// Emitted when inventory becomes full
        /// </summary>
        [Signal]
        public delegate void InventoryFullEventHandler();

        #endregion

        #region Survival Stats Events

        /// <summary>
        /// Emitted when a survival stat changes
        /// </summary>
        [Signal]
        public delegate void StatChangedEventHandler(string statName, float currentValue, float maxValue, float previousValue);

        /// <summary>
        /// Emitted when a survival stat reaches a critical threshold
        /// </summary>
        [Signal]
        public delegate void StatThresholdReachedEventHandler(string statName, float threshold, string severity);

        /// <summary>
        /// Emitted when a survival stat is depleted
        /// </summary>
        [Signal]
        public delegate void StatDepletedEventHandler(string statName);

        /// <summary>
        /// Emitted when a consumable item is used
        /// </summary>
        [Signal]
        public delegate void ConsumableUsedEventHandler(string itemId, string effectsJson);

        /// <summary>
        /// Emitted when the player dies
        /// </summary>
        [Signal]
        public delegate void PlayerDiedEventHandler(string cause);

        /// <summary>
        /// Emitted when the player respawns
        /// </summary>
        [Signal]
        public delegate void PlayerRespawnedEventHandler();

        #endregion

        #region Temperature and Environmental Events

        /// <summary>
        /// Emitted when temperature changes
        /// </summary>
        [Signal]
        public delegate void TemperatureChangedEventHandler(float temperature, int zone);

        /// <summary>
        /// Emitted when temperature causes a hazard
        /// </summary>
        [Signal]
        public delegate void TemperatureHazardEventHandler(string hazardType, float severity);

        /// <summary>
        /// Emitted when protection values change
        /// </summary>
        [Signal]
        public delegate void ProtectionChangedEventHandler(float clothingProtection, float shelterProtection);

        /// <summary>
        /// Emitted when an environmental hazard starts
        /// </summary>
        [Signal]
        public delegate void HazardStartedEventHandler(string hazardType, float severity, float duration);

        /// <summary>
        /// Emitted when an environmental hazard ends
        /// </summary>
        [Signal]
        public delegate void HazardEndedEventHandler(string hazardType);

        /// <summary>
        /// Emitted when a hazard causes damage
        /// </summary>
        [Signal]
        public delegate void HazardDamageEventHandler(string hazardType, float damage);

        /// <summary>
        /// Emitted when a natural disaster occurs
        /// </summary>
        [Signal]
        public delegate void NaturalDisasterEventHandler(string disasterType, float magnitude);

        /// <summary>
        /// Emitted when clothing is equipped
        /// </summary>
        [Signal]
        public delegate void ClothingEquippedEventHandler(string slotName, string itemId);

        /// <summary>
        /// Emitted when clothing is unequipped
        /// </summary>
        [Signal]
        public delegate void ClothingUnequippedEventHandler(string slotName, string itemId);

        /// <summary>
        /// Emitted when clothing is destroyed
        /// </summary>
        [Signal]
        public delegate void ClothingDestroyedEventHandler(string itemId, string slotName);

        /// <summary>
        /// Emitted when weather changes
        /// </summary>
        [Signal]
        public delegate void WeatherChangedEventHandler(int oldWeather, int newWeather, float intensity);

        /// <summary>
        /// Emitted when season changes
        /// </summary>
        [Signal]
        public delegate void SeasonChangedEventHandler(int oldSeason, int newSeason);

        /// <summary>
        /// Emitted when weather forecast is updated
        /// </summary>
        [Signal]
        public delegate void WeatherForecastUpdatedEventHandler();

        /// <summary>
        /// Emitted when day/night changes
        /// </summary>
        [Signal]
        public delegate void DayNightChangedEventHandler(float currentTime, bool isNight);

        /// <summary>
        /// Emitted when seasonal resources change
        /// </summary>
        [Signal]
        public delegate void SeasonalResourcesChangedEventHandler(int season, string resourceModifiersJson);

        #endregion

        #region Event Emission Methods

        public void EmitTemperatureChanged(float temperature, int zone)
        {
            EmitSignal(SignalName.TemperatureChanged, temperature, zone);
        }

        public void EmitTemperatureHazard(string hazardType, float severity)
        {
            EmitSignal(SignalName.TemperatureHazard, hazardType, severity);
        }

        public void EmitProtectionChanged(float clothingProtection, float shelterProtection)
        {
            EmitSignal(SignalName.ProtectionChanged, clothingProtection, shelterProtection);
        }

        public void EmitHazardStarted(string hazardType, float severity, float duration)
        {
            EmitSignal(SignalName.HazardStarted, hazardType, severity, duration);
        }

        public void EmitHazardEnded(string hazardType)
        {
            EmitSignal(SignalName.HazardEnded, hazardType);
        }

        public void EmitHazardDamage(string hazardType, float damage)
        {
            EmitSignal(SignalName.HazardDamage, hazardType, damage);
        }

        public void EmitNaturalDisaster(string disasterType, float magnitude)
        {
            EmitSignal(SignalName.NaturalDisaster, disasterType, magnitude);
        }

        public void EmitClothingEquipped(string slotName, string itemId)
        {
            EmitSignal(SignalName.ClothingEquipped, slotName, itemId);
        }

        public void EmitClothingUnequipped(string slotName, string itemId)
        {
            EmitSignal(SignalName.ClothingUnequipped, slotName, itemId);
        }

        public void EmitClothingDestroyed(string itemId, string slotName)
        {
            EmitSignal(SignalName.ClothingDestroyed, itemId, slotName);
        }

        public void EmitWeatherChanged(int oldWeather, int newWeather, float intensity)
        {
            EmitSignal(SignalName.WeatherChanged, oldWeather, newWeather, intensity);
        }

        public void EmitSeasonChanged(int oldSeason, int newSeason)
        {
            EmitSignal(SignalName.SeasonChanged, oldSeason, newSeason);
        }

        public void EmitWeatherForecastUpdated()
        {
            EmitSignal(SignalName.WeatherForecastUpdated);
        }

        public void OnDayNightChanged(float currentTime, bool isNight)
        {
            EmitSignal(SignalName.DayNightChanged, currentTime, isNight);
        }

        public void EmitSeasonalResourcesChanged(int season, string resourceModifiersJson)
        {
            EmitSignal(SignalName.SeasonalResourcesChanged, season, resourceModifiersJson);
        }

        public void EmitStatChanged(string statName, float currentValue, float maxValue, float previousValue)
        {
            EmitSignal(SignalName.StatChanged, statName, currentValue, maxValue, previousValue);
        }

        #endregion

        #region Combat Events

        /// <summary>
        /// Emitted when a weapon is fired
        /// </summary>
        [Signal]
        public delegate void WeaponFiredEventHandler(string weaponId, string weaponName, float damage, int remainingAmmo);

        /// <summary>
        /// Emitted when weapon reload starts
        /// </summary>
        [Signal]
        public delegate void WeaponReloadStartedEventHandler(string weaponId, string weaponName, float reloadTime);

        /// <summary>
        /// Emitted when weapon reload completes
        /// </summary>
        [Signal]
        public delegate void WeaponReloadCompletedEventHandler(string weaponId, string weaponName, int newAmmoCount);

        /// <summary>
        /// Emitted when weapon runs out of ammo
        /// </summary>
        [Signal]
        public delegate void WeaponAmmoDepletedEventHandler(string weaponId, string weaponName);

        /// <summary>
        /// Emitted when weapon switches
        /// </summary>
        [Signal]
        public delegate void WeaponSwitchedEventHandler(string oldWeaponId, string newWeaponId, string oldWeaponName, string newWeaponName);

        /// <summary>
        /// Emitted when weapon condition changes significantly
        /// </summary>
        [Signal]
        public delegate void WeaponConditionChangedEventHandler(string weaponId, string weaponName, float condition, float previousCondition);

        #endregion

        #region Crafting Events

        /// <summary>
        /// Emitted when an item is successfully crafted
        /// </summary>
        [Signal]
        public delegate void ItemCraftedEventHandler(string recipeId, string outputItemId, int outputQuantity);

        /// <summary>
        /// Emitted when crafting fails
        /// </summary>
        [Signal]
        public delegate void CraftingFailedEventHandler(string recipeId, string reason);

        /// <summary>
        /// Emitted when crafting starts (for UI feedback)
        /// </summary>
        [Signal]
        public delegate void CraftingStartedEventHandler(string recipeId, float craftingTime);

        #endregion

        #region Game State Events

        /// <summary>
        /// Emitted when the game is saved
        /// </summary>
        [Signal]
        public delegate void GameSavedEventHandler(bool success, string message, string saveFilePath);

        /// <summary>
        /// Emitted when the game is loaded
        /// </summary>
        [Signal]
        public delegate void GameLoadedEventHandler(bool success, string message, string saveFilePath);

        /// <summary>
        /// Emitted when save data becomes corrupted
        /// </summary>
        [Signal]
        public delegate void SaveCorruptedEventHandler(string errorMessage, string saveFilePath);

        /// <summary>
        /// Emitted when game state changes (paused, unpaused, etc.)
        /// </summary>
        [Signal]
        public delegate void GameStateChangedEventHandler(string previousState, string newState);

        #endregion

        #region Player Events

        /// <summary>
        /// Emitted when the player moves
        /// </summary>
        [Signal]
        public delegate void PlayerMovedEventHandler(Vector2 position, Vector2 velocity, bool isSprinting);

        /// <summary>
        /// Emitted when the player performs an action
        /// </summary>
        [Signal]
        public delegate void PlayerActionPerformedEventHandler(string actionType, string targetId, float staminaCost);

        /// <summary>
        /// Emitted when the player interacts with something
        /// </summary>
        [Signal]
        public delegate void PlayerInteractedEventHandler(string interactionType, string targetId, Vector2 position);

        #endregion

        #region Item Pickup Events

        /// <summary>
        /// Emitted when an item pickup is created in the world
        /// </summary>
        [Signal]
        public delegate void ItemPickupCreatedEventHandler(string itemId, int quantity, Vector2 position);

        /// <summary>
        /// Emitted when a player enters the range of an item pickup
        /// </summary>
        [Signal]
        public delegate void PlayerEnteredPickupRangeEventHandler(string itemId, int quantity, Vector2 position);

        /// <summary>
        /// Emitted when a player exits the range of an item pickup
        /// </summary>
        [Signal]
        public delegate void PlayerExitedPickupRangeEventHandler(string itemId, int quantity, Vector2 position);

        /// <summary>
        /// Emitted when an item is successfully picked up from the world
        /// </summary>
        [Signal]
        public delegate void ItemPickedUpFromWorldEventHandler(string itemId, int quantity, Vector2 position);

        /// <summary>
        /// Emitted when item pickup fails due to full inventory
        /// </summary>
        [Signal]
        public delegate void ItemPickupFailedEventHandler(string itemId, int quantity, string reason);

        #endregion

        #region UI Events

        /// <summary>
        /// Emitted when UI panels are opened or closed
        /// </summary>
        [Signal]
        public delegate void UIPanelToggledEventHandler(string panelName, bool isVisible);

        /// <summary>
        /// Emitted when UI needs to be refreshed
        /// </summary>
        [Signal]
        public delegate void UIRefreshRequestedEventHandler(string uiComponent);

        /// <summary>
        /// Emitted when a notification should be shown to the player
        /// </summary>
        [Signal]
        public delegate void NotificationRequestedEventHandler(string message, string type, float duration);

        #endregion

        #region Day/Night Cycle Events



        /// <summary>
        /// Emitted when a new day begins
        /// </summary>
        [Signal]
        public delegate void NewDayStartedEventHandler(int dayNumber);

        /// <summary>
        /// Emitted when night begins
        /// </summary>
        [Signal]
        public delegate void NightBeganEventHandler(float timeOfDay);

        #endregion

        #region Weather Events



        #endregion

        #region Player Progression Events

        /// <summary>
        /// Emitted when player level changes
        /// </summary>
        [Signal]
        public delegate void PlayerLevelChangedEventHandler(int newLevel, float experience);

        /// <summary>
        /// Emitted when player skill level increases
        /// </summary>
        [Signal]
        public delegate void SkillLevelIncreasedEventHandler(string skillName, int newLevel);

        /// <summary>
        /// Emitted when a skill levels up
        /// </summary>
        [Signal]
        public delegate void SkillLevelUpEventHandler(string skillId, int newLevel);

        /// <summary>
        /// Emitted when experience is gained for a specific skill category
        /// </summary>
        [Signal]
        public delegate void ExperienceGainedEventHandler(string source, int category, float amount);

        /// <summary>
        /// Emitted when a skill is unlocked
        /// </summary>
        [Signal]
        public delegate void SkillUnlockedEventHandler(string skillId);

        /// <summary>
        /// Emitted when skill points are awarded
        /// </summary>
        [Signal]
        public delegate void SkillPointsAwardedEventHandler(int points, string reason);

        #endregion

        #region Defense System Events

        /// <summary>
        /// Emitted when a threat is detected by the defense system
        /// </summary>
        [Signal]
        public delegate void ThreatDetectedEventHandler(Enemy enemy, float threatLevel);

        /// <summary>
        /// Emitted when a threat is neutralized
        /// </summary>
        [Signal]
        public delegate void ThreatNeutralizedEventHandler(Enemy enemy);

        /// <summary>
        /// Emitted when a base raid starts
        /// </summary>
        [Signal]
        public delegate void BaseRaidStartedEventHandler(float intensity, int enemyCount);

        /// <summary>
        /// Emitted when a base raid ends
        /// </summary>
        [Signal]
        public delegate void BaseRaidEndedEventHandler(bool playerVictory, float damageDealt);

        /// <summary>
        /// Emitted when an alarm is activated
        /// </summary>
        [Signal]
        public delegate void AlarmActivatedEventHandler(string alarmType, Vector2 position);

        /// <summary>
        /// Emitted when a defensive structure is damaged
        /// </summary>
        [Signal]
        public delegate void DefenseStructureDamagedEventHandler(Structure structure, float damage);

        /// <summary>
        /// Emitted when a defensive structure is destroyed
        /// </summary>
        [Signal]
        public delegate void DefenseStructureDestroyedEventHandler(Structure structure);

        /// <summary>
        /// Emitted when a turret fires
        /// </summary>
        [Signal]
        public delegate void TurretFiredEventHandler(DefensiveTurret turret, Enemy target);

        /// <summary>
        /// Emitted when a structure needs repair
        /// </summary>
        [Signal]
        public delegate void StructureNeedsRepairEventHandler(Structure structure, float healthPercentage);

        #endregion

        #region Progression System Events

        /// <summary>
        /// Emitted when a combat buff is applied
        /// </summary>
        [Signal]
        public delegate void CombatBuffAppliedEventHandler(string buffType, float value, float duration);

        /// <summary>
        /// Emitted when a combat buff is removed
        /// </summary>
        [Signal]
        public delegate void CombatBuffRemovedEventHandler(string buffType);

        /// <summary>
        /// Emitted when instant reload is triggered
        /// </summary>
        [Signal]
        public delegate void InstantReloadTriggeredEventHandler();

        /// <summary>
        /// Emitted when health is restored by ability
        /// </summary>
        [Signal]
        public delegate void HealthRestoredEventHandler(float percentage);

        /// <summary>
        /// Emitted when a crafting buff is applied
        /// </summary>
        [Signal]
        public delegate void CraftingBuffAppliedEventHandler(string buffType, float value);

        /// <summary>
        /// Emitted when a crafting buff is removed
        /// </summary>
        [Signal]
        public delegate void CraftingBuffRemovedEventHandler(string buffType);

        /// <summary>
        /// Emitted when a harvesting buff is applied
        /// </summary>
        [Signal]
        public delegate void HarvestingBuffAppliedEventHandler(string buffType, float value);

        /// <summary>
        /// Emitted when a harvesting buff is removed
        /// </summary>
        [Signal]
        public delegate void HarvestingBuffRemovedEventHandler(string buffType);

        /// <summary>
        /// Emitted when a building buff is applied
        /// </summary>
        [Signal]
        public delegate void BuildingBuffAppliedEventHandler(string buffType, float value);

        /// <summary>
        /// Emitted when a building buff is removed
        /// </summary>
        [Signal]
        public delegate void BuildingBuffRemovedEventHandler(string buffType);

        #endregion

        #region Convenience Methods for Event Emission

        /// <summary>
        /// Emits an inventory item added event
        /// </summary>
        public void EmitItemAdded(string itemId, int quantity)
        {
            EmitSignal(SignalName.ItemAdded, itemId, quantity);
        }

        /// <summary>
        /// Emits an inventory item removed event
        /// </summary>
        public void EmitItemRemoved(string itemId, int quantity)
        {
            EmitSignal(SignalName.ItemRemoved, itemId, quantity);
        }



        /// <summary>
        /// Emits a weapon fired event
        /// </summary>
        public void EmitWeaponFired(string weaponId, string weaponName, float damage, int remainingAmmo)
        {
            EmitSignal(SignalName.WeaponFired, weaponId, weaponName, damage, remainingAmmo);
        }

        /// <summary>
        /// Emits an item crafted event
        /// </summary>
        public void EmitItemCrafted(string recipeId, string outputItemId, int outputQuantity)
        {
            EmitSignal(SignalName.ItemCrafted, recipeId, outputItemId, outputQuantity);
        }

        /// <summary>
        /// Emits a notification request event
        /// </summary>
        public void EmitNotificationRequested(string message, string type = "info", float duration = 3.0f)
        {
            EmitSignal(SignalName.NotificationRequested, message, type, duration);
        }

        /// <summary>
        /// Emits a UI panel toggled event
        /// </summary>
        public void EmitUIPanelToggled(string panelName, bool isVisible)
        {
            EmitSignal(SignalName.UIPanelToggled, panelName, isVisible);
        }

        /// <summary>
        /// Emits a UI refresh requested event
        /// </summary>
        public void EmitUIRefreshRequested(string uiComponent)
        {
            EmitSignal(SignalName.UIRefreshRequested, uiComponent);
        }

        /// <summary>
        /// Emits an item pickup created event
        /// </summary>
        public void EmitItemPickupCreated(string itemId, int quantity, Vector2 position)
        {
            EmitSignal(SignalName.ItemPickupCreated, itemId, quantity, position);
        }

        /// <summary>
        /// Emits an item picked up from world event
        /// </summary>
        public void EmitItemPickedUpFromWorld(string itemId, int quantity, Vector2 position)
        {
            EmitSignal(SignalName.ItemPickedUpFromWorld, itemId, quantity, position);
        }



        /// <summary>
        /// Emits a player level changed event
        /// </summary>
        public void EmitPlayerLevelChanged(int newLevel, float experience)
        {
            EmitSignal(SignalName.PlayerLevelChanged, newLevel, experience);
        }

        /// <summary>
        /// Emits an experience gained event
        /// </summary>
        public void EmitExperienceGained(string source, SkillType category, float amount)
        {
            EmitSignal(SignalName.ExperienceGained, source, (int)category, amount);
        }

        /// <summary>
        /// Emits a threat detected event
        /// </summary>
        public void EmitThreatDetected(Enemy enemy, float threatLevel)
        {
            EmitSignal(SignalName.ThreatDetected, enemy, threatLevel);
        }

        /// <summary>
        /// Emits a base raid started event
        /// </summary>
        public void EmitBaseRaidStarted(float intensity, int enemyCount)
        {
            EmitSignal(SignalName.BaseRaidStarted, intensity, enemyCount);
        }

        /// <summary>
        /// Emits an alarm activated event
        /// </summary>
        public void EmitAlarmActivated(string alarmType, Vector2 position)
        {
            EmitSignal(SignalName.AlarmActivated, alarmType, position);
        }

        /// <summary>
        /// Emits a structure needs repair event
        /// </summary>
        public void EmitStructureNeedsRepair(Structure structure, float healthPercentage)
        {
            EmitSignal(SignalName.StructureNeedsRepair, structure, healthPercentage);
        }

        /// <summary>
        /// Emits a combat buff applied event
        /// </summary>
        public void EmitCombatBuffApplied(string buffType, float value, float duration)
        {
            EmitSignal(SignalName.CombatBuffApplied, buffType, value, duration);
        }

        /// <summary>
        /// Emits a combat buff removed event
        /// </summary>
        public void EmitCombatBuffRemoved(string buffType)
        {
            EmitSignal(SignalName.CombatBuffRemoved, buffType);
        }

        /// <summary>
        /// Emits an instant reload triggered event
        /// </summary>
        public void EmitInstantReloadTriggered()
        {
            EmitSignal(SignalName.InstantReloadTriggered);
        }

        /// <summary>
        /// Emits a health restored event
        /// </summary>
        public void EmitHealthRestored(float percentage)
        {
            EmitSignal(SignalName.HealthRestored, percentage);
        }

        /// <summary>
        /// Emits a crafting buff applied event
        /// </summary>
        public void EmitCraftingBuffApplied(string buffType, float value)
        {
            EmitSignal(SignalName.CraftingBuffApplied, buffType, value);
        }

        /// <summary>
        /// Emits a crafting buff removed event
        /// </summary>
        public void EmitCraftingBuffRemoved(string buffType)
        {
            EmitSignal(SignalName.CraftingBuffRemoved, buffType);
        }

        /// <summary>
        /// Emits a harvesting buff applied event
        /// </summary>
        public void EmitHarvestingBuffApplied(string buffType, float value)
        {
            EmitSignal(SignalName.HarvestingBuffApplied, buffType, value);
        }

        /// <summary>
        /// Emits a harvesting buff removed event
        /// </summary>
        public void EmitHarvestingBuffRemoved(string buffType)
        {
            EmitSignal(SignalName.HarvestingBuffRemoved, buffType);
        }

        /// <summary>
        /// Emits a building buff applied event
        /// </summary>
        public void EmitBuildingBuffApplied(string buffType, float value)
        {
            EmitSignal(SignalName.BuildingBuffApplied, buffType, value);
        }

        /// <summary>
        /// Emits a building buff removed event
        /// </summary>
        public void EmitBuildingBuffRemoved(string buffType)
        {
            EmitSignal(SignalName.BuildingBuffRemoved, buffType);
        }



        #endregion

        #region Polish System Events

        /// <summary>
        /// Emitted when an enemy is killed
        /// </summary>
        [Signal]
        public delegate void EnemyKilledEventHandler(string enemyType, bool wasHeadshot);

        /// <summary>
        /// Emitted when an item is picked up
        /// </summary>
        [Signal]
        public delegate void ItemPickedUpEventHandler(string itemId);

        /// <summary>
        /// Emitted when a weapon is equipped
        /// </summary>
        [Signal]
        public delegate void WeaponEquippedEventHandler(string weaponId);

        /// <summary>
        /// Emitted when a POI is discovered
        /// </summary>
        [Signal]
        public delegate void POIDiscoveredEventHandler(string poiId);

        /// <summary>
        /// Emitted when a structure is built
        /// </summary>
        [Signal]
        public delegate void StructureBuiltEventHandler(string structureType);

        /// <summary>
        /// Emitted when a biome is entered
        /// </summary>
        [Signal]
        public delegate void BiomeEnteredEventHandler(string biomeType);

        /// <summary>
        /// Emitted when an item is consumed
        /// </summary>
        [Signal]
        public delegate void ItemConsumedEventHandler(string itemId, string itemType);

        /// <summary>
        /// Emits an enemy killed event
        /// </summary>
        public void EmitEnemyKilled(string enemyType, bool wasHeadshot = false)
        {
            EmitSignal(SignalName.EnemyKilled, enemyType, wasHeadshot);
        }

        /// <summary>
        /// Emits an item picked up event
        /// </summary>
        public void EmitItemPickedUp(string itemId)
        {
            EmitSignal(SignalName.ItemPickedUp, itemId);
        }

        /// <summary>
        /// Emits a weapon equipped event
        /// </summary>
        public void EmitWeaponEquipped(string weaponId)
        {
            EmitSignal(SignalName.WeaponEquipped, weaponId);
        }

        /// <summary>
        /// Emits a POI discovered event
        /// </summary>
        public void EmitPOIDiscovered(string poiId)
        {
            EmitSignal(SignalName.POIDiscovered, poiId);
        }

        /// <summary>
        /// Emits a structure built event
        /// </summary>
        public void EmitStructureBuilt(string structureType)
        {
            EmitSignal(SignalName.StructureBuilt, structureType);
        }

        /// <summary>
        /// Emits a biome entered event
        /// </summary>
        public void EmitBiomeEntered(string biomeType)
        {
            EmitSignal(SignalName.BiomeEntered, biomeType);
        }

        /// <summary>
        /// Emits an item consumed event
        /// </summary>
        public void EmitItemConsumed(string itemId, string itemType)
        {
            EmitSignal(SignalName.ItemConsumed, itemId, itemType);
        }

        /// <summary>
        /// Emits a player died event
        /// </summary>
        public void EmitPlayerDied()
        {
            EmitSignal(SignalName.PlayerDied, "unknown");
        }



        #endregion

        public override void _ExitTree()
        {
            if (_instance == this)
            {
                _instance = null;
            }
        }
        #region Endgame Content Events

        /// <summary>
        /// Emitted when a quest is completed
        /// </summary>
        [Signal]
        public delegate void QuestCompletedEventHandler(Node quest);

        /// <summary>
        /// Emitted when a quest fails
        /// </summary>
        [Signal]
        public delegate void QuestFailedEventHandler(Node quest);

        /// <summary>
        /// Emitted when a boss enters rage mode
        /// </summary>
        [Signal]
        public delegate void BossEnterRageEventHandler(Node boss);

        /// <summary>
        /// Emitted when a boss is defeated
        /// </summary>
        [Signal]
        public delegate void BossDefeatedEventHandler(Node boss);

        /// <summary>
        /// Emitted when a special event starts
        /// </summary>
        [Signal]
        public delegate void SpecialEventStartedEventHandler(Node specialEvent);

        /// <summary>
        /// Emitted when a special event ends
        /// </summary>
        [Signal]
        public delegate void SpecialEventEndedEventHandler(Node specialEvent);

        /// <summary>
        /// Emitted when New Game Plus is started
        /// </summary>
        [Signal]
        public delegate void NewGamePlusStartedEventHandler(int plusLevel);

        /// <summary>
        /// Emitted when a challenge is completed
        /// </summary>
        [Signal]
        public delegate void ChallengeCompletedEventHandler(string challengeName, float score);

        /// <summary>
        /// Emitted when an enemy is killed (for quest tracking)
        #endregion

        #region Endgame Event Emission Methods

        /// <summary>
        /// Emits a quest completed event
        /// </summary>
        public void EmitQuestCompleted(Node quest)
        {
            EmitSignal(SignalName.QuestCompleted, quest);
        }

        /// <summary>
        /// Emits a quest failed event
        /// </summary>
        public void EmitQuestFailed(Node quest)
        {
            EmitSignal(SignalName.QuestFailed, quest);
        }

        /// <summary>
        /// Emits a boss enter rage event
        /// </summary>
        public void EmitBossEnterRage(Node boss)
        {
            EmitSignal(SignalName.BossEnterRage, boss);
        }

        /// <summary>
        /// Emits a boss defeated event
        /// </summary>
        public void EmitBossDefeated(Node boss)
        {
            EmitSignal(SignalName.BossDefeated, boss);
        }

        /// <summary>
        /// Emits a special event started event
        /// </summary>
        public void EmitSpecialEventStarted(Node specialEvent)
        {
            EmitSignal(SignalName.SpecialEventStarted, specialEvent);
        }

        /// <summary>
        /// Emits a special event ended event
        /// </summary>
        public void EmitSpecialEventEnded(Node specialEvent)
        {
            EmitSignal(SignalName.SpecialEventEnded, specialEvent);
        }

        /// <summary>
        /// Emits a New Game Plus started event
        /// </summary>
        public void EmitNewGamePlusStarted(int plusLevel)
        {
            EmitSignal(SignalName.NewGamePlusStarted, plusLevel);
        }

        /// <summary>
        /// Emits a challenge completed event
        /// </summary>
        public void EmitChallengeCompleted(string challengeName, float score)
        {
            EmitSignal(SignalName.ChallengeCompleted, challengeName, score);
        }

        /// <summary>
        /// Emits an enemy killed event
        /// </summary>
        public void EmitEnemyKilled(Node enemy)
        {
            EmitSignal(SignalName.EnemyKilled, enemy);
        }

        /// <summary>
        /// Emits an item picked up event
        /// </summary>
        public void EmitItemPickedUp(string itemId, int amount)
        {
            EmitSignal(SignalName.ItemPickedUp, itemId, amount);
        }

        #endregion
    }
}