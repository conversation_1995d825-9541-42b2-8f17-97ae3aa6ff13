[gd_scene load_steps=2 format=3 uid="uid://c5ijec5gcx4xj"]

[ext_resource type="Script" path="res://Scripts/PolishTestRunner.cs" id="1_test123"]

[node name="PolishTestScene" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
script = ExtResource("1_test123")

[node name="UI" type="VBoxContainer" parent="."]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -200.0
offset_top = -150.0
offset_right = 200.0
offset_bottom = 150.0

[node name="Title" type="Label" parent="UI"]
layout_mode = 2
text = "POLISH SYSTEMS TEST"
horizontal_alignment = 1

[node name="HSeparator" type="HSeparator" parent="UI"]
layout_mode = 2

[node name="TestButtons" type="VBoxContainer" parent="UI"]
layout_mode = 2

[node name="TestAchievements" type="Button" parent="UI/TestButtons"]
layout_mode = 2
text = "Test Achievement System"

[node name="TestAudio" type="Button" parent="UI/TestButtons"]
layout_mode = 2
text = "Test Audio System"

[node name="TestTutorial" type="Button" parent="UI/TestButtons"]
layout_mode = 2
text = "Start Tutorial"

[node name="TestSettings" type="Button" parent="UI/TestButtons"]
layout_mode = 2
text = "Open Settings"

[node name="TestStats" type="Button" parent="UI/TestButtons"]
layout_mode = 2
text = "View Statistics"

[node name="TestProfiler" type="Button" parent="UI/TestButtons"]
layout_mode = 2
text = "Toggle Performance Profiler"

[node name="HSeparator2" type="HSeparator" parent="UI"]
layout_mode = 2

[node name="InfoLabel" type="Label" parent="UI"]
layout_mode = 2
text = "Press F1-F5 for quick access to polish features
F1: Settings, F2: Achievements, F3: Profiler, F5: Statistics
ESC: Pause Menu"
horizontal_alignment = 1