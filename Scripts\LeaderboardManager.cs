using Godot;
using System;
using System.Collections.Generic;
using System.Linq;

namespace SurvivalLooterShooter
{
    public enum LeaderboardCategory
{
    SurvivalTime,
    EnemiesKilled,
    BossesDefeated,
    ItemsCrafted,
    StructuresBuilt,
    ResourcesGathered,
    QuestsCompleted,
    SkillLevel,
    NGPlusLevel,
    PlayTime
}

[System.Serializable]
public class LeaderboardEntry
{
    public string PlayerName { get; set; }
    public string PlayerId { get; set; }
    public float Score { get; set; }
    public DateTime Timestamp { get; set; }
    public Dictionary<string, object> Metadata { get; set; } = new();
    public int Rank { get; set; }
}

[System.Serializable]
public class LeaderboardData
{
    public LeaderboardCategory Category { get; set; }
    public List<LeaderboardEntry> Entries { get; set; } = new();
    public DateTime LastUpdated { get; set; }
}

public partial class LeaderboardManager : Node
{
    public static LeaderboardManager Instance { get; private set; }
    
    [Export] public string PlayerName { get; set; } = "Player";
    [Export] public int MaxEntriesPerBoard { get; set; } = 100;
    [Export] public float UpdateInterval { get; set; } = 300f; // Update every 5 minutes
    
    private Dictionary<LeaderboardCategory, LeaderboardData> _leaderboards = new();
    private Timer _updateTimer;
    private string _playerId;
    private const string LeaderboardSaveFile = "user://leaderboards.save";

    public override void _Ready()
    {
        if (Instance == null)
        {
            Instance = this;
            _playerId = GeneratePlayerId();
            InitializeLeaderboards();
            SetupUpdateTimer();
            LoadLeaderboards();
        }
        else
        {
            QueueFree();
        }
    }

    private string GeneratePlayerId()
    {
        // Generate unique player ID based on system info and timestamp
        var systemInfo = OS.GetProcessorName() + OS.GetModelName();
        var timestamp = DateTime.Now.Ticks;
        return (systemInfo + timestamp).GetHashCode().ToString("X");
    }

    private void InitializeLeaderboards()
    {
        foreach (LeaderboardCategory category in Enum.GetValues<LeaderboardCategory>())
        {
            _leaderboards[category] = new LeaderboardData
            {
                Category = category,
                Entries = new List<LeaderboardEntry>(),
                LastUpdated = DateTime.Now
            };
        }
    }

    private void SetupUpdateTimer()
    {
        _updateTimer = new Timer();
        _updateTimer.WaitTime = UpdateInterval;
        _updateTimer.Timeout += UpdatePlayerScores;
        _updateTimer.Autostart = true;
        AddChild(_updateTimer);
    }

    private void UpdatePlayerScores()
    {
        var currentScores = GatherCurrentPlayerScores();
        
        foreach (var score in currentScores)
        {
            UpdateLeaderboard(score.Key, score.Value);
        }
        
        SaveLeaderboards();
    }

    private Dictionary<LeaderboardCategory, float> GatherCurrentPlayerScores()
    {
        var scores = new Dictionary<LeaderboardCategory, float>();
        
        // Gather scores from various game systems
        if (PlayerStatistics.Instance != null)
        {
            scores[LeaderboardCategory.SurvivalTime] = PlayerStatistics.Instance.GetStat("survival_time");
            scores[LeaderboardCategory.EnemiesKilled] = PlayerStatistics.Instance.GetStat("enemies_killed");
            scores[LeaderboardCategory.BossesDefeated] = PlayerStatistics.Instance.GetStat("bosses_defeated");
            scores[LeaderboardCategory.ItemsCrafted] = PlayerStatistics.Instance.GetStat("items_crafted");
            scores[LeaderboardCategory.StructuresBuilt] = PlayerStatistics.Instance.GetStat("structures_built");
            scores[LeaderboardCategory.ResourcesGathered] = PlayerStatistics.Instance.GetStat("resources_gathered");
            scores[LeaderboardCategory.QuestsCompleted] = PlayerStatistics.Instance.GetStat("quests_completed");
            scores[LeaderboardCategory.PlayTime] = PlayerStatistics.Instance.GetStat("total_playtime");
        }
        
        if (SkillManager.Instance != null)
        {
            scores[LeaderboardCategory.SkillLevel] = SkillManager.Instance.GetTotalSkillLevel();
        }
        
        if (NewGamePlusManager.Instance != null)
        {
            scores[LeaderboardCategory.NGPlusLevel] = NewGamePlusManager.Instance.NGPlusData.PlusLevel;
        }
        
        return scores;
    }

    public void UpdateLeaderboard(LeaderboardCategory category, float score)
    {
        if (!_leaderboards.ContainsKey(category)) return;
        
        var leaderboard = _leaderboards[category];
        var existingEntry = leaderboard.Entries.FirstOrDefault(e => e.PlayerId == _playerId);
        
        if (existingEntry != null)
        {
            // Update existing entry if score is better
            if (score > existingEntry.Score)
            {
                existingEntry.Score = score;
                existingEntry.Timestamp = DateTime.Now;
                existingEntry.Metadata = GatherScoreMetadata(category, score);
            }
        }
        else
        {
            // Add new entry
            var newEntry = new LeaderboardEntry
            {
                PlayerName = PlayerName,
                PlayerId = _playerId,
                Score = score,
                Timestamp = DateTime.Now,
                Metadata = GatherScoreMetadata(category, score)
            };
            
            leaderboard.Entries.Add(newEntry);
        }
        
        // Sort and trim leaderboard
        leaderboard.Entries = leaderboard.Entries
            .OrderByDescending(e => e.Score)
            .Take(MaxEntriesPerBoard)
            .ToList();
        
        // Update ranks
        for (int i = 0; i < leaderboard.Entries.Count; i++)
        {
            leaderboard.Entries[i].Rank = i + 1;
        }
        
        leaderboard.LastUpdated = DateTime.Now;
    }

    private Dictionary<string, object> GatherScoreMetadata(LeaderboardCategory category, float score)
    {
        var metadata = new Dictionary<string, object>
        {
            ["timestamp"] = DateTime.Now.ToString(),
            ["game_version"] = "1.0.0"
        };
        
        switch (category)
        {
            case LeaderboardCategory.SurvivalTime:
                metadata["difficulty"] = NewGamePlusManager.Instance?.GetCurrentDifficultyMultiplier() ?? 1f;
                metadata["ng_plus_level"] = NewGamePlusManager.Instance?.NGPlusData.PlusLevel ?? 0;
                break;
                
            case LeaderboardCategory.EnemiesKilled:
                metadata["favorite_weapon"] = GetMostUsedWeapon();
                metadata["boss_kills"] = PlayerStatistics.Instance?.GetStat("bosses_defeated") ?? 0;
                break;
                
            case LeaderboardCategory.BossesDefeated:
                metadata["survival_time"] = PlayerStatistics.Instance?.GetStat("survival_time") ?? 0;
                metadata["difficulty"] = NewGamePlusManager.Instance?.GetCurrentDifficultyMultiplier() ?? 1f;
                break;
                
            case LeaderboardCategory.ItemsCrafted:
                metadata["favorite_recipe"] = GetMostCraftedItem();
                metadata["crafting_stations_used"] = GetCraftingStationsUsed();
                break;
                
            case LeaderboardCategory.StructuresBuilt:
                metadata["favorite_structure"] = GetMostBuiltStructure();
                metadata["base_size"] = GetLargestBaseSize();
                break;
                
            case LeaderboardCategory.SkillLevel:
                metadata["highest_skill"] = GetHighestSkill();
                metadata["skill_distribution"] = GetSkillDistribution();
                break;
        }
        
        return metadata;
    }

    private string GetMostUsedWeapon()
    {
        // This would query weapon usage statistics
        return PlayerStatistics.Instance?.GetMostUsedWeapon() ?? "unknown";
    }

    private string GetMostCraftedItem()
    {
        // This would query crafting statistics
        return PlayerStatistics.Instance?.GetMostCraftedItem() ?? "unknown";
    }

    private int GetCraftingStationsUsed()
    {
        // This would count unique crafting stations used
        return PlayerStatistics.Instance?.GetCraftingStationsUsed() ?? 0;
    }

    private string GetMostBuiltStructure()
    {
        // This would query building statistics
        return PlayerStatistics.Instance?.GetMostBuiltStructure() ?? "unknown";
    }

    private int GetLargestBaseSize()
    {
        // This would calculate the largest base built
        return PlayerStatistics.Instance?.GetLargestBaseSize() ?? 0;
    }

    private string GetHighestSkill()
    {
        // This would find the highest skill level
        return SkillManager.Instance?.GetHighestSkill() ?? "unknown";
    }

    private Dictionary<string, float> GetSkillDistribution()
    {
        // This would return skill level distribution
        return SkillManager.Instance?.GetAllSkillLevels() ?? new Dictionary<string, float>();
    }

    public List<LeaderboardEntry> GetLeaderboard(LeaderboardCategory category, int count = 10)
    {
        if (!_leaderboards.ContainsKey(category)) return new List<LeaderboardEntry>();
        
        return _leaderboards[category].Entries.Take(count).ToList();
    }

    public LeaderboardEntry GetPlayerRank(LeaderboardCategory category)
    {
        if (!_leaderboards.ContainsKey(category)) return null;
        
        return _leaderboards[category].Entries.FirstOrDefault(e => e.PlayerId == _playerId);
    }

    public Dictionary<LeaderboardCategory, int> GetPlayerRanks()
    {
        var ranks = new Dictionary<LeaderboardCategory, int>();
        
        foreach (var category in _leaderboards.Keys)
        {
            var entry = GetPlayerRank(category);
            ranks[category] = entry?.Rank ?? -1;
        }
        
        return ranks;
    }

    public void SubmitChallenge(string challengeName, float score, Dictionary<string, object> metadata = null)
    {
        // Submit score for special challenges or events
        var challengeEntry = new LeaderboardEntry
        {
            PlayerName = PlayerName,
            PlayerId = _playerId,
            Score = score,
            Timestamp = DateTime.Now,
            Metadata = metadata ?? new Dictionary<string, object>()
        };
        
        // This would be sent to a server or stored locally for challenges
        GD.Print($"Challenge submitted: {challengeName} - Score: {score}");
        EventBus.Instance?.EmitSignal(EventBus.SignalName.ChallengeCompleted, challengeName, score);
    }

    public List<LeaderboardEntry> GetTopPlayers(LeaderboardCategory category, int count = 10)
    {
        return GetLeaderboard(category, count);
    }

    public Dictionary<string, object> GetLeaderboardStats()
    {
        var stats = new Dictionary<string, object>();
        
        foreach (var category in _leaderboards.Keys)
        {
            var leaderboard = _leaderboards[category];
            var playerEntry = GetPlayerRank(category);
            
            stats[$"{category}_total_entries"] = leaderboard.Entries.Count;
            stats[$"{category}_player_rank"] = playerEntry?.Rank ?? -1;
            stats[$"{category}_player_score"] = playerEntry?.Score ?? 0f;
            stats[$"{category}_top_score"] = leaderboard.Entries.FirstOrDefault()?.Score ?? 0f;
        }
        
        return stats;
    }

    private void SaveLeaderboards()
    {
        try
        {
            using var saveFile = FileAccess.Open(LeaderboardSaveFile, FileAccess.ModeFlags.Write);
            if (saveFile != null)
            {
                var saveData = new Godot.Collections.Dictionary();
                
                foreach (var leaderboard in _leaderboards)
                {
                    saveData[leaderboard.Key.ToString()] = SerializeLeaderboard(leaderboard.Value);
                }
                
                var jsonString = Json.Stringify(saveData);
                saveFile.StoreString(jsonString);
                GD.Print("Leaderboards saved successfully");
            }
        }
        catch (Exception ex)
        {
            GD.PrintErr($"Failed to save leaderboards: {ex.Message}");
        }
    }

    private void LoadLeaderboards()
    {
        try
        {
            if (FileAccess.FileExists(LeaderboardSaveFile))
            {
                using var saveFile = FileAccess.Open(LeaderboardSaveFile, FileAccess.ModeFlags.Read);
                if (saveFile != null)
                {
                    var jsonString = saveFile.GetAsText();
                    var json = new Json();
                    var parseResult = json.Parse(jsonString);
                    
                    if (parseResult == Error.Ok)
                    {
                        var data = json.Data.AsGodotDictionary();
                        
                        foreach (var item in data)
                        {
                            if (Enum.TryParse<LeaderboardCategory>(item.Key.AsString(), out var category))
                            {
                                _leaderboards[category] = DeserializeLeaderboard(item.Value.AsGodotDictionary());
                            }
                        }
                        
                        GD.Print("Leaderboards loaded successfully");
                    }
                }
            }
        }
        catch (Exception ex)
        {
            GD.PrintErr($"Failed to load leaderboards: {ex.Message}");
        }
    }

    private Godot.Collections.Dictionary SerializeLeaderboard(LeaderboardData leaderboard)
    {
        var dict = new Godot.Collections.Dictionary();
        dict["Category"] = leaderboard.Category.ToString();
        dict["LastUpdated"] = leaderboard.LastUpdated.ToBinary();
        
        var entriesArray = new Godot.Collections.Array();
        foreach (var entry in leaderboard.Entries)
        {
            var entryDict = new Godot.Collections.Dictionary();
            entryDict["PlayerName"] = entry.PlayerName;
            entryDict["PlayerId"] = entry.PlayerId;
            entryDict["Score"] = entry.Score;
            entryDict["Timestamp"] = entry.Timestamp.ToBinary();
            entryDict["Rank"] = entry.Rank;
            var metadataDict = new Godot.Collections.Dictionary();
            foreach (var kvp in entry.Metadata)
            {
                metadataDict[kvp.Key] = Variant.From(kvp.Value);
            }
            entryDict["Metadata"] = metadataDict;
            entriesArray.Add(entryDict);
        }
        dict["Entries"] = entriesArray;
        
        return dict;
    }

    private LeaderboardData DeserializeLeaderboard(Godot.Collections.Dictionary dict)
    {
        var leaderboard = new LeaderboardData();
        
        if (dict.ContainsKey("Category"))
        {
            if (Enum.TryParse<LeaderboardCategory>(dict["Category"].AsString(), out var category))
                leaderboard.Category = category;
        }
        
        if (dict.ContainsKey("LastUpdated"))
            leaderboard.LastUpdated = DateTime.FromBinary(dict["LastUpdated"].AsInt64());
        
        if (dict.ContainsKey("Entries"))
        {
            var entriesArray = dict["Entries"].AsGodotArray();
            foreach (var entryData in entriesArray)
            {
                var entryDict = entryData.AsGodotDictionary();
                var entry = new LeaderboardEntry();
                
                if (entryDict.ContainsKey("PlayerName"))
                    entry.PlayerName = entryDict["PlayerName"].AsString();
                if (entryDict.ContainsKey("PlayerId"))
                    entry.PlayerId = entryDict["PlayerId"].AsString();
                if (entryDict.ContainsKey("Score"))
                    entry.Score = entryDict["Score"].AsSingle();
                if (entryDict.ContainsKey("Timestamp"))
                    entry.Timestamp = DateTime.FromBinary(entryDict["Timestamp"].AsInt64());
                if (entryDict.ContainsKey("Rank"))
                    entry.Rank = entryDict["Rank"].AsInt32();
                if (entryDict.ContainsKey("Metadata"))
                {
                    var metadataDict = entryDict["Metadata"].AsGodotDictionary();
                    foreach (var metadata in metadataDict)
                    {
                        entry.Metadata[metadata.Key.AsString()] = metadata.Value;
                    }
                }
                
                leaderboard.Entries.Add(entry);
            }
        }
        
        return leaderboard;
    }

    public void SetPlayerName(string name)
    {
        PlayerName = name;
        
        // Update all existing entries with new name
        foreach (var leaderboard in _leaderboards.Values)
        {
            var playerEntries = leaderboard.Entries.Where(e => e.PlayerId == _playerId);
            foreach (var entry in playerEntries)
            {
                entry.PlayerName = name;
            }
        }
    }

    public void ResetLeaderboards()
    {
        InitializeLeaderboards();
        SaveLeaderboards();
        GD.Print("Leaderboards reset");
    }
}}
