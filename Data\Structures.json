[{"id": "wooden_wall", "name": "<PERSON>en Wall", "type": "defense", "health": 200, "build_cost": [{"item": "wood", "amount": 4}, {"item": "nails", "amount": 2}], "upgrade_levels": [{"level": 2, "health": 400, "cost": [{"item": "metal_scrap", "amount": 2}]}], "size": {"width": 1, "height": 2}, "placement_rules": {"requires_foundation": false, "min_distance_from_other": 0, "can_place_on_terrain": true, "blocks_movement": true}}, {"id": "wooden_foundation", "name": "Wooden Foundation", "type": "foundation", "health": 150, "build_cost": [{"item": "wood", "amount": 6}, {"item": "stone", "amount": 4}], "upgrade_levels": [{"level": 2, "health": 300, "cost": [{"item": "concrete", "amount": 4}]}], "size": {"width": 2, "height": 2}, "placement_rules": {"requires_foundation": false, "min_distance_from_other": 0, "can_place_on_terrain": true, "blocks_movement": false}}, {"id": "workbench", "name": "Workbench", "type": "crafting_station", "health": 100, "build_cost": [{"item": "wood", "amount": 8}, {"item": "metal_scrap", "amount": 4}], "upgrade_levels": [{"level": 2, "health": 200, "cost": [{"item": "advanced_components", "amount": 2}]}], "size": {"width": 2, "height": 1}, "placement_rules": {"requires_foundation": true, "min_distance_from_other": 1, "can_place_on_terrain": false, "blocks_movement": true}, "crafting_recipes": ["advanced_weapon_crafting", "tool_crafting"]}, {"id": "forge", "name": "Forge", "type": "crafting_station", "health": 150, "build_cost": [{"item": "stone", "amount": 12}, {"item": "metal_scrap", "amount": 8}, {"item": "coal", "amount": 6}], "upgrade_levels": [{"level": 2, "health": 300, "cost": [{"item": "fire_brick", "amount": 8}]}, {"level": 3, "health": 500, "cost": [{"item": "advanced_components", "amount": 4}]}], "size": {"width": 2, "height": 2}, "placement_rules": {"requires_foundation": true, "min_distance_from_other": 2, "can_place_on_terrain": false, "blocks_movement": true}, "crafting_recipes": ["metal_working", "weapon_forging", "armor_crafting"]}, {"id": "chemistry_lab", "name": "Chemistry Lab", "type": "crafting_station", "health": 120, "build_cost": [{"item": "glass", "amount": 10}, {"item": "metal_scrap", "amount": 6}, {"item": "electronic_components", "amount": 4}], "upgrade_levels": [{"level": 2, "health": 240, "cost": [{"item": "precision_instruments", "amount": 3}]}], "size": {"width": 2, "height": 1}, "placement_rules": {"requires_foundation": true, "min_distance_from_other": 1, "can_place_on_terrain": false, "blocks_movement": true}, "crafting_recipes": ["chemical_synthesis", "pharmaceutical_crafting", "explosive_crafting"]}, {"id": "storage_chest", "name": "Storage Chest", "type": "storage", "health": 80, "build_cost": [{"item": "wood", "amount": 6}, {"item": "metal_scrap", "amount": 2}], "upgrade_levels": [{"level": 2, "health": 160, "cost": [{"item": "reinforced_metal", "amount": 3}]}], "size": {"width": 1, "height": 1}, "placement_rules": {"requires_foundation": true, "min_distance_from_other": 0, "can_place_on_terrain": false, "blocks_movement": true}, "storage_capacity": 50}, {"id": "weapon_locker", "name": "Weapon Locker", "type": "storage", "health": 120, "build_cost": [{"item": "metal_scrap", "amount": 8}, {"item": "reinforced_metal", "amount": 4}], "upgrade_levels": [{"level": 2, "health": 240, "cost": [{"item": "advanced_components", "amount": 2}]}], "size": {"width": 1, "height": 2}, "placement_rules": {"requires_foundation": true, "min_distance_from_other": 0, "can_place_on_terrain": false, "blocks_movement": true}, "storage_capacity": 20}, {"id": "material_storage", "name": "Material Storage", "type": "storage", "health": 100, "build_cost": [{"item": "wood", "amount": 12}, {"item": "metal_scrap", "amount": 6}], "upgrade_levels": [{"level": 2, "health": 200, "cost": [{"item": "reinforced_metal", "amount": 4}]}], "size": {"width": 2, "height": 1}, "placement_rules": {"requires_foundation": true, "min_distance_from_other": 0, "can_place_on_terrain": false, "blocks_movement": true}, "storage_capacity": 100}, {"id": "secure_vault", "name": "<PERSON><PERSON>", "type": "storage", "health": 200, "build_cost": [{"item": "reinforced_metal", "amount": 10}, {"item": "electronic_components", "amount": 3}, {"item": "advanced_components", "amount": 2}], "upgrade_levels": [{"level": 2, "health": 400, "cost": [{"item": "titanium_alloy", "amount": 5}]}], "size": {"width": 1, "height": 1}, "placement_rules": {"requires_foundation": true, "min_distance_from_other": 1, "can_place_on_terrain": false, "blocks_movement": true}, "storage_capacity": 30}, {"id": "defensive_turret", "name": "Defensive Turret", "type": "defense", "health": 300, "build_cost": [{"item": "metal_scrap", "amount": 10}, {"item": "electronic_components", "amount": 5}, {"item": "ammunition", "amount": 50}], "upgrade_levels": [{"level": 2, "health": 500, "cost": [{"item": "advanced_components", "amount": 5}]}], "size": {"width": 1, "height": 1}, "placement_rules": {"requires_foundation": true, "min_distance_from_other": 3, "can_place_on_terrain": false, "blocks_movement": true}, "defense_stats": {"range": 200, "damage": 25, "fire_rate": 2.0, "ammo_consumption": 1}}, {"id": "reinforced_wall", "name": "Reinforced Wall", "type": "defense", "health": 400, "build_cost": [{"item": "metal_scrap", "amount": 6}, {"item": "concrete", "amount": 4}], "upgrade_levels": [{"level": 2, "health": 800, "cost": [{"item": "reinforced_metal", "amount": 4}]}], "size": {"width": 1, "height": 2}, "placement_rules": {"requires_foundation": false, "min_distance_from_other": 0, "can_place_on_terrain": true, "blocks_movement": true}}, {"id": "security_gate", "name": "Security Gate", "type": "defense", "health": 250, "build_cost": [{"item": "metal_scrap", "amount": 8}, {"item": "electronic_components", "amount": 3}], "upgrade_levels": [{"level": 2, "health": 500, "cost": [{"item": "advanced_components", "amount": 3}]}], "size": {"width": 2, "height": 1}, "placement_rules": {"requires_foundation": true, "min_distance_from_other": 0, "can_place_on_terrain": false, "blocks_movement": false}}, {"id": "alarm_system", "name": "Alarm System", "type": "defense", "health": 100, "build_cost": [{"item": "electronic_components", "amount": 4}, {"item": "metal_scrap", "amount": 2}], "upgrade_levels": [{"level": 2, "health": 200, "cost": [{"item": "advanced_components", "amount": 2}]}], "size": {"width": 1, "height": 1}, "placement_rules": {"requires_foundation": true, "min_distance_from_other": 1, "can_place_on_terrain": false, "blocks_movement": false}}, {"id": "barricade", "name": "Barricade", "type": "defense", "health": 150, "build_cost": [{"item": "wood", "amount": 6}, {"item": "metal_scrap", "amount": 2}], "upgrade_levels": [{"level": 2, "health": 300, "cost": [{"item": "reinforced_metal", "amount": 2}]}], "size": {"width": 2, "height": 1}, "placement_rules": {"requires_foundation": false, "min_distance_from_other": 0, "can_place_on_terrain": true, "blocks_movement": true}}]