using System;
using System.Collections.Generic;

namespace SurvivalLooterShooter
{
    /// <summary>
    /// Weapon class extending Item with combat-specific properties
    /// </summary>
    public class Weapon : Item
    {
        // Combat properties
        public float Damage { get; private set; }
        public float FireRate { get; private set; }
        public string AmmoType { get; private set; }
        public float Durability { get; set; }
        public float MaxDurability { get; private set; }
        public string WeaponType { get; private set; }
        public int MagazineSize { get; private set; }
        public int CurrentAmmo { get; set; }
        public float ReloadTime { get; private set; }
        public float Range { get; private set; }

        public Weapon() : base()
        {
            InitializeFromMetadata();
        }

        public Weapon(string id, string name, string type, int maxStack = 1) : base(id, name, type, maxStack)
        {
            InitializeFromMetadata();
        }

        /// <summary>
        /// Initializes weapon properties from metadata
        /// </summary>
        public void InitializeFromMetadata()
        {
            Damage = GetMetadata<float>("damage", 10f);
            FireRate = GetMetadata<float>("fire_rate", 1f);
            AmmoType = GetMetadata<string>("ammo_type", "generic_ammo");
            MaxDurability = GetMetadata<float>("max_durability", 100f);
            Durability = GetMetadata<float>("durability", MaxDurability);
            WeaponType = GetMetadata<string>("weapon_type", "single");
            MagazineSize = GetMetadata<int>("magazine_size", 30);
            CurrentAmmo = GetMetadata<int>("current_ammo", 0);
            ReloadTime = GetMetadata<float>("reload_time", 2f);
            Range = GetMetadata<float>("range", 100f);
        }

        /// <summary>
        /// Creates a Weapon instance from an Item
        /// </summary>
        public static Weapon FromItem(Item item)
        {
            if (item == null || item.Type != "weapon")
                return null;

            var weapon = new Weapon(item.Id, item.Name, item.Type, item.MaxStack);
            weapon.Metadata = new Dictionary<string, object>(item.Metadata);
            weapon.InitializeFromMetadata();
            return weapon;
        }

        /// <summary>
        /// Checks if the weapon can fire
        /// </summary>
        public bool CanFire()
        {
            return CurrentAmmo > 0 && Durability > 0;
        }

        /// <summary>
        /// Fires the weapon, consuming ammo and durability
        /// </summary>
        public bool Fire()
        {
            if (!CanFire())
                return false;

            CurrentAmmo--;
            Durability = Math.Max(0, Durability - 1f); // Reduce durability by 1 per shot
            
            // Update metadata
            SetMetadata("current_ammo", CurrentAmmo);
            SetMetadata("durability", Durability);
            
            return true;
        }

        /// <summary>
        /// Reloads the weapon with the specified amount of ammo
        /// </summary>
        public int Reload(int availableAmmo)
        {
            if (availableAmmo <= 0)
                return 0;

            int ammoNeeded = MagazineSize - CurrentAmmo;
            int ammoToLoad = Math.Min(ammoNeeded, availableAmmo);
            
            CurrentAmmo += ammoToLoad;
            SetMetadata("current_ammo", CurrentAmmo);
            
            return ammoToLoad; // Return amount of ammo consumed
        }

        /// <summary>
        /// Checks if the weapon needs reloading
        /// </summary>
        public bool NeedsReload()
        {
            return CurrentAmmo < MagazineSize;
        }

        /// <summary>
        /// Gets the weapon's current condition as a percentage
        /// </summary>
        public float GetConditionPercentage()
        {
            return MaxDurability > 0 ? (Durability / MaxDurability) * 100f : 0f;
        }

        /// <summary>
        /// Checks if the weapon is broken (durability at 0)
        /// </summary>
        public bool IsBroken()
        {
            return Durability <= 0;
        }

        /// <summary>
        /// Repairs the weapon by the specified amount
        /// </summary>
        public void Repair(float repairAmount)
        {
            Durability = Math.Min(MaxDurability, Durability + repairAmount);
            SetMetadata("durability", Durability);
        }

        /// <summary>
        /// Gets damage modified by weapon condition
        /// </summary>
        public float GetEffectiveDamage()
        {
            float conditionModifier = GetConditionPercentage() / 100f;
            return Damage * Math.Max(0.1f, conditionModifier); // Minimum 10% damage even when broken
        }

        public override string ToString()
        {
            return $"{Name} ({CurrentAmmo}/{MagazineSize}) - {GetConditionPercentage():F1}% condition";
        }
    }
}