using Godot;

namespace SurvivalLooterShooter
{
    /// <summary>
    /// UI controller for individual inventory slots
    /// </summary>
    public partial class InventorySlotUI : Control
    {
        [Export] public string SlotKey { get; set; } = "";
        
        private Panel _background;
        private TextureRect _itemIcon;
        private Label _quantityLabel;
        private ProgressBar _durabilityBar;
        
        private InventorySlot _slot;
        private bool _isEmpty = true;

        // Signals for interaction
        [Signal]
        public delegate void SlotClickedEventHandler(string slotKey);
        
        [Signal]
        public delegate void SlotHoveredEventHandler(string slotKey, Vector2 globalPosition);
        
        [Signal]
        public delegate void SlotUnhoveredEventHandler();

        public override void _Ready()
        {
            // Get references to child nodes
            _background = GetNode<Panel>("Background");
            _itemIcon = GetNode<TextureRect>("ItemIcon");
            _quantityLabel = GetNode<Label>("QuantityLabel");
            _durabilityBar = GetNode<ProgressBar>("DurabilityBar");
            
            // Connect mouse events
            MouseEntered += OnMouseEntered;
            MouseExited += OnMouseExited;
            GuiInput += OnGuiInput;
            
            // Initialize as empty slot
            UpdateDisplay();
        }

        /// <summary>
        /// Updates the slot with new inventory data
        /// </summary>
        /// <param name="slot">The inventory slot data</param>
        /// <param name="slotKey">The key identifying this slot</param>
        public void UpdateSlot(InventorySlot slot, string slotKey)
        {
            _slot = slot;
            SlotKey = slotKey;
            _isEmpty = slot == null || slot.IsEmpty;
            
            UpdateDisplay();
        }

        /// <summary>
        /// Updates the visual display of the slot
        /// </summary>
        private void UpdateDisplay()
        {
            if (_isEmpty || _slot == null)
            {
                // Empty slot
                _itemIcon.Texture = null;
                _quantityLabel.Visible = false;
                _durabilityBar.Visible = false;
                _background.Modulate = Colors.White;
                return;
            }

            // Get item data
            var item = ItemDatabase.Instance?.GetItem(_slot.ItemId);
            if (item == null)
            {
                GD.PrintErr($"Item not found in database: {_slot.ItemId}");
                return;
            }

            // Update item icon (placeholder for now - would load actual textures)
            _itemIcon.Texture = null; // TODO: Load actual item textures
            
            // Update quantity display
            if (_slot.Quantity > 1)
            {
                _quantityLabel.Text = _slot.Quantity.ToString();
                _quantityLabel.Visible = true;
            }
            else
            {
                _quantityLabel.Visible = false;
            }

            // Update durability bar if item has durability
            if (_slot.Metadata.ContainsKey("durability"))
            {
                var currentDurability = _slot.GetMetadata<float>("durability", 100f);
                var maxDurability = item.GetMetadata<float>("durability", 100f);
                
                _durabilityBar.Value = currentDurability;
                _durabilityBar.MaxValue = maxDurability;
                _durabilityBar.Visible = true;
                
                // Color code durability bar
                if (currentDurability / maxDurability > 0.6f)
                    _durabilityBar.Modulate = Colors.Green;
                else if (currentDurability / maxDurability > 0.3f)
                    _durabilityBar.Modulate = Colors.Yellow;
                else
                    _durabilityBar.Modulate = Colors.Red;
            }
            else
            {
                _durabilityBar.Visible = false;
            }

            // Highlight equipped items
            if (IsEquippedItem())
            {
                _background.Modulate = Colors.LightBlue;
            }
            else
            {
                _background.Modulate = Colors.White;
            }
        }

        /// <summary>
        /// Checks if this item is currently equipped
        /// </summary>
        private bool IsEquippedItem()
        {
            // This would need to check with the inventory system
            // For now, return false as a placeholder
            return false;
        }

        /// <summary>
        /// Gets the inventory slot data
        /// </summary>
        public InventorySlot GetSlot()
        {
            return _slot?.Clone();
        }

        /// <summary>
        /// Checks if this slot is empty
        /// </summary>
        public bool IsEmpty()
        {
            return _isEmpty;
        }

        private void OnMouseEntered()
        {
            if (!_isEmpty && _slot != null)
            {
                EmitSignal(SignalName.SlotHovered, SlotKey, GlobalPosition);
            }
        }

        private void OnMouseExited()
        {
            EmitSignal(SignalName.SlotUnhovered);
        }

        private void OnGuiInput(InputEvent @event)
        {
            if (@event is InputEventMouseButton mouseEvent && mouseEvent.Pressed)
            {
                if (mouseEvent.ButtonIndex == MouseButton.Left)
                {
                    EmitSignal(SignalName.SlotClicked, SlotKey);
                }
            }
        }
    }
}