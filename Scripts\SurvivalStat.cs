using System;
using Godot;

namespace SurvivalLooterShooter
{
    /// <summary>
    /// Base class for survival stats like health, hunger, thirst, and stamina
    /// </summary>
    public partial class SurvivalStat : Node
    {
        [Signal]
        public delegate void StatChangedEventHandler(float currentValue, float maxValue);
        
        [Signal]
        public delegate void StatDepletedEventHandler();
        
        [Signal]
        public delegate void ThresholdReachedEventHandler(float threshold, float currentValue);

        private float _currentValue;
        private float _maxValue;
        private float _decayRate;
        private bool _canDecay;
        private float _previousValue;
        
        public string StatName { get; private set; }
        public float CurrentValue 
        { 
            get => _currentValue;
            private set
            {
                _previousValue = _currentValue;
                _currentValue = Mathf.Clamp(value, 0f, _maxValue);
                
                if (!Mathf.IsEqualApprox(_previousValue, _currentValue))
                {
                    EmitSignal(SignalName.StatChanged, _currentValue, _maxValue);
                    
                    if (Mathf.IsZeroApprox(_currentValue))
                    {
                        EmitSignal(SignalName.StatDepleted);
                    }
                }
            }
        }
        
        public float MaxValue 
        { 
            get => _maxValue;
            private set
            {
                _maxValue = Mathf.Max(0f, value);
                CurrentValue = Mathf.Min(CurrentValue, _maxValue);
            }
        }
        
        public float DecayRate 
        { 
            get => _decayRate;
            set => _decayRate = Mathf.Max(0f, value);
        }
        
        public bool CanDecay 
        { 
            get => _canDecay;
            set => _canDecay = value;
        }
        
        public float Percentage => _maxValue > 0 ? (_currentValue / _maxValue) * 100f : 0f;
        public bool IsEmpty => Mathf.IsZeroApprox(_currentValue);
        public bool IsFull => Mathf.IsEqualApprox(_currentValue, _maxValue);
        public float PreviousValue => _previousValue;

        /// <summary>
        /// Initializes the survival stat
        /// </summary>
        public void Initialize(string statName, float maxValue, float initialValue = -1f, float decayRate = 0f, bool canDecay = true)
        {
            StatName = statName;
            _maxValue = Mathf.Max(0f, maxValue);
            _decayRate = Mathf.Max(0f, decayRate);
            _canDecay = canDecay;
            
            // If no initial value specified, start at max
            float startValue = initialValue >= 0 ? initialValue : maxValue;
            _currentValue = Mathf.Clamp(startValue, 0f, _maxValue);
            _previousValue = _currentValue;
        }

        /// <summary>
        /// Modifies the current stat value
        /// </summary>
        public void ModifyValue(float amount)
        {
            CurrentValue += amount;
        }

        /// <summary>
        /// Sets the current stat value directly
        /// </summary>
        public void SetValue(float value)
        {
            CurrentValue = value;
        }

        /// <summary>
        /// Sets the maximum stat value
        /// </summary>
        public void SetMaxValue(float maxValue)
        {
            MaxValue = maxValue;
        }

        /// <summary>
        /// Restores the stat to full
        /// </summary>
        public void RestoreToFull()
        {
            CurrentValue = _maxValue;
        }

        /// <summary>
        /// Applies decay to the stat if decay is enabled
        /// </summary>
        public void ApplyDecay(float deltaTime)
        {
            if (_canDecay && _decayRate > 0f && _currentValue > 0f)
            {
                ModifyValue(-_decayRate * deltaTime);
            }
        }

        /// <summary>
        /// Checks if the stat is below a certain threshold percentage
        /// </summary>
        public bool IsBelowThreshold(float thresholdPercentage)
        {
            return Percentage < thresholdPercentage;
        }

        /// <summary>
        /// Gets the stat data for serialization
        /// </summary>
        public SurvivalStatData GetStatData()
        {
            return new SurvivalStatData
            {
                StatName = StatName,
                CurrentValue = CurrentValue,
                MaxValue = MaxValue,
                DecayRate = DecayRate,
                CanDecay = CanDecay
            };
        }

        /// <summary>
        /// Loads stat data from serialization
        /// </summary>
        public void LoadStatData(SurvivalStatData data)
        {
            Initialize(data.StatName, data.MaxValue, data.CurrentValue, data.DecayRate, data.CanDecay);
        }

        public override string ToString()
        {
            return $"{StatName}: {CurrentValue:F1}/{MaxValue:F1} ({Percentage:F1}%)";
        }
    }

    /// <summary>
    /// Data structure for serializing survival stat information
    /// </summary>
    [Serializable]
    public class SurvivalStatData
    {
        public string StatName { get; set; }
        public float CurrentValue { get; set; }
        public float MaxValue { get; set; }
        public float DecayRate { get; set; }
        public bool CanDecay { get; set; }
    }
}