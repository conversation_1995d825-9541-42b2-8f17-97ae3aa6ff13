using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace SurvivalLooterShooter
{
	[Serializable]
	public class Item
	{
		[JsonPropertyName("id")]
		public string Id { get; set; }

		[JsonPropertyName("name")]
		public string Name { get; set; }

		[JsonPropertyName("type")]
		public string Type { get; set; }

		[JsonPropertyName("max_stack")]
		public int MaxStack { get; set; } = 1;

		[JsonPropertyName("metadata")]
		public Dictionary<string, object> Metadata { get; set; } = new Dictionary<string, object>();

		public Item()
		{
		}

		public Item(string id, string name, string type, int maxStack = 1)
		{
			Id = id;
			Name = name;
			Type = type;
			MaxStack = maxStack;
		}

		/// <summary>
		/// Gets a metadata value as the specified type
		/// </summary>
		public T GetMetadata<T>(string key, T defaultValue = default(T))
		{
			if (Metadata.ContainsKey(key))
			{
				try
				{
					return (T)Convert.ChangeType(Metadata[key], typeof(T));
				}
				catch
				{
					return defaultValue;
				}
			}
			return defaultValue;
		}

		/// <summary>
		/// Sets a metadata value
		/// </summary>
		public void SetMetadata<T>(string key, T value)
		{
			Metadata[key] = value;
		}

		/// <summary>
		/// Checks if this item can stack with another item
		/// </summary>
		public bool CanStackWith(Item other)
		{
			return other != null && Id == other.Id && MaxStack > 1;
		}

		public override string ToString()
		{
			return $"{Name} ({Id})";
		}
	}
}
