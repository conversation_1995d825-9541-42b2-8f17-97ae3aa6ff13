[gd_scene load_steps=2 format=3 uid="uid://ch8x44q261u1y"]

[ext_resource type="Script" uid="uid://cc3kqx2qg348v" path="res://Scripts/StorageContainerUI.cs" id="1_storage_ui"]

[node name="StorageContainerUI" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("1_storage_ui")

[node name="Background" type="Panel" parent="."]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -400.0
offset_top = -300.0
offset_right = 400.0
offset_bottom = 300.0
grow_horizontal = 2
grow_vertical = 2

[node name="VBoxContainer" type="VBoxContainer" parent="Background"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 10.0
offset_top = 10.0
offset_right = -10.0
offset_bottom = -10.0
grow_horizontal = 2
grow_vertical = 2

[node name="HeaderContainer" type="HBoxContainer" parent="Background/VBoxContainer"]
layout_mode = 2

[node name="ContainerNameLabel" type="Label" parent="Background/VBoxContainer/HeaderContainer"]
layout_mode = 2
text = "Storage Container"
horizontal_alignment = 1

[node name="CapacityLabel" type="Label" parent="Background/VBoxContainer/HeaderContainer"]
layout_mode = 2
size_flags_horizontal = 3
text = "0/50"
horizontal_alignment = 2

[node name="ControlsContainer" type="HBoxContainer" parent="Background/VBoxContainer"]
layout_mode = 2

[node name="SortButton" type="Button" parent="Background/VBoxContainer/ControlsContainer"]
layout_mode = 2
text = "Sort"

[node name="LockButton" type="Button" parent="Background/VBoxContainer/ControlsContainer"]
layout_mode = 2
text = "Lock"

[node name="SortModeOption" type="OptionButton" parent="Background/VBoxContainer/ControlsContainer"]
layout_mode = 2
selected = 0
item_count = 5
popup/item_0/text = "None"
popup/item_0/id = 0
popup/item_1/text = "Name"
popup/item_1/id = 1
popup/item_2/text = "Type"
popup/item_2/id = 2
popup/item_3/text = "Quantity"
popup/item_3/id = 3
popup/item_4/text = "Value"
popup/item_4/id = 4

[node name="FilterTypeOption" type="OptionButton" parent="Background/VBoxContainer/ControlsContainer"]
layout_mode = 2
selected = 0
item_count = 5
popup/item_0/text = "All Items"
popup/item_0/id = 0
popup/item_1/text = "Weapons"
popup/item_1/id = 1
popup/item_2/text = "Tools"
popup/item_2/id = 2
popup/item_3/text = "Materials"
popup/item_3/id = 3
popup/item_4/text = "Consumables"
popup/item_4/id = 4

[node name="AutoSortCheckbox" type="CheckBox" parent="Background/VBoxContainer/ControlsContainer"]
layout_mode = 2
text = "Auto Sort"

[node name="ContentContainer" type="HSplitContainer" parent="Background/VBoxContainer"]
layout_mode = 2
size_flags_vertical = 3

[node name="ContainerSection" type="VBoxContainer" parent="Background/VBoxContainer/ContentContainer"]
layout_mode = 2
size_flags_horizontal = 3

[node name="ContainerLabel" type="Label" parent="Background/VBoxContainer/ContentContainer/ContainerSection"]
layout_mode = 2
text = "Container"

[node name="ContainerScroll" type="ScrollContainer" parent="Background/VBoxContainer/ContentContainer/ContainerSection"]
layout_mode = 2
size_flags_vertical = 3

[node name="ContainerGrid" type="GridContainer" parent="Background/VBoxContainer/ContentContainer/ContainerSection/ContainerScroll"]
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 3
columns = 8

[node name="InventorySection" type="VBoxContainer" parent="Background/VBoxContainer/ContentContainer"]
layout_mode = 2
size_flags_horizontal = 3

[node name="InventoryLabel" type="Label" parent="Background/VBoxContainer/ContentContainer/InventorySection"]
layout_mode = 2
text = "Your Inventory"

[node name="InventoryScroll" type="ScrollContainer" parent="Background/VBoxContainer/ContentContainer/InventorySection"]
layout_mode = 2
size_flags_vertical = 3

[node name="InventoryGrid" type="GridContainer" parent="Background/VBoxContainer/ContentContainer/InventorySection/InventoryScroll"]
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 3
columns = 8

[node name="CloseButton" type="Button" parent="Background"]
layout_mode = 1
anchors_preset = 1
anchor_left = 1.0
anchor_right = 1.0
offset_left = -60.0
offset_bottom = 30.0
grow_horizontal = 0
text = "Close"

[node name="AccessCodePanel" type="Panel" parent="Background"]
visible = false
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -150.0
offset_top = -75.0
offset_right = 150.0
offset_bottom = 75.0
grow_horizontal = 2
grow_vertical = 2

[node name="VBoxContainer" type="VBoxContainer" parent="Background/AccessCodePanel"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 10.0
offset_top = 10.0
offset_right = -10.0
offset_bottom = -10.0
grow_horizontal = 2
grow_vertical = 2

[node name="CodeLabel" type="Label" parent="Background/AccessCodePanel/VBoxContainer"]
layout_mode = 2
text = "Enter Access Code:"
horizontal_alignment = 1

[node name="AccessCodeInput" type="LineEdit" parent="Background/AccessCodePanel/VBoxContainer"]
layout_mode = 2
placeholder_text = "Access Code"
secret = true

[node name="ButtonContainer" type="HBoxContainer" parent="Background/AccessCodePanel/VBoxContainer"]
layout_mode = 2

[node name="UnlockButton" type="Button" parent="Background/AccessCodePanel/VBoxContainer/ButtonContainer"]
layout_mode = 2
size_flags_horizontal = 3
text = "Unlock"

[node name="CancelButton" type="Button" parent="Background/AccessCodePanel/VBoxContainer/ButtonContainer"]
layout_mode = 2
size_flags_horizontal = 3
text = "Cancel"
