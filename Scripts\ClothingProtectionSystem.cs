using Godot;
using System;
using System.Collections.Generic;
using System.Linq;

namespace SurvivalLooterShooter
{
    /// <summary>
    /// Manages clothing and equipment that provides environmental protection
    /// Integrates with temperature and hazard systems for protection calculations
    /// </summary>
    public partial class ClothingProtectionSystem : Node
    {
        private static ClothingProtectionSystem _instance;
        public static ClothingProtectionSystem Instance => _instance;

        // Equipment slots
        private Dictionary<EquipmentSlot, ClothingItem> _equippedClothing = new Dictionary<EquipmentSlot, ClothingItem>();
        
        // Protection values
        private float _totalColdProtection = 0f;
        private float _totalHeatProtection = 0f;
        private float _totalWaterResistance = 0f;
        private float _totalWindResistance = 0f;
        private Dictionary<string, float> _hazardResistances = new Dictionary<string, float>();

        // Events
        [Signal] public delegate void ClothingEquippedEventHandler(string slotName, string itemId);
        [Signal] public delegate void ClothingUnequippedEventHandler(string slotName, string itemId);
        [Signal] public delegate void ProtectionUpdatedEventHandler(float coldProtection, float heatProtection, float waterResistance, float windResistance);

        public enum EquipmentSlot
        {
            Head,
            Chest,
            Legs,
            Feet,
            Hands,
            Back,
            Accessory
        }

        public override void _Ready()
        {
            // Singleton setup
            if (_instance == null)
            {
                _instance = this;
                AddToGroup("clothing_protection_system");
                Logger.LogInfo("ClothingProtectionSystem", "ClothingProtectionSystem singleton initialized");
            }
            else
            {
                Logger.LogError("ClothingProtectionSystem", "Multiple ClothingProtectionSystem instances detected! Removing duplicate.");
                QueueFree();
                return;
            }

            InitializeEquipmentSlots();
            ConnectToSystems();
            
            Logger.LogInfo("ClothingProtectionSystem", "Clothing protection system initialized");
        }

        /// <summary>
        /// Initializes all equipment slots as empty
        /// </summary>
        private void InitializeEquipmentSlots()
        {
            foreach (EquipmentSlot slot in Enum.GetValues<EquipmentSlot>())
            {
                _equippedClothing[slot] = null;
            }
        }

        /// <summary>
        /// Connects to other systems for protection updates
        /// </summary>
        private void ConnectToSystems()
        {
            // Connect to temperature system
            if (TemperatureSystem.Instance != null)
            {
                ProtectionUpdated += OnProtectionUpdated;
            }
        }

        /// <summary>
        /// Equips a clothing item to the appropriate slot
        /// </summary>
        public bool EquipClothing(string itemId)
        {
            var item = ItemDatabase.Instance?.GetItem(itemId);
            if (item == null || item.Type != "clothing")
            {
                Logger.LogWarning("ClothingProtectionSystem", $"Item {itemId} is not valid clothing");
                return false;
            }

            var clothingItem = CreateClothingItem(item);
            if (clothingItem == null)
            {
                Logger.LogWarning("ClothingProtectionSystem", $"Failed to create clothing item for {itemId}");
                return false;
            }

            // Unequip existing item in this slot
            if (_equippedClothing[clothingItem.Slot] != null)
            {
                UnequipClothing(clothingItem.Slot);
            }

            // Equip new item
            _equippedClothing[clothingItem.Slot] = clothingItem;
            UpdateProtectionValues();
            
            EmitSignal(SignalName.ClothingEquipped, clothingItem.Slot.ToString(), itemId);
            EventBus.Instance?.EmitClothingEquipped(clothingItem.Slot.ToString(), itemId);
            
            Logger.LogInfo("ClothingProtectionSystem", $"Equipped {item.Name} to {clothingItem.Slot} slot");
            return true;
        }

        /// <summary>
        /// Unequips clothing from a specific slot
        /// </summary>
        public bool UnequipClothing(EquipmentSlot slot)
        {
            if (_equippedClothing[slot] == null)
            {
                return false;
            }

            var item = _equippedClothing[slot];
            _equippedClothing[slot] = null;
            UpdateProtectionValues();
            
            EmitSignal(SignalName.ClothingUnequipped, slot.ToString(), item.ItemId);
            EventBus.Instance?.EmitClothingUnequipped(slot.ToString(), item.ItemId);
            
            Logger.LogInfo("ClothingProtectionSystem", $"Unequipped {item.ItemId} from {slot} slot");
            return true;
        }

        /// <summary>
        /// Creates a clothing item from an item definition
        /// </summary>
        private ClothingItem CreateClothingItem(Item item)
        {
            if (!item.Metadata.ContainsKey("equipment_slot"))
            {
                Logger.LogWarning("ClothingProtectionSystem", $"Clothing item {item.Id} missing equipment_slot metadata");
                return null;
            }

            string slotName = item.GetMetadata<string>("equipment_slot", "");
            if (!Enum.TryParse<EquipmentSlot>(slotName, true, out EquipmentSlot slot))
            {
                Logger.LogWarning("ClothingProtectionSystem", $"Invalid equipment slot: {slotName}");
                return null;
            }

            return new ClothingItem
            {
                ItemId = item.Id,
                Name = item.Name,
                Slot = slot,
                ColdProtection = item.GetMetadata<float>("cold_protection", 0f),
                HeatProtection = item.GetMetadata<float>("heat_protection", 0f),
                WaterResistance = item.GetMetadata<float>("water_resistance", 0f),
                WindResistance = item.GetMetadata<float>("wind_resistance", 0f),
                Durability = item.GetMetadata<float>("durability", 100f),
                MaxDurability = item.GetMetadata<float>("max_durability", 100f),
                HazardResistances = ParseHazardResistances(item)
            };
        }

        /// <summary>
        /// Parses hazard resistances from item metadata
        /// </summary>
        private Dictionary<string, float> ParseHazardResistances(Item item)
        {
            var resistances = new Dictionary<string, float>();
            
            // Check for specific hazard resistances
            string[] hazardTypes = { "lightning", "fire", "cold", "heat", "water", "wind", "radiation" };
            
            foreach (string hazardType in hazardTypes)
            {
                string key = $"{hazardType}_resistance";
                if (item.Metadata.ContainsKey(key))
                {
                    resistances[hazardType] = item.GetMetadata<float>(key, 0f);
                }
            }
            
            return resistances;
        }

        /// <summary>
        /// Updates total protection values from all equipped clothing
        /// </summary>
        private void UpdateProtectionValues()
        {
            _totalColdProtection = 0f;
            _totalHeatProtection = 0f;
            _totalWaterResistance = 0f;
            _totalWindResistance = 0f;
            _hazardResistances.Clear();

            foreach (var clothingItem in _equippedClothing.Values.Where(item => item != null))
            {
                // Apply durability modifier to protection values
                float durabilityModifier = clothingItem.Durability / clothingItem.MaxDurability;
                
                _totalColdProtection += clothingItem.ColdProtection * durabilityModifier;
                _totalHeatProtection += clothingItem.HeatProtection * durabilityModifier;
                _totalWaterResistance += clothingItem.WaterResistance * durabilityModifier;
                _totalWindResistance += clothingItem.WindResistance * durabilityModifier;

                // Combine hazard resistances
                foreach (var resistance in clothingItem.HazardResistances)
                {
                    if (_hazardResistances.ContainsKey(resistance.Key))
                    {
                        _hazardResistances[resistance.Key] += resistance.Value * durabilityModifier;
                    }
                    else
                    {
                        _hazardResistances[resistance.Key] = resistance.Value * durabilityModifier;
                    }
                }
            }

            // Cap protection values at 1.0 (100%)
            _totalColdProtection = Mathf.Min(_totalColdProtection, 1f);
            _totalHeatProtection = Mathf.Min(_totalHeatProtection, 1f);
            _totalWaterResistance = Mathf.Min(_totalWaterResistance, 1f);
            _totalWindResistance = Mathf.Min(_totalWindResistance, 1f);

            foreach (var key in _hazardResistances.Keys.ToList())
            {
                _hazardResistances[key] = Mathf.Min(_hazardResistances[key], 1f);
            }

            EmitSignal(SignalName.ProtectionUpdated, _totalColdProtection, _totalHeatProtection, 
                _totalWaterResistance, _totalWindResistance);
            
            Logger.LogInfo("ClothingProtectionSystem", 
                $"Protection updated - Cold: {_totalColdProtection:F2}, Heat: {_totalHeatProtection:F2}, " +
                $"Water: {_totalWaterResistance:F2}, Wind: {_totalWindResistance:F2}");
        }

        /// <summary>
        /// Damages clothing durability from environmental effects
        /// </summary>
        public void DamageClothing(float damage, string damageType = "general")
        {
            foreach (var clothingItem in _equippedClothing.Values.Where(item => item != null))
            {
                // Apply damage based on type and resistances
                float actualDamage = damage;
                
                if (clothingItem.HazardResistances.ContainsKey(damageType))
                {
                    actualDamage *= (1f - clothingItem.HazardResistances[damageType]);
                }

                clothingItem.Durability = Mathf.Max(0f, clothingItem.Durability - actualDamage);
                
                if (clothingItem.Durability <= 0f)
                {
                    Logger.LogInfo("ClothingProtectionSystem", $"{clothingItem.Name} has been destroyed!");
                    EventBus.Instance?.EmitClothingDestroyed(clothingItem.ItemId, clothingItem.Slot.ToString());
                }
            }

            UpdateProtectionValues();
        }

        /// <summary>
        /// Handles protection updates to other systems
        /// </summary>
        private void OnProtectionUpdated(float coldProtection, float heatProtection, float waterResistance, float windResistance)
        {
            // Update temperature system with clothing protection
            float temperatureProtection = Mathf.Max(coldProtection, heatProtection);
            TemperatureSystem.Instance?.SetClothingProtection(temperatureProtection);
        }

        #region Public API

        /// <summary>
        /// Gets the currently equipped item in a specific slot
        /// </summary>
        public ClothingItem GetEquippedItem(EquipmentSlot slot)
        {
            return _equippedClothing[slot];
        }

        /// <summary>
        /// Gets all currently equipped clothing items
        /// </summary>
        public Dictionary<EquipmentSlot, ClothingItem> GetAllEquippedItems()
        {
            return new Dictionary<EquipmentSlot, ClothingItem>(_equippedClothing);
        }

        /// <summary>
        /// Gets total protection values
        /// </summary>
        public ClothingProtectionData GetProtectionData()
        {
            return new ClothingProtectionData
            {
                ColdProtection = _totalColdProtection,
                HeatProtection = _totalHeatProtection,
                WaterResistance = _totalWaterResistance,
                WindResistance = _totalWindResistance,
                HazardResistances = new Dictionary<string, float>(_hazardResistances)
            };
        }

        /// <summary>
        /// Gets resistance to a specific hazard type
        /// </summary>
        public float GetHazardResistance(string hazardType)
        {
            return _hazardResistances.GetValueOrDefault(hazardType, 0f);
        }

        /// <summary>
        /// Checks if a clothing item can be equipped
        /// </summary>
        public bool CanEquipClothing(string itemId)
        {
            var item = ItemDatabase.Instance?.GetItem(itemId);
            return item != null && item.Type == "clothing" && item.Metadata.ContainsKey("equipment_slot");
        }

        /// <summary>
        /// Gets the equipment slot for a clothing item
        /// </summary>
        public EquipmentSlot? GetItemSlot(string itemId)
        {
            var item = ItemDatabase.Instance?.GetItem(itemId);
            if (item == null || item.Type != "clothing" || !item.Metadata.ContainsKey("equipment_slot"))
            {
                return null;
            }

            string slotName = item.GetMetadata<string>("equipment_slot", "");
            if (Enum.TryParse<EquipmentSlot>(slotName, true, out EquipmentSlot slot))
            {
                return slot;
            }

            return null;
        }

        /// <summary>
        /// Repairs clothing durability
        /// </summary>
        public bool RepairClothing(EquipmentSlot slot, float repairAmount)
        {
            var item = _equippedClothing[slot];
            if (item == null) return false;

            item.Durability = Mathf.Min(item.MaxDurability, item.Durability + repairAmount);
            UpdateProtectionValues();
            
            Logger.LogInfo("ClothingProtectionSystem", $"Repaired {item.Name} by {repairAmount:F1} durability");
            return true;
        }

        #endregion

        public override void _ExitTree()
        {
            if (_instance == this)
            {
                _instance = null;
            }
        }
    }

    /// <summary>
    /// Represents a clothing item with protection properties
    /// </summary>
    public class ClothingItem
    {
        public string ItemId { get; set; }
        public string Name { get; set; }
        public ClothingProtectionSystem.EquipmentSlot Slot { get; set; }
        public float ColdProtection { get; set; }
        public float HeatProtection { get; set; }
        public float WaterResistance { get; set; }
        public float WindResistance { get; set; }
        public float Durability { get; set; }
        public float MaxDurability { get; set; }
        public Dictionary<string, float> HazardResistances { get; set; } = new Dictionary<string, float>();

        public float DurabilityPercentage => MaxDurability > 0 ? Durability / MaxDurability : 0f;
        public bool IsDestroyed => Durability <= 0f;
    }

    /// <summary>
    /// Data structure for clothing protection information
    /// </summary>
    public class ClothingProtectionData
    {
        public float ColdProtection { get; set; }
        public float HeatProtection { get; set; }
        public float WaterResistance { get; set; }
        public float WindResistance { get; set; }
        public Dictionary<string, float> HazardResistances { get; set; } = new Dictionary<string, float>();
    }
}