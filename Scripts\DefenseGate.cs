using Godot;
using System;
using System.Collections.Generic;

namespace SurvivalLooterShooter
{
    /// <summary>
    /// Defensive gate that can be opened/closed and provides controlled access
    /// Integrates with security systems and can be automated or manually controlled
    /// </summary>
    public partial class DefenseGate : Node2D
    {
        [Export] public float OpenSpeed { get; set; } = 2.0f;
        [Export] public bool AutoClose { get; set; } = true;
        [Export] public float AutoCloseDelay { get; set; } = 5.0f;
        [Export] public bool RequiresKey { get; set; } = false;
        [Export] public string RequiredKeyId { get; set; } = "";

        private Structure _parentStructure;
        private StaticBody2D _gateBody;
        private CollisionShape2D _gateCollision;
        private Sprite2D _gateSprite;
        private Area2D _interactionArea;
        private Area2D _triggerArea;
        private Timer _autoCloseTimer;
        private AudioStreamPlayer2D _gateSound;
        
        // Gate state
        private bool _isOpen = false;
        private bool _isMoving = false;
        private bool _isLocked = false;
        private Vector2 _closedPosition;
        private Vector2 _openPosition;
        private readonly HashSet<Node2D> _entitiesInTriggerArea = [];

        // Security integration
        private bool _securityEnabled = false;
        private string _accessLevel = "public"; // public, restricted, private

        public bool IsOpen => _isOpen;
        public bool IsMoving => _isMoving;
        public bool IsLocked => _isLocked;
        public bool CanBeOpened => !IsLocked && !IsMoving;
        public string AccessLevel => _accessLevel;

        // Events
        [Signal] public delegate void GateOpenedEventHandler(DefenseGate gate);
        [Signal] public delegate void GateClosedEventHandler(DefenseGate gate);
        [Signal] public delegate void GateLockedEventHandler(DefenseGate gate);
        [Signal] public delegate void GateUnlockedEventHandler(DefenseGate gate);
        [Signal] public delegate void AccessDeniedEventHandler(DefenseGate gate, Node2D entity);
        [Signal] public delegate void GateDestroyedEventHandler(DefenseGate gate);

        public override void _Ready()
        {
            SetupComponents();
            SetupTimers();
            CalculatePositions();
            
            Logger.LogInfo("DefenseGate", "Defense gate initialized");
        }

        /// <summary>
        /// Sets up gate components and collision
        /// </summary>
        private void SetupComponents()
        {
            // Create gate body
            _gateBody = new StaticBody2D();
            _gateBody.Name = "GateBody";
            AddChild(_gateBody);

            // Create gate collision
            _gateCollision = new CollisionShape2D();
            _gateCollision.Name = "GateCollision";
            var rectShape = new RectangleShape2D();
            rectShape.Size = new Vector2(64, 16); // Gate size
            _gateCollision.Shape = rectShape;
            _gateBody.AddChild(_gateCollision);

            // Create gate sprite
            _gateSprite = new Sprite2D();
            _gateSprite.Name = "GateSprite";
            _gateSprite.Texture = CreateGateTexture();
            AddChild(_gateSprite);

            // Create interaction area
            _interactionArea = new Area2D();
            _interactionArea.Name = "InteractionArea";
            var interactionShape = new CollisionShape2D();
            var interactionCircle = new CircleShape2D();
            interactionCircle.Radius = 50f;
            interactionShape.Shape = interactionCircle;
            _interactionArea.AddChild(interactionShape);
            AddChild(_interactionArea);

            // Create trigger area for auto-open
            _triggerArea = new Area2D();
            _triggerArea.Name = "TriggerArea";
            var triggerShape = new CollisionShape2D();
            var triggerCircle = new CircleShape2D();
            triggerCircle.Radius = 80f;
            triggerShape.Shape = triggerCircle;
            _triggerArea.AddChild(triggerShape);
            AddChild(_triggerArea);

            // Connect signals
            _interactionArea.InputEvent += OnInteractionAreaInputEvent;
            _triggerArea.BodyEntered += OnTriggerAreaEntered;
            _triggerArea.BodyExited += OnTriggerAreaExited;

            // Create audio player
            _gateSound = new AudioStreamPlayer2D();
            _gateSound.Name = "GateSound";
            AddChild(_gateSound);
        }

        /// <summary>
        /// Sets up timers for auto-close functionality
        /// </summary>
        private void SetupTimers()
        {
            _autoCloseTimer = new Timer();
            _autoCloseTimer.Name = "AutoCloseTimer";
            _autoCloseTimer.WaitTime = AutoCloseDelay;
            _autoCloseTimer.OneShot = true;
            _autoCloseTimer.Timeout += OnAutoCloseTimeout;
            AddChild(_autoCloseTimer);
        }

        /// <summary>
        /// Calculates open and closed positions for the gate
        /// </summary>
        private void CalculatePositions()
        {
            _closedPosition = Vector2.Zero;
            _openPosition = new Vector2(0, -40); // Gate slides up when opened
        }

        /// <summary>
        /// Creates a simple gate texture
        /// </summary>
        private ImageTexture CreateGateTexture()
        {
            var image = Image.CreateEmpty(64, 16, false, Image.Format.Rgb8);
            
            // Create gate pattern
            for (int x = 0; x < 64; x++)
            {
                for (int y = 0; y < 16; y++)
                {
                    if (x % 8 == 0 || y == 0 || y == 15)
                    {
                        image.SetPixel(x, y, Colors.DarkGray);
                    }
                    else
                    {
                        image.SetPixel(x, y, Colors.Gray);
                    }
                }
            }
            
            return ImageTexture.CreateFromImage(image);
        }

        /// <summary>
        /// Initializes the gate with a parent structure
        /// </summary>
        public void Initialize(Structure parentStructure)
        {
            _parentStructure = parentStructure;
            
            if (parentStructure != null)
            {
                // Connect to structure events
                parentStructure.StructureDestroyed += OnParentStructureDestroyed;
                parentStructure.StructureDamaged += OnParentStructureDamaged;
                
                // Adjust gate size based on structure
                if (parentStructure.Blueprint?.Size != null)
                {
                    var size = parentStructure.Blueprint.Size;
                    if (_gateCollision.Shape is RectangleShape2D rect)
                    {
                        rect.Size = new Vector2(size.Width * 32, 16);
                    }
                }
            }
        }

        /// <summary>
        /// Opens the gate
        /// </summary>
        public void Open()
        {
            if (_isOpen || _isMoving || _isLocked) return;

            _isMoving = true;
            
            // Disable collision while opening
            _gateCollision.Disabled = true;
            
            // Animate gate opening
            var tween = CreateTween();
            tween.TweenProperty(_gateSprite, "position", _openPosition, 1f / OpenSpeed);
            tween.TweenCallback(Callable.From(OnGateOpenComplete));
            
            // Play sound
            PlayGateSound("open");
            
            Logger.LogInfo("DefenseGate", "Gate opening...");
        }

        /// <summary>
        /// Closes the gate
        /// </summary>
        public void Close()
        {
            if (!_isOpen || _isMoving) return;

            // Check if anything is blocking the gate
            if (IsGateBlocked())
            {
                Logger.LogInfo("DefenseGate", "Gate blocked, cannot close");
                return;
            }

            _isMoving = true;
            
            // Animate gate closing
            var tween = CreateTween();
            tween.TweenProperty(_gateSprite, "position", _closedPosition, 1f / OpenSpeed);
            tween.TweenCallback(Callable.From(OnGateCloseComplete));
            
            // Play sound
            PlayGateSound("close");
            
            Logger.LogInfo("DefenseGate", "Gate closing...");
        }

        /// <summary>
        /// Toggles the gate open/closed state
        /// </summary>
        public void Toggle()
        {
            if (_isOpen)
                Close();
            else
                Open();
        }

        /// <summary>
        /// Locks the gate
        /// </summary>
        public void Lock()
        {
            if (_isLocked) return;

            _isLocked = true;
            
            // Close gate if it's open
            if (_isOpen)
            {
                Close();
            }
            
            // Update visual appearance
            _gateSprite.Modulate = Colors.Red;
            
            EmitSignal(SignalName.GateLocked, this);
            Logger.LogInfo("DefenseGate", "Gate locked");
        }

        /// <summary>
        /// Unlocks the gate
        /// </summary>
        public void Unlock()
        {
            if (!_isLocked) return;

            _isLocked = false;
            
            // Reset visual appearance
            _gateSprite.Modulate = Colors.White;
            
            EmitSignal(SignalName.GateUnlocked, this);
            Logger.LogInfo("DefenseGate", "Gate unlocked");
        }

        /// <summary>
        /// Checks if the gate is blocked by entities
        /// </summary>
        private bool IsGateBlocked()
        {
            // Check if any entities are in the gate's path
            var spaceState = GetWorld2D().DirectSpaceState;
            var query = PhysicsRayQueryParameters2D.Create(
                GlobalPosition + _openPosition,
                GlobalPosition + _closedPosition
            );
            
            var result = spaceState.IntersectRay(query);
            return result.Count > 0;
        }

        /// <summary>
        /// Checks if an entity has access to operate the gate
        /// </summary>
        private bool HasAccess(Node2D entity)
        {
            // Public access - anyone can use
            if (_accessLevel == "public") return true;
            
            // Check if it's the player
            if (entity.IsInGroup("player"))
            {
                // Check for required key if needed
                if (RequiresKey && !string.IsNullOrEmpty(RequiredKeyId))
                {
                    var inventory = GetNode<Inventory>("/root/GameManager/Inventory");
                    if (inventory == null || !inventory.HasItem(RequiredKeyId, 1))
                    {
                        return false;
                    }
                }
                return true;
            }
            
            // Restricted access - only specific entities
            if (_accessLevel == "restricted")
            {
                // Could check for specific tags, groups, or IDs
                return entity.IsInGroup("authorized");
            }
            
            // Private access - no one except owner
            return false;
        }

        /// <summary>
        /// Plays gate sound effect
        /// </summary>
        private void PlayGateSound(string soundType)
        {
            // Try to load appropriate sound
            string soundPath = $"res://Assets/Audio/gate_{soundType}.ogg";
            if (ResourceLoader.Exists(soundPath))
            {
                _gateSound.Stream = GD.Load<AudioStream>(soundPath);
                _gateSound.Play();
            }
        }

        /// <summary>
        /// Called when gate opening animation completes
        /// </summary>
        private void OnGateOpenComplete()
        {
            _isMoving = false;
            _isOpen = true;
            
            // Start auto-close timer if enabled
            if (AutoClose)
            {
                _autoCloseTimer.Start();
            }
            
            EmitSignal(SignalName.GateOpened, this);
            Logger.LogInfo("DefenseGate", "Gate opened");
        }

        /// <summary>
        /// Called when gate closing animation completes
        /// </summary>
        private void OnGateCloseComplete()
        {
            _isMoving = false;
            _isOpen = false;
            
            // Re-enable collision
            _gateCollision.Disabled = false;
            
            EmitSignal(SignalName.GateClosed, this);
            Logger.LogInfo("DefenseGate", "Gate closed");
        }

        /// <summary>
        /// Handles interaction area input events
        /// </summary>
        private void OnInteractionAreaInputEvent(Node viewport, InputEvent @event, long shapeIdx)
        {
            if (@event is InputEventMouseButton mouseEvent && 
                mouseEvent.Pressed && 
                mouseEvent.ButtonIndex == MouseButton.Left)
            {
                // Try to operate the gate
                var player = GetTree().GetFirstNodeInGroup("player") as Node2D;
                if (player != null)
                {
                    OperateGate(player);
                }
            }
        }

        /// <summary>
        /// Handles entity entering trigger area
        /// </summary>
        private void OnTriggerAreaEntered(Node2D body)
        {
            _entitiesInTriggerArea.Add(body);
            
            // Auto-open for authorized entities
            if (!_isOpen && HasAccess(body))
            {
                Open();
            }
        }

        /// <summary>
        /// Handles entity exiting trigger area
        /// </summary>
        private void OnTriggerAreaExited(Node2D body)
        {
            _entitiesInTriggerArea.Remove(body);
            
            // Start auto-close timer if no entities remain
            if (_entitiesInTriggerArea.Count == 0 && _isOpen && AutoClose)
            {
                _autoCloseTimer.Start();
            }
        }

        /// <summary>
        /// Handles auto-close timer timeout
        /// </summary>
        private void OnAutoCloseTimeout()
        {
            // Only close if no entities are in the trigger area
            if (_entitiesInTriggerArea.Count == 0)
            {
                Close();
            }
        }

        /// <summary>
        /// Attempts to operate the gate with the specified entity
        /// </summary>
        public void OperateGate(Node2D entity)
        {
            if (!HasAccess(entity))
            {
                EmitSignal(SignalName.AccessDenied, this, entity);
                Logger.LogInfo("DefenseGate", $"Access denied for {entity.Name}");
                return;
            }

            Toggle();
        }

        /// <summary>
        /// Sets the security level for the gate
        /// </summary>
        public void SetSecurityLevel(string level)
        {
            _accessLevel = level.ToLower();
            Logger.LogInfo("DefenseGate", $"Security level set to: {_accessLevel}");
        }

        /// <summary>
        /// Enables or disables security features
        /// </summary>
        public void SetSecurityEnabled(bool enabled)
        {
            _securityEnabled = enabled;
            
            if (enabled)
            {
                // Connect to defense system events
                if (DefenseSystem.Instance != null)
                {
                    DefenseSystem.Instance.BaseRaidStarted += OnBaseRaidStarted;
                    DefenseSystem.Instance.BaseRaidEnded += OnBaseRaidEnded;
                }
            }
            else
            {
                // Disconnect from defense system events
                if (DefenseSystem.Instance != null)
                {
                    DefenseSystem.Instance.BaseRaidStarted -= OnBaseRaidStarted;
                    DefenseSystem.Instance.BaseRaidEnded -= OnBaseRaidEnded;
                }
            }
        }

        /// <summary>
        /// Handles base raid start - automatically locks gate
        /// </summary>
        private void OnBaseRaidStarted(float intensity, int enemyCount)
        {
            if (_securityEnabled)
            {
                Lock();
                Logger.LogInfo("DefenseGate", "Gate automatically locked due to base raid");
            }
        }

        /// <summary>
        /// Handles base raid end - unlocks gate if player won
        /// </summary>
        private void OnBaseRaidEnded(bool playerVictory, float damageDealt)
        {
            if (_securityEnabled && playerVictory)
            {
                Unlock();
                Logger.LogInfo("DefenseGate", "Gate automatically unlocked after successful defense");
            }
        }

        /// <summary>
        /// Handles parent structure destruction
        /// </summary>
        private void OnParentStructureDestroyed(Structure structure)
        {
            EmitSignal(SignalName.GateDestroyed, this);
            Logger.LogInfo("DefenseGate", "Gate destroyed with parent structure");
        }

        /// <summary>
        /// Handles parent structure damage
        /// </summary>
        private void OnParentStructureDamaged(Structure structure, int damage)
        {
            // Reduce gate operation speed when damaged
            float healthPercent = structure.HealthPercentage;
            OpenSpeed = 2.0f * healthPercent;
            
            if (healthPercent < 0.3f)
            {
                // Gate may malfunction when heavily damaged
                if (GD.Randf() < 0.1f) // 10% chance
                {
                    Logger.LogInfo("DefenseGate", "Gate malfunctioned due to damage");
                    if (_isOpen)
                        Close();
                    else
                        Open();
                }
            }
        }

        /// <summary>
        /// Gets the parent structure
        /// </summary>
        public Structure GetStructure()
        {
            return _parentStructure;
        }

        /// <summary>
        /// Gets gate status information
        /// </summary>
        public Dictionary<string, object> GetStatus()
        {
            return new Dictionary<string, object>
            {
                ["is_open"] = _isOpen,
                ["is_moving"] = _isMoving,
                ["is_locked"] = _isLocked,
                ["access_level"] = _accessLevel,
                ["security_enabled"] = _securityEnabled,
                ["auto_close"] = AutoClose,
                ["requires_key"] = RequiresKey,
                ["required_key_id"] = RequiredKeyId,
                ["entities_in_area"] = _entitiesInTriggerArea.Count
            };
        }

        public override void _ExitTree()
        {
            if (DefenseSystem.Instance != null)
            {
                DefenseSystem.Instance.BaseRaidStarted -= OnBaseRaidStarted;
                DefenseSystem.Instance.BaseRaidEnded -= OnBaseRaidEnded;
            }
        }
    }
}