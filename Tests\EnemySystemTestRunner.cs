using Godot;
using System;

namespace SurvivalLooterShooter
{
    /// <summary>
    /// Test runner for the enemy system that can be executed independently
    /// Verifies that all enemy system components work correctly
    /// </summary>
    public partial class EnemySystemTestRunner : Node
    {
        private bool _testsCompleted = false;
        private int _totalTests = 0;
        private int _passedTests = 0;
        private int _failedTests = 0;

        public override void _Ready()
        {
            GD.Print("=== Enemy System Test Runner ===");
            CallDeferred(nameof(RunAllTests));
        }

        private void RunAllTests()
        {
            try
            {
                // Test 1: Enemy Data Loading
                TestEnemyDataLoading();

                // Test 2: Enemy Creation
                TestEnemyCreation();

                // Test 3: Enemy Health System
                TestEnemyHealthSystem();

                // Test 4: Enemy Movement
                TestEnemyMovement();

                // Test 5: Enemy Range Detection
                TestEnemyRangeDetection();

                PrintTestResults();
                _testsCompleted = true;
            }
            catch (Exception ex)
            {
                GD.PrintErr($"Test runner failed: {ex.Message}");
                _testsCompleted = true;
            }
        }

        private void TestEnemyDataLoading()
        {
            _totalTests++;
            GD.Print("Testing enemy data loading...");

            try
            {
                // Check if enemy data file exists
                if (!FileAccess.FileExists("res://Data/Enemies.json"))
                {
                    throw new Exception("Enemies.json file not found");
                }

                _passedTests++;
                GD.Print("✓ Enemy data loading test passed");
            }
            catch (Exception ex)
            {
                _failedTests++;
                GD.PrintErr($"✗ Enemy data loading test failed: {ex.Message}");
            }
        }

        private void TestEnemyCreation()
        {
            _totalTests++;
            GD.Print("Testing enemy creation...");

            try
            {
                var enemy = new Enemy();

                // Test default values
                if (enemy.MaxHealth != 100f)
                {
                    throw new Exception($"Default max health incorrect: expected 100, got {enemy.MaxHealth}");
                }

                if (enemy.CurrentHealth != 100f)
                {
                    throw new Exception($"Default current health incorrect: expected 100, got {enemy.CurrentHealth}");
                }

                if (enemy.IsDead)
                {
                    throw new Exception("Enemy should not be dead initially");
                }

                _passedTests++;
                GD.Print("✓ Enemy creation test passed");
            }
            catch (Exception ex)
            {
                _failedTests++;
                GD.PrintErr($"✗ Enemy creation test failed: {ex.Message}");
            }
        }

        private void TestEnemyHealthSystem()
        {
            _totalTests++;
            GD.Print("Testing enemy health system...");

            try
            {
                var enemy = new Enemy();

                var enemyData = new EnemyData
                {
                    Id = "test_enemy",
                    Name = "Test Enemy",
                    Health = 100f,
                    MaxHealth = 100f
                };
                enemy.Initialize(enemyData);

                // Test damage
                enemy.TakeDamage(30f);
                if (enemy.CurrentHealth != 70f)
                {
                    throw new Exception($"Damage not applied correctly: expected 70, got {enemy.CurrentHealth}");
                }

                // Test healing
                enemy.Heal(10f);
                if (enemy.CurrentHealth != 80f)
                {
                    throw new Exception($"Healing not applied correctly: expected 80, got {enemy.CurrentHealth}");
                }

                // Test death
                bool deathEventFired = false;
                enemy.EnemyDied += () => { deathEventFired = true; };

                enemy.TakeDamage(100f);
                if (!enemy.IsDead)
                {
                    throw new Exception("Enemy should be dead after lethal damage");
                }

                if (!deathEventFired)
                {
                    throw new Exception("Death event should have been fired");
                }

                _passedTests++;
                GD.Print("✓ Enemy health system test passed");
            }
            catch (Exception ex)
            {
                _failedTests++;
                GD.PrintErr($"✗ Enemy health system test failed: {ex.Message}");
            }
        }

        private void TestEnemyMovement()
        {
            _totalTests++;
            GD.Print("Testing enemy movement...");

            try
            {
                var enemy = new Enemy();

                var enemyData = new EnemyData
                {
                    Id = "test_enemy",
                    Name = "Test Enemy",
                    Speed = 100f
                };
                enemy.Initialize(enemyData);

                enemy.GlobalPosition = Vector2.Zero;
                var targetPosition = new Vector2(100, 0);

                // Test AI controller integration
                var aiController = enemy.GetAIController();
                if (aiController == null)
                {
                    throw new Exception("Enemy should have AI controller");
                }

                // Test AI state
                if (aiController.CurrentState == AIController.AIState.Dead)
                {
                    throw new Exception("AI should not start in dead state");
                }

                // Test stop movement
                enemy.StopMovement();
                if (enemy.Velocity != Vector2.Zero)
                {
                    throw new Exception("Enemy should stop moving");
                }

                _passedTests++;
                GD.Print("✓ Enemy movement test passed");
            }
            catch (Exception ex)
            {
                _failedTests++;
                GD.PrintErr($"✗ Enemy movement test failed: {ex.Message}");
            }
        }

        private void TestEnemyRangeDetection()
        {
            _totalTests++;
            GD.Print("Testing enemy range detection...");

            try
            {
                var enemy = new Enemy();

                var enemyData = new EnemyData
                {
                    Id = "test_enemy",
                    Name = "Test Enemy",
                    DetectionRange = 100f,
                    AttackRange = 50f
                };
                enemy.Initialize(enemyData);

                enemy.GlobalPosition = Vector2.Zero;
                
                // Create Node2D targets at different positions
                var closeTarget = new Node2D();
                closeTarget.GlobalPosition = new Vector2(30, 0);
                var mediumTarget = new Node2D();
                mediumTarget.GlobalPosition = new Vector2(75, 0);
                var farTarget = new Node2D();
                farTarget.GlobalPosition = new Vector2(150, 0);

                // Test attack range
                if (!enemy.IsTargetInAttackRange(closeTarget))
                {
                    throw new Exception("Close target should be in attack range");
                }

                if (enemy.IsTargetInAttackRange(mediumTarget))
                {
                    throw new Exception("Medium target should not be in attack range");
                }

                // Test detection range
                if (!enemy.IsTargetInDetectionRange(closeTarget))
                {
                    throw new Exception("Close target should be in detection range");
                }

                if (!enemy.IsTargetInDetectionRange(mediumTarget))
                {
                    throw new Exception("Medium target should be in detection range");
                }

                if (enemy.IsTargetInDetectionRange(farTarget))
                {
                    throw new Exception("Far target should not be in detection range");
                }

                _passedTests++;
                GD.Print("✓ Enemy range detection test passed");
            }
            catch (Exception ex)
            {
                _failedTests++;
                GD.PrintErr($"✗ Enemy range detection test failed: {ex.Message}");
            }
        }

        private void PrintTestResults()
        {
            GD.Print("=== Enemy System Test Results ===");
            GD.Print($"Total Tests: {_totalTests}");
            GD.Print($"Passed: {_passedTests}");
            GD.Print($"Failed: {_failedTests}");

            if (_failedTests == 0)
            {
                GD.Print("🎉 All enemy system tests passed!");
            }
            else
            {
                GD.PrintErr($"❌ {_failedTests} test(s) failed");
            }

            GD.Print("=== End Test Results ===");
        }

        public bool AreTestsCompleted()
        {
            return _testsCompleted;
        }

        public bool AllTestsPassed()
        {
            return _testsCompleted && _failedTests == 0;
        }
    }
}