[{"id": "bandage", "name": "Bandage", "type": "consumable", "max_stack": 10, "metadata": {"health_restore": 25, "use_time": 2.0}}, {"id": "assault_rifle", "name": "Assault Rifle", "type": "weapon", "max_stack": 1, "metadata": {"damage": 35, "fire_rate": 0.1, "ammo_type": "rifle_ammo", "durability": 100, "max_durability": 100, "weapon_type": "automatic", "magazine_size": 30, "current_ammo": 0, "reload_time": 2.0, "range": 100}}, {"id": "rifle_ammo", "name": "Rifle Ammunition", "type": "ammo", "max_stack": 100, "metadata": {"ammo_type": "rifle"}}, {"id": "cloth", "name": "<PERSON><PERSON><PERSON>", "type": "material", "max_stack": 50, "metadata": {}}, {"id": "alcohol", "name": "Alcohol", "type": "material", "max_stack": 20, "metadata": {}}, {"id": "metal_scrap", "name": "Metal Scrap", "type": "material", "max_stack": 50, "metadata": {}}, {"id": "gunpowder", "name": "Gunpowder", "type": "material", "max_stack": 30, "metadata": {}}, {"id": "herbs", "name": "<PERSON><PERSON>", "type": "material", "max_stack": 25, "metadata": {}}, {"id": "water", "name": "Water", "type": "consumable", "max_stack": 10, "metadata": {"thirst_restore": 30, "use_time": 1.0}}, {"id": "canned_food", "name": "Canned Food", "type": "consumable", "max_stack": 15, "metadata": {"hunger_restore": 40, "use_time": 3.0}}, {"id": "energy_drink", "name": "Energy Drink", "type": "consumable", "max_stack": 8, "metadata": {"stamina_restore": 50, "thirst_restore": 15, "use_time": 1.5}}, {"id": "mre", "name": "MRE (Meal Ready to Eat)", "type": "consumable", "max_stack": 5, "metadata": {"hunger_restore": 60, "thirst_restore": 10, "health_restore": 10, "use_time": 4.0}}, {"id": "herbal_tea", "name": "Herbal Tea", "type": "consumable", "max_stack": 12, "metadata": {"thirst_restore": 25, "stamina_restore": 20, "use_time": 2.0}}, {"id": "health_potion", "name": "Health Potion", "type": "consumable", "max_stack": 5, "metadata": {"health_restore": 75, "use_time": 1.0}}, {"id": "pistol", "name": "Pistol", "type": "weapon", "max_stack": 1, "metadata": {"damage": 25, "fire_rate": 0.5, "ammo_type": "pistol_ammo", "durability": 100, "max_durability": 100, "weapon_type": "single", "magazine_size": 12, "current_ammo": 0, "reload_time": 1.5, "range": 50}}, {"id": "pistol_ammo", "name": "Pistol Ammunition", "type": "ammo", "max_stack": 100, "metadata": {"ammo_type": "pistol"}}, {"id": "shotgun", "name": "Shotgun", "type": "weapon", "max_stack": 1, "metadata": {"damage": 80, "fire_rate": 1.2, "ammo_type": "shotgun_ammo", "durability": 100, "max_durability": 100, "weapon_type": "shotgun", "magazine_size": 6, "current_ammo": 0, "reload_time": 2.5, "range": 30}}, {"id": "shotgun_ammo", "name": "Shotgun Shells", "type": "ammo", "max_stack": 50, "metadata": {"ammo_type": "shotgun"}}, {"id": "wood", "name": "<PERSON>", "type": "resource", "max_stack": 100, "metadata": {"resource_type": "organic", "tool_required": "axe"}}, {"id": "stone", "name": "Stone", "type": "resource", "max_stack": 100, "metadata": {"resource_type": "mineral", "tool_required": "pickaxe"}}, {"id": "metal_ore", "name": "Metal Ore", "type": "resource", "max_stack": 50, "metadata": {"resource_type": "mineral", "tool_required": "pickaxe"}}, {"id": "berries", "name": "Berries", "type": "resource", "max_stack": 25, "metadata": {"resource_type": "organic", "tool_required": "none"}}, {"id": "mushrooms", "name": "Mushrooms", "type": "resource", "max_stack": 20, "metadata": {"resource_type": "organic", "tool_required": "none"}}, {"id": "axe", "name": "Axe", "type": "tool", "max_stack": 1, "metadata": {"tool_type": "axe", "durability": 100, "max_durability": 100, "efficiency": 1.0}}, {"id": "pickaxe", "name": "Pickaxe", "type": "tool", "max_stack": 1, "metadata": {"tool_type": "pickaxe", "durability": 100, "max_durability": 100, "efficiency": 1.0}}, {"id": "coal", "name": "Coal", "type": "resource", "max_stack": 100, "metadata": {"resource_type": "mineral", "tool_required": "pickaxe"}}, {"id": "gems", "name": "Gems", "type": "resource", "max_stack": 25, "metadata": {"resource_type": "mineral", "tool_required": "pickaxe"}}, {"id": "reeds", "name": "<PERSON><PERSON>", "type": "resource", "max_stack": 50, "metadata": {"resource_type": "organic", "tool_required": "none"}}, {"id": "ice", "name": "Ice", "type": "resource", "max_stack": 75, "metadata": {"resource_type": "mineral", "tool_required": "pickaxe"}}, {"id": "raw_meat", "name": "Raw Meat", "type": "consumable", "max_stack": 20, "metadata": {"hunger_restore": 20, "use_time": 2.0, "can_cook": true}}, {"id": "wolf_pelt", "name": "<PERSON>", "type": "material", "max_stack": 10, "metadata": {"crafting_material": true, "tier": "common"}}, {"id": "bone", "name": "Bone", "type": "material", "max_stack": 50, "metadata": {"crafting_material": true, "tier": "common"}}, {"id": "leather", "name": "Leather", "type": "material", "max_stack": 25, "metadata": {"crafting_material": true, "tier": "common"}}, {"id": "poison_gland", "name": "Poison Gland", "type": "material", "max_stack": 15, "metadata": {"crafting_material": true, "tier": "uncommon", "special_property": "poison"}}, {"id": "chitin", "name": "<PERSON><PERSON>", "type": "material", "max_stack": 20, "metadata": {"crafting_material": true, "tier": "uncommon"}}, {"id": "venom", "name": "Venom", "type": "material", "max_stack": 10, "metadata": {"crafting_material": true, "tier": "uncommon", "special_property": "poison"}}, {"id": "bear_pelt", "name": "<PERSON>", "type": "material", "max_stack": 5, "metadata": {"crafting_material": true, "tier": "uncommon"}}, {"id": "fat", "name": "Fat", "type": "material", "max_stack": 30, "metadata": {"crafting_material": true, "tier": "common"}}, {"id": "ice_wolf_pelt", "name": "<PERSON>t", "type": "material", "max_stack": 8, "metadata": {"crafting_material": true, "tier": "uncommon", "special_property": "cold_resistance"}}, {"id": "ice_crystal", "name": "Ice Crystal", "type": "material", "max_stack": 15, "metadata": {"crafting_material": true, "tier": "rare", "special_property": "frost"}}, {"id": "wolf_fang", "name": "<PERSON>", "type": "material", "max_stack": 10, "metadata": {"crafting_material": true, "tier": "rare", "special_property": "sharp"}}, {"id": "alpha_pelt", "name": "Alpha Pelt", "type": "material", "max_stack": 3, "metadata": {"crafting_material": true, "tier": "rare", "special_property": "leadership"}}, {"id": "wolf_essence", "name": "<PERSON>", "type": "material", "max_stack": 5, "metadata": {"crafting_material": true, "tier": "rare", "special_property": "pack_hunter"}}, {"id": "bear_claw", "name": "Bear <PERSON>law", "type": "material", "max_stack": 8, "metadata": {"crafting_material": true, "tier": "rare", "special_property": "strength"}}, {"id": "thick_hide", "name": "<PERSON><PERSON><PERSON>", "type": "material", "max_stack": 5, "metadata": {"crafting_material": true, "tier": "rare", "special_property": "armor"}}, {"id": "bear_strength_essence", "name": "Bear Strength Essence", "type": "material", "max_stack": 3, "metadata": {"crafting_material": true, "tier": "rare", "special_property": "strength_boost"}}, {"id": "scorpion_stinger", "name": "<PERSON><PERSON><PERSON>er", "type": "material", "max_stack": 6, "metadata": {"crafting_material": true, "tier": "rare", "special_property": "poison_weapon"}}, {"id": "venom_sac", "name": "Venom Sac", "type": "material", "max_stack": 4, "metadata": {"crafting_material": true, "tier": "rare", "special_property": "deadly_poison"}}, {"id": "chitin_plate", "name": "Chitin Plate", "type": "material", "max_stack": 5, "metadata": {"crafting_material": true, "tier": "rare", "special_property": "natural_armor"}}, {"id": "rare_bone", "name": "Rare Bone", "type": "material", "max_stack": 10, "metadata": {"crafting_material": true, "tier": "rare", "special_property": "enhanced_durability"}}, {"id": "essence_crystal", "name": "Essence Crystal", "type": "material", "max_stack": 5, "metadata": {"crafting_material": true, "tier": "rare", "special_property": "magical_essence"}}, {"id": "monster_core", "name": "Monster Core", "type": "material", "max_stack": 3, "metadata": {"crafting_material": true, "tier": "rare", "special_property": "life_force"}}, {"id": "legendary_essence", "name": "Legend<PERSON>", "type": "material", "max_stack": 1, "metadata": {"crafting_material": true, "tier": "legendary", "special_property": "legendary_power"}}, {"id": "ancient_relic", "name": "Ancient Relic", "type": "material", "max_stack": 1, "metadata": {"crafting_material": true, "tier": "legendary", "special_property": "ancient_knowledge"}}, {"id": "power_crystal", "name": "Power Crystal", "type": "material", "max_stack": 1, "metadata": {"crafting_material": true, "tier": "legendary", "special_property": "immense_power"}}, {"id": "wood", "name": "<PERSON>", "type": "building_material", "max_stack": 100, "metadata": {"crafting_material": true, "building_material": true}}, {"id": "stone", "name": "Stone", "type": "building_material", "max_stack": 100, "metadata": {"crafting_material": true, "building_material": true}}, {"id": "nails", "name": "Nails", "type": "building_material", "max_stack": 50, "metadata": {"crafting_material": true, "building_material": true}}, {"id": "concrete", "name": "Concrete", "type": "building_material", "max_stack": 50, "metadata": {"crafting_material": true, "building_material": true}}, {"id": "electronic_components", "name": "Electronic Components", "type": "building_material", "max_stack": 20, "metadata": {"crafting_material": true, "building_material": true, "tier": "advanced"}}, {"id": "advanced_components", "name": "Advanced Components", "type": "building_material", "max_stack": 10, "metadata": {"crafting_material": true, "building_material": true, "tier": "legendary"}}, {"id": "reinforced_metal", "name": "Reinforced Metal", "type": "building_material", "max_stack": 25, "metadata": {"crafting_material": true, "building_material": true, "tier": "advanced"}}, {"id": "ammunition", "name": "Ammunition", "type": "ammo", "max_stack": 200, "metadata": {"ammo_type": "generic"}}, {"id": "rifle_barrel", "name": "Rifle Barrel", "type": "intermediate", "max_stack": 5, "metadata": {"crafting_material": true, "tier": "advanced"}}, {"id": "steel_ingot", "name": "Steel Ingot", "type": "intermediate", "max_stack": 20, "metadata": {"crafting_material": true, "tier": "common"}}, {"id": "carbon", "name": "Carbon", "type": "material", "max_stack": 50, "metadata": {"crafting_material": true}}, {"id": "purified_extract", "name": "Purified Extract", "type": "intermediate", "max_stack": 10, "metadata": {"crafting_material": true, "tier": "advanced"}}, {"id": "stabilizer", "name": "Stabilizer", "type": "material", "max_stack": 25, "metadata": {"crafting_material": true}}, {"id": "glass_vial", "name": "Glass Vial", "type": "material", "max_stack": 30, "metadata": {"crafting_material": true}}, {"id": "medicinal_herbs", "name": "Medicinal Herbs", "type": "resource", "max_stack": 40, "metadata": {"resource_type": "organic", "tool_required": "none"}}, {"id": "distilled_water", "name": "Distilled Water", "type": "material", "max_stack": 20, "metadata": {"crafting_material": true}}, {"id": "plant_extract", "name": "Plant Extract", "type": "intermediate", "max_stack": 15, "metadata": {"crafting_material": true}}, {"id": "purification_agent", "name": "Purification Agent", "type": "material", "max_stack": 20, "metadata": {"crafting_material": true}}, {"id": "health_serum", "name": "Health Serum", "type": "consumable", "max_stack": 8, "metadata": {"health_restore": 100, "use_time": 0.5}}, {"id": "bullet_casing", "name": "Bullet Casing", "type": "material", "max_stack": 100, "metadata": {"crafting_material": true}}, {"id": "explosive_compound", "name": "Explosive Compound", "type": "intermediate", "max_stack": 20, "metadata": {"crafting_material": true, "tier": "advanced"}}, {"id": "primer", "name": "Primer", "type": "material", "max_stack": 100, "metadata": {"crafting_material": true}}, {"id": "explosive_ammo", "name": "Explosive Ammo", "type": "ammo", "max_stack": 50, "metadata": {"ammo_type": "explosive", "damage_bonus": 50}}, {"id": "chemical_catalyst", "name": "Chemical Catalyst", "type": "material", "max_stack": 15, "metadata": {"crafting_material": true, "tier": "advanced"}}, {"id": "energy_crystal", "name": "Energy Crystal", "type": "intermediate", "max_stack": 5, "metadata": {"crafting_material": true, "tier": "rare"}}, {"id": "conductive_wire", "name": "Conductive Wire", "type": "material", "max_stack": 50, "metadata": {"crafting_material": true}}, {"id": "insulation_material", "name": "Insulation Material", "type": "material", "max_stack": 30, "metadata": {"crafting_material": true}}, {"id": "metal_housing", "name": "Metal Housing", "type": "intermediate", "max_stack": 10, "metadata": {"crafting_material": true, "tier": "advanced"}}, {"id": "power_core", "name": "Power Core", "type": "component", "max_stack": 3, "metadata": {"crafting_material": true, "tier": "legendary"}}, {"id": "raw_crystal", "name": "Raw Crystal", "type": "resource", "max_stack": 15, "metadata": {"resource_type": "mineral", "tool_required": "pickaxe"}}, {"id": "crystal_polish", "name": "Crystal Polish", "type": "material", "max_stack": 20, "metadata": {"crafting_material": true}}, {"id": "titanium_alloy", "name": "Titanium Alloy", "type": "material", "max_stack": 15, "metadata": {"crafting_material": true, "tier": "rare"}}, {"id": "precision_tools", "name": "Precision Tools", "type": "tool", "max_stack": 1, "metadata": {"tool_type": "precision", "durability": 50, "max_durability": 50}}, {"id": "advanced_rifle", "name": "Advanced Rifle", "type": "weapon", "max_stack": 1, "metadata": {"damage": 55, "fire_rate": 0.08, "ammo_type": "rifle_ammo", "durability": 150, "max_durability": 150, "weapon_type": "automatic", "magazine_size": 40, "current_ammo": 0, "reload_time": 1.8, "range": 120}}, {"id": "armor_plates", "name": "Armor Plates", "type": "intermediate", "max_stack": 10, "metadata": {"crafting_material": true, "tier": "advanced"}}, {"id": "kevlar_fabric", "name": "<PERSON><PERSON><PERSON>", "type": "material", "max_stack": 20, "metadata": {"crafting_material": true, "tier": "advanced"}}, {"id": "padding_material", "name": "Padding Material", "type": "material", "max_stack": 40, "metadata": {"crafting_material": true}}, {"id": "armor_joints", "name": "Armor Joints", "type": "intermediate", "max_stack": 25, "metadata": {"crafting_material": true, "tier": "advanced"}}, {"id": "carbon_fiber", "name": "Carbon Fiber", "type": "material", "max_stack": 15, "metadata": {"crafting_material": true, "tier": "advanced"}}, {"id": "flexible_metal", "name": "Flexible Metal", "type": "material", "max_stack": 20, "metadata": {"crafting_material": true, "tier": "advanced"}}, {"id": "lubricant", "name": "Lubricant", "type": "material", "max_stack": 30, "metadata": {"crafting_material": true}}, {"id": "advanced_armor", "name": "Advanced Armor", "type": "armor", "max_stack": 1, "metadata": {"armor_value": 75, "durability": 200, "max_durability": 200, "armor_type": "body"}}]