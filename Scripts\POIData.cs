using Godot;
using System;
using System.Collections.Generic;

namespace SurvivalLooterShooter
{
    /// <summary>
    /// Enumeration of different POI types
    /// </summary>
    public enum POIType
    {
        AbandonedCabin,
        Cave,
        Ruins,
        AbandonedShack,
        MiningOutpost,
        AncientTemple,
        Oasis,
        IceCave,
        Shipwreck,
        Farmstead,
        AbandonedOutpost,
        UndergroundCave
    }

    /// <summary>
    /// Enumeration of room types within POIs
    /// </summary>
    public enum RoomType
    {
        MainRoom,
        LivingRoom,
        Kitchen,
        Bedroom,
        Storage,
        MainChamber,
        Tunnel,
        TreasureRoom,
        MainHall,
        Chamber,
        Vault,
        Office,
        Workshop,
        Sanctum
    }

    /// <summary>
    /// Enumeration of connection types between rooms
    /// </summary>
    public enum ConnectionType
    {
        Door,
        Passage,
        Stairs,
        Ladder,
        Hidden
    }

    /// <summary>
    /// Enumeration of loot tiers for rarity system
    /// </summary>
    public enum LootTier
    {
        Common,
        Uncommon,
        Rare,
        Epic,
        Legendary
    }

    /// <summary>
    /// Template data for POI generation
    /// </summary>
    [Serializable]
    public class POIData
    {
        public string Id { get; set; }
        public string Name { get; set; }
        public POIType Type { get; set; }
        public Vector2 InteriorSize { get; set; }
        public int MinRooms { get; set; }
        public int MaxRooms { get; set; }
        public string LootTableId { get; set; }
        public float ResetTime { get; set; } = 3600f; // 1 hour default
    }

    /// <summary>
    /// Represents a Point of Interest instance in the world
    /// </summary>
    [Serializable]
    public class PointOfInterest
    {
        public string Id { get; set; }
        public string Name { get; set; }
        public POIType Type { get; set; }
        public Vector2 WorldPosition { get; set; }
        public Vector2I ChunkCoords { get; set; }
        public POIData Template { get; set; }
        public POIInterior Interior { get; set; }
        public bool IsDiscovered { get; set; } = false;
        public double DiscoveryTime { get; set; } = 0;
        public double LastResetTime { get; set; } = 0;
    }

    /// <summary>
    /// Represents the interior layout of a POI
    /// </summary>
    [Serializable]
    public class POIInterior
    {
        public Vector2 Size { get; set; }
        public List<Room> Rooms { get; set; } = new();
        public List<RoomConnection> Connections { get; set; } = new();
    }

    /// <summary>
    /// Represents a room within a POI interior
    /// </summary>
    [Serializable]
    public class Room
    {
        public string Id { get; set; }
        public RoomType Type { get; set; }
        public Vector2 Position { get; set; }
        public Vector2 Size { get; set; }
        public List<LootSpawn> LootSpawns { get; set; } = new();
    }

    /// <summary>
    /// Represents a connection between two rooms
    /// </summary>
    [Serializable]
    public class RoomConnection
    {
        public string FromRoomId { get; set; }
        public string ToRoomId { get; set; }
        public ConnectionType Type { get; set; }
        public Vector2 Position { get; set; }
    }

    /// <summary>
    /// Represents a loot spawn point within a room
    /// </summary>
    [Serializable]
    public class LootSpawn
    {
        public string ItemId { get; set; }
        public int Quantity { get; set; }
        public Vector2 Position { get; set; }
        public bool IsLooted { get; set; } = false;
        public double LastLootTime { get; set; } = 0;
        public float RespawnTime { get; set; } = 3600f; // 1 hour default
    }

    /// <summary>
    /// Loot table for POI loot generation
    /// </summary>
    [Serializable]
    public class LootTable
    {
        public string Id { get; set; }
        public string Name { get; set; }
        public List<LootTableItem> Items { get; set; } = new();
    }

    /// <summary>
    /// Individual item in a loot table
    /// </summary>
    [Serializable]
    public class LootTableItem
    {
        public string ItemId { get; set; }
        public float Weight { get; set; }
        public LootTier Tier { get; set; }
        public int MinQuantity { get; set; }
        public int MaxQuantity { get; set; }
        public float RespawnTime { get; set; } = 3600f;
    }

    /// <summary>
    /// Save data for POI system
    /// </summary>
    [Serializable]
    public class POISaveData
    {
        public List<string> DiscoveredPOIs { get; set; } = new();
        public Dictionary<string, POIState> POIStates { get; set; } = new();
    }

    /// <summary>
    /// State data for individual POI
    /// </summary>
    [Serializable]
    public class POIState
    {
        public double LastResetTime { get; set; }
        public List<LootState> LootStates { get; set; } = new();
    }

    /// <summary>
    /// State data for individual loot spawn
    /// </summary>
    [Serializable]
    public class LootState
    {
        public bool IsLooted { get; set; }
        public double LastLootTime { get; set; }
    }
}