using Godot;
using System.Collections.Generic;

namespace SurvivalLooterShooter
{
    /// <summary>
    /// Tests for the crafting station system
    /// </summary>
    public partial class CraftingStationTests : Node
    {
        private CraftingStation _testStation;
        private Inventory _testInventory;
        private bool _testsCompleted = false;

        public override void _Ready()
        {
            Logger.LogInfo("CraftingStationTests", "Starting crafting station tests");
            
            // Wait a frame for systems to initialize
            GetTree().ProcessFrame += RunTests;
        }

        private void RunTests()
        {
            if (_testsCompleted) return;
            _testsCompleted = true;

            try
            {
                SetupTestEnvironment();
                TestBasicStationFunctionality();
                TestAdvancedRecipeSupport();
                TestCraftingQueue();
                TestStationUpgrade();
                TestMultiStepCrafting();
                
                Logger.LogInfo("CraftingStationTests", "All crafting station tests completed successfully");
            }
            catch (System.Exception ex)
            {
                Logger.LogError("CraftingStationTests", $"Test failed: {ex.Message}");
            }
        }

        private void SetupTestEnvironment()
        {
            // Create test inventory
            _testInventory = new Inventory();
            AddChild(_testInventory);
            
            // Add test materials
            _testInventory.AddItem("metal_scrap", 50);
            _testInventory.AddItem("advanced_components", 10);
            _testInventory.AddItem("steel_ingot", 20);
            _testInventory.AddItem("carbon", 15);
            _testInventory.AddItem("medicinal_herbs", 30);
            _testInventory.AddItem("distilled_water", 20);
            _testInventory.AddItem("purification_agent", 10);
            
            // Create test crafting station
            _testStation = new CraftingStation();
            AddChild(_testStation);
            _testStation.Initialize("test_workbench", "workbench", 1);
            
            Logger.LogInfo("CraftingStationTests", "Test environment set up");
        }

        private void TestBasicStationFunctionality()
        {
            Logger.LogInfo("CraftingStationTests", "Testing basic station functionality");
            
            // Test station properties
            Assert(_testStation.StationType == "workbench", "Station type should be workbench");
            Assert(_testStation.StationLevel == 1, "Station level should be 1");
            Assert(!_testStation.IsCrafting, "Station should not be crafting initially");
            Assert(_testStation.QueueSize == 0, "Queue should be empty initially");
            
            // Test available recipes
            var availableRecipes = _testStation.GetAvailableRecipes();
            Logger.LogInfo("CraftingStationTests", $"Found {availableRecipes.Count} available recipes");
            
            Logger.LogInfo("CraftingStationTests", "Basic station functionality test passed");
        }

        private void TestAdvancedRecipeSupport()
        {
            Logger.LogInfo("CraftingStationTests", "Testing advanced recipe support");
            
            // Create a test advanced recipe
            var advancedRecipe = new AdvancedRecipe
            {
                Id = "test_advanced_item",
                Inputs = new List<RecipeInput>
                {
                    new RecipeInput("metal_scrap", 5),
                    new RecipeInput("advanced_components", 2)
                },
                Output = new RecipeOutput("test_output", 1),
                CraftingTime = 10.0f,
                RequiredStation = "workbench",
                RequiredStationLevel = 1
            };
            
            // Test recipe validation
            Assert(_testStation.CanCraftRecipe(advancedRecipe), "Station should be able to craft advanced recipe");
            
            // Test with higher level requirement
            advancedRecipe.RequiredStationLevel = 3;
            Assert(!_testStation.CanCraftRecipe(advancedRecipe), "Station should not craft recipe requiring higher level");
            
            // Test with wrong station type
            advancedRecipe.RequiredStationLevel = 1;
            advancedRecipe.RequiredStation = "forge";
            Assert(!_testStation.CanCraftRecipe(advancedRecipe), "Station should not craft recipe for different station type");
            
            Logger.LogInfo("CraftingStationTests", "Advanced recipe support test passed");
        }

        private void TestCraftingQueue()
        {
            Logger.LogInfo("CraftingStationTests", "Testing crafting queue");
            
            // Create simple test recipe
            var testRecipe = new Recipe
            {
                Id = "test_simple_item",
                Inputs = new List<RecipeInput> { new RecipeInput("metal_scrap", 2) },
                Output = new RecipeOutput("test_simple_output", 1),
                CraftingTime = 1.0f
            };
            
            // Test queuing recipe
            bool queued = _testStation.QueueRecipe(testRecipe, 3, _testInventory);
            Assert(queued, "Should be able to queue recipe");
            Assert(_testStation.QueueSize == 1, "Queue size should be 1 after queuing");
            
            // Test queue limit
            for (int i = 0; i < 10; i++)
            {
                _testStation.QueueRecipe(testRecipe, 1, _testInventory);
            }
            Assert(_testStation.QueueSize <= _testStation.MaxQueueSize, "Queue should not exceed max size");
            
            // Test clearing queue
            _testStation.ClearQueue();
            Assert(_testStation.QueueSize == 0, "Queue should be empty after clearing");
            
            Logger.LogInfo("CraftingStationTests", "Crafting queue test passed");
        }

        private void TestStationUpgrade()
        {
            Logger.LogInfo("CraftingStationTests", "Testing station upgrade");
            
            int initialLevel = _testStation.StationLevel;
            float initialEfficiency = _testStation.EfficiencyMultiplier;
            int initialQueueSize = _testStation.MaxQueueSize;
            
            // Simulate upgrade (without structure dependency for test)
            _testStation.StationLevel = 2;
            
            // Test that properties updated
            Assert(_testStation.StationLevel == 2, "Station level should be upgraded");
            
            Logger.LogInfo("CraftingStationTests", "Station upgrade test passed");
        }

        private void TestMultiStepCrafting()
        {
            Logger.LogInfo("CraftingStationTests", "Testing multi-step crafting");
            
            // Create multi-step recipe
            var multiStepRecipe = new AdvancedRecipe
            {
                Id = "test_multistep",
                Inputs = new List<RecipeInput> { new RecipeInput("purified_extract", 1) },
                Output = new RecipeOutput("test_final_product", 1),
                CraftingTime = 5.0f,
                IntermediateSteps = new List<IntermediateStep>
                {
                    new IntermediateStep
                    {
                        StepName = "Extract Essence",
                        Inputs = new List<RecipeInput> { new RecipeInput("medicinal_herbs", 3) },
                        Output = new RecipeOutput("plant_extract", 1),
                        ProcessingTime = 3.0f
                    },
                    new IntermediateStep
                    {
                        StepName = "Purify Extract",
                        Inputs = new List<RecipeInput> 
                        { 
                            new RecipeInput("plant_extract", 1),
                            new RecipeInput("purification_agent", 1)
                        },
                        Output = new RecipeOutput("purified_extract", 1),
                        ProcessingTime = 4.0f
                    }
                }
            };
            
            // Test multi-step properties
            Assert(multiStepRecipe.IsMultiStep, "Recipe should be identified as multi-step");
            Assert(multiStepRecipe.TotalSteps == 3, "Recipe should have 3 total steps (2 intermediate + 1 final)");
            
            float totalTime = multiStepRecipe.GetTotalCraftingTime(1.0f);
            Assert(totalTime == 12.0f, $"Total crafting time should be 12.0f, got {totalTime}");
            
            // Test all required materials calculation
            var allMaterials = multiStepRecipe.GetAllRequiredMaterials();
            Logger.LogInfo("CraftingStationTests", $"Multi-step recipe requires {allMaterials.Count} different materials");
            
            Logger.LogInfo("CraftingStationTests", "Multi-step crafting test passed");
        }

        private void Assert(bool condition, string message)
        {
            if (!condition)
            {
                throw new System.Exception($"Assertion failed: {message}");
            }
        }
    }
}