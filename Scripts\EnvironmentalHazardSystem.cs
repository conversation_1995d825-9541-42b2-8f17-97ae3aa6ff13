using Godot;
using System;
using System.Collections.Generic;
using System.Linq;

namespace SurvivalLooterShooter
{
    /// <summary>
    /// Manages environmental hazards including storms, natural disasters, and extreme weather events
    /// Provides dynamic hazard spawning and effects on gameplay
    /// </summary>
    public partial class EnvironmentalHazardSystem : Node
    {
        private static EnvironmentalHazardSystem _instance;
        public static EnvironmentalHazardSystem Instance => _instance;

        // Active hazards
        private List<EnvironmentalHazard> _activeHazards = new List<EnvironmentalHazard>();
        
        // Hazard spawning
        private Timer _hazardSpawnTimer;
        private const float HAZARD_CHECK_INTERVAL = 60f; // Check for new hazards every minute
        
        // Hazard probabilities (per check)
        private readonly Dictionary<HazardType, float> _hazardProbabilities = new Dictionary<HazardType, float>
        {
            [HazardType.Lightning] = 0.05f,      // 5% chance during thunderstorms
            [HazardType.Tornado] = 0.001f,       // 0.1% chance during severe weather
            [HazardType.Hailstorm] = 0.02f,      // 2% chance during storms
            [HazardType.Wildfire] = 0.003f,      // 0.3% chance in hot, dry conditions
            [HazardType.Flood] = 0.01f,          // 1% chance during heavy rain
            [HazardType.Earthquake] = 0.0005f,   // 0.05% chance randomly
            [HazardType.Blizzard] = 0.03f,       // 3% chance in winter/cold biomes
            [HazardType.Sandstorm] = 0.04f       // 4% chance in desert biomes
        };

        // Events
        [Signal] public delegate void HazardStartedEventHandler(string hazardType, float severity, float duration);
        [Signal] public delegate void HazardEndedEventHandler(string hazardType);
        [Signal] public delegate void HazardDamageEventHandler(string hazardType, float damage);
        [Signal] public delegate void NaturalDisasterEventHandler(string disasterType, float magnitude);

        public enum HazardType
        {
            Lightning,
            Tornado,
            Hailstorm,
            Wildfire,
            Flood,
            Earthquake,
            Blizzard,
            Sandstorm
        }

        public override void _Ready()
        {
            // Singleton setup
            if (_instance == null)
            {
                _instance = this;
                AddToGroup("environmental_hazard_system");
                Logger.LogInfo("EnvironmentalHazardSystem", "EnvironmentalHazardSystem singleton initialized");
            }
            else
            {
                Logger.LogError("EnvironmentalHazardSystem", "Multiple EnvironmentalHazardSystem instances detected! Removing duplicate.");
                QueueFree();
                return;
            }

            SetupHazardSpawning();
            ConnectToWeatherSystem();
            
            Logger.LogInfo("EnvironmentalHazardSystem", "Environmental hazard system initialized");
        }

        public override void _Process(double delta)
        {
            UpdateActiveHazards((float)delta);
        }

        /// <summary>
        /// Sets up hazard spawning timer
        /// </summary>
        private void SetupHazardSpawning()
        {
            _hazardSpawnTimer = new Timer();
            AddChild(_hazardSpawnTimer);
            _hazardSpawnTimer.WaitTime = HAZARD_CHECK_INTERVAL;
            _hazardSpawnTimer.Autostart = true;
            _hazardSpawnTimer.Timeout += CheckForNewHazards;
        }

        /// <summary>
        /// Connects to weather system for hazard triggering
        /// </summary>
        private void ConnectToWeatherSystem()
        {
            if (WeatherManager.Instance != null)
            {
                WeatherManager.Instance.WeatherChanged += OnWeatherChanged;
            }
        }

        private void OnWeatherChanged(WeatherManager.WeatherType oldWeather, WeatherManager.WeatherType newWeather, float intensity)
        {
            throw new NotImplementedException();
        }

        /// <summary>
        /// Updates all active hazards
        /// </summary>
        private void UpdateActiveHazards(float delta)
        {
            for (int i = _activeHazards.Count - 1; i >= 0; i--)
            {
                var hazard = _activeHazards[i];
                hazard.Update(delta);
                
                if (hazard.IsExpired)
                {
                    EndHazard(hazard);
                    _activeHazards.RemoveAt(i);
                }
            }
        }

        /// <summary>
        /// Checks for new hazards based on current conditions
        /// </summary>
        private void CheckForNewHazards()
        {
            if (WeatherManager.Instance == null) return;

            var currentWeather = WeatherManager.Instance.CurrentWeather;
            var currentSeason = WeatherManager.Instance.CurrentSeason;
            var weatherIntensity = WeatherManager.Instance.WeatherIntensity;

            // Check each hazard type based on current conditions
            foreach (var hazardType in Enum.GetValues<HazardType>())
            {
                if (ShouldSpawnHazard(hazardType, currentWeather, currentSeason, weatherIntensity))
                {
                    SpawnHazard(hazardType, weatherIntensity);
                }
            }
        }

        /// <summary>
        /// Determines if a hazard should spawn based on current conditions
        /// </summary>
        private bool ShouldSpawnHazard(HazardType hazardType, WeatherManager.WeatherType weather, 
            WeatherManager.Season season, float intensity)
        {
            // Don't spawn if hazard is already active
            if (_activeHazards.Any(h => h.Type == hazardType))
                return false;

            float baseChance = _hazardProbabilities[hazardType];
            float modifiedChance = baseChance;

            // Modify chance based on weather conditions
            switch (hazardType)
            {
                case HazardType.Lightning:
                    if (weather == WeatherManager.WeatherType.Thunderstorm)
                        modifiedChance *= 10f * intensity;
                    else
                        modifiedChance = 0f;
                    break;

                case HazardType.Tornado:
                    if (weather == WeatherManager.WeatherType.Thunderstorm && intensity > 0.8f)
                        modifiedChance *= 5f;
                    else
                        modifiedChance = 0f;
                    break;

                case HazardType.Hailstorm:
                    if (weather == WeatherManager.WeatherType.Hail || 
                        (weather == WeatherManager.WeatherType.Thunderstorm && intensity > 0.6f))
                        modifiedChance *= 8f * intensity;
                    else
                        modifiedChance = 0f;
                    break;

                case HazardType.Wildfire:
                    if (weather == WeatherManager.WeatherType.Clear && season == WeatherManager.Season.Summer)
                        modifiedChance *= 3f;
                    else if (weather == WeatherManager.WeatherType.LightRain || 
                             weather == WeatherManager.WeatherType.HeavyRain)
                        modifiedChance = 0f;
                    break;

                case HazardType.Flood:
                    if (weather == WeatherManager.WeatherType.HeavyRain && intensity > 0.7f)
                        modifiedChance *= 6f * intensity;
                    else
                        modifiedChance = 0f;
                    break;

                case HazardType.Blizzard:
                    if (weather == WeatherManager.WeatherType.Blizzard || 
                        (weather == WeatherManager.WeatherType.Snow && season == WeatherManager.Season.Winter))
                        modifiedChance *= 4f * intensity;
                    else
                        modifiedChance = 0f;
                    break;

                case HazardType.Sandstorm:
                    if (weather == WeatherManager.WeatherType.Sandstorm)
                        modifiedChance *= 6f * intensity;
                    else
                        modifiedChance = 0f;
                    break;

                case HazardType.Earthquake:
                    // Earthquakes are random and not weather-dependent
                    break;
            }

            return GD.Randf() < modifiedChance;
        }

        /// <summary>
        /// Spawns a new environmental hazard
        /// </summary>
        private void SpawnHazard(HazardType hazardType, float weatherIntensity)
        {
            var hazard = CreateHazard(hazardType, weatherIntensity);
            _activeHazards.Add(hazard);
            
            EmitSignal(SignalName.HazardStarted, hazardType.ToString(), hazard.Severity, hazard.Duration);
            EventBus.Instance?.EmitHazardStarted(hazardType.ToString(), hazard.Severity, hazard.Duration);
            
            // Check if this is a major disaster
            if (hazard.Severity >= 0.8f)
            {
                EmitSignal(SignalName.NaturalDisaster, hazardType.ToString(), hazard.Severity);
                EventBus.Instance?.EmitNaturalDisaster(hazardType.ToString(), hazard.Severity);
            }
            
            Logger.LogInfo("EnvironmentalHazardSystem", 
                $"Spawned {hazardType} hazard - Severity: {hazard.Severity:F2}, Duration: {hazard.Duration:F1}s");
        }

        /// <summary>
        /// Creates a hazard instance based on type and conditions
        /// </summary>
        private EnvironmentalHazard CreateHazard(HazardType hazardType, float weatherIntensity)
        {
            return hazardType switch
            {
                HazardType.Lightning => new LightningHazard(weatherIntensity),
                HazardType.Tornado => new TornadoHazard(weatherIntensity),
                HazardType.Hailstorm => new HailstormHazard(weatherIntensity),
                HazardType.Wildfire => new WildfireHazard(weatherIntensity),
                HazardType.Flood => new FloodHazard(weatherIntensity),
                HazardType.Earthquake => new EarthquakeHazard(),
                HazardType.Blizzard => new BlizzardHazard(weatherIntensity),
                HazardType.Sandstorm => new SandstormHazard(weatherIntensity),
                _ => new EnvironmentalHazard(hazardType, 0.5f, 60f)
            };
        }

        /// <summary>
        /// Ends a hazard and cleans up its effects
        /// </summary>
        private void EndHazard(EnvironmentalHazard hazard)
        {
            EmitSignal(SignalName.HazardEnded, hazard.Type.ToString());
            EventBus.Instance?.EmitHazardEnded(hazard.Type.ToString());
            
            Logger.LogInfo("EnvironmentalHazardSystem", $"{hazard.Type} hazard ended");
        }

        /// <summary>
        /// Handles weather changes that might trigger hazards
        /// </summary>
        private void OnWeatherChanged(int oldWeather, int newWeather, float intensity)
        {
            // Immediately check for hazards when weather changes to severe conditions
            var weather = (WeatherManager.WeatherType)newWeather;
            
            if (weather == WeatherManager.WeatherType.Thunderstorm && intensity > 0.7f)
            {
                // High chance of immediate lightning during severe thunderstorms
                if (GD.Randf() < 0.3f)
                {
                    SpawnHazard(HazardType.Lightning, intensity);
                }
            }
        }

        #region Public API

        /// <summary>
        /// Gets all currently active hazards
        /// </summary>
        public List<EnvironmentalHazard> GetActiveHazards()
        {
            return new List<EnvironmentalHazard>(_activeHazards);
        }

        /// <summary>
        /// Checks if a specific hazard type is currently active
        /// </summary>
        public bool IsHazardActive(HazardType hazardType)
        {
            return _activeHazards.Any(h => h.Type == hazardType);
        }

        /// <summary>
        /// Forces a specific hazard to spawn (for testing or scripted events)
        /// </summary>
        public void ForceSpawnHazard(HazardType hazardType, float severity = 0.5f)
        {
            var hazard = CreateHazard(hazardType, severity);
            hazard.Severity = severity; // Override with specified severity
            _activeHazards.Add(hazard);
            
            EmitSignal(SignalName.HazardStarted, hazardType.ToString(), hazard.Severity, hazard.Duration);
            EventBus.Instance?.EmitHazardStarted(hazardType.ToString(), hazard.Severity, hazard.Duration);
            
            Logger.LogInfo("EnvironmentalHazardSystem", $"Force spawned {hazardType} hazard");
        }

        /// <summary>
        /// Ends all active hazards (for testing or emergency situations)
        /// </summary>
        public void EndAllHazards()
        {
            foreach (var hazard in _activeHazards.ToList())
            {
                EndHazard(hazard);
            }
            _activeHazards.Clear();
            
            Logger.LogInfo("EnvironmentalHazardSystem", "All hazards ended");
        }

        #endregion

        public override void _ExitTree()
        {
            if (_instance == this)
            {
                _instance = null;
            }
        }
    }

    /// <summary>
    /// Base class for environmental hazards
    /// </summary>
    public class EnvironmentalHazard
    {
        public EnvironmentalHazardSystem.HazardType Type { get; protected set; }
        public float Severity { get; set; } // 0.0 to 1.0
        public float Duration { get; protected set; } // Duration in seconds
        public float TimeRemaining { get; protected set; }
        public bool IsExpired => TimeRemaining <= 0f;

        protected float _effectTimer;
        protected float _effectInterval = 5f; // Apply effects every 5 seconds

        public EnvironmentalHazard(EnvironmentalHazardSystem.HazardType type, float severity, float duration)
        {
            Type = type;
            Severity = Mathf.Clamp(severity, 0f, 1f);
            Duration = duration;
            TimeRemaining = duration;
            _effectTimer = _effectInterval; // Initialize effect timer
        }

        public virtual void Update(float delta)
        {
            TimeRemaining -= delta;
            
            // Apply periodic effects
            _effectTimer -= delta;
            if (_effectTimer <= 0f)
            {
                ApplyEffects();
                _effectTimer = _effectInterval;
            }
        }

        protected virtual void ApplyEffects()
        {
            // Base implementation - override in derived classes
        }
    }

    /// <summary>
    /// Lightning hazard - causes direct damage and fire risk
    /// </summary>
    public class LightningHazard : EnvironmentalHazard
    {
        public LightningHazard(float intensity) : base(EnvironmentalHazardSystem.HazardType.Lightning, intensity, 30f)
        {
            _effectInterval = 3f; // Lightning strikes every 3 seconds
        }

        protected override void ApplyEffects()
        {
            if (SurvivalStatsSystem.Instance != null && GD.Randf() < 0.1f * Severity)
            {
                // 10% chance per effect cycle to be struck by lightning
                float damage = 20f * Severity;
                SurvivalStatsSystem.Instance.Health.ModifyValue(-damage);
                
                EnvironmentalHazardSystem.Instance?.EmitSignal(
                    EnvironmentalHazardSystem.SignalName.HazardDamage, "lightning", damage);
                EventBus.Instance?.EmitHazardDamage("lightning", damage);
                
                Logger.LogInfo("LightningHazard", $"Lightning strike! Damage: {damage:F1}");
            }
        }
    }

    /// <summary>
    /// Tornado hazard - extreme damage and movement disruption
    /// </summary>
    public class TornadoHazard : EnvironmentalHazard
    {
        public TornadoHazard(float intensity) : base(EnvironmentalHazardSystem.HazardType.Tornado, intensity, 120f)
        {
            _effectInterval = 2f;
        }

        protected override void ApplyEffects()
        {
            if (SurvivalStatsSystem.Instance != null)
            {
                // Continuous damage and stamina drain
                float damage = 5f * Severity;
                float staminaDrain = 15f * Severity;
                
                SurvivalStatsSystem.Instance.Health.ModifyValue(-damage);
                SurvivalStatsSystem.Instance.Stamina.ModifyValue(-staminaDrain);
                
                EnvironmentalHazardSystem.Instance?.EmitSignal(
                    EnvironmentalHazardSystem.SignalName.HazardDamage, "tornado", damage);
                EventBus.Instance?.EmitHazardDamage("tornado", damage);
            }
        }
    }

    /// <summary>
    /// Hailstorm hazard - periodic damage and movement impairment
    /// </summary>
    public class HailstormHazard : EnvironmentalHazard
    {
        public HailstormHazard(float intensity) : base(EnvironmentalHazardSystem.HazardType.Hailstorm, intensity, 180f)
        {
            _effectInterval = 4f;
        }

        protected override void ApplyEffects()
        {
            if (SurvivalStatsSystem.Instance != null)
            {
                float damage = 3f * Severity;
                SurvivalStatsSystem.Instance.Health.ModifyValue(-damage);
                
                EnvironmentalHazardSystem.Instance?.EmitSignal(
                    EnvironmentalHazardSystem.SignalName.HazardDamage, "hail", damage);
                EventBus.Instance?.EmitHazardDamage("hail", damage);
            }
        }
    }

    /// <summary>
    /// Wildfire hazard - heat damage and smoke inhalation
    /// </summary>
    public class WildfireHazard : EnvironmentalHazard
    {
        public WildfireHazard(float intensity) : base(EnvironmentalHazardSystem.HazardType.Wildfire, intensity, 300f)
        {
            _effectInterval = 3f;
        }

        protected override void ApplyEffects()
        {
            if (SurvivalStatsSystem.Instance != null)
            {
                float damage = 4f * Severity;
                float thirstDrain = 8f * Severity;
                
                SurvivalStatsSystem.Instance.Health.ModifyValue(-damage);
                SurvivalStatsSystem.Instance.Thirst.ModifyValue(-thirstDrain);
                
                EnvironmentalHazardSystem.Instance?.EmitSignal(
                    EnvironmentalHazardSystem.SignalName.HazardDamage, "wildfire", damage);
                EventBus.Instance?.EmitHazardDamage("wildfire", damage);
            }
        }
    }

    /// <summary>
    /// Flood hazard - movement impairment and hypothermia risk
    /// </summary>
    public class FloodHazard : EnvironmentalHazard
    {
        public FloodHazard(float intensity) : base(EnvironmentalHazardSystem.HazardType.Flood, intensity, 240f)
        {
            _effectInterval = 6f;
        }

        protected override void ApplyEffects()
        {
            if (SurvivalStatsSystem.Instance != null)
            {
                float damage = 2f * Severity;
                float staminaDrain = 10f * Severity;
                
                SurvivalStatsSystem.Instance.Health.ModifyValue(-damage);
                SurvivalStatsSystem.Instance.Stamina.ModifyValue(-staminaDrain);
                
                EnvironmentalHazardSystem.Instance?.EmitSignal(
                    EnvironmentalHazardSystem.SignalName.HazardDamage, "flood", damage);
                EventBus.Instance?.EmitHazardDamage("flood", damage);
            }
        }
    }

    /// <summary>
    /// Earthquake hazard - structural damage and injury risk
    /// </summary>
    public class EarthquakeHazard : EnvironmentalHazard
    {
        public EarthquakeHazard() : base(EnvironmentalHazardSystem.HazardType.Earthquake, GD.Randf() * 0.5f + 0.3f, 45f)
        {
            _effectInterval = 8f;
        }

        protected override void ApplyEffects()
        {
            if (SurvivalStatsSystem.Instance != null && GD.Randf() < 0.2f * Severity)
            {
                float damage = 15f * Severity;
                SurvivalStatsSystem.Instance.Health.ModifyValue(-damage);
                
                EnvironmentalHazardSystem.Instance?.EmitSignal(
                    EnvironmentalHazardSystem.SignalName.HazardDamage, "earthquake", damage);
                EventBus.Instance?.EmitHazardDamage("earthquake", damage);
            }
        }
    }

    /// <summary>
    /// Blizzard hazard - extreme cold and visibility loss
    /// </summary>
    public class BlizzardHazard : EnvironmentalHazard
    {
        public BlizzardHazard(float intensity) : base(EnvironmentalHazardSystem.HazardType.Blizzard, intensity, 360f)
        {
            _effectInterval = 5f;
        }

        protected override void ApplyEffects()
        {
            if (SurvivalStatsSystem.Instance != null)
            {
                float damage = 3f * Severity;
                float hungerDrain = 6f * Severity;
                float staminaDrain = 8f * Severity;
                
                SurvivalStatsSystem.Instance.Health.ModifyValue(-damage);
                SurvivalStatsSystem.Instance.Hunger.ModifyValue(-hungerDrain);
                SurvivalStatsSystem.Instance.Stamina.ModifyValue(-staminaDrain);
                
                EnvironmentalHazardSystem.Instance?.EmitSignal(
                    EnvironmentalHazardSystem.SignalName.HazardDamage, "blizzard", damage);
                EventBus.Instance?.EmitHazardDamage("blizzard", damage);
            }
        }
    }

    /// <summary>
    /// Sandstorm hazard - visibility loss and dehydration
    /// </summary>
    public class SandstormHazard : EnvironmentalHazard
    {
        public SandstormHazard(float intensity) : base(EnvironmentalHazardSystem.HazardType.Sandstorm, intensity, 200f)
        {
            _effectInterval = 4f;
        }

        protected override void ApplyEffects()
        {
            if (SurvivalStatsSystem.Instance != null)
            {
                float damage = 2f * Severity;
                float thirstDrain = 10f * Severity;
                
                SurvivalStatsSystem.Instance.Health.ModifyValue(-damage);
                SurvivalStatsSystem.Instance.Thirst.ModifyValue(-thirstDrain);
                
                EnvironmentalHazardSystem.Instance?.EmitSignal(
                    EnvironmentalHazardSystem.SignalName.HazardDamage, "sandstorm", damage);
                EventBus.Instance?.EmitHazardDamage("sandstorm", damage);
            }
        }
    }
}