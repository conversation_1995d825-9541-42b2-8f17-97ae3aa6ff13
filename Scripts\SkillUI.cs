using Godot;
using System.Collections.Generic;
using System.Linq;

namespace SurvivalLooterShooter
{
    public partial class SkillUI : Control
{
    [Export] private TabContainer _skillTabs;
    [Export] private Label _skillPointsLabel;
    [Export] private Button _closeButton;
    
    private Dictionary<SkillType, VBoxContainer> _skillContainers = new Dictionary<SkillType, VBoxContainer>();
    private Dictionary<string, SkillButton> _skillButtons = new Dictionary<string, SkillButton>();
    
    public override void _Ready()
    {
        SetupUI();
        ConnectSignals();
        RefreshUI();
        
        // Hide by default
        Visible = false;
    }
    
    private void SetupUI()
    {
        if (_skillTabs == null)
        {
            _skillTabs = GetNode<TabContainer>("VBox/SkillTabs");
        }
        
        if (_skillPointsLabel == null)
        {
            _skillPointsLabel = GetNode<Label>("VBox/Header/SkillPointsLabel");
        }
        
        if (_closeButton == null)
        {
            _closeButton = GetNode<Button>("VBox/Header/CloseButton");
        }
        
        CreateSkillTabs();
    }
    
    private void CreateSkillTabs()
    {
        foreach (SkillType category in System.Enum.GetValues<SkillType>())
        {
            // Create tab
            var scrollContainer = new ScrollContainer();
            scrollContainer.Name = category.ToString();
            
            var vbox = new VBoxContainer();
            vbox.Name = "SkillList";
            scrollContainer.AddChild(vbox);
            
            _skillTabs.AddChild(scrollContainer);
            _skillContainers[category] = vbox;
            
            // Set tab title
            _skillTabs.SetTabTitle(_skillTabs.GetTabCount() - 1, category.ToString());
        }
    }
    
    private void ConnectSignals()
    {
        if (_closeButton != null)
        {
            _closeButton.Pressed += OnClosePressed;
        }
        
        if (SkillManager.Instance != null)
        {
            SkillManager.Instance.SkillPointsChanged += OnSkillPointsChanged;
            SkillManager.Instance.SkillUnlocked += OnSkillUnlocked;
        }
        
        if (EventBus.Instance != null)
        {
            EventBus.Instance.SkillLevelUp += OnSkillLevelUp;
        }
    }
    
    private void RefreshUI()
    {
        UpdateSkillPointsDisplay();
        PopulateSkillTrees();
    }
    
    private void UpdateSkillPointsDisplay()
    {
        if (_skillPointsLabel != null && SkillManager.Instance != null)
        {
            int availablePoints = SkillManager.Instance.GetAvailableSkillPoints();
            _skillPointsLabel.Text = $"Skill Points: {availablePoints}";
        }
    }
    
    private void PopulateSkillTrees()
    {
        if (SkillManager.Instance == null) return;
        
        foreach (SkillType category in System.Enum.GetValues<SkillType>())
        {
            if (!_skillContainers.ContainsKey(category)) continue;
            
            var container = _skillContainers[category];
            var skills = SkillManager.Instance.GetSkillsByCategory(category);
            
            // Clear existing buttons
            foreach (Node child in container.GetChildren())
            {
                child.QueueFree();
            }
            
            // Create skill buttons
            foreach (var skill in skills)
            {
                var skillButton = CreateSkillButton(skill);
                container.AddChild(skillButton);
                _skillButtons[skill.Id] = skillButton;
            }
        }
    }
    
    private SkillButton CreateSkillButton(Skill skill)
    {
        var skillButton = new SkillButton();
        skillButton.SetSkill(skill);
        skillButton.SkillUnlockRequested += OnSkillUnlockRequested;
        return skillButton;
    }
    
    private void OnSkillUnlockRequested(string skillId)
    {
        if (SkillManager.Instance != null)
        {
            bool success = SkillManager.Instance.UnlockSkill(skillId);
            if (!success)
            {
                // Show error message
                EventBus.Instance?.EmitNotificationRequested("Cannot unlock skill: insufficient points or prerequisites not met", "error");
            }
        }
    }
    
    private void OnSkillPointsChanged(int newAmount)
    {
        UpdateSkillPointsDisplay();
        
        // Update all skill buttons to reflect availability
        foreach (var button in _skillButtons.Values)
        {
            button.UpdateAvailability();
        }
    }
    
    private void OnSkillUnlocked(string skillId)
    {
        if (_skillButtons.ContainsKey(skillId))
        {
            _skillButtons[skillId].UpdateDisplay();
        }
        
        EventBus.Instance?.EmitNotificationRequested($"Skill unlocked: {skillId}", "success");
    }
    
    private void OnSkillLevelUp(string skillId, int newLevel)
    {
        if (_skillButtons.ContainsKey(skillId))
        {
            _skillButtons[skillId].UpdateDisplay();
        }
        
        var skill = SkillManager.Instance?.GetSkill(skillId);
        if (skill != null)
        {
            EventBus.Instance?.EmitNotificationRequested($"{skill.Name} reached level {newLevel}!", "success");
        }
    }
    
    private void OnClosePressed()
    {
        Visible = false;
        EventBus.Instance?.EmitUIPanelToggled("SkillUI", false);
    }
    
    public void ToggleVisibility()
    {
        Visible = !Visible;
        if (Visible)
        {
            RefreshUI();
        }
        EventBus.Instance?.EmitUIPanelToggled("SkillUI", Visible);
    }
    
    public override void _Input(InputEvent @event)
    {
        if (@event.IsActionPressed("toggle_skills"))
        {
            ToggleVisibility();
        }
    }
}

public partial class SkillButton : Control
{
    [Signal]
    public delegate void SkillUnlockRequestedEventHandler(string skillId);
    
    private Skill _skill;
    private Button _unlockButton;
    private Label _nameLabel;
    private Label _descriptionLabel;
    private Label _levelLabel;
    private ProgressBar _experienceBar;
    private VBoxContainer _bonusesContainer;
    
    public override void _Ready()
    {
        SetupUI();
    }
    
    private void SetupUI()
    {
        // Create UI elements
        var vbox = new VBoxContainer();
        AddChild(vbox);
        
        // Header with name and level
        var header = new HBoxContainer();
        vbox.AddChild(header);
        
        _nameLabel = new Label();
        _nameLabel.AddThemeStyleboxOverride("normal", new StyleBoxFlat());
        header.AddChild(_nameLabel);
        
        _levelLabel = new Label();
        _levelLabel.HorizontalAlignment = HorizontalAlignment.Right;
        header.AddChild(_levelLabel);
        
        // Description
        _descriptionLabel = new Label();
        _descriptionLabel.AutowrapMode = TextServer.AutowrapMode.WordSmart;
        vbox.AddChild(_descriptionLabel);
        
        // Experience bar
        _experienceBar = new ProgressBar();
        _experienceBar.ShowPercentage = false;
        vbox.AddChild(_experienceBar);
        
        // Bonuses container
        _bonusesContainer = new VBoxContainer();
        vbox.AddChild(_bonusesContainer);
        
        // Unlock button
        _unlockButton = new Button();
        _unlockButton.Text = "Unlock";
        _unlockButton.Pressed += OnUnlockPressed;
        vbox.AddChild(_unlockButton);
        
        // Styling
        var styleBox = new StyleBoxFlat();
        styleBox.BgColor = new Color(0.2f, 0.2f, 0.3f, 0.8f);
        styleBox.BorderColor = new Color(0.4f, 0.4f, 0.5f);
        styleBox.BorderWidthTop = 1;
        styleBox.BorderWidthBottom = 1;
        styleBox.BorderWidthLeft = 1;
        styleBox.BorderWidthRight = 1;
        styleBox.CornerRadiusTopLeft = 4;
        styleBox.CornerRadiusTopRight = 4;
        styleBox.CornerRadiusBottomLeft = 4;
        styleBox.CornerRadiusBottomRight = 4;
        
        AddThemeStyleboxOverride("panel", styleBox);
    }
    
    public void SetSkill(Skill skill)
    {
        _skill = skill;
        UpdateDisplay();
    }
    
    public void UpdateDisplay()
    {
        if (_skill == null) return;
        
        _nameLabel.Text = _skill.Name;
        _descriptionLabel.Text = _skill.Description;
        _levelLabel.Text = $"Level {_skill.Level}/{_skill.MaxLevel}";
        
        // Update experience bar
        if (_skill.Level < _skill.MaxLevel)
        {
            float currentLevelXP = _skill.Experience;
            float requiredXP = _skill.GetTotalExperienceRequired(_skill.Level + 1);
            float previousLevelXP = _skill.Level > 0 ? _skill.GetTotalExperienceRequired(_skill.Level) : 0f;
            
            _experienceBar.Value = (currentLevelXP - previousLevelXP) / (requiredXP - previousLevelXP) * 100f;
            _experienceBar.Visible = true;
        }
        else
        {
            _experienceBar.Visible = false;
        }
        
        // Update bonuses display
        UpdateBonusesDisplay();
        
        // Update unlock button
        UpdateAvailability();
    }
    
    public void UpdateAvailability()
    {
        if (_skill == null || SkillManager.Instance == null) return;
        
        bool canUnlock = _skill.Level == 0 && 
                        SkillManager.Instance.GetAvailableSkillPoints() > 0 &&
                        ArePrerequisitesMet();
        
        _unlockButton.Visible = _skill.Level == 0;
        _unlockButton.Disabled = !canUnlock;
        
        if (!canUnlock && _skill.Level == 0)
        {
            if (SkillManager.Instance.GetAvailableSkillPoints() <= 0)
            {
                _unlockButton.Text = "No Skill Points";
            }
            else if (!ArePrerequisitesMet())
            {
                _unlockButton.Text = "Prerequisites Not Met";
            }
        }
        else
        {
            _unlockButton.Text = "Unlock";
        }
    }
    
    private bool ArePrerequisitesMet()
    {
        if (_skill?.Prerequisites == null || SkillManager.Instance == null) return true;
        
        foreach (var prereq in _skill.Prerequisites)
        {
            var prereqSkill = SkillManager.Instance.GetSkill(prereq);
            if (prereqSkill == null || prereqSkill.Level == 0)
            {
                return false;
            }
        }
        
        return true;
    }
    
    private void UpdateBonusesDisplay()
    {
        // Clear existing bonuses
        foreach (Node child in _bonusesContainer.GetChildren())
        {
            child.QueueFree();
        }
        
        if (_skill?.PassiveBonuses == null || _skill.Level == 0) return;
        
        foreach (var bonus in _skill.PassiveBonuses)
        {
            var bonusLabel = new Label();
            float bonusValue = _skill.GetBonusValue(bonus.Key);
            bonusLabel.Text = $"{bonus.Key}: +{bonusValue:F1}";
            bonusLabel.AddThemeColorOverride("font_color", new Color(0.7f, 1f, 0.7f));
            _bonusesContainer.AddChild(bonusLabel);
        }
    }
    
    private void OnUnlockPressed()
    {
        if (_skill != null)
        {
            EmitSignal(SignalName.SkillUnlockRequested, _skill.Id);
        }
    }
}
}