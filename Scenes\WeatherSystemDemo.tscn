[gd_scene load_steps=4 format=3 uid="uid://bqxvn8ywqxqxr"]

[ext_resource type="Script" path="res://Scripts/WeatherUI.cs" id="1_weather_ui"]
[ext_resource type="Script" path="res://Scripts/Logger.cs" id="2_logger"]
[ext_resource type="Script" path="res://Scripts/DayNightCycle.cs" id="3_day_night"]

[node name="WeatherSystemDemo" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
script = ExtResource("res://Scripts/WeatherSystemDemo.cs")

[node name="Logger" type="Node" parent="."]
script = ExtResource("2_logger")

[node name="DayNightCycle" type="Node" parent="."]
script = ExtResource("3_day_night")

[node name="WeatherUI" type="Control" parent="."]
layout_mode = 1
anchors_preset = 2
anchor_top = 1.0
anchor_bottom = 1.0
offset_left = 20.0
offset_top = -200.0
offset_right = 300.0
offset_bottom = -20.0
script = ExtResource("1_weather_ui")

[node name="TestControls" type="VBoxContainer" parent="."]
layout_mode = 1
anchors_preset = 1
anchor_left = 1.0
anchor_right = 1.0
offset_left = -200.0
offset_top = 20.0
offset_right = -20.0
offset_bottom = 300.0

[node name="Label" type="Label" parent="TestControls"]
layout_mode = 2
text = "Weather System Demo"

[node name="ClearWeatherButton" type="Button" parent="TestControls"]
layout_mode = 2
text = "Set Clear Weather"

[node name="RainButton" type="Button" parent="TestControls"]
layout_mode = 2
text = "Set Heavy Rain"

[node name="SnowButton" type="Button" parent="TestControls"]
layout_mode = 2
text = "Set Snow"

[node name="SandstormButton" type="Button" parent="TestControls"]
layout_mode = 2
text = "Set Sandstorm"

[node name="DesertBiomeButton" type="Button" parent="TestControls"]
layout_mode = 2
text = "Set Desert Biome"

[node name="TundraBiomeButton" type="Button" parent="TestControls"]
layout_mode = 2
text = "Set Tundra Biome"