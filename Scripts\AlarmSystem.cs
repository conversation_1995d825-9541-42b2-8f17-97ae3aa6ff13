using Godot;
using System;
using System.Collections.Generic;

namespace SurvivalLooterShooter
{
    /// <summary>
    /// Alarm system for base security and threat detection
    /// Provides audio and visual alerts for various threat levels
    /// </summary>
    public partial class AlarmSystem : Node2D
    {
        [Export] public float DetectionRadius { get; set; } = 150f;
        [Export] public float AlarmDuration { get; set; } = 30f;
        [Export] public bool AutoActivate { get; set; } = true;

        private Structure _parentStructure;
        private Area2D _detectionArea;
        private AudioStreamPlayer2D _alarmSound;
        private Timer _alarmTimer;
        private Timer _flashTimer;
        private Sprite2D _alarmSprite;
        
        // Alarm state
        private bool _isActive = false;
        private string _currentAlarmType = "";
        private readonly HashSet<Enemy> _detectedEnemies = [];
        private float _threatLevel = 0f;

        // Visual effects
        private Color _normalColor = Colors.Green;
        private Color _warningColor = Colors.Yellow;
        private Color _dangerColor = Colors.Red;
        private Color _raidColor = Colors.Purple;

        public bool IsActive => _isActive;
        public string CurrentAlarmType => _currentAlarmType;
        public float CurrentThreatLevel => _threatLevel;
        public int DetectedEnemyCount => _detectedEnemies.Count;

        // Events
        [Signal] public delegate void AlarmTriggeredEventHandler(string alarmType, Vector2 position);
        [Signal] public delegate void AlarmDeactivatedEventHandler(Vector2 position);
        [Signal] public delegate void ThreatLevelChangedEventHandler(float oldLevel, float newLevel);
        [Signal] public delegate void EnemyDetectedByAlarmEventHandler(Enemy enemy, AlarmSystem alarm);

        public override void _Ready()
        {
            SetupComponents();
            SetupTimers();
            
            // Register with defense system
            DefenseSystem.Instance?.RegisterAlarmSystem(this);
            
            Logger.LogInfo("AlarmSystem", $"Alarm system initialized with {DetectionRadius} detection radius");
        }

        /// <summary>
        /// Sets up alarm components and detection area
        /// </summary>
        private void SetupComponents()
        {
            // Create alarm sprite
            _alarmSprite = new Sprite2D();
            _alarmSprite.Name = "AlarmSprite";
            _alarmSprite.Texture = CreateAlarmTexture();
            _alarmSprite.Modulate = _normalColor;
            AddChild(_alarmSprite);

            // Note: Light2D removed due to compatibility issues

            // Create detection area
            _detectionArea = new Area2D();
            _detectionArea.Name = "DetectionArea";
            var detectionShape = new CollisionShape2D();
            var circleShape = new CircleShape2D();
            circleShape.Radius = DetectionRadius;
            detectionShape.Shape = circleShape;
            _detectionArea.AddChild(detectionShape);
            AddChild(_detectionArea);

            // Connect detection signals
            _detectionArea.BodyEntered += OnEnemyEntered;
            _detectionArea.BodyExited += OnEnemyExited;

            // Create audio player
            _alarmSound = new AudioStreamPlayer2D();
            _alarmSound.Name = "AlarmSound";
            _alarmSound.MaxDistance = 500f;
            _alarmSound.Attenuation = 2.0f;
            AddChild(_alarmSound);

            // Try to load alarm sound
            if (ResourceLoader.Exists("res://Assets/Audio/alarm.ogg"))
            {
                _alarmSound.Stream = GD.Load<AudioStream>("res://Assets/Audio/alarm.ogg");
            }
        }

        /// <summary>
        /// Sets up timers for alarm duration and flashing effects
        /// </summary>
        private void SetupTimers()
        {
            // Alarm duration timer
            _alarmTimer = new Timer();
            _alarmTimer.Name = "AlarmTimer";
            _alarmTimer.WaitTime = AlarmDuration;
            _alarmTimer.OneShot = true;
            _alarmTimer.Timeout += OnAlarmTimeout;
            AddChild(_alarmTimer);

            // Flash timer for visual effects
            _flashTimer = new Timer();
            _flashTimer.Name = "FlashTimer";
            _flashTimer.WaitTime = 0.5f;
            _flashTimer.Timeout += OnFlashTimeout;
            AddChild(_flashTimer);
        }

        /// <summary>
        /// Creates a simple alarm texture
        /// </summary>
        private ImageTexture CreateAlarmTexture()
        {
            var image = Image.CreateEmpty(24, 24, false, Image.Format.Rgb8);
            image.Fill(Colors.Black);
            
            // Draw alarm shape (simple circle with center)
            for (int x = 0; x < 24; x++)
            {
                for (int y = 0; y < 24; y++)
                {
                    float distance = new Vector2(x - 12, y - 12).Length();
                    if (distance < 10 && distance > 6)
                    {
                        image.SetPixel(x, y, Colors.DarkGray);
                    }
                    else if (distance < 6 && distance > 3)
                    {
                        image.SetPixel(x, y, Colors.Gray);
                    }
                    else if (distance < 3)
                    {
                        image.SetPixel(x, y, Colors.White);
                    }
                }
            }
            
            return ImageTexture.CreateFromImage(image);
        }

        /// <summary>
        /// Creates a light texture for the alarm light
        /// </summary>
        private ImageTexture CreateLightTexture()
        {
            var image = Image.CreateEmpty(64, 64, false, Image.Format.Rgb8);
            
            // Create radial gradient
            for (int x = 0; x < 64; x++)
            {
                for (int y = 0; y < 64; y++)
                {
                    float distance = new Vector2(x - 32, y - 32).Length();
                    float intensity = Math.Max(0, 1f - (distance / 32f));
                    var color = Colors.White * intensity;
                    image.SetPixel(x, y, color);
                }
            }
            
            return ImageTexture.CreateFromImage(image);
        }

        /// <summary>
        /// Initializes the alarm system with a parent structure
        /// </summary>
        public void Initialize(Structure parentStructure)
        {
            _parentStructure = parentStructure;
            
            if (parentStructure != null)
            {
                // Connect to structure events
                parentStructure.StructureDestroyed += OnParentStructureDestroyed;
                parentStructure.StructureDamaged += OnParentStructureDamaged;
            }
        }

        public override void _Process(double delta)
        {
            if (AutoActivate && !_isActive)
            {
                CheckForThreats();
            }
            
            UpdateThreatLevel();
        }

        /// <summary>
        /// Checks for threats in the detection area
        /// </summary>
        private void CheckForThreats()
        {
            if (_detectedEnemies.Count > 0)
            {
                string alarmType = DetermineAlarmType();
                if (!string.IsNullOrEmpty(alarmType))
                {
                    Activate(alarmType);
                }
            }
        }

        /// <summary>
        /// Updates the current threat level based on detected enemies
        /// </summary>
        private void UpdateThreatLevel()
        {
            float oldThreatLevel = _threatLevel;
            _threatLevel = 0f;

            foreach (var enemy in _detectedEnemies)
            {
                if (enemy != null && !enemy.IsDead)
                {
                    float distance = GlobalPosition.DistanceTo(enemy.GlobalPosition);
                    float distanceFactor = 1f - (distance / DetectionRadius);
                    float enemyThreat = (enemy.Damage / 10f) * distanceFactor;
                    _threatLevel += enemyThreat;
                }
            }

            if (Math.Abs(_threatLevel - oldThreatLevel) > 0.1f)
            {
                EmitSignal(SignalName.ThreatLevelChanged, oldThreatLevel, _threatLevel);
            }
        }

        /// <summary>
        /// Determines the appropriate alarm type based on current threats
        /// </summary>
        private string DetermineAlarmType()
        {
            if (_threatLevel > 8f) return "high_threat";
            if (_threatLevel > 4f) return "medium_threat";
            if (_threatLevel > 1f) return "low_threat";
            return "";
        }

        /// <summary>
        /// Activates the alarm with the specified type
        /// </summary>
        public void Activate(string alarmType)
        {
            if (_isActive && _currentAlarmType == alarmType) return;

            _isActive = true;
            _currentAlarmType = alarmType;

            // Set visual and audio based on alarm type
            SetAlarmAppearance(alarmType);
            
            // Start alarm sound
            if (_alarmSound.Stream != null)
            {
                _alarmSound.Play();
            }

            // Start flashing effect
            _flashTimer.Start();

            // Visual alarm activated

            // Set alarm duration
            _alarmTimer.WaitTime = GetAlarmDuration(alarmType);
            _alarmTimer.Start();

            // Emit activation event
            EmitSignal(SignalName.AlarmTriggered, alarmType, GlobalPosition);

            Logger.LogInfo("AlarmSystem", $"Alarm activated: {alarmType} at {GlobalPosition}");
        }

        /// <summary>
        /// Sets the alarm appearance based on the alarm type
        /// </summary>
        private void SetAlarmAppearance(string alarmType)
        {
            Color alarmColor = alarmType switch
            {
                "low_threat" => _warningColor,
                "medium_threat" => _dangerColor,
                "high_threat" => _raidColor,
                "raid" => _raidColor,
                _ => _normalColor
            };

            _alarmSprite.Modulate = alarmColor;
        }

        /// <summary>
        /// Gets the alarm duration for the specified type
        /// </summary>
        private float GetAlarmDuration(string alarmType)
        {
            return alarmType switch
            {
                "low_threat" => AlarmDuration * 0.5f,
                "medium_threat" => AlarmDuration,
                "high_threat" => AlarmDuration * 1.5f,
                "raid" => AlarmDuration * 3f,
                _ => AlarmDuration
            };
        }

        /// <summary>
        /// Deactivates the alarm
        /// </summary>
        public void Deactivate()
        {
            if (!_isActive) return;

            _isActive = false;
            _currentAlarmType = "";

            // Stop timers
            _alarmTimer.Stop();
            _flashTimer.Stop();

            // Stop alarm sound
            _alarmSound.Stop();

            // Visual alarm deactivated

            // Reset appearance
            _alarmSprite.Modulate = _normalColor;

            // Emit deactivation event
            EmitSignal(SignalName.AlarmDeactivated, GlobalPosition);

            Logger.LogInfo("AlarmSystem", "Alarm deactivated");
        }

        /// <summary>
        /// Handles enemy entering detection area
        /// </summary>
        private void OnEnemyEntered(Node2D body)
        {
            if (body is Enemy enemy && !enemy.IsDead)
            {
                _detectedEnemies.Add(enemy);
                EmitSignal(SignalName.EnemyDetectedByAlarm, enemy, this);
                
                Logger.LogInfo("AlarmSystem", $"Enemy detected: {enemy.EnemyName}");
                
                // Connect to enemy death to clean up detection
                enemy.EnemyDied += OnDetectedEnemyDied;
            }
        }

        private void OnDetectedEnemyDied()
        {
            throw new NotImplementedException();
        }

        /// <summary>
        /// Handles enemy exiting detection area
        /// </summary>
        private void OnEnemyExited(Node2D body)
        {
            if (body is Enemy enemy)
            {
                _detectedEnemies.Remove(enemy);
                enemy.EnemyDied -= OnDetectedEnemyDied;
                
                Logger.LogInfo("AlarmSystem", $"Enemy left detection area: {enemy.EnemyName}");
                
                // Deactivate alarm if no more threats
                if (_detectedEnemies.Count == 0 && _isActive)
                {
                    Deactivate();
                }
            }
        }

        /// <summary>
        /// Handles detected enemy death
        /// </summary>
        private void OnDetectedEnemyDied(Enemy enemy)
        {
            _detectedEnemies.Remove(enemy);
            enemy.EnemyDied -= OnDetectedEnemyDied;
            
            // Deactivate alarm if no more threats
            if (_detectedEnemies.Count == 0 && _isActive)
            {
                Deactivate();
            }
        }

        /// <summary>
        /// Handles alarm timeout
        /// </summary>
        private void OnAlarmTimeout()
        {
            // Check if threats still exist
            if (_detectedEnemies.Count == 0 || !AutoActivate)
            {
                Deactivate();
            }
            else
            {
                // Extend alarm if threats persist
                string newAlarmType = DetermineAlarmType();
                if (!string.IsNullOrEmpty(newAlarmType))
                {
                    _alarmTimer.WaitTime = GetAlarmDuration(newAlarmType);
                    _alarmTimer.Start();
                }
                else
                {
                    Deactivate();
                }
            }
        }

        /// <summary>
        /// Handles flash timer timeout for visual effects
        /// </summary>
        private void OnFlashTimeout()
        {
            if (!_isActive) return;

            // Toggle between alarm color and dimmed version
            Color currentColor = _alarmSprite.Modulate;
            Color targetColor = currentColor.Lightened(0.3f);
            
            var tween = CreateTween();
            tween.TweenProperty(_alarmSprite, "modulate", targetColor, 0.2f);
            tween.TweenProperty(_alarmSprite, "modulate", currentColor, 0.2f);
        }

        /// <summary>
        /// Handles parent structure destruction
        /// </summary>
        private void OnParentStructureDestroyed(Structure structure)
        {
            Deactivate();
            Logger.LogInfo("AlarmSystem", "Alarm system destroyed with parent structure");
        }

        /// <summary>
        /// Handles parent structure damage
        /// </summary>
        private void OnParentStructureDamaged(Structure structure, int damage)
        {
            // Reduce detection radius when damaged
            float healthPercent = structure.HealthPercentage;
            float adjustedRadius = DetectionRadius * healthPercent;
            
            if (_detectionArea.GetChild(0) is CollisionShape2D shape && 
                shape.Shape is CircleShape2D circle)
            {
                circle.Radius = adjustedRadius;
            }
        }

        /// <summary>
        /// Manually triggers the alarm (for testing or external activation)
        /// </summary>
        public void TriggerAlarm(string alarmType, float duration = -1f)
        {
            if (duration > 0)
            {
                AlarmDuration = duration;
            }
            
            Activate(alarmType);
        }

        /// <summary>
        /// Gets alarm system status
        /// </summary>
        public Dictionary<string, object> GetStatus()
        {
            return new Dictionary<string, object>
            {
                ["is_active"] = _isActive,
                ["alarm_type"] = _currentAlarmType,
                ["threat_level"] = _threatLevel,
                ["detected_enemies"] = _detectedEnemies.Count,
                ["detection_radius"] = DetectionRadius,
                ["auto_activate"] = AutoActivate,
                ["time_remaining"] = _alarmTimer.TimeLeft
            };
        }

        /// <summary>
        /// Gets all currently detected enemies
        /// </summary>
        public List<Enemy> GetDetectedEnemies()
        {
            var enemies = new List<Enemy>();
            foreach (var enemy in _detectedEnemies)
            {
                if (enemy != null && !enemy.IsDead)
                {
                    enemies.Add(enemy);
                }
            }
            return enemies;
        }

        public override void _ExitTree()
        {
            DefenseSystem.Instance?.UnregisterAlarmSystem(this);
        }
    }
}