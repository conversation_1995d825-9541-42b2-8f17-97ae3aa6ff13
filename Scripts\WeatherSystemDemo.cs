using Godot;

namespace SurvivalLooterShooter
{
    /// <summary>
    /// Demo controller for testing the weather system
    /// </summary>
    public partial class WeatherSystemDemo : Control
    {
        private Button _clearWeatherButton;
        private Button _rainButton;
        private Button _snowButton;
        private Button _sandstormButton;
        private Button _desertBiomeButton;
        private Button _tundraBiomeButton;

        public override void _Ready()
        {
            SetupButtons();
            ConnectButtons();
        }

        private void SetupButtons()
        {
            var testControls = GetNode<VBoxContainer>("TestControls");
            
            _clearWeatherButton = testControls.GetNode<Button>("ClearWeatherButton");
            _rainButton = testControls.GetNode<Button>("RainButton");
            _snowButton = testControls.GetNode<Button>("SnowButton");
            _sandstormButton = testControls.GetNode<Button>("SandstormButton");
            _desertBiomeButton = testControls.GetNode<Button>("DesertBiomeButton");
            _tundraBiomeButton = testControls.GetNode<Button>("TundraBiomeButton");
        }

        private void ConnectButtons()
        {
            _clearWeatherButton.Pressed += () => SetWeather(WeatherManager.WeatherType.Clear);
            _rainButton.Pressed += () => SetWeather(WeatherManager.WeatherType.HeavyRain);
            _snowButton.Pressed += () => SetWeather(WeatherManager.WeatherType.Snow);
            _sandstormButton.Pressed += () => SetWeather(WeatherManager.WeatherType.Sandstorm);
            _desertBiomeButton.Pressed += () => SetBiome("desert");
            _tundraBiomeButton.Pressed += () => SetBiome("tundra");
        }

        private void SetWeather(WeatherManager.WeatherType weatherType)
        {
            if (WeatherManager.Instance != null)
            {
                WeatherManager.Instance.ForceWeatherChange(weatherType);
                GD.Print($"Weather changed to: {weatherType}");
            }
            else
            {
                GD.PrintErr("WeatherManager not available");
            }
        }

        private void SetBiome(string biomeId)
        {
            if (WeatherManager.Instance != null)
            {
                WeatherManager.Instance.SetCurrentBiome(biomeId);
                GD.Print($"Biome changed to: {biomeId}");
            }
            else
            {
                GD.PrintErr("WeatherManager not available");
            }
        }
    }
}