[gd_scene load_steps=2 format=3 uid="uid://dj66m3347xaac"]

[ext_resource type="Script" uid="uid://v2pcpdqu4vn3" path="res://Scripts/CraftingStationUI.cs" id="1_abc123"]

[node name="CraftingStationUI" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("1_abc123")

[node name="Background" type="Panel" parent="."]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -400.0
offset_top = -300.0
offset_right = 400.0
offset_bottom = 300.0
grow_horizontal = 2
grow_vertical = 2

[node name="CloseButton" type="Button" parent="Background"]
layout_mode = 1
anchors_preset = 1
anchor_left = 1.0
anchor_right = 1.0
offset_left = -30.0
offset_bottom = 30.0
grow_horizontal = 0
text = "X"

[node name="VBoxContainer" type="VBoxContainer" parent="Background"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 10.0
offset_top = 10.0
offset_right = -10.0
offset_bottom = -10.0
grow_horizontal = 2
grow_vertical = 2

[node name="Header" type="VBoxContainer" parent="Background/VBoxContainer"]
layout_mode = 2

[node name="StationInfo" type="HBoxContainer" parent="Background/VBoxContainer/Header"]
layout_mode = 2

[node name="StationNameLabel" type="Label" parent="Background/VBoxContainer/Header/StationInfo"]
layout_mode = 2
text = "Crafting Station"

[node name="StationLevelLabel" type="Label" parent="Background/VBoxContainer/Header/StationInfo"]
layout_mode = 2
text = "Level 1"

[node name="CraftingProgress" type="ProgressBar" parent="Background/VBoxContainer/Header"]
visible = false
layout_mode = 2

[node name="QueueTimeLabel" type="Label" parent="Background/VBoxContainer/Header"]
layout_mode = 2
text = "Queue Time: 0s"

[node name="MainContainer" type="HBoxContainer" parent="Background/VBoxContainer"]
layout_mode = 2
size_flags_vertical = 3

[node name="RecipeList" type="VBoxContainer" parent="Background/VBoxContainer/MainContainer"]
layout_mode = 2
size_flags_horizontal = 3

[node name="Label" type="Label" parent="Background/VBoxContainer/MainContainer/RecipeList"]
layout_mode = 2
text = "Available Recipes"

[node name="RecipeScrollContainer" type="ScrollContainer" parent="Background/VBoxContainer/MainContainer/RecipeList"]
layout_mode = 2
size_flags_vertical = 3

[node name="RecipeContainer" type="VBoxContainer" parent="Background/VBoxContainer/MainContainer/RecipeList/RecipeScrollContainer"]
layout_mode = 2
size_flags_horizontal = 3

[node name="QueuePanel" type="VBoxContainer" parent="Background/VBoxContainer/MainContainer"]
layout_mode = 2
size_flags_horizontal = 3

[node name="Label" type="Label" parent="Background/VBoxContainer/MainContainer/QueuePanel"]
layout_mode = 2
text = "Crafting Queue"

[node name="QueueScrollContainer" type="ScrollContainer" parent="Background/VBoxContainer/MainContainer/QueuePanel"]
layout_mode = 2
size_flags_vertical = 3

[node name="QueueContainer" type="VBoxContainer" parent="Background/VBoxContainer/MainContainer/QueuePanel/QueueScrollContainer"]
layout_mode = 2
size_flags_horizontal = 3

[node name="RecipeDetails" type="VBoxContainer" parent="Background/VBoxContainer/MainContainer"]
layout_mode = 2
size_flags_horizontal = 3

[node name="Label" type="Label" parent="Background/VBoxContainer/MainContainer/RecipeDetails"]
layout_mode = 2
text = "Recipe Details"

[node name="SelectedRecipePanel" type="Panel" parent="Background/VBoxContainer/MainContainer/RecipeDetails"]
layout_mode = 2
size_flags_vertical = 3

[node name="SelectedRecipeContainer" type="VBoxContainer" parent="Background/VBoxContainer/MainContainer/RecipeDetails/SelectedRecipePanel"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 5.0
offset_top = 5.0
offset_right = -5.0
offset_bottom = -5.0
grow_horizontal = 2
grow_vertical = 2

[node name="RecipeNameLabel" type="Label" parent="Background/VBoxContainer/MainContainer/RecipeDetails/SelectedRecipePanel/SelectedRecipeContainer"]
layout_mode = 2
text = "Select a recipe"

[node name="MaterialsContainer" type="VBoxContainer" parent="Background/VBoxContainer/MainContainer/RecipeDetails/SelectedRecipePanel/SelectedRecipeContainer"]
layout_mode = 2

[node name="StepsContainer" type="VBoxContainer" parent="Background/VBoxContainer/MainContainer/RecipeDetails/SelectedRecipePanel/SelectedRecipeContainer"]
layout_mode = 2

[node name="OutputContainer" type="HBoxContainer" parent="Background/VBoxContainer/MainContainer/RecipeDetails/SelectedRecipePanel/SelectedRecipeContainer"]
layout_mode = 2

[node name="CraftingControls" type="HBoxContainer" parent="Background/VBoxContainer/MainContainer/RecipeDetails/SelectedRecipePanel/SelectedRecipeContainer"]
layout_mode = 2

[node name="Label" type="Label" parent="Background/VBoxContainer/MainContainer/RecipeDetails/SelectedRecipePanel/SelectedRecipeContainer/CraftingControls"]
layout_mode = 2
text = "Quantity:"

[node name="QuantitySpinBox" type="SpinBox" parent="Background/VBoxContainer/MainContainer/RecipeDetails/SelectedRecipePanel/SelectedRecipeContainer/CraftingControls"]
layout_mode = 2
min_value = 1.0
value = 1.0

[node name="QueueButton" type="Button" parent="Background/VBoxContainer/MainContainer/RecipeDetails/SelectedRecipePanel/SelectedRecipeContainer/CraftingControls"]
layout_mode = 2
disabled = true
text = "Add to Queue"

[node name="UpgradeButton" type="Button" parent="Background/VBoxContainer/MainContainer/RecipeDetails/SelectedRecipePanel/SelectedRecipeContainer"]
visible = false
layout_mode = 2
text = "Upgrade Station"

[connection signal="pressed" from="Background/CloseButton" to="." method="_on_close_button_pressed"]
[connection signal="pressed" from="Background/VBoxContainer/MainContainer/RecipeDetails/SelectedRecipePanel/SelectedRecipeContainer/CraftingControls/QueueButton" to="." method="_on_queue_button_pressed"]
[connection signal="pressed" from="Background/VBoxContainer/MainContainer/RecipeDetails/SelectedRecipePanel/SelectedRecipeContainer/UpgradeButton" to="." method="_on_upgrade_button_pressed"]
