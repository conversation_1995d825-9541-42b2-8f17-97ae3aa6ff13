[gd_scene load_steps=3 format=3 uid="uid://bqxvhqxvhqxvh"]

[ext_resource type="Script" uid="uid://wtf16cxq6slo" path="res://Scripts/InventoryUI.cs" id="1_1a2b3"]

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_1"]
bg_color = Color(0.2, 0.2, 0.2, 0.9)
border_width_left = 2
border_width_top = 2
border_width_right = 2
border_width_bottom = 2
border_color = Color(0.5, 0.5, 0.5, 1)
corner_radius_top_left = 5
corner_radius_top_right = 5
corner_radius_bottom_right = 5
corner_radius_bottom_left = 5

[node name="InventoryUI" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2
script = ExtResource("1_1a2b3")

[node name="Background" type="Panel" parent="."]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -400.0
offset_top = -300.0
offset_right = 400.0
offset_bottom = 300.0
grow_horizontal = 2
grow_vertical = 2
theme_override_styles/panel = SubResource("StyleBoxFlat_1")

[node name="VBoxContainer" type="VBoxContainer" parent="Background"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 10.0
offset_top = 10.0
offset_right = -10.0
offset_bottom = -10.0
grow_horizontal = 2
grow_vertical = 2

[node name="TitleLabel" type="Label" parent="Background/VBoxContainer"]
layout_mode = 2
text = "Inventory"
horizontal_alignment = 1
vertical_alignment = 1

[node name="HSeparator" type="HSeparator" parent="Background/VBoxContainer"]
layout_mode = 2

[node name="ScrollContainer" type="ScrollContainer" parent="Background/VBoxContainer"]
layout_mode = 2
size_flags_vertical = 3

[node name="InventoryGrid" type="GridContainer" parent="Background/VBoxContainer/ScrollContainer"]
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 3
columns = 8

[node name="CloseButton" type="Button" parent="Background"]
layout_mode = 1
anchors_preset = 1
anchor_left = 1.0
anchor_right = 1.0
offset_left = -30.0
offset_top = 5.0
offset_right = -5.0
offset_bottom = 30.0
grow_horizontal = 0
text = "X"

[node name="ItemTooltip" type="Panel" parent="."]
visible = false
layout_mode = 0
offset_right = 200.0
offset_bottom = 100.0
theme_override_styles/panel = SubResource("StyleBoxFlat_1")

[node name="TooltipLabel" type="RichTextLabel" parent="ItemTooltip"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 5.0
offset_top = 5.0
offset_right = -5.0
offset_bottom = -5.0
grow_horizontal = 2
grow_vertical = 2
bbcode_enabled = true
fit_content = true

[connection signal="pressed" from="Background/CloseButton" to="." method="_on_close_button_pressed"]
