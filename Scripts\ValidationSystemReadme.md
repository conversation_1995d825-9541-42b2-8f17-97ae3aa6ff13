# Data Validation and Error Handling System

## Overview

This document describes the comprehensive data validation and error handling system implemented for the Survival Looter Shooter game. The system provides robust error recovery, logging, and fallback mechanisms to ensure the game remains stable even when encountering corrupted or invalid data.

## Components

### 1. Logger System (`Logger.cs`)

A centralized logging system that provides:
- Multiple log levels (Debug, Info, Warning, Error, Critical)
- File logging with automatic buffer management
- Exception logging with full stack traces
- Thread-safe operations
- Configurable log levels and file output

**Usage:**
```csharp
Logger.LogInfo("SystemName", "Information message");
Logger.LogError("SystemName", "Error message");
Logger.LogException("SystemName", exception, "Context");
```

### 2. Data Validator (`DataValidator.cs`)

Comprehensive validation utilities for all game data types:

#### Item Validation
- Validates item IDs, names, types, and stack sizes
- Provides automatic correction for common issues
- Type-specific validation for weapons, consumables, and ammo
- Creates fallback metadata when missing

#### Recipe Validation
- Validates recipe structure and requirements
- Checks input/output item references
- Corrects invalid crafting times and quantities
- Ensures recipe integrity

#### Save Data Validation
- Validates save file structure and content
- Corrects null collections and invalid timestamps
- Removes invalid inventory items
- Provides comprehensive error recovery

#### JSON Validation
- Pre-validates JSON content before deserialization
- Provides detailed error messages for malformed JSON
- Supports both array and object root elements

### 3. Enhanced ItemDatabase (`ItemDatabaseVerification.cs`)

An improved version of the ItemDatabase with:
- Comprehensive JSON loading with error recovery
- Data integrity verification after loading
- Fallback data creation when files are corrupted
- Orphaned recipe detection and cleanup
- Detailed status reporting

**Features:**
- Validates each item and recipe during loading
- Automatically corrects recoverable errors
- Creates fallback data when critical errors occur
- Provides status information about loading success
- Emits signals for error reporting

### 4. Enhanced SaveManager

Improved save/load operations with:
- Atomic file operations to prevent corruption
- Backup creation and restoration
- Save file verification before and after operations
- Comprehensive error handling and recovery
- Data validation before saving and after loading

**Features:**
- Creates backups before overwriting saves
- Verifies save files before loading
- Attempts backup recovery on main save failure
- Validates all data before restoration
- Provides detailed error reporting

### 5. Enhanced Inventory System

Improved inventory operations with:
- Input validation for all operations
- Error handling for invalid items and quantities
- Graceful handling of database lookup failures
- Event system error handling
- Comprehensive logging

## Error Handling Strategies

### 1. Graceful Degradation
When non-critical errors occur, the system:
- Logs the error for debugging
- Applies automatic corrections where possible
- Continues operation with corrected data
- Notifies the user if necessary

### 2. Fallback Mechanisms
When critical data is corrupted or missing:
- Creates minimal fallback data to maintain functionality
- Loads emergency items and recipes
- Provides basic game functionality
- Alerts the user to the data issue

### 3. Data Recovery
For corrupted save files:
- Attempts to load from backup files
- Validates data integrity before restoration
- Provides partial recovery when possible
- Offers new game option when recovery fails

### 4. Validation Pipeline
All data goes through a validation pipeline:
1. **Format Validation**: JSON structure and syntax
2. **Schema Validation**: Required fields and data types
3. **Business Logic Validation**: Game-specific rules
4. **Reference Validation**: Cross-references between data
5. **Integrity Validation**: Overall data consistency

## Testing

### 1. Unit Tests (`DataValidationTests.cs`)
Tests individual validation components:
- Item validation with various error conditions
- Recipe validation with missing or invalid data
- Save data validation and correction
- JSON format validation
- Fallback data creation

### 2. Integration Tests (`ErrorHandlingIntegrationTests.cs`)
Tests system integration with error conditions:
- Corrupted data file recovery
- Inventory error handling
- Save/load error scenarios
- System integration with invalid data
- Fallback mechanism effectiveness

## Configuration

### Logger Configuration
```csharp
Logger.SetLogLevel(Logger.LogLevel.Info);  // Set minimum log level
Logger.SetFileLogging(true);               // Enable file logging
```

### Validation Configuration
The validation system uses reasonable defaults but can be customized:
- Maximum stack sizes
- Valid item types
- Save version compatibility
- Fallback data definitions

## Best Practices

### For Developers
1. Always use the Logger for error reporting
2. Validate input parameters in public methods
3. Use try-catch blocks for external operations
4. Provide meaningful error messages
5. Test error conditions explicitly

### For Data Files
1. Use consistent JSON formatting
2. Include all required fields
3. Validate data before committing changes
4. Keep backup copies of working data
5. Test with the validation system

## Error Recovery Examples

### Corrupted Items.json
```
1. System detects invalid JSON format
2. Logs detailed error message
3. Creates fallback items for basic functionality
4. Emits signal to notify UI of data issue
5. Game continues with minimal item set
```

### Missing Recipe Data
```
1. System detects missing recipe file
2. Logs warning about missing data
3. Creates basic fallback recipes
4. Continues game operation
5. User can still craft basic items
```

### Corrupted Save File
```
1. System detects save file corruption
2. Attempts to load from backup
3. If backup also corrupted, offers new game
4. Logs all recovery attempts
5. Preserves user choice and data when possible
```

## Monitoring and Debugging

### Log Analysis
- Check log files for recurring errors
- Monitor validation warnings for data quality issues
- Track fallback data usage
- Analyze error patterns

### Status Reporting
```csharp
var status = ItemDatabaseVerification.Instance.GetStatus();
if (!status.ItemsLoadedSuccessfully) {
    // Handle data loading issues
}
```

### Error Metrics
The system tracks:
- Number of validation errors corrected
- Frequency of fallback data usage
- Save/load success rates
- Data integrity issues

This comprehensive system ensures that the game remains stable and playable even when encountering various data corruption or error scenarios, while providing developers with detailed information for debugging and improvement.