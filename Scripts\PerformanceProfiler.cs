using Godot;
using System;
using System.Collections.Generic;
using System.Diagnostics;

public partial class PerformanceProfiler : Node
{
    private static PerformanceProfiler _instance;
    public static PerformanceProfiler Instance => _instance;

    private Dictionary<string, ProfileData> _profiles = new Dictionary<string, ProfileData>();
    private bool _isEnabled = false;
    private Label _debugLabel;
    private VBoxContainer _debugContainer;
    private bool _showDebugInfo = false;

    private struct ProfileData
    {
        public Stopwatch Stopwatch;
        public double TotalTime;
        public int CallCount;
        public double AverageTime => CallCount > 0 ? TotalTime / CallCount : 0;
    }

    public override void _Ready()
    {
        _instance = this;
        SetupDebugUI();

        // Enable profiling in debug builds
        _isEnabled = OS.IsDebugBuild();
    }

    public override void _Input(InputEvent @event)
    {
        if (@event is InputEventKey keyEvent && keyEvent.Pressed)
        {
            if (keyEvent.Keycode == Key.F3)
            {
                ToggleDebugInfo();
            }
            else if (keyEvent.Keycode == Key.F4)
            {
                ClearProfiles();
            }
        }
    }

    private void SetupDebugUI()
    {
        _debugContainer = new VBoxContainer();
        _debugContainer.Position = new Vector2(10, 10);
        _debugContainer.Visible = false;
        AddChild(_debugContainer);

        _debugLabel = new Label();
        _debugLabel.AddThemeStyleboxOverride("normal", new StyleBoxFlat());
        var styleBox = _debugLabel.GetThemeStylebox("normal") as StyleBoxFlat;
        if (styleBox != null)
        {
            styleBox.BgColor = new Color(0, 0, 0, 0.7f);
            styleBox.SetBorderWidthAll(1);
            styleBox.BorderColor = new Color(1.0f, 1.0f, 1.0f); // White
        }
        _debugContainer.AddChild(_debugLabel);
    }

    public void StartProfile(string name)
    {
        if (!_isEnabled) return;

        if (!_profiles.ContainsKey(name))
        {
            _profiles[name] = new ProfileData
            {
                Stopwatch = new Stopwatch(),
                TotalTime = 0,
                CallCount = 0
            };
        }

        var profile = _profiles[name];
        profile.Stopwatch.Restart();
        _profiles[name] = profile;
    }

    public void EndProfile(string name)
    {
        if (!_isEnabled || !_profiles.ContainsKey(name)) return;

        var profile = _profiles[name];
        profile.Stopwatch.Stop();
        profile.TotalTime += profile.Stopwatch.Elapsed.TotalMilliseconds;
        profile.CallCount++;
        _profiles[name] = profile;
    }

    public void ToggleDebugInfo()
    {
        _showDebugInfo = !_showDebugInfo;
        _debugContainer.Visible = _showDebugInfo;
    }

    public void ClearProfiles()
    {
        _profiles.Clear();
        GD.Print("Performance profiles cleared");
    }

    public override void _Process(double delta)
    {
        if (_showDebugInfo && _debugLabel != null)
        {
            UpdateDebugDisplay();
        }
    }

    private void UpdateDebugDisplay()
    {
        var text = "=== PERFORMANCE PROFILER ===\n";
        text += $"FPS: {Engine.GetFramesPerSecond()}\n";
        text += $"Memory: {GC.GetTotalMemory(false) / 1024 / 1024} MB\n";
        text += $"Render Objects: {RenderingServer.GetRenderingInfo(RenderingServer.RenderingInfo.TotalPrimitivesInFrame)}\n";
        text += "\n=== CUSTOM PROFILES ===\n";

        foreach (var kvp in _profiles)
        {
            var profile = kvp.Value;
            text += $"{kvp.Key}: {profile.AverageTime:F2}ms avg ({profile.CallCount} calls)\n";
        }

        text += "\nF3: Toggle Debug | F4: Clear Profiles";
        _debugLabel.Text = text;
    }

    public static void Profile(string name, Action action)
    {
        Instance?.StartProfile(name);
        action?.Invoke();
        Instance?.EndProfile(name);
    }
}