using Godot;
using System;
using System.Collections.Generic;
using System.Linq;

namespace SurvivalLooterShooter
{
    /// <summary>
    /// Test runner for the loot and drop system
    /// Validates loot table functionality, drop generation, and integration with enemy system
    /// </summary>
    public partial class LootSystemTests : Node
    {
        private bool _testsCompleted = false;
        private int _testsPassed = 0;
        private int _testsFailed = 0;

        public override void _Ready()
        {
            // Wait a frame for all systems to initialize
            CallDeferred(nameof(RunTests));
        }

        private void RunTests()
        {
            Logger.LogInfo("LootSystemTests", "Starting loot system tests...");

            try
            {
                TestLootTableManager();
                TestLootDropSystem();
                TestEnemyLootIntegration();
                TestRareItemGeneration();
                TestLootExplosionEffects();

                _testsCompleted = true;
                Logger.LogInfo("LootSystemTests", $"Loot system tests completed. Passed: {_testsPassed}, Failed: {_testsFailed}");
            }
            catch (Exception ex)
            {
                Logger.LogException("LootSystemTests", ex, "RunTests");
                _testsFailed++;
            }
        }

        /// <summary>
        /// Tests the LootTableManager functionality
        /// </summary>
        private void TestLootTableManager()
        {
            Logger.LogInfo("LootSystemTests", "Testing LootTableManager...");

            try
            {
                // Test singleton access
                var manager = LootTableManager.Instance;
                if (manager == null)
                {
                    Logger.LogError("LootSystemTests", "LootTableManager instance is null");
                    _testsFailed++;
                    return;
                }

                // Test loot table loading
                var lootTableIds = manager.GetLootTableIds();
                if (lootTableIds.Count == 0)
                {
                    Logger.LogWarning("LootSystemTests", "No loot tables loaded, but this might be expected if file doesn't exist");
                }
                else
                {
                    Logger.LogInfo("LootSystemTests", $"Loaded {lootTableIds.Count} loot tables: {string.Join(", ", lootTableIds)}");
                }

                // Test loot generation from a table (if any exist)
                if (lootTableIds.Count > 0)
                {
                    string testTableId = lootTableIds.First();
                    var loot = manager.GenerateLoot(testTableId);
                    Logger.LogInfo("LootSystemTests", $"Generated {loot.Count} loot items from table '{testTableId}'");

                    foreach (var drop in loot)
                    {
                        Logger.LogInfo("LootSystemTests", $"  - {drop.Item} x{drop.Quantity[0]} (Tier: {drop.Tier}, Rare: {drop.IsRare})");
                    }
                }

                // Test getting non-existent loot table
                var nonExistentTable = manager.GetLootTable("non_existent_table");
                if (nonExistentTable == null)
                {
                    Logger.LogInfo("LootSystemTests", "Correctly returned null for non-existent loot table");
                    _testsPassed++;
                }
                else
                {
                    Logger.LogError("LootSystemTests", "Should have returned null for non-existent loot table");
                    _testsFailed++;
                }

                _testsPassed++;
                Logger.LogInfo("LootSystemTests", "LootTableManager tests passed");
            }
            catch (Exception ex)
            {
                Logger.LogException("LootSystemTests", ex, "TestLootTableManager");
                _testsFailed++;
            }
        }

        /// <summary>
        /// Tests the LootDropSystem functionality
        /// </summary>
        private void TestLootDropSystem()
        {
            Logger.LogInfo("LootSystemTests", "Testing LootDropSystem...");

            try
            {
                // Test singleton access
                var dropSystem = LootDropSystem.Instance;
                if (dropSystem == null)
                {
                    Logger.LogError("LootSystemTests", "LootDropSystem instance is null");
                    _testsFailed++;
                    return;
                }

                // Test single item drop creation
                Vector2 testPosition = new Vector2(100, 100);
                dropSystem.CreateSingleItemDrop("raw_meat", 3, testPosition, "Common");
                Logger.LogInfo("LootSystemTests", "Created single item drop successfully");

                // Test loot drop from table (if available)
                if (LootTableManager.Instance != null)
                {
                    var tableIds = LootTableManager.Instance.GetLootTableIds();
                    if (tableIds.Count > 0)
                    {
                        dropSystem.CreateLootDropsFromTable(tableIds.First(), testPosition + new Vector2(50, 0));
                        Logger.LogInfo("LootSystemTests", "Created loot drops from table successfully");
                    }
                }

                _testsPassed++;
                Logger.LogInfo("LootSystemTests", "LootDropSystem tests passed");
            }
            catch (Exception ex)
            {
                Logger.LogException("LootSystemTests", ex, "TestLootDropSystem");
                _testsFailed++;
            }
        }

        /// <summary>
        /// Tests integration between enemy system and loot drops
        /// </summary>
        private void TestEnemyLootIntegration()
        {
            Logger.LogInfo("LootSystemTests", "Testing enemy loot integration...");

            try
            {
                // Create a test enemy with loot table
                var enemy = new Enemy();
                var enemyData = new EnemyData
                {
                    Id = "test_wolf",
                    Name = "Test Wolf",
                    Health = 60f,
                    MaxHealth = 60f,
                    Damage = 15f,
                    Speed = 120f,
                    DetectionRange = 200f,
                    AttackRange = 50f,
                    AIType = "aggressive",
                    Biomes = new List<string> { "forest" },
                    LootTable = new List<LootDrop>
                    {
                        new LootDrop { Item = "raw_meat", Chance = 0.8f, Quantity = new List<int> { 1, 3 } },
                        new LootDrop { Item = "wolf_pelt", Chance = 0.4f, Quantity = new List<int> { 1, 1 } },
                        new LootDrop { Item = "bone", Chance = 0.3f, Quantity = new List<int> { 1, 2 } }
                    },
                    ExperienceReward = 25f,
                    SpawnWeight = 1.0f
                };
                enemy.Initialize(enemyData);

                // Add enemy to scene temporarily
                GetTree().CurrentScene.AddChild(enemy);
                enemy.GlobalPosition = new Vector2(200, 200);

                // Test loot drop creation from enemy
                if (LootDropSystem.Instance != null)
                {
                    LootDropSystem.Instance.CreateLootDropsFromEnemy(enemy, enemy.GlobalPosition);
                    Logger.LogInfo("LootSystemTests", "Created loot drops from enemy successfully");
                }

                // Clean up test enemy
                enemy.QueueFree();

                _testsPassed++;
                Logger.LogInfo("LootSystemTests", "Enemy loot integration tests passed");
            }
            catch (Exception ex)
            {
                Logger.LogException("LootSystemTests", ex, "TestEnemyLootIntegration");
                _testsFailed++;
            }
        }

        /// <summary>
        /// Tests rare item generation and enhancement
        /// </summary>
        private void TestRareItemGeneration()
        {
            Logger.LogInfo("LootSystemTests", "Testing rare item generation...");

            try
            {
                // Test creating rare items directly
                if (LootDropSystem.Instance != null)
                {
                    Vector2 testPosition = new Vector2(300, 300);

                    // Create items of different rarities
                    LootDropSystem.Instance.CreateSingleItemDrop("wolf_fang", 1, testPosition, "Rare");
                    LootDropSystem.Instance.CreateSingleItemDrop("essence_crystal", 1, testPosition + new Vector2(30, 0), "Rare");
                    LootDropSystem.Instance.CreateSingleItemDrop("legendary_essence", 1, testPosition + new Vector2(60, 0), "Legendary");

                    Logger.LogInfo("LootSystemTests", "Created rare items with different tiers successfully");
                }

                // Test loot table rare item generation
                var lootTable = new EnemyLootTable
                {
                    Id = "test_rare_table",
                    Name = "Test Rare Table"
                };

                lootTable.AddItem(new EnemyLootTableItem
                {
                    ItemId = "wolf_fang",
                    Weight = 1.0f,
                    Tier = "Rare",
                    QuantityRange = new List<int> { 1, 1 },
                    DropChance = 1.0f // Guaranteed for testing
                });

                var generatedLoot = lootTable.GenerateLoot();
                if (generatedLoot.Count > 0)
                {
                    Logger.LogInfo("LootSystemTests", $"Generated {generatedLoot.Count} rare items from test table");
                    foreach (var drop in generatedLoot)
                    {
                        Logger.LogInfo("LootSystemTests", $"  - {drop.Item} (Tier: {drop.Tier}, Rare: {drop.IsRare})");
                    }
                }

                _testsPassed++;
                Logger.LogInfo("LootSystemTests", "Rare item generation tests passed");
            }
            catch (Exception ex)
            {
                Logger.LogException("LootSystemTests", ex, "TestRareItemGeneration");
                _testsFailed++;
            }
        }

        /// <summary>
        /// Tests loot explosion visual effects
        /// </summary>
        private void TestLootExplosionEffects()
        {
            Logger.LogInfo("LootSystemTests", "Testing loot explosion effects...");

            try
            {
                // Create multiple items to test explosion effect
                var testDrops = new List<EnhancedLootDrop>
                {
                    new EnhancedLootDrop { Item = "raw_meat", Quantity = new List<int> { 2, 2 }, Tier = "Common" },
                    new EnhancedLootDrop { Item = "bone", Quantity = new List<int> { 1, 1 }, Tier = "Common" },
                    new EnhancedLootDrop { Item = "wolf_fang", Quantity = new List<int> { 1, 1 }, Tier = "Rare", IsRare = true }
                };

                // This would normally be called internally, but we can test the positioning logic
                Vector2 explosionCenter = new Vector2(400, 400);

                // Test that we can create the drops (the visual effects are harder to test automatically)
                if (LootDropSystem.Instance != null)
                {
                    foreach (var drop in testDrops)
                    {
                        LootDropSystem.Instance.CreateSingleItemDrop(drop.Item, drop.Quantity[0], explosionCenter, drop.Tier);
                    }
                    Logger.LogInfo("LootSystemTests", "Created loot explosion effect successfully");
                }

                _testsPassed++;
                Logger.LogInfo("LootSystemTests", "Loot explosion effects tests passed");
            }
            catch (Exception ex)
            {
                Logger.LogException("LootSystemTests", ex, "TestLootExplosionEffects");
                _testsFailed++;
            }
        }

        /// <summary>
        /// Gets the test results
        /// </summary>
        public (bool completed, int passed, int failed) GetTestResults()
        {
            return (_testsCompleted, _testsPassed, _testsFailed);
        }

        public override void _ExitTree()
        {
            Logger.LogInfo("LootSystemTests", "Loot system tests completed and cleaned up");
        }
    }
}