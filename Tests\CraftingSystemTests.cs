using Godot;
using System.Collections.Generic;

namespace SurvivalLooterShooter
{
	/// <summary>
	/// Unit tests for the CraftingSystem class
	/// </summary>
	public partial class CraftingSystemTests : Node
	{
		private CraftingSystem _craftingSystem;
		private Inventory _inventory;
		private ItemDatabase _itemDatabase;

		public override void _Ready()
		{
			GD.Print("Starting CraftingSystem tests...");
			
			// Initialize test environment
			SetupTestEnvironment();
			
			// Run all tests
			RunAllTests();
		}

		private void SetupTestEnvironment()
		{
			// Create test instances
			_inventory = new Inventory();
			_craftingSystem = new CraftingSystem();
			_craftingSystem.SetInventory(_inventory);

			// Create a mock ItemDatabase for testing
			_itemDatabase = new ItemDatabase();
			
			// Add the nodes to the scene tree so they can initialize
			AddChild(_inventory);
			AddChild(_craftingSystem);
			AddChild(_itemDatabase);
		}

		private void RunAllTests()
		{
			TestCanCraftWithSufficientMaterials();
			TestCanCraftWithInsufficientMaterials();
			TestCraftItemSuccess();
			TestCraftItemInsufficientMaterials();
			TestCraftItemInsufficientInventorySpace();
			TestGetMissingMaterials();
			TestGetMissingMaterialsText();
			TestGetAvailableRecipes();
			TestGetRecipesForItem();
			TestCanCraftItem();
			TestGetMaxCraftableQuantity();
			TestCraftingWithInvalidRecipe();
			TestCraftingRollback();
			
			GD.Print("All CraftingSystem tests completed!");
		}

		private void TestCanCraftWithSufficientMaterials()
		{
			GD.Print("Testing CanCraft with sufficient materials...");
			
			// Create a test recipe
			var recipe = new Recipe(
				"test_bandage",
				new List<RecipeInput> 
				{
					new RecipeInput("cloth", 2),
					new RecipeInput("alcohol", 1)
				},
				new RecipeOutput("bandage", 3)
			);

			// Add required materials to inventory
			_inventory.AddItem("cloth", 5);
			_inventory.AddItem("alcohol", 2);

			// Test that we can craft the recipe
			bool canCraft = _craftingSystem.CanCraft(recipe);
			
			if (canCraft)
			{
				GD.Print("✓ CanCraft correctly identified sufficient materials");
			}
			else
			{
				GD.PrintErr("✗ CanCraft failed to identify sufficient materials");
			}

			// Clean up
			_inventory.Clear();
		}

		private void TestCanCraftWithInsufficientMaterials()
		{
			GD.Print("Testing CanCraft with insufficient materials...");
			
			var recipe = new Recipe(
				"test_bandage",
				new List<RecipeInput> 
				{
					new RecipeInput("cloth", 2),
					new RecipeInput("alcohol", 1)
				},
				new RecipeOutput("bandage", 3)
			);

			// Add insufficient materials
			_inventory.AddItem("cloth", 1); // Need 2
			_inventory.AddItem("alcohol", 1); // Have enough

			bool canCraft = _craftingSystem.CanCraft(recipe);
			
			if (!canCraft)
			{
				GD.Print("✓ CanCraft correctly identified insufficient materials");
			}
			else
			{
				GD.PrintErr("✗ CanCraft incorrectly allowed crafting with insufficient materials");
			}

			_inventory.Clear();
		}

		private void TestCraftItemSuccess()
		{
			GD.Print("Testing successful item crafting...");
			
			var recipe = new Recipe(
				"test_bandage",
				new List<RecipeInput> 
				{
					new RecipeInput("cloth", 2),
					new RecipeInput("alcohol", 1)
				},
				new RecipeOutput("bandage", 3)
			);

			// Add required materials
			_inventory.AddItem("cloth", 5);
			_inventory.AddItem("alcohol", 2);

			int clothBefore = _inventory.GetItemQuantity("cloth");
			int alcoholBefore = _inventory.GetItemQuantity("alcohol");
			int bandageBefore = _inventory.GetItemQuantity("bandage");

			bool craftSuccess = _craftingSystem.CraftItem(recipe);

			int clothAfter = _inventory.GetItemQuantity("cloth");
			int alcoholAfter = _inventory.GetItemQuantity("alcohol");
			int bandageAfter = _inventory.GetItemQuantity("bandage");

			if (craftSuccess && 
				clothAfter == clothBefore - 2 && 
				alcoholAfter == alcoholBefore - 1 && 
				bandageAfter == bandageBefore + 3)
			{
				GD.Print("✓ Item crafting successful with correct material consumption and output");
			}
			else
			{
				GD.PrintErr($"✗ Item crafting failed. Success: {craftSuccess}, Cloth: {clothBefore}->{clothAfter}, Alcohol: {alcoholBefore}->{alcoholAfter}, Bandage: {bandageBefore}->{bandageAfter}");
			}

			_inventory.Clear();
		}

		private void TestCraftItemInsufficientMaterials()
		{
			GD.Print("Testing crafting with insufficient materials...");
			
			var recipe = new Recipe(
				"test_bandage",
				new List<RecipeInput> 
				{
					new RecipeInput("cloth", 2),
					new RecipeInput("alcohol", 1)
				},
				new RecipeOutput("bandage", 3)
			);

			// Add insufficient materials
			_inventory.AddItem("cloth", 1);

			bool craftSuccess = _craftingSystem.CraftItem(recipe);

			if (!craftSuccess)
			{
				GD.Print("✓ Crafting correctly failed with insufficient materials");
			}
			else
			{
				GD.PrintErr("✗ Crafting incorrectly succeeded with insufficient materials");
			}

			_inventory.Clear();
		}

		private void TestCraftItemInsufficientInventorySpace()
		{
			GD.Print("Testing crafting with insufficient inventory space...");
			
			// This test would require implementing inventory space limits
			// For now, we'll skip it as the current inventory implementation doesn't have strict space limits
			GD.Print("⚠ Skipping inventory space test - not implemented in current inventory system");
		}

		private void TestGetMissingMaterials()
		{
			GD.Print("Testing GetMissingMaterials...");
			
			var recipe = new Recipe(
				"test_bandage",
				new List<RecipeInput> 
				{
					new RecipeInput("cloth", 5),
					new RecipeInput("alcohol", 3)
				},
				new RecipeOutput("bandage", 3)
			);

			// Add partial materials
			_inventory.AddItem("cloth", 2); // Missing 3
			_inventory.AddItem("alcohol", 3); // Have enough

			var missingMaterials = _craftingSystem.GetMissingMaterials(recipe);

			if (missingMaterials.ContainsKey("cloth") && 
				missingMaterials["cloth"] == 3 && 
				!missingMaterials.ContainsKey("alcohol"))
			{
				GD.Print("✓ GetMissingMaterials correctly identified missing materials");
			}
			else
			{
				GD.PrintErr($"✗ GetMissingMaterials failed. Missing cloth: {missingMaterials.GetValueOrDefault("cloth", 0)}, Missing alcohol: {missingMaterials.GetValueOrDefault("alcohol", 0)}");
			}

			_inventory.Clear();
		}

		private void TestGetMissingMaterialsText()
		{
			GD.Print("Testing GetMissingMaterialsText...");
			
			var recipe = new Recipe(
				"test_bandage",
				new List<RecipeInput> 
				{
					new RecipeInput("cloth", 5),
					new RecipeInput("alcohol", 3)
				},
				new RecipeOutput("bandage", 3)
			);

			// Add partial materials
			_inventory.AddItem("cloth", 2);

			string missingText = _craftingSystem.GetMissingMaterialsText(recipe);

			if (missingText.Contains("Missing:") && missingText.Contains("cloth") && missingText.Contains("alcohol"))
			{
				GD.Print($"✓ GetMissingMaterialsText generated correct text: {missingText}");
			}
			else
			{
				GD.PrintErr($"✗ GetMissingMaterialsText generated incorrect text: {missingText}");
			}

			_inventory.Clear();
		}

		private void TestGetAvailableRecipes()
		{
			GD.Print("Testing GetAvailableRecipes...");
			
			// Add materials for some recipes
			_inventory.AddItem("cloth", 10);
			_inventory.AddItem("alcohol", 5);

			var availableRecipes = _craftingSystem.GetAvailableRecipes();

			// The actual number depends on what's loaded in ItemDatabase
			// For now, just check that the method doesn't crash
			GD.Print($"✓ GetAvailableRecipes returned {availableRecipes.Count} recipes");

			_inventory.Clear();
		}

		private void TestGetRecipesForItem()
		{
			GD.Print("Testing GetRecipesForItem...");
			
			var recipes = _craftingSystem.GetRecipesForItem("bandage");

			// The actual number depends on what's loaded in ItemDatabase
			GD.Print($"✓ GetRecipesForItem returned {recipes.Count} recipes for bandage");
		}

		private void TestCanCraftItem()
		{
			GD.Print("Testing CanCraftItem...");
			
			// Add materials
			_inventory.AddItem("cloth", 10);
			_inventory.AddItem("alcohol", 5);

			bool canCraftBandage = _craftingSystem.CanCraftItem("bandage");

			GD.Print($"✓ CanCraftItem for bandage: {canCraftBandage}");

			_inventory.Clear();
		}

		private void TestGetMaxCraftableQuantity()
		{
			GD.Print("Testing GetMaxCraftableQuantity...");
			
			// Add materials for multiple crafts
			_inventory.AddItem("cloth", 10);
			_inventory.AddItem("alcohol", 5);

			int maxQuantity = _craftingSystem.GetMaxCraftableQuantity("bandage");

			GD.Print($"✓ GetMaxCraftableQuantity for bandage: {maxQuantity}");

			_inventory.Clear();
		}

		private void TestCraftingWithInvalidRecipe()
		{
			GD.Print("Testing crafting with invalid recipe...");
			
			// Test with null recipe
			bool craftSuccess1 = _craftingSystem.CraftItem(null);

			// Test with invalid recipe
			var invalidRecipe = new Recipe("invalid", new List<RecipeInput>(), null);
			bool craftSuccess2 = _craftingSystem.CraftItem(invalidRecipe);

			if (!craftSuccess1 && !craftSuccess2)
			{
				GD.Print("✓ Crafting correctly failed with invalid recipes");
			}
			else
			{
				GD.PrintErr($"✗ Crafting incorrectly succeeded with invalid recipes: {craftSuccess1}, {craftSuccess2}");
			}
		}

		private void TestCraftingRollback()
		{
			GD.Print("Testing crafting rollback...");
			
			// This test would require simulating a failure during crafting
			// For now, we'll just verify the method exists and doesn't crash
			GD.Print("⚠ Crafting rollback test requires more complex failure simulation");
		}
	}
}
