using Godot;
using System;

namespace SurvivalLooterShooter
{
    /// <summary>
    /// Simple test runner for resource harvesting system
    /// </summary>
    public partial class ResourceHarvestingTestRunner : Node
    {
        public override void _Ready()
        {
            GD.Print("=== Resource Harvesting Test Runner ===");
            
            // Run basic functionality tests
            CallDeferred(nameof(RunBasicTests));
        }

        private void RunBasicTests()
        {
            try
            {
                // Test 1: Create ResourceNode
                GD.Print("Test 1: Creating ResourceNode...");
                var resourceNode = new ResourceNode();
                resourceNode.ResourceId = "wood";
                resourceNode.ResourceType = "organic";
                resourceNode.RequiredTool = "axe";
                resourceNode.MinYield = 1;
                resourceNode.MaxYield = 5;
                resourceNode.HarvestTime = 2.0f;
                resourceNode.RegenerationTime = 300f;
                resourceNode.InteractionRange = 50f;
                resourceNode.GlobalPosition = new Vector2(100, 100);
                
                AddChild(resourceNode);
                GD.Print("✅ ResourceNode created successfully");

                // Test 2: Create ResourceHarvestingSystem
                GD.Print("Test 2: Creating ResourceHarvestingSystem...");
                var resourceSystem = new ResourceHarvestingSystem();
                AddChild(resourceSystem);
                GD.Print("✅ ResourceHarvestingSystem created successfully");

                // Test 3: Test resource node properties
                GD.Print("Test 3: Testing ResourceNode properties...");
                GD.Print($"  Resource ID: {resourceNode.ResourceId}");
                GD.Print($"  Resource Type: {resourceNode.ResourceType}");
                GD.Print($"  Required Tool: {resourceNode.RequiredTool}");
                GD.Print($"  Current Yield: {resourceNode.CurrentYield}");
                GD.Print($"  Is Depleted: {resourceNode.IsDepleted}");
                GD.Print($"  Is Being Harvested: {resourceNode.IsBeingHarvested}");
                GD.Print("✅ ResourceNode properties working correctly");

                // Test 4: Test save/load functionality
                GD.Print("Test 4: Testing save/load functionality...");
                var saveData = resourceNode.GetSaveData();
                GD.Print($"  Save data created: ResourceId={saveData.ResourceId}, Position={saveData.Position}");
                GD.Print("✅ Save/load functionality working");

                // Test 5: Test resource system stats
                GD.Print("Test 5: Testing resource system stats...");
                var stats = resourceSystem.GetStats();
                GD.Print($"  Total Nodes: {stats.TotalResourceNodes}");
                GD.Print($"  Active Nodes: {stats.ActiveResourceNodes}");
                GD.Print($"  Resource Types: {stats.ResourceTypes}");
                GD.Print("✅ Resource system stats working");

                GD.Print("\n🎉 All basic tests passed! Resource harvesting system is working correctly.");
                
                // Clean up
                resourceNode.QueueFree();
                resourceSystem.QueueFree();
            }
            catch (Exception ex)
            {
                GD.PrintErr($"❌ Test failed: {ex.Message}");
                GD.PrintErr($"Stack trace: {ex.StackTrace}");
            }
        }
    }
}