using Godot;
using System;
using System.Collections.Generic;

namespace SurvivalLooterShooter
{
    /// <summary>
    /// Tests for the enhanced biome system with resource distribution
    /// </summary>
    public partial class BiomeSystemTests : Node
    {
        private BiomeGenerator _biomeGenerator;
        private WorldManager _worldManager;

        public override void _Ready()
        {
            GD.Print("Starting Biome System Tests...");
            RunTests();
        }

        private void RunTests()
        {
            try
            {
                TestBiomeDataLoading();
                TestBiomeTransitions();
                TestResourceClustering();
                TestWeatherModifiers();
                TestVisualProperties();
                
                GD.Print("✓ All biome system tests passed!");
            }
            catch (Exception ex)
            {
                GD.PrintErr($"✗ Biome system test failed: {ex.Message}");
            }
        }

        /// <summary>
        /// Test that biome data loads correctly from JSON
        /// </summary>
        private void TestBiomeDataLoading()
        {
            _biomeGenerator = new BiomeGenerator();
            AddChild(_biomeGenerator);
            
            // Wait for Ready to be called
            _biomeGenerator._Ready();
            
            // Test that biomes are loaded
            var plainsData = _biomeGenerator.GetBiomeData("plains");
            if (plainsData == null)
                throw new Exception("Plains biome data not loaded");
                
            if (plainsData.Name != "Rolling Plains")
                throw new Exception($"Expected 'Rolling Plains', got '{plainsData.Name}'");
                
            if (plainsData.ResourceSpawns.Count == 0)
                throw new Exception("No resource spawns loaded for plains biome");
                
            GD.Print("✓ Biome data loading test passed");
        }

        /// <summary>
        /// Test biome transition calculations
        /// </summary>
        private void TestBiomeTransitions()
        {
            if (_biomeGenerator == null)
                throw new Exception("BiomeGenerator not initialized");
                
            // Initialize with test seed
            var biomeNoise = new FastNoiseLite();
            var elevationNoise = new FastNoiseLite();
            _biomeGenerator.Initialize(12345, biomeNoise, elevationNoise);
            
            // Test transition factor calculation
            Vector2 pos1 = new Vector2(0, 0);
            Vector2 pos2 = new Vector2(10, 10);
            
            float transitionFactor = _biomeGenerator.GetBiomeTransitionFactor(pos1, pos2);
            
            if (transitionFactor < 0.0f || transitionFactor > 1.0f)
                throw new Exception($"Transition factor out of range: {transitionFactor}");
                
            GD.Print("✓ Biome transitions test passed");
        }

        /// <summary>
        /// Test resource clustering functionality
        /// </summary>
        private void TestResourceClustering()
        {
            if (_biomeGenerator == null)
                throw new Exception("BiomeGenerator not initialized");
                
            // Create a test chunk
            var testChunk = new WorldChunk(new Vector2I(0, 0), 64);
            AddChild(testChunk);
            
            // Generate chunk content
            _biomeGenerator.GenerateChunk(testChunk);
            
            // Check that resources were generated
            var resources = testChunk.GetResources();
            if (resources.Count == 0)
                throw new Exception("No resources generated in test chunk");
                
            // Check for clustering (resources should be grouped)
            bool foundCluster = false;
            foreach (var resource in resources)
            {
                int nearbyCount = 0;
                foreach (var other in resources)
                {
                    if (resource != other && resource.WorldPosition.DistanceTo(other.WorldPosition) < 10.0f)
                    {
                        nearbyCount++;
                    }
                }
                if (nearbyCount > 0)
                {
                    foundCluster = true;
                    break;
                }
            }
            
            if (!foundCluster)
                GD.Print("⚠ Warning: No resource clusters found (may be normal with small test chunk)");
            else
                GD.Print("✓ Resource clustering test passed");
                
            testChunk.QueueFree();
        }

        /// <summary>
        /// Test weather modifier functionality
        /// </summary>
        private void TestWeatherModifiers()
        {
            if (_biomeGenerator == null)
                throw new Exception("BiomeGenerator not initialized");
                
            Vector2 testPos = new Vector2(100, 100);
            var weatherModifiers = _biomeGenerator.GetWeatherModifiers(testPos);
            
            if (weatherModifiers == null)
                throw new Exception("Weather modifiers not returned");
                
            if (weatherModifiers.RainFrequency < 0.0f || weatherModifiers.RainFrequency > 1.0f)
                throw new Exception($"Invalid rain frequency: {weatherModifiers.RainFrequency}");
                
            GD.Print("✓ Weather modifiers test passed");
        }

        /// <summary>
        /// Test visual properties loading
        /// </summary>
        private void TestVisualProperties()
        {
            if (_biomeGenerator == null)
                throw new Exception("BiomeGenerator not initialized");
                
            var plainsData = _biomeGenerator.GetBiomeData("plains");
            if (plainsData?.VisualProperties == null)
                throw new Exception("Visual properties not loaded");
                
            if (string.IsNullOrEmpty(plainsData.VisualProperties.GroundColor))
                throw new Exception("Ground color not set");
                
            if (string.IsNullOrEmpty(plainsData.VisualProperties.ParticleEffect))
                throw new Exception("Particle effect not set");
                
            GD.Print("✓ Visual properties test passed");
        }

        public override void _ExitTree()
        {
            _biomeGenerator?.QueueFree();
        }
    }
}