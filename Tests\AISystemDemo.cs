using Godot;
using System;
using System.Collections.Generic;

namespace SurvivalLooterShooter
{
    /// <summary>
    /// Demo scene for AI system functionality
    /// Shows different AI personalities in action
    /// </summary>
    public partial class AISystemDemo : Node2D
    {
        private List<Enemy> _enemies = new List<Enemy>();
        private CharacterBody2D _player;
        private Label _infoLabel;
        private Camera2D _camera;

        public override void _Ready()
        {
            SetupDemo();
        }

        /// <summary>
        /// Sets up the demo scene
        /// </summary>
        private void SetupDemo()
        {
            // Create camera
            _camera = new Camera2D();
            _camera.Position = new Vector2(960, 540);
            _camera.Zoom = new Vector2(0.8f, 0.8f);
            AddChild(_camera);

            // Create player
            _player = new CharacterBody2D();
            _player.Name = "Player";
            _player.Position = new Vector2(960, 540);
            _player.AddToGroup("player");
            
            // Add player collision
            var playerCollision = new CollisionShape2D();
            var playerShape = new CircleShape2D();
            playerShape.Radius = 20f;
            playerCollision.Shape = playerShape;
            _player.AddChild(playerCollision);
            
            // Add player sprite
            var playerSprite = new Sprite2D();
            var playerTexture = CreateColorTexture(Colors.Blue, 40, 40);
            playerSprite.Texture = playerTexture;
            _player.AddChild(playerSprite);
            
            AddChild(_player);

            // Create info label
            _infoLabel = new Label();
            _infoLabel.Position = new Vector2(50, 50);
            _infoLabel.Text = "AI System Demo\nWASD: Move Player\nWatch different AI behaviors:\nRed: Aggressive, Green: Defensive, Yellow: Territorial, Purple: Pack, Orange: Ambush";
            _infoLabel.AddThemeColorOverride("font_color", Colors.White);
            AddChild(_infoLabel);

            // Create enemies with different AI personalities
            CreateEnemyWithAI("Aggressive", new Vector2(700, 300), Colors.Red, AIController.AIPersonality.Aggressive);
            CreateEnemyWithAI("Defensive", new Vector2(1200, 300), Colors.Green, AIController.AIPersonality.Defensive);
            CreateEnemyWithAI("Territorial", new Vector2(700, 700), Colors.Yellow, AIController.AIPersonality.Territorial);
            CreateEnemyWithAI("Pack1", new Vector2(1200, 700), Colors.Purple, AIController.AIPersonality.Pack);
            CreateEnemyWithAI("Pack2", new Vector2(1250, 750), Colors.Purple, AIController.AIPersonality.Pack);
            CreateEnemyWithAI("Ambush", new Vector2(500, 500), Colors.Orange, AIController.AIPersonality.Ambush);

            Logger.LogInfo("AISystemDemo", "AI System Demo initialized with 6 enemies");
        }

        /// <summary>
        /// Creates an enemy with specific AI personality
        /// </summary>
        private void CreateEnemyWithAI(string name, Vector2 position, Color color, AIController.AIPersonality personality)
        {
            var enemy = new Enemy();
            enemy.Name = name;
            enemy.Position = position;
            AddChild(enemy);

            // Initialize with test data
            var enemyData = new EnemyData
            {
                Id = name.ToLower(),
                Name = name,
                Health = 100f,
                MaxHealth = 100f,
                Damage = 15f,
                Speed = 100f,
                DetectionRange = 200f,
                AttackRange = 50f,
                AIType = personality.ToString().ToLower(),
                Biomes = new List<string> { "demo" },
                LootTable = new List<LootDrop>(),
                ExperienceReward = 10f,
                SpawnWeight = 1f
            };

            enemy.Initialize(enemyData);

            // Change enemy color to match personality
            var sprite = enemy.GetNode<Sprite2D>("Sprite2D");
            if (sprite != null)
            {
                sprite.Texture = CreateColorTexture(color, 32, 32);
            }

            _enemies.Add(enemy);
        }

        /// <summary>
        /// Creates a colored texture
        /// </summary>
        private ImageTexture CreateColorTexture(Color color, int width, int height)
        {
            var image = Image.CreateEmpty(width, height, false, Image.Format.Rgb8);
            image.Fill(color);
            return ImageTexture.CreateFromImage(image);
        }

        public override void _PhysicsProcess(double delta)
        {
            HandlePlayerMovement((float)delta);
            UpdateInfoDisplay();
        }

        /// <summary>
        /// Handles player movement
        /// </summary>
        private void HandlePlayerMovement(float delta)
        {
            Vector2 velocity = Vector2.Zero;
            
            if (Input.IsActionPressed("ui_left") || Input.IsKeyPressed(Key.A))
                velocity.X -= 1;
            if (Input.IsActionPressed("ui_right") || Input.IsKeyPressed(Key.D))
                velocity.X += 1;
            if (Input.IsActionPressed("ui_up") || Input.IsKeyPressed(Key.W))
                velocity.Y -= 1;
            if (Input.IsActionPressed("ui_down") || Input.IsKeyPressed(Key.S))
                velocity.Y += 1;

            velocity = velocity.Normalized() * 200f;
            _player.Velocity = velocity;
            _player.MoveAndSlide();

            // Update camera to follow player
            _camera.Position = _player.Position;
        }

        /// <summary>
        /// Updates the info display with AI states
        /// </summary>
        private void UpdateInfoDisplay()
        {
            string info = "AI System Demo - Enemy States:\n";
            
            foreach (var enemy in _enemies)
            {
                if (enemy == null || enemy.IsQueuedForDeletion()) continue;
                
                var aiController = enemy.GetAIController();
                if (aiController != null)
                {
                    string state = aiController.CurrentState.ToString();
                    string personality = aiController.Personality.ToString();
                    float distance = enemy.GlobalPosition.DistanceTo(_player.GlobalPosition);
                    
                    info += $"{enemy.Name} ({personality}): {state} - Dist: {distance:F0}\n";
                }
            }
            
            info += "\nWASD: Move Player\nESC: Exit Demo";
            _infoLabel.Text = info;
        }

        public override void _Input(InputEvent @event)
        {
            if (@event is InputEventKey keyEvent && keyEvent.Pressed)
            {
                if (keyEvent.Keycode == Key.Escape)
                {
                    GetTree().Quit();
                }
            }
        }

        public override void _Draw()
        {
            // Draw background
            DrawRect(new Rect2(0, 0, 2000, 2000), new Color(0.1f, 0.2f, 0.1f));
            
            // Draw detection ranges for enemies
            foreach (var enemy in _enemies)
            {
                if (enemy == null || enemy.IsQueuedForDeletion()) continue;
                
                var aiController = enemy.GetAIController();
                if (aiController != null)
                {
                    // Draw detection range
                    DrawCircle(enemy.GlobalPosition, aiController.DetectionRange, new Color(1, 1, 1, 0.1f));
                    
                    // Draw attack range
                    DrawCircle(enemy.GlobalPosition, aiController.AttackRange, new Color(1, 0, 0, 0.2f));
                    
                    // Draw line to target if chasing
                    if (aiController.CurrentState == AIController.AIState.Chase && aiController.GetCurrentTarget() != null)
                    {
                        DrawLine(enemy.GlobalPosition, aiController.GetCurrentTarget().GlobalPosition, Colors.Red, 2f);
                    }
                }
            }
        }
    }
}