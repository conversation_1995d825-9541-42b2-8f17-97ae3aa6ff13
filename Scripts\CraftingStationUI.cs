using Godot;
using System;
using System.Collections.Generic;
using System.Linq;

namespace SurvivalLooterShooter
{
    /// <summary>
    /// UI for managing crafting stations, queues, and advanced recipes
    /// </summary>
    public partial class CraftingStationUI : Control
    {
        private Panel _background;
        private Label _stationNameLabel;
        private Label _stationLevelLabel;
        private VBoxContainer _recipeContainer;
        private VBoxContainer _queueContainer;
        private VBoxContainer _selectedRecipeContainer;
        private Label _recipeNameLabel;
        private VBoxContainer _materialsContainer;
        private VBoxContainer _stepsContainer;
        private HBoxContainer _outputContainer;
        private SpinBox _quantitySpinBox;
        private Button _queueButton;
        private Button _upgradeButton;
        private Button _closeButton;
        private ProgressBar _currentCraftingProgress;
        private Label _queueTimeLabel;
        
        private CraftingStation _currentStation;
        private AdvancedRecipe _selectedRecipe;
        private Inventory _inventory;
        private Dictionary<string, Button> _recipeButtons = new Dictionary<string, Button>();
        private List<Control> _queueItems = new List<Control>();
        
        private bool _isVisible = false;

        // Colors for visual feedback
        private readonly Color _availableColor = new Color(0.2f, 0.8f, 0.2f); // Green
        private readonly Color _unavailableColor = new Color(0.8f, 0.2f, 0.2f); // Red
        private readonly Color _lockedColor = new Color(0.5f, 0.5f, 0.5f); // Gray
        private readonly Color _queuedColor = new Color(0.2f, 0.2f, 0.8f); // Blue

        public override void _Ready()
        {
            // Get references to child nodes
            _background = GetNode<Panel>("Background");
            _stationNameLabel = GetNode<Label>("Background/VBoxContainer/Header/StationInfo/StationNameLabel");
            _stationLevelLabel = GetNode<Label>("Background/VBoxContainer/Header/StationInfo/StationLevelLabel");
            _recipeContainer = GetNode<VBoxContainer>("Background/VBoxContainer/MainContainer/RecipeList/RecipeScrollContainer/RecipeContainer");
            _queueContainer = GetNode<VBoxContainer>("Background/VBoxContainer/MainContainer/QueuePanel/QueueScrollContainer/QueueContainer");
            _selectedRecipeContainer = GetNode<VBoxContainer>("Background/VBoxContainer/MainContainer/RecipeDetails/SelectedRecipePanel/SelectedRecipeContainer");
            _recipeNameLabel = GetNode<Label>("Background/VBoxContainer/MainContainer/RecipeDetails/SelectedRecipePanel/SelectedRecipeContainer/RecipeNameLabel");
            _materialsContainer = GetNode<VBoxContainer>("Background/VBoxContainer/MainContainer/RecipeDetails/SelectedRecipePanel/SelectedRecipeContainer/MaterialsContainer");
            _stepsContainer = GetNode<VBoxContainer>("Background/VBoxContainer/MainContainer/RecipeDetails/SelectedRecipePanel/SelectedRecipeContainer/StepsContainer");
            _outputContainer = GetNode<HBoxContainer>("Background/VBoxContainer/MainContainer/RecipeDetails/SelectedRecipePanel/SelectedRecipeContainer/OutputContainer");
            _quantitySpinBox = GetNode<SpinBox>("Background/VBoxContainer/MainContainer/RecipeDetails/SelectedRecipePanel/SelectedRecipeContainer/CraftingControls/QuantitySpinBox");
            _queueButton = GetNode<Button>("Background/VBoxContainer/MainContainer/RecipeDetails/SelectedRecipePanel/SelectedRecipeContainer/CraftingControls/QueueButton");
            _upgradeButton = GetNode<Button>("Background/VBoxContainer/MainContainer/RecipeDetails/SelectedRecipePanel/SelectedRecipeContainer/UpgradeButton");
            _closeButton = GetNode<Button>("Background/CloseButton");
            _currentCraftingProgress = GetNode<ProgressBar>("Background/VBoxContainer/Header/CraftingProgress");
            _queueTimeLabel = GetNode<Label>("Background/VBoxContainer/Header/QueueTimeLabel");
            
            // Initially hide the UI
            Visible = false;
            _isVisible = false;
            
            // Get inventory reference
            _inventory = GetNode<Inventory>("/root/GameManager/Inventory");
        }

        /// <summary>
        /// Opens the crafting station UI for a specific station
        /// </summary>
        public void OpenStation(CraftingStation station)
        {
            _currentStation = station;
            _isVisible = true;
            Visible = true;
            
            if (_currentStation != null)
            {
                // Connect to station events
                _currentStation.CraftingStarted += OnCraftingStarted;
                _currentStation.CraftingCompleted += OnCraftingCompleted;
                _currentStation.CraftingQueueChanged += OnCraftingQueueChanged;
                _currentStation.StationUpgraded += OnStationUpgraded;
                
                UpdateStationInfo();
                UpdateRecipeList();
                UpdateQueue();
                UpdateCraftingProgress();
            }
        }

        /// <summary>
        /// Closes the crafting station UI
        /// </summary>
        public void CloseStation()
        {
            if (_currentStation != null)
            {
                // Disconnect from station events
                _currentStation.CraftingStarted -= OnCraftingStarted;
                _currentStation.CraftingCompleted -= OnCraftingCompleted;
                _currentStation.CraftingQueueChanged -= OnCraftingQueueChanged;
                _currentStation.StationUpgraded -= OnStationUpgraded;
            }
            
            _currentStation = null;
            _selectedRecipe = null;
            _isVisible = false;
            Visible = false;
            
            ClearSelectedRecipe();
        }

        public override void _Process(double delta)
        {
            if (_isVisible && _currentStation != null)
            {
                UpdateCraftingProgress();
                UpdateQueueTime();
            }
        }

        private void UpdateStationInfo()
        {
            if (_currentStation == null) return;
            
            _stationNameLabel.Text = $"{_currentStation.StationType} Station";
            _stationLevelLabel.Text = $"Level {_currentStation.StationLevel}";
            
            // Update upgrade button
            if (_currentStation.GetParent<Structure>()?.CanUpgrade() == true)
            {
                _upgradeButton.Visible = true;
                _upgradeButton.Disabled = false;
                _upgradeButton.Text = "Upgrade Station";
            }
            else
            {
                _upgradeButton.Visible = false;
            }
        }

        private void UpdateRecipeList()
        {
            if (_currentStation == null) return;

            // Clear existing recipe buttons
            foreach (var button in _recipeButtons.Values)
            {
                button.QueueFree();
            }
            _recipeButtons.Clear();

            // Get station-specific recipes
            var stationRecipes = _currentStation.GetAvailableRecipes();
            
            foreach (var recipe in stationRecipes)
            {
                CreateRecipeButton(recipe);
            }
        }

        private void CreateRecipeButton(Recipe recipe)
        {
            var button = new Button();
            
            // Get output item for display
            var outputItem = ItemDatabase.Instance?.GetItem(recipe.Output.Id);
            string displayName = outputItem?.Name ?? recipe.Output.Id;
            
            button.Text = $"{displayName} x{recipe.Output.Amount}";
            button.SizeFlagsHorizontal = Control.SizeFlags.ExpandFill;
            
            // Determine button color based on availability and unlock status
            Color buttonColor = _lockedColor;
            bool canCraft = false;
            
            if (recipe is AdvancedRecipe advancedRecipe)
            {
                if (advancedRecipe.IsUnlocked())
                {
                    canCraft = CanCraftRecipe(recipe);
                    buttonColor = canCraft ? _availableColor : _unavailableColor;
                }
            }
            else
            {
                canCraft = CanCraftRecipe(recipe);
                buttonColor = canCraft ? _availableColor : _unavailableColor;
            }
            
            // Set button style
            var styleBox = new StyleBoxFlat();
            styleBox.BgColor = buttonColor;
            styleBox.BorderWidthLeft = 1;
            styleBox.BorderWidthTop = 1;
            styleBox.BorderWidthRight = 1;
            styleBox.BorderWidthBottom = 1;
            styleBox.BorderColor = Colors.Gray;
            styleBox.CornerRadiusTopLeft = 3;
            styleBox.CornerRadiusTopRight = 3;
            styleBox.CornerRadiusBottomLeft = 3;
            styleBox.CornerRadiusBottomRight = 3;
            
            button.AddThemeStyleboxOverride("normal", styleBox);
            button.AddThemeStyleboxOverride("hover", styleBox);
            button.AddThemeStyleboxOverride("pressed", styleBox);
            
            // Connect button signal
            button.Pressed += () => OnRecipeSelected(recipe);
            
            // Add to container and store reference
            _recipeContainer.AddChild(button);
            _recipeButtons[recipe.Id] = button;
        }

        private void UpdateQueue()
        {
            if (_currentStation == null) return;

            // Clear existing queue items
            foreach (var item in _queueItems)
            {
                item.QueueFree();
            }
            _queueItems.Clear();

            // Add current crafting item
            if (_currentStation.CurrentCrafting != null)
            {
                CreateQueueItem(_currentStation.CurrentCrafting, true);
            }

            // Add queued items
            var queue = _currentStation.CraftingQueue;
            for (int i = 0; i < queue.Count; i++)
            {
                CreateQueueItem(queue[i], false, i);
            }
        }

        private void CreateQueueItem(CraftingQueueItem queueItem, bool isCurrent, int queueIndex = -1)
        {
            var container = new HBoxContainer();
            
            // Recipe name and quantity
            var outputItem = ItemDatabase.Instance?.GetItem(queueItem.Recipe.Output.Id);
            string displayName = outputItem?.Name ?? queueItem.Recipe.Output.Id;
            
            var nameLabel = new Label();
            nameLabel.Text = $"{displayName} x{queueItem.RemainingQuantity}";
            nameLabel.SizeFlagsHorizontal = Control.SizeFlags.ExpandFill;
            container.AddChild(nameLabel);
            
            // Progress bar for current item
            if (isCurrent)
            {
                var progressBar = new ProgressBar();
                progressBar.MinValue = 0;
                progressBar.MaxValue = 100;
                progressBar.Value = queueItem.Progress * 100;
                progressBar.CustomMinimumSize = new Vector2(100, 20);
                container.AddChild(progressBar);
                
                nameLabel.Text += " (Crafting...)";
            }
            else
            {
                // Remove button for queued items
                var removeButton = new Button();
                removeButton.Text = "Remove";
                removeButton.CustomMinimumSize = new Vector2(80, 20);
                removeButton.Pressed += () => RemoveFromQueue(queueIndex);
                container.AddChild(removeButton);
            }
            
            _queueContainer.AddChild(container);
            _queueItems.Add(container);
        }

        private void OnRecipeSelected(Recipe recipe)
        {
            _selectedRecipe = recipe as AdvancedRecipe ?? new AdvancedRecipe
            {
                Id = recipe.Id,
                Inputs = recipe.Inputs,
                Output = recipe.Output,
                CraftingTime = recipe.CraftingTime
            };
            
            UpdateRecipeDetails();
        }

        private void UpdateRecipeDetails()
        {
            if (_selectedRecipe == null)
            {
                ClearSelectedRecipe();
                return;
            }

            // Get output item for display
            var outputItem = ItemDatabase.Instance?.GetItem(_selectedRecipe.Output.Id);
            string displayName = outputItem?.Name ?? _selectedRecipe.Output.Id;
            
            _recipeNameLabel.Text = $"Craft {displayName}";

            // Clear existing displays
            ClearContainer(_materialsContainer);
            ClearContainer(_stepsContainer);
            ClearContainer(_outputContainer);

            // Display required materials
            foreach (var input in _selectedRecipe.Inputs)
            {
                CreateMaterialLabel(input, _materialsContainer);
            }

            // Display intermediate steps if any
            if (_selectedRecipe.IsMultiStep)
            {
                var stepsLabel = new Label();
                stepsLabel.Text = "Multi-Step Process:";
                stepsLabel.AddThemeStyleboxOverride("normal", new StyleBoxFlat { BgColor = Colors.DarkGray });
                _stepsContainer.AddChild(stepsLabel);

                foreach (var step in _selectedRecipe.IntermediateSteps)
                {
                    var stepLabel = new Label();
                    stepLabel.Text = $"Step: {step.StepName}";
                    _stepsContainer.AddChild(stepLabel);

                    foreach (var input in step.Inputs)
                    {
                        CreateMaterialLabel(input, _stepsContainer, "  ");
                    }
                }
            }

            // Display output
            var outputLabel = new Label();
            outputLabel.Text = $"{displayName} x{_selectedRecipe.Output.Amount}";
            _outputContainer.AddChild(outputLabel);

            // Update quantity controls
            _quantitySpinBox.MinValue = 1;
            _quantitySpinBox.MaxValue = Math.Min(10, _selectedRecipe.BatchSize);
            _quantitySpinBox.Value = 1;

            // Update queue button
            bool canQueue = CanCraftRecipe(_selectedRecipe) && _currentStation.QueueSize < _currentStation.MaxQueueSize;
            _queueButton.Disabled = !canQueue;
            _queueButton.Text = canQueue ? "Add to Queue" : "Cannot Queue";
        }

        private void CreateMaterialLabel(RecipeInput input, Container parent, string prefix = "")
        {
            var materialItem = ItemDatabase.Instance?.GetItem(input.Id);
            string materialName = materialItem?.Name ?? input.Id;
            
            int availableQuantity = _inventory?.GetItemQuantity(input.Id) ?? 0;
            int requiredQuantity = input.Amount;
            
            var materialLabel = new Label();
            materialLabel.Text = $"{prefix}{materialName}: {availableQuantity}/{requiredQuantity}";
            
            // Color code based on availability
            if (availableQuantity >= requiredQuantity)
            {
                materialLabel.AddThemeColorOverride("font_color", _availableColor);
            }
            else
            {
                materialLabel.AddThemeColorOverride("font_color", _unavailableColor);
            }
            
            parent.AddChild(materialLabel);
        }

        private void ClearContainer(Container container)
        {
            foreach (Node child in container.GetChildren())
            {
                child.QueueFree();
            }
        }

        private void ClearSelectedRecipe()
        {
            _selectedRecipe = null;
            _recipeNameLabel.Text = "Select a recipe";
            
            ClearContainer(_materialsContainer);
            ClearContainer(_stepsContainer);
            ClearContainer(_outputContainer);
            
            _queueButton.Disabled = true;
            _queueButton.Text = "Add to Queue";
        }

        private bool CanCraftRecipe(Recipe recipe)
        {
            if (_inventory == null || recipe == null) return false;

            foreach (var input in recipe.Inputs)
            {
                if (!_inventory.HasItem(input.Id, input.Amount))
                    return false;
            }

            return true;
        }

        private void UpdateCraftingProgress()
        {
            if (_currentStation?.IsCrafting == true)
            {
                var currentItem = _currentStation.CurrentCrafting;
                if (currentItem != null)
                {
                    float timeRemaining = _currentStation.GetCraftingTimeRemaining();
                    float totalTime = currentItem.Recipe.CraftingTime / _currentStation.EfficiencyMultiplier;
                    float progress = 1.0f - (timeRemaining / totalTime);
                    
                    _currentCraftingProgress.Value = progress * 100;
                    _currentCraftingProgress.Visible = true;
                }
            }
            else
            {
                _currentCraftingProgress.Visible = false;
            }
        }

        private void UpdateQueueTime()
        {
            if (_currentStation != null)
            {
                float totalTime = _currentStation.GetTotalQueueTime();
                _queueTimeLabel.Text = $"Queue Time: {totalTime:F1}s";
            }
        }

        private void RemoveFromQueue(int queueIndex)
        {
            _currentStation?.RemoveFromQueue(queueIndex);
        }

        // Event handlers
        private void OnCraftingStarted(CraftingStation station, string recipeId)
        {
            UpdateQueue();
        }

        private void OnCraftingCompleted(CraftingStation station, string recipeId, string outputItemId, int outputQuantity)
        {
            UpdateQueue();
            UpdateRecipeList(); // Update in case materials changed
        }

        private void OnCraftingQueueChanged(CraftingStation station, int queueSize)
        {
            UpdateQueue();
        }

        private void OnStationUpgraded(CraftingStation station, int newLevel)
        {
            UpdateStationInfo();
            UpdateRecipeList();
        }

        // UI event handlers
        private void _on_queue_button_pressed()
        {
            if (_selectedRecipe != null && _currentStation != null)
            {
                int quantity = (int)_quantitySpinBox.Value;
                _currentStation.QueueRecipe(_selectedRecipe, quantity, _inventory);
            }
        }

        private void _on_upgrade_button_pressed()
        {
            _currentStation?.UpgradeStation(_inventory);
        }

        private void _on_close_button_pressed()
        {
            CloseStation();
        }
    }
}