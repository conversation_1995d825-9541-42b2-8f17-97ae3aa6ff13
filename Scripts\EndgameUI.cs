using Godot;
using System;
using System.Collections.Generic;
using System.Linq;

namespace SurvivalLooterShooter
{
    public partial class EndgameUI : Control
    {
        [Export] public TabContainer TabContainer { get; set; }
        [Export] public Control QuestPanel { get; set; }
        [Export] public Control LeaderboardPanel { get; set; }
        [Export] public Control NGPlusPanel { get; set; }
        [Export] public Control ModPanel { get; set; }

        // Quest UI elements
        [Export] public VBoxContainer ActiveQuestsList { get; set; }
        [Export] public VBoxContainer AvailableQuestsList { get; set; }
        [Export] public RichTextLabel QuestDetailsLabel { get; set; }

        // Leaderboard UI elements
        [Export] public OptionButton LeaderboardCategoryOption { get; set; }
        [Export] public VBoxContainer LeaderboardList { get; set; }
        [Export] public Label PlayerRankLabel { get; set; }
        [Export] public Label PlayerScoreLabel { get; set; }

        // New Game Plus UI elements
        [Export] public Label NGPlusLevelLabel { get; set; }
        [Export] public Label DifficultyMultiplierLabel { get; set; }
        [Export] public Label TotalPlaytimeLabel { get; set; }
        [Export] public Button StartNGPlusButton { get; set; }
        [Export] public VBoxContainer RetainedSkillsList { get; set; }
        [Export] public VBoxContainer PermanentUnlocksList { get; set; }

        // Mod UI elements
        [Export] public VBoxContainer ModsList { get; set; }
        [Export] public Button RefreshModsButton { get; set; }
        [Export] public Button CreateModButton { get; set; }
        [Export] public RichTextLabel ModDetailsLabel { get; set; }

        private PackedScene _questItemScene;
        private PackedScene _leaderboardEntryScene;
        private PackedScene _modItemScene;

        public override void _Ready()
        {
            LoadUIScenes();
            SetupUIConnections();
            RefreshAllPanels();

            // Connect to event bus
            if (EventBus.Instance != null)
            {
                EventBus.Instance.QuestCompleted += OnQuestCompleted;
                EventBus.Instance.QuestFailed += OnQuestFailed;
                EventBus.Instance.NewGamePlusStarted += OnNewGamePlusStarted;
            }
        }

        private void OnQuestFailed(Node quest)
        {
            throw new NotImplementedException();
        }

        private void OnQuestCompleted(Node quest)
        {
            throw new NotImplementedException();
        }

        private void LoadUIScenes()
        {
            // These would be actual scene files in a real implementation
            _questItemScene = GD.Load<PackedScene>("res://UI/QuestItem.tscn");
            _leaderboardEntryScene = GD.Load<PackedScene>("res://UI/LeaderboardEntry.tscn");
            _modItemScene = GD.Load<PackedScene>("res://UI/ModItem.tscn");
        }

        private void SetupUIConnections()
        {
            if (LeaderboardCategoryOption != null)
            {
                LeaderboardCategoryOption.ItemSelected += OnLeaderboardCategoryChanged;
                PopulateLeaderboardCategories();
            }

            if (StartNGPlusButton != null)
            {
                StartNGPlusButton.Pressed += OnStartNGPlusPressed;
            }

            if (RefreshModsButton != null)
            {
                RefreshModsButton.Pressed += OnRefreshModsPressed;
            }

            if (CreateModButton != null)
            {
                CreateModButton.Pressed += OnCreateModPressed;
            }
        }

        private void PopulateLeaderboardCategories()
        {
            if (LeaderboardCategoryOption == null) return;

            LeaderboardCategoryOption.Clear();
            foreach (LeaderboardCategory category in Enum.GetValues<LeaderboardCategory>())
            {
                LeaderboardCategoryOption.AddItem(category.ToString().Replace("_", " "));
            }
        }

        public void RefreshAllPanels()
        {
            RefreshQuestPanel();
            RefreshLeaderboardPanel();
            RefreshNGPlusPanel();
            RefreshModPanel();
        }

        private void RefreshQuestPanel()
        {
            if (QuestManager.Instance == null) return;

            RefreshActiveQuests();
            RefreshAvailableQuests();
        }

        private void RefreshActiveQuests()
        {
            if (ActiveQuestsList == null) return;

            // Clear existing quest items
            foreach (Node child in ActiveQuestsList.GetChildren())
            {
                child.QueueFree();
            }

            var activeQuests = QuestManager.Instance.GetActiveQuests();
            foreach (var quest in activeQuests)
            {
                var questItem = CreateQuestItem(quest, true);
                ActiveQuestsList.AddChild(questItem);
            }
        }

        private void RefreshAvailableQuests()
        {
            if (AvailableQuestsList == null) return;

            // Clear existing quest items
            foreach (Node child in AvailableQuestsList.GetChildren())
            {
                child.QueueFree();
            }

            var availableQuests = QuestManager.Instance.GetAvailableQuests();
            foreach (var quest in availableQuests)
            {
                var questItem = CreateQuestItem(quest, false);
                AvailableQuestsList.AddChild(questItem);
            }
        }

        private Control CreateQuestItem(Quest quest, bool isActive)
        {
            var questItem = new VBoxContainer();

            // Quest title
            var titleLabel = new Label();
            titleLabel.Text = quest.Title;
            titleLabel.AddThemeStyleboxOverride("normal", new StyleBoxFlat());
            questItem.AddChild(titleLabel);

            // Quest description
            var descLabel = new Label();
            descLabel.Text = quest.Description;
            descLabel.AutowrapMode = TextServer.AutowrapMode.WordSmart;
            questItem.AddChild(descLabel);

            // Quest objectives
            foreach (var objective in quest.Objectives)
            {
                var objLabel = new Label();
                objLabel.Text = $"• {objective.Description}: {objective.CurrentAmount}/{objective.RequiredAmount}";
                objLabel.Modulate = objective.IsCompleted ? Colors.Green : Colors.White;
                questItem.AddChild(objLabel);
            }

            // Quest rewards
            var rewardsLabel = new Label();
            var rewardTexts = quest.Rewards.Select(r =>
                r.ExperiencePoints > 0 ? $"{r.ExperiencePoints} XP" : $"{r.Amount}x {r.ItemId}"
            );
            rewardsLabel.Text = "Rewards: " + string.Join(", ", rewardTexts);
            questItem.AddChild(rewardsLabel);

            // Action button
            var actionButton = new Button();
            if (isActive)
            {
                actionButton.Text = "Abandon";
                actionButton.Pressed += () => QuestManager.Instance.AbandonQuest(quest);
            }
            else
            {
                actionButton.Text = "Accept";
                actionButton.Pressed += () => QuestManager.Instance.AcceptQuest(quest);
            }
            questItem.AddChild(actionButton);

            // Time remaining for timed quests
            if (quest.TimeLimit > 0)
            {
                var timeLabel = new Label();
                timeLabel.Text = $"Time remaining: {quest.RemainingTime:F0}s";
                timeLabel.Modulate = quest.RemainingTime < 60 ? Colors.Red : Colors.Yellow;
                questItem.AddChild(timeLabel);
            }

            return questItem;
        }

        private void RefreshLeaderboardPanel()
        {
            if (LeaderboardManager.Instance == null || LeaderboardList == null) return;

            var selectedCategory = (LeaderboardCategory)LeaderboardCategoryOption.Selected;
            var leaderboard = LeaderboardManager.Instance.GetLeaderboard(selectedCategory, 10);

            // Clear existing entries
            foreach (Node child in LeaderboardList.GetChildren())
            {
                child.QueueFree();
            }

            // Add leaderboard entries
            foreach (var entry in leaderboard)
            {
                var entryItem = CreateLeaderboardEntry(entry);
                LeaderboardList.AddChild(entryItem);
            }

            // Update player rank info
            var playerEntry = LeaderboardManager.Instance.GetPlayerRank(selectedCategory);
            if (PlayerRankLabel != null)
            {
                PlayerRankLabel.Text = playerEntry != null ? $"Your Rank: #{playerEntry.Rank}" : "Your Rank: Unranked";
            }
            if (PlayerScoreLabel != null)
            {
                PlayerScoreLabel.Text = playerEntry != null ? $"Your Score: {playerEntry.Score:F0}" : "Your Score: 0";
            }
        }

        private Control CreateLeaderboardEntry(LeaderboardEntry entry)
        {
            var entryContainer = new HBoxContainer();

            // Rank
            var rankLabel = new Label();
            rankLabel.Text = $"#{entry.Rank}";
            rankLabel.CustomMinimumSize = new Vector2(50, 0);
            entryContainer.AddChild(rankLabel);

            // Player name
            var nameLabel = new Label();
            nameLabel.Text = entry.PlayerName;
            nameLabel.SizeFlagsHorizontal = Control.SizeFlags.ExpandFill;
            entryContainer.AddChild(nameLabel);

            // Score
            var scoreLabel = new Label();
            scoreLabel.Text = entry.Score.ToString("F0");
            scoreLabel.CustomMinimumSize = new Vector2(100, 0);
            scoreLabel.HorizontalAlignment = HorizontalAlignment.Right;
            entryContainer.AddChild(scoreLabel);

            // Timestamp
            var timeLabel = new Label();
            timeLabel.Text = entry.Timestamp.ToString("MM/dd/yyyy");
            timeLabel.CustomMinimumSize = new Vector2(80, 0);
            entryContainer.AddChild(timeLabel);

            return entryContainer;
        }

        private void RefreshNGPlusPanel()
        {
            if (NewGamePlusManager.Instance == null) return;

            var ngPlusData = NewGamePlusManager.Instance.NGPlusData;
            var stats = NewGamePlusManager.Instance.GetNGPlusStats();

            if (NGPlusLevelLabel != null)
            {
                NGPlusLevelLabel.Text = $"New Game Plus Level: {ngPlusData.PlusLevel}";
            }

            if (DifficultyMultiplierLabel != null)
            {
                var multiplier = NewGamePlusManager.Instance.GetCurrentDifficultyMultiplier();
                DifficultyMultiplierLabel.Text = $"Difficulty Multiplier: {multiplier:F2}x";
            }

            if (TotalPlaytimeLabel != null)
            {
                var hours = ngPlusData.TotalPlayTime / 3600f;
                TotalPlaytimeLabel.Text = $"Total Playtime: {hours:F1} hours";
            }

            if (StartNGPlusButton != null)
            {
                StartNGPlusButton.Disabled = !NewGamePlusManager.Instance.CanStartNewGamePlus();
                StartNGPlusButton.Text = ngPlusData.PlusLevel >= NewGamePlusManager.Instance.MaxNGPlusLevel
                    ? "Max Level Reached"
                    : "Start New Game Plus";
            }

            RefreshRetainedSkills();
            RefreshPermanentUnlocks();
        }

        private void RefreshRetainedSkills()
        {
            if (RetainedSkillsList == null) return;

            // Clear existing items
            foreach (Node child in RetainedSkillsList.GetChildren())
            {
                child.QueueFree();
            }

            var retainedSkills = NewGamePlusManager.Instance.NGPlusData.RetainedSkills;
            foreach (var skill in retainedSkills)
            {
                var skillLabel = new Label();
                skillLabel.Text = $"{skill.Key}: Level {skill.Value:F1}";
                RetainedSkillsList.AddChild(skillLabel);
            }
        }

        private void RefreshPermanentUnlocks()
        {
            if (PermanentUnlocksList == null) return;

            // Clear existing items
            foreach (Node child in PermanentUnlocksList.GetChildren())
            {
                child.QueueFree();
            }

            var permanentUnlocks = NewGamePlusManager.Instance.NGPlusData.PermanentUnlocks;
            foreach (var unlock in permanentUnlocks)
            {
                var unlockLabel = new Label();
                unlockLabel.Text = unlock.Replace("_", " ");
                PermanentUnlocksList.AddChild(unlockLabel);
            }
        }

        private void RefreshModPanel()
        {
            if (ModManager.Instance == null || ModsList == null) return;

            // Clear existing mod items
            foreach (Node child in ModsList.GetChildren())
            {
                child.QueueFree();
            }

            var loadedMods = ModManager.Instance.GetLoadedMods();
            foreach (var mod in loadedMods)
            {
                var modItem = CreateModItem(mod);
                ModsList.AddChild(modItem);
            }
        }

        private Control CreateModItem(ModInfo mod)
        {
            var modContainer = new VBoxContainer();

            // Mod header
            var headerContainer = new HBoxContainer();

            var enabledCheckbox = new CheckBox();
            enabledCheckbox.ButtonPressed = mod.IsEnabled;
            enabledCheckbox.Toggled += (bool enabled) => ModManager.Instance.EnableMod(mod.ModId, enabled);
            headerContainer.AddChild(enabledCheckbox);

            var nameLabel = new Label();
            nameLabel.Text = $"{mod.Name} v{mod.Version}";
            nameLabel.SizeFlagsHorizontal = Control.SizeFlags.ExpandFill;
            headerContainer.AddChild(nameLabel);

            var authorLabel = new Label();
            authorLabel.Text = $"by {mod.Author}";
            headerContainer.AddChild(authorLabel);

            modContainer.AddChild(headerContainer);

            // Mod description
            var descLabel = new Label();
            descLabel.Text = mod.Description;
            descLabel.AutowrapMode = TextServer.AutowrapMode.WordSmart;
            modContainer.AddChild(descLabel);

            // Dependencies
            if (mod.Dependencies.Count > 0)
            {
                var depsLabel = new Label();
                depsLabel.Text = "Dependencies: " + string.Join(", ", mod.Dependencies);
                depsLabel.Modulate = Colors.Gray;
                modContainer.AddChild(depsLabel);
            }

            return modContainer;
        }

        // Event handlers
        private void OnLeaderboardCategoryChanged(long index)
        {
            RefreshLeaderboardPanel();
        }

        private void OnStartNGPlusPressed()
        {
            if (NewGamePlusManager.Instance.CanStartNewGamePlus())
            {
                // Show confirmation dialog
                var confirmDialog = new ConfirmationDialog();
                confirmDialog.DialogText = "Starting New Game Plus will reset your current progress but retain some skills and unlocks. Continue?";
                confirmDialog.Confirmed += () => NewGamePlusManager.Instance.StartNewGamePlus();
                GetTree().Root.AddChild(confirmDialog);
                confirmDialog.PopupCentered();
            }
        }

        private void OnRefreshModsPressed()
        {
            ModManager.Instance?.RefreshMods();
            RefreshModPanel();
        }

        private void OnCreateModPressed()
        {
            // Show mod creation dialog
            var dialog = new AcceptDialog();
            var vbox = new VBoxContainer();

            var modIdEdit = new LineEdit();
            modIdEdit.PlaceholderText = "Mod ID (e.g., my_awesome_mod)";
            vbox.AddChild(new Label { Text = "Mod ID:" });
            vbox.AddChild(modIdEdit);

            var modNameEdit = new LineEdit();
            modNameEdit.PlaceholderText = "Mod Name";
            vbox.AddChild(new Label { Text = "Mod Name:" });
            vbox.AddChild(modNameEdit);

            var authorEdit = new LineEdit();
            authorEdit.PlaceholderText = "Author Name";
            vbox.AddChild(new Label { Text = "Author:" });
            vbox.AddChild(authorEdit);

            var createButton = new Button();
            createButton.Text = "Create Mod Template";
            createButton.Pressed += () =>
            {
                if (!string.IsNullOrEmpty(modIdEdit.Text) && !string.IsNullOrEmpty(modNameEdit.Text))
                {
                    ModManager.Instance?.CreateModTemplate(modIdEdit.Text, modNameEdit.Text, authorEdit.Text);
                    dialog.Hide();
                    RefreshModPanel();
                }
            };
            vbox.AddChild(createButton);

            dialog.AddChild(vbox);
            GetTree().Root.AddChild(dialog);
            dialog.PopupCentered();
        }

        private void OnQuestCompleted(Quest quest)
        {
            RefreshQuestPanel();

            // Show completion notification
            if (EventBus.Instance != null)
            {
                EventBus.Instance.EmitNotificationRequested($"Quest Completed: {quest.Title}", "success", 5f);
            }
        }

        private void OnQuestFailed(Quest quest)
        {
            RefreshQuestPanel();

            // Show failure notification
            if (EventBus.Instance != null)
            {
                EventBus.Instance.EmitNotificationRequested($"Quest Failed: {quest.Title}", "error", 5f);
            }
        }

        private void OnNewGamePlusStarted(int plusLevel)
        {
            RefreshNGPlusPanel();

            // Show NG+ started notification
            if (EventBus.Instance != null)
            {
                EventBus.Instance.EmitNotificationRequested($"New Game Plus Level {plusLevel} Started!", "info", 10f);
            }
        }

        public override void _Process(double delta)
        {
            // Update quest timers
            if (Visible && TabContainer?.CurrentTab == 0) // Quest tab
            {
                RefreshActiveQuests();
            }
        }
    }
}