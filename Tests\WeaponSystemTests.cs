using Godot;
using System.Collections.Generic;

namespace SurvivalLooterShooter.Tests
{
    /// <summary>
    /// Unit tests for weapon mechanics and ammo management
    /// </summary>
    public partial class WeaponSystemTests : Node
    {
        public override void _Ready()
        {
            // Run all weapon system tests
            RunAllTests();
        }

        private void RunAllTests()
        {
            GD.Print("=== Running Weapon System Tests ===");
            
            // Weapon class tests
            TestWeaponCreation();
            TestWeaponFiring();
            TestWeaponReloading();
            TestWeaponDurability();
            TestWeaponFromItem();
            
            // WeaponController tests
            TestWeaponControllerInitialization();
            TestWeaponControllerFiring();
            TestWeaponControllerReloading();
            TestWeaponSwitching();
            
            // DamageCalculator tests
            TestBasicDamageCalculation();
            TestDistanceFalloff();
            TestTargetTypeModifiers();
            TestCriticalHits();
            TestArmorPenetration();
            
            GD.Print("=== Weapon System Tests Complete ===");
        }

        #region Weapon Class Tests

        private void TestWeaponCreation()
        {
            GD.Print("Testing Weapon creation...");
            
            var weapon = new Weapon("test_rifle", "Test Rifle", "weapon");
            weapon.SetMetadata("damage", 50f);
            weapon.SetMetadata("fire_rate", 0.2f);
            weapon.SetMetadata("ammo_type", "test_ammo");
            weapon.SetMetadata("magazine_size", 20);
            weapon.InitializeFromMetadata();
            
            Assert(weapon.Id == "test_rifle", "Weapon ID should be set correctly");
            Assert(weapon.Name == "Test Rifle", "Weapon name should be set correctly");
            Assert(weapon.Damage == 50f, "Weapon damage should be initialized from metadata");
            Assert(weapon.FireRate == 0.2f, "Weapon fire rate should be initialized from metadata");
            Assert(weapon.AmmoType == "test_ammo", "Weapon ammo type should be initialized from metadata");
            Assert(weapon.MagazineSize == 20, "Weapon magazine size should be initialized from metadata");
            
            GD.Print("✓ Weapon creation tests passed");
        }

        private void TestWeaponFiring()
        {
            GD.Print("Testing Weapon firing...");
            
            var weapon = new Weapon("test_rifle", "Test Rifle", "weapon");
            weapon.SetMetadata("damage", 30f);
            weapon.SetMetadata("magazine_size", 10);
            weapon.SetMetadata("current_ammo", 5);
            weapon.SetMetadata("durability", 100f);
            weapon.InitializeFromMetadata();
            
            // Test successful firing
            Assert(weapon.CanFire(), "Weapon should be able to fire with ammo and durability");
            Assert(weapon.Fire(), "Weapon should fire successfully");
            Assert(weapon.CurrentAmmo == 4, "Ammo should decrease after firing");
            Assert(weapon.Durability == 99f, "Durability should decrease after firing");
            
            // Test firing until empty
            for (int i = 0; i < 4; i++)
            {
                weapon.Fire();
            }
            Assert(weapon.CurrentAmmo == 0, "Weapon should be empty after firing all ammo");
            Assert(!weapon.CanFire(), "Weapon should not be able to fire when empty");
            Assert(!weapon.Fire(), "Weapon should not fire when empty");
            
            GD.Print("✓ Weapon firing tests passed");
        }

        private void TestWeaponReloading()
        {
            GD.Print("Testing Weapon reloading...");
            
            var weapon = new Weapon("test_rifle", "Test Rifle", "weapon");
            weapon.SetMetadata("magazine_size", 10);
            weapon.SetMetadata("current_ammo", 3);
            weapon.InitializeFromMetadata();
            
            // Test reload with sufficient ammo
            int ammoConsumed = weapon.Reload(20);
            Assert(ammoConsumed == 7, "Should consume 7 ammo to fill magazine");
            Assert(weapon.CurrentAmmo == 10, "Magazine should be full after reload");
            
            // Test reload when already full
            ammoConsumed = weapon.Reload(10);
            Assert(ammoConsumed == 0, "Should not consume ammo when magazine is full");
            
            // Test reload with insufficient ammo
            weapon.SetMetadata("current_ammo", 2);
            weapon.InitializeFromMetadata();
            ammoConsumed = weapon.Reload(5);
            Assert(ammoConsumed == 5, "Should consume all available ammo");
            Assert(weapon.CurrentAmmo == 7, "Should have 7 ammo after partial reload");
            
            GD.Print("✓ Weapon reloading tests passed");
        }

        private void TestWeaponDurability()
        {
            GD.Print("Testing Weapon durability...");
            
            var weapon = new Weapon("test_rifle", "Test Rifle", "weapon");
            weapon.SetMetadata("damage", 50f);
            weapon.SetMetadata("max_durability", 100f);
            weapon.SetMetadata("durability", 100f);
            weapon.SetMetadata("current_ammo", 50);
            weapon.InitializeFromMetadata();
            
            float initialDamage = weapon.GetEffectiveDamage();
            Assert(initialDamage == 50f, "Full durability should give full damage");
            
            // Reduce durability to 50%
            weapon.SetMetadata("durability", 50f);
            weapon.InitializeFromMetadata();
            float halfDamage = weapon.GetEffectiveDamage();
            Assert(halfDamage == 25f, "Half durability should give half damage");
            
            // Test broken weapon
            weapon.SetMetadata("durability", 0f);
            weapon.InitializeFromMetadata();
            Assert(weapon.IsBroken(), "Weapon should be broken at 0 durability");
            Assert(!weapon.CanFire(), "Broken weapon should not be able to fire");
            
            float brokenDamage = weapon.GetEffectiveDamage();
            Assert(brokenDamage == 5f, "Broken weapon should still do minimum 10% damage");
            
            // Test repair
            weapon.Repair(30f);
            Assert(weapon.Durability == 30f, "Weapon should be repaired");
            Assert(!weapon.IsBroken(), "Repaired weapon should not be broken");
            
            GD.Print("✓ Weapon durability tests passed");
        }

        private void TestWeaponFromItem()
        {
            GD.Print("Testing Weapon from Item...");
            
            var item = new Item("assault_rifle", "Assault Rifle", "weapon");
            item.SetMetadata("damage", 35f);
            item.SetMetadata("fire_rate", 0.1f);
            item.SetMetadata("ammo_type", "rifle_ammo");
            item.SetMetadata("magazine_size", 30);
            
            var weapon = Weapon.FromItem(item);
            Assert(weapon != null, "Should create weapon from item");
            Assert(weapon.Id == "assault_rifle", "Weapon should have correct ID");
            Assert(weapon.Damage == 35f, "Weapon should have correct damage");
            Assert(weapon.AmmoType == "rifle_ammo", "Weapon should have correct ammo type");
            
            // Test with non-weapon item
            var nonWeapon = new Item("bandage", "Bandage", "consumable");
            var nullWeapon = Weapon.FromItem(nonWeapon);
            Assert(nullWeapon == null, "Should not create weapon from non-weapon item");
            
            GD.Print("✓ Weapon from Item tests passed");
        }

        #endregion

        #region WeaponController Tests

        private void TestWeaponControllerInitialization()
        {
            GD.Print("Testing WeaponController initialization...");
            
            var inventory = new Inventory();
            var weaponController = new WeaponController();
            AddChild(weaponController);
            
            weaponController.Initialize(inventory);
            
            Assert(weaponController.CurrentWeapon == null, "Should have no weapon initially");
            Assert(!weaponController.IsReloading, "Should not be reloading initially");
            
            weaponController.QueueFree();
            GD.Print("✓ WeaponController initialization tests passed");
        }

        private void TestWeaponControllerFiring()
        {
            GD.Print("Testing WeaponController firing...");
            
            // Setup
            var inventory = new Inventory();
            var weaponController = new WeaponController();
            AddChild(weaponController);
            weaponController.Initialize(inventory);
            
            // Add weapon to inventory and equip it
            var weaponMetadata = new Dictionary<string, object>
            {
                ["current_ammo"] = 10,
                ["durability"] = 100f
            };
            inventory.AddItem("assault_rifle", 1, weaponMetadata);
            inventory.EquipItem("assault_rifle", "weapon");
            
            // Allow weapon to be equipped (process immediately)
            weaponController._Process(0.016); // Simulate one frame
            
            Assert(weaponController.CurrentWeapon != null, "Should have equipped weapon");
            
            // Test firing
            bool fired = weaponController.Fire();
            Assert(fired, "Should be able to fire weapon");
            
            weaponController.QueueFree();
            GD.Print("✓ WeaponController firing tests passed");
        }

        private void TestWeaponControllerReloading()
        {
            GD.Print("Testing WeaponController reloading...");
            
            // Setup
            var inventory = new Inventory();
            var weaponController = new WeaponController();
            AddChild(weaponController);
            weaponController.Initialize(inventory);
            
            // Add weapon and ammo to inventory
            var weaponMetadata = new Dictionary<string, object>
            {
                ["current_ammo"] = 5,
                ["durability"] = 100f
            };
            inventory.AddItem("assault_rifle", 1, weaponMetadata);
            inventory.AddItem("rifle_ammo", 50);
            inventory.EquipItem("assault_rifle", "weapon");
            
            // Allow weapon to be equipped (process immediately)
            weaponController._Process(0.016); // Simulate one frame
            
            // Test reload start
            bool reloadStarted = weaponController.StartReload();
            Assert(reloadStarted, "Should be able to start reload");
            Assert(weaponController.IsReloading, "Should be in reloading state");
            
            weaponController.QueueFree();
            GD.Print("✓ WeaponController reloading tests passed");
        }

        private void TestWeaponSwitching()
        {
            GD.Print("Testing Weapon switching...");
            
            // Setup
            var inventory = new Inventory();
            var weaponController = new WeaponController();
            AddChild(weaponController);
            weaponController.Initialize(inventory);
            
            // Add multiple weapons
            inventory.AddItem("assault_rifle", 1);
            
            // Test switching to weapon
            bool switched = weaponController.SwitchWeapon("assault_rifle");
            Assert(switched, "Should be able to switch to available weapon");
            
            // Test switching to non-existent weapon
            bool failedSwitch = weaponController.SwitchWeapon("nonexistent_weapon");
            Assert(!failedSwitch, "Should not be able to switch to non-existent weapon");
            
            weaponController.QueueFree();
            GD.Print("✓ Weapon switching tests passed");
        }

        #endregion

        #region DamageCalculator Tests

        private void TestBasicDamageCalculation()
        {
            GD.Print("Testing Basic damage calculation...");
            
            var weapon = new Weapon("test_rifle", "Test Rifle", "weapon");
            weapon.SetMetadata("damage", 50f);
            weapon.SetMetadata("durability", 100f);
            weapon.SetMetadata("max_durability", 100f);
            weapon.SetMetadata("range", 100f);
            weapon.InitializeFromMetadata();
            
            float damage = DamageCalculator.CalculateDamage(weapon);
            Assert(damage == 50f, "Basic damage should equal weapon damage");
            
            // Test with null weapon
            float nullDamage = DamageCalculator.CalculateDamage(null);
            Assert(nullDamage == 0f, "Null weapon should do no damage");
            
            GD.Print("✓ Basic damage calculation tests passed");
        }

        private void TestDistanceFalloff()
        {
            GD.Print("Testing Distance falloff...");
            
            var weapon = new Weapon("test_rifle", "Test Rifle", "weapon");
            weapon.SetMetadata("damage", 100f);
            weapon.SetMetadata("durability", 100f);
            weapon.SetMetadata("max_durability", 100f);
            weapon.SetMetadata("range", 100f);
            weapon.InitializeFromMetadata();
            
            // Test close range (no falloff)
            float closeDamage = DamageCalculator.CalculateDamage(weapon, "enemy", 10f);
            Assert(closeDamage == 100f, "Close range should have no damage falloff");
            
            // Test max range (minimum damage)
            float maxRangeDamage = DamageCalculator.CalculateDamage(weapon, "enemy", 100f);
            Assert(maxRangeDamage >= 10f, "Max range should have minimum 10% damage");
            
            // Test beyond max range
            float beyondRangeDamage = DamageCalculator.CalculateDamage(weapon, "enemy", 150f);
            Assert(beyondRangeDamage >= 10f, "Beyond max range should still have minimum damage");
            
            GD.Print("✓ Distance falloff tests passed");
        }

        private void TestTargetTypeModifiers()
        {
            GD.Print("Testing Target type modifiers...");
            
            var automaticWeapon = new Weapon("auto_rifle", "Auto Rifle", "weapon");
            automaticWeapon.SetMetadata("damage", 100f);
            automaticWeapon.SetMetadata("durability", 100f);
            automaticWeapon.SetMetadata("max_durability", 100f);
            automaticWeapon.SetMetadata("weapon_type", "automatic");
            automaticWeapon.InitializeFromMetadata();
            
            float enemyDamage = DamageCalculator.CalculateDamage(automaticWeapon, "enemy");
            float structureDamage = DamageCalculator.CalculateDamage(automaticWeapon, "structure");
            
            Assert(enemyDamage == 100f, "Automatic weapon should do full damage to enemies");
            Assert(structureDamage == 70f, "Automatic weapon should do reduced damage to structures");
            
            GD.Print("✓ Target type modifier tests passed");
        }

        private void TestCriticalHits()
        {
            GD.Print("Testing Critical hits...");
            
            // Test guaranteed critical hit
            bool isCritical = DamageCalculator.IsCriticalHit(1f); // 100% chance
            Assert(isCritical, "100% critical chance should always crit");
            
            // Test no critical hit
            bool isNotCritical = DamageCalculator.IsCriticalHit(0f); // 0% chance
            Assert(!isNotCritical, "0% critical chance should never crit");
            
            // Test critical damage calculation
            float critDamage = DamageCalculator.CalculateCriticalDamage(100f, 2f);
            Assert(critDamage == 200f, "Critical hit should double damage");
            
            GD.Print("✓ Critical hit tests passed");
        }

        private void TestArmorPenetration()
        {
            GD.Print("Testing Armor penetration...");
            
            // Test damage without armor
            float noArmorDamage = DamageCalculator.CalculateArmorPenetration(100f, 0f);
            Assert(noArmorDamage == 100f, "No armor should not reduce damage");
            
            // Test damage with armor
            float armorDamage = DamageCalculator.CalculateArmorPenetration(100f, 50f);
            Assert(armorDamage < 100f, "Armor should reduce damage");
            Assert(armorDamage > 0f, "Armor should not completely negate damage");
            
            // Test armor penetration
            float penetrationDamage = DamageCalculator.CalculateArmorPenetration(100f, 50f, 25f);
            Assert(penetrationDamage > armorDamage, "Armor penetration should increase damage");
            
            GD.Print("✓ Armor penetration tests passed");
        }

        #endregion

        #region Test Utilities

        private void Assert(bool condition, string message)
        {
            if (!condition)
            {
                GD.PrintErr($"ASSERTION FAILED: {message}");
            }
        }

        #endregion
    }
}