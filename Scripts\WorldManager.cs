using Godot;
using System;
using System.Collections.Generic;

namespace SurvivalLooterShooter
{
    /// <summary>
    /// WorldManager singleton coordinates world generation, chunk loading, and biome management
    /// Handles procedural world creation and efficient streaming of world content
    /// </summary>
    public partial class WorldManager : Node
    {
        private static WorldManager _instance;
        public static WorldManager Instance => _instance;

        // World generation parameters
        [Export] public int WorldSeed { get; set; } = 12345;
        [Export] public int ChunkSize { get; set; } = 64; // Size of each world chunk in units
        [Export] public int RenderDistance { get; set; } = 3; // Chunks to keep loaded around player
        [Export] public float BiomeScale { get; set; } = 0.01f; // Scale for biome noise generation

        // Core systems
        private BiomeGenerator _biomeGenerator;
        private ChunkLoader _chunkLoader;
        private POIGenerator _poiGenerator;
        private Dictionary<Vector2I, WorldChunk> _loadedChunks = new Dictionary<Vector2I, WorldChunk>();
        private Vector2I _lastPlayerChunk = Vector2I.Zero;

        // World state
        private bool _worldGenerated = false;
        private FastNoiseLite _biomeNoise;
        private FastNoiseLite _elevationNoise;

        // Events for world changes
        [Signal]
        public delegate void ChunkLoadedEventHandler(Vector2I chunkCoords);
        
        [Signal]
        public delegate void ChunkUnloadedEventHandler(Vector2I chunkCoords);
        
        [Signal]
        public delegate void BiomeChangedEventHandler(string oldBiome, string newBiome, Vector2 position);

        public bool IsWorldGenerated => _worldGenerated;
        public Dictionary<Vector2I, WorldChunk> LoadedChunks => _loadedChunks;

        public override void _Ready()
        {
            if (_instance == null)
            {
                _instance = this;
                AddToGroup("world_manager");
                InitializeWorldManager();
                GD.Print("WorldManager singleton initialized");
            }
            else
            {
                GD.PrintErr("Multiple WorldManager instances detected! Removing duplicate.");
                QueueFree();
            }
        }

        /// <summary>
        /// Initializes the world manager and its subsystems
        /// </summary>
        private void InitializeWorldManager()
        {
            // Initialize noise generators
            _biomeNoise = new FastNoiseLite();
            _biomeNoise.Seed = WorldSeed;
            _biomeNoise.NoiseType = FastNoiseLite.NoiseTypeEnum.Simplex;
            _biomeNoise.Frequency = BiomeScale;

            _elevationNoise = new FastNoiseLite();
            _elevationNoise.Seed = WorldSeed + 1000;
            _elevationNoise.NoiseType = FastNoiseLite.NoiseTypeEnum.Perlin;
            _elevationNoise.Frequency = BiomeScale * 2.0f;

            // Create subsystems
            _biomeGenerator = new BiomeGenerator();
            AddChild(_biomeGenerator);
            _biomeGenerator.Initialize(WorldSeed, _biomeNoise, _elevationNoise);

            _chunkLoader = new ChunkLoader();
            AddChild(_chunkLoader);
            _chunkLoader.Initialize(ChunkSize, RenderDistance);

            // Create POI generator
            _poiGenerator = new POIGenerator();
            AddChild(_poiGenerator);
            _poiGenerator.Initialize(WorldSeed);

            // Connect to chunk loader events
            _chunkLoader.ChunkRequested += OnChunkRequested;
            _chunkLoader.ChunkUnloadRequested += OnChunkUnloadRequested;
        }

        /// <summary>
        /// Generates the initial world with the specified seed
        /// </summary>
        public void GenerateWorld(int seed = -1)
        {
            if (seed != -1)
            {
                WorldSeed = seed;
                _biomeNoise.Seed = seed;
                _elevationNoise.Seed = seed + 1000;
                _biomeGenerator.UpdateSeed(seed);
            }

            _worldGenerated = true;
            
            // Generate initial chunks around spawn point (0, 0)
            Vector2I spawnChunk = WorldToChunkCoords(Vector2.Zero);
            LoadChunksAroundPosition(Vector2.Zero);

            GD.Print($"World generated with seed: {WorldSeed}");
            
            // Emit event for other systems
            EventBus.Instance?.EmitSignal(EventBus.SignalName.GameStateChanged, "generating", "world_ready");
        }

        /// <summary>
        /// Updates world loading based on player position
        /// </summary>
        public void UpdateWorldLoading(Vector2 playerPosition)
        {
            Vector2I currentChunk = WorldToChunkCoords(playerPosition);
            
            if (currentChunk != _lastPlayerChunk)
            {
                _lastPlayerChunk = currentChunk;
                LoadChunksAroundPosition(playerPosition);
                UnloadDistantChunks(playerPosition);
            }
        }

        /// <summary>
        /// Loads chunks around the specified world position
        /// </summary>
        private void LoadChunksAroundPosition(Vector2 worldPosition)
        {
            Vector2I centerChunk = WorldToChunkCoords(worldPosition);
            
            for (int x = -RenderDistance; x <= RenderDistance; x++)
            {
                for (int y = -RenderDistance; y <= RenderDistance; y++)
                {
                    Vector2I chunkCoords = centerChunk + new Vector2I(x, y);
                    
                    if (!_loadedChunks.ContainsKey(chunkCoords))
                    {
                        LoadChunk(chunkCoords);
                    }
                }
            }
        }

        /// <summary>
        /// Unloads chunks that are too far from the player
        /// </summary>
        private void UnloadDistantChunks(Vector2 playerPosition)
        {
            Vector2I playerChunk = WorldToChunkCoords(playerPosition);
            List<Vector2I> chunksToUnload = new List<Vector2I>();

            foreach (var chunkCoords in _loadedChunks.Keys)
            {
                float distance = chunkCoords.DistanceTo(playerChunk);
                if (distance > RenderDistance + 1) // Add buffer to prevent constant loading/unloading
                {
                    chunksToUnload.Add(chunkCoords);
                }
            }

            foreach (var chunkCoords in chunksToUnload)
            {
                UnloadChunk(chunkCoords);
            }
        }

        /// <summary>
        /// Loads a specific chunk at the given coordinates
        /// </summary>
        private void LoadChunk(Vector2I chunkCoords)
        {
            if (_loadedChunks.ContainsKey(chunkCoords))
                return;

            // Create new chunk
            var chunk = new WorldChunk(chunkCoords, ChunkSize);
            
            // Generate chunk content using biome generator
            _biomeGenerator.GenerateChunk(chunk);
            
            // Generate POIs for the chunk
            _poiGenerator.GeneratePOIsForChunk(chunk);
            
            // Add to loaded chunks
            _loadedChunks[chunkCoords] = chunk;
            
            // Add chunk to scene tree for rendering
            AddChild(chunk);
            
            // Emit event
            EmitSignal(SignalName.ChunkLoaded, chunkCoords);
            EventBus.Instance?.EmitSignal(EventBus.SignalName.GameStateChanged, "loading_chunk", "chunk_loaded");
            
            GD.Print($"Loaded chunk at {chunkCoords}");
        }

        /// <summary>
        /// Unloads a specific chunk at the given coordinates
        /// </summary>
        private void UnloadChunk(Vector2I chunkCoords)
        {
            if (!_loadedChunks.TryGetValue(chunkCoords, out WorldChunk chunk))
                return;

            // Remove from loaded chunks
            _loadedChunks.Remove(chunkCoords);
            
            // Remove from scene tree
            chunk.QueueFree();
            
            // Emit event
            EmitSignal(SignalName.ChunkUnloaded, chunkCoords);
            
            GD.Print($"Unloaded chunk at {chunkCoords}");
        }

        /// <summary>
        /// Converts world coordinates to chunk coordinates
        /// </summary>
        public Vector2I WorldToChunkCoords(Vector2 worldPosition)
        {
            return new Vector2I(
                Mathf.FloorToInt(worldPosition.X / ChunkSize),
                Mathf.FloorToInt(worldPosition.Y / ChunkSize)
            );
        }

        /// <summary>
        /// Converts chunk coordinates to world position (chunk center)
        /// </summary>
        public Vector2 ChunkToWorldCoords(Vector2I chunkCoords)
        {
            return new Vector2(
                chunkCoords.X * ChunkSize + ChunkSize * 0.5f,
                chunkCoords.Y * ChunkSize + ChunkSize * 0.5f
            );
        }

        /// <summary>
        /// Gets the biome type at a specific world position
        /// </summary>
        public BiomeType GetBiomeAt(Vector2 worldPosition)
        {
            return _biomeGenerator?.GetBiomeAt(worldPosition) ?? BiomeType.Plains;
        }

        /// <summary>
        /// Gets the elevation at a specific world position
        /// </summary>
        public float GetElevationAt(Vector2 worldPosition)
        {
            return _elevationNoise.GetNoise2D(worldPosition.X, worldPosition.Y);
        }

        /// <summary>
        /// Gets the chunk containing the specified world position
        /// </summary>
        public WorldChunk GetChunkAt(Vector2 worldPosition)
        {
            Vector2I chunkCoords = WorldToChunkCoords(worldPosition);
            _loadedChunks.TryGetValue(chunkCoords, out WorldChunk chunk);
            return chunk;
        }

        /// <summary>
        /// Checks if a chunk is loaded at the specified coordinates
        /// </summary>
        public bool IsChunkLoaded(Vector2I chunkCoords)
        {
            return _loadedChunks.ContainsKey(chunkCoords);
        }

        /// <summary>
        /// Gets world generation data for saving
        /// </summary>
        public WorldSaveData GetWorldSaveData()
        {
            return new WorldSaveData
            {
                Seed = WorldSeed,
                GeneratedChunks = new List<Vector2I>(_loadedChunks.Keys),
                LastPlayerPosition = _lastPlayerChunk
            };
        }

        /// <summary>
        /// Loads world data from save
        /// </summary>
        public void LoadWorldSaveData(WorldSaveData saveData)
        {
            WorldSeed = saveData.Seed;
            _lastPlayerChunk = saveData.LastPlayerPosition;
            
            // Regenerate world with saved seed
            GenerateWorld(WorldSeed);
        }

        #region Event Handlers

        private void OnChunkRequested(Vector2I chunkCoords)
        {
            LoadChunk(chunkCoords);
        }

        private void OnChunkUnloadRequested(Vector2I chunkCoords)
        {
            UnloadChunk(chunkCoords);
        }

        #endregion

        public override void _ExitTree()
        {
            if (_instance == this)
            {
                _instance = null;
            }
        }

        internal Rect2 GetWorldBounds()
        {
            throw new NotImplementedException();
        }

        internal void GenerateNewWorld(int newSeed)
        {
            throw new NotImplementedException();
        }

        internal void EnableFastTravel()
        {
            throw new NotImplementedException();
        }
    }

    /// <summary>
    /// Represents different biome types in the world
    /// </summary>
    public enum BiomeType
    {
        Plains,
        Forest,
        Desert,
        Mountains,
        Swamp,
        Tundra,
        Ocean
    }

    /// <summary>
    /// Data structure for saving world state
    /// </summary>
    [System.Serializable]
    public class WorldSaveData
    {
        public int Seed { get; set; }
        public List<Vector2I> GeneratedChunks { get; set; } = new List<Vector2I>();
        public Vector2I LastPlayerPosition { get; set; }
        // Endgame content support methods
        public Rect2 GetWorldBounds()
        {
            // Return the bounds of the generated world
            return new Rect2(-2000, -2000, 4000, 4000);
        }

        public void GenerateNewWorld(int seed)
        {
            _worldSeed = seed;
            GD.Print($"Generating new world with seed: {seed}");
        }

        public void EnableFastTravel()
        {
            _fastTravelEnabled = true;
            GD.Print("Fast travel system enabled");
        }

        private int _worldSeed = 0;
        private bool _fastTravelEnabled = false;
    }

    /// <summary>
    /// Data structure for saving world state
}