using Godot;
using System;
using System.Collections.Generic;

namespace SurvivalLooterShooter
{
    /// <summary>
    /// Represents an item that can be picked up from the world
    /// Handles visual feedback, interaction detection, and inventory integration
    /// </summary>
    public partial class ItemPickup : Area2D
    {
        // Item data
        [Export] public string ItemId { get; set; } = "";
        [Export] public int Quantity { get; set; } = 1;
        public Dictionary<string, object> ItemMetadata { get; set; } = new Dictionary<string, object>();
        
        // Visual components
        private Sprite2D _sprite;
        private Label _quantityLabel;
        private AnimationPlayer _animationPlayer;
        
        // Interaction state
        private bool _isPlayerNearby = false;
        private bool _isPickedUp = false;
        private PlayerController _nearbyPlayer = null;
        
        // Visual feedback
        [Export] public Color HighlightColor { get; set; } = Colors.Yellow;
        [Export] public float HighlightIntensity { get; set; } = 1.5f;
        [Export] public float BobSpeed { get; set; } = 2.0f;
        [Export] public float BobHeight { get; set; } = 10.0f;
        
        // Pickup settings
        [Export] public bool AutoPickup { get; set; } = false;
        [Export] public float AutoPickupDelay { get; set; } = 0.5f;
        [Export] public float InteractionRange { get; set; } = 100.0f;
        
        // Events
        [Signal]
        public delegate void ItemPickedUpEventHandler(string itemId, int quantity, PlayerController player);
        
        [Signal]
        public delegate void PlayerEnteredRangeEventHandler(PlayerController player);
        
        [Signal]
        public delegate void PlayerExitedRangeEventHandler(PlayerController player);

        private float _bobTimer = 0.0f;
        private Vector2 _originalPosition;
        private Timer _autoPickupTimer;

        public override void _Ready()
        {
            // Set up collision detection
            BodyEntered += OnBodyEntered;
            BodyExited += OnBodyExited;
            
            // Store original position for bobbing animation
            _originalPosition = Position;
            
            // Set up visual components
            SetupVisualComponents();
            
            // Set up auto pickup timer if needed
            if (AutoPickup)
            {
                SetupAutoPickupTimer();
            }
            
            // Validate item data
            ValidateItemData();
            
            GD.Print($"ItemPickup created: {ItemId} x{Quantity}");
        }

        /// <summary>
        /// Sets up the visual components for the item pickup
        /// </summary>
        private void SetupVisualComponents()
        {
            // Create sprite if not already present
            _sprite = GetNode<Sprite2D>("Sprite2D");
            if (_sprite == null)
            {
                _sprite = new Sprite2D();
                _sprite.Name = "Sprite2D";
                AddChild(_sprite);
            }
            
            // Create quantity label if not already present
            _quantityLabel = GetNode<Label>("QuantityLabel");
            if (_quantityLabel == null)
            {
                _quantityLabel = new Label();
                _quantityLabel.Name = "QuantityLabel";
                _quantityLabel.Position = new Vector2(-20, -40);
                _quantityLabel.AddThemeStyleboxOverride("normal", new StyleBoxFlat());
                AddChild(_quantityLabel);
            }
            
            // Try to get animation player
            _animationPlayer = GetNode<AnimationPlayer>("AnimationPlayer");
            
            // Update visual display
            UpdateVisualDisplay();
        }

        /// <summary>
        /// Sets up the auto pickup timer
        /// </summary>
        private void SetupAutoPickupTimer()
        {
            _autoPickupTimer = new Timer();
            _autoPickupTimer.Name = "AutoPickupTimer";
            _autoPickupTimer.WaitTime = AutoPickupDelay;
            _autoPickupTimer.OneShot = true;
            _autoPickupTimer.Timeout += OnAutoPickupTimeout;
            AddChild(_autoPickupTimer);
        }

        /// <summary>
        /// Validates the item data and logs warnings for invalid data
        /// </summary>
        private void ValidateItemData()
        {
            if (string.IsNullOrEmpty(ItemId))
            {
                GD.PrintErr("ItemPickup: ItemId is empty or null");
                return;
            }
            
            var item = ItemDatabase.Instance?.GetItem(ItemId);
            if (item == null)
            {
                GD.PrintErr($"ItemPickup: Item '{ItemId}' not found in database");
                return;
            }
            
            if (Quantity <= 0)
            {
                GD.PrintErr($"ItemPickup: Invalid quantity {Quantity} for item {ItemId}");
                Quantity = 1;
            }
            
            if (Quantity > item.MaxStack)
            {
                GD.PrintErr($"ItemPickup: Quantity {Quantity} exceeds max stack {item.MaxStack} for item {ItemId}");
                Quantity = item.MaxStack;
            }
        }

        /// <summary>
        /// Updates the visual display based on item data
        /// </summary>
        private void UpdateVisualDisplay()
        {
            var item = ItemDatabase.Instance?.GetItem(ItemId);
            if (item == null) return;
            
            // Update quantity label
            if (_quantityLabel != null)
            {
                if (Quantity > 1)
                {
                    _quantityLabel.Text = Quantity.ToString();
                    _quantityLabel.Visible = true;
                }
                else
                {
                    _quantityLabel.Visible = false;
                }
            }
            
            // Set sprite texture based on item type (placeholder logic)
            if (_sprite != null)
            {
                // This would normally load the actual item texture
                // For now, we'll use a simple colored rectangle as placeholder
                var texture = new ImageTexture();
                var image = Image.CreateEmpty(32, 32, false, Image.Format.Rgba8);
                
                // Color based on item type
                Color itemColor = item.Type switch
                {
                    "weapon" => Colors.Red,
                    "consumable" => Colors.Green,
                    "material" => Colors.Blue,
                    "tool" => Colors.Orange,
                    _ => Colors.White
                };
                
                image.Fill(itemColor);
                texture.SetImage(image);
                _sprite.Texture = texture;
            }
        }

        public override void _Process(double delta)
        {
            if (_isPickedUp) return;
            
            // Handle bobbing animation
            HandleBobAnimation((float)delta);
            
            // Handle highlight effect when player is nearby
            HandleHighlightEffect();
        }

        /// <summary>
        /// Handles the bobbing animation for the item
        /// </summary>
        private void HandleBobAnimation(float delta)
        {
            _bobTimer += delta * BobSpeed;
            float bobOffset = Mathf.Sin(_bobTimer) * BobHeight;
            Position = _originalPosition + new Vector2(0, bobOffset);
        }

        /// <summary>
        /// Handles the highlight effect when player is nearby
        /// </summary>
        private void HandleHighlightEffect()
        {
            if (_sprite == null) return;
            
            if (_isPlayerNearby)
            {
                // Apply highlight effect
                _sprite.Modulate = HighlightColor * HighlightIntensity;
                
                // Add pulsing effect
                float pulse = (Mathf.Sin((float)Time.GetUnixTimeFromSystem() * 5.0f) + 1.0f) * 0.5f;
                _sprite.Modulate = _sprite.Modulate.Lerp(Colors.White, pulse * 0.3f);
            }
            else
            {
                // Normal appearance
                _sprite.Modulate = Colors.White;
            }
        }

        /// <summary>
        /// Called when a body enters the pickup area
        /// </summary>
        public void OnBodyEntered(Node2D body)
        {
            if (_isPickedUp) return;
            
            if (body is PlayerController player)
            {
                _isPlayerNearby = true;
                _nearbyPlayer = player;
                
                // Emit signal
                EmitSignal(SignalName.PlayerEnteredRange, player);
                
                // Emit global event
                EventBus.Instance?.EmitSignal(EventBus.SignalName.PlayerEnteredPickupRange, ItemId, Quantity, GlobalPosition);
                
                // Show interaction prompt
                ShowInteractionPrompt(true);
                
                // Start auto pickup timer if enabled
                if (AutoPickup && _autoPickupTimer != null)
                {
                    _autoPickupTimer.Start();
                }
                
                GD.Print($"Player entered range of {ItemId}");
            }
        }

        /// <summary>
        /// Called when a body exits the pickup area
        /// </summary>
        public void OnBodyExited(Node2D body)
        {
            if (body is PlayerController player && player == _nearbyPlayer)
            {
                _isPlayerNearby = false;
                _nearbyPlayer = null;
                
                // Emit signal
                EmitSignal(SignalName.PlayerExitedRange, player);
                
                // Emit global event
                EventBus.Instance?.EmitSignal(EventBus.SignalName.PlayerExitedPickupRange, ItemId, Quantity, GlobalPosition);
                
                // Hide interaction prompt
                ShowInteractionPrompt(false);
                
                // Stop auto pickup timer
                if (_autoPickupTimer != null && !_autoPickupTimer.IsStopped())
                {
                    _autoPickupTimer.Stop();
                }
                
                GD.Print($"Player exited range of {ItemId}");
            }
        }

        /// <summary>
        /// Called when auto pickup timer expires
        /// </summary>
        private void OnAutoPickupTimeout()
        {
            if (_isPlayerNearby && _nearbyPlayer != null)
            {
                TryPickupItem(_nearbyPlayer);
            }
        }

        /// <summary>
        /// Shows or hides the interaction prompt
        /// </summary>
        private void ShowInteractionPrompt(bool show)
        {
            // This would normally show a UI prompt
            // For now, we'll just emit an event that the UI can listen to
            if (show)
            {
                var item = ItemDatabase.Instance?.GetItem(ItemId);
                string message = $"Press E to pick up {item?.Name ?? ItemId}";
                if (Quantity > 1)
                {
                    message += $" x{Quantity}";
                }
                
                EventBus.Instance?.EmitNotificationRequested(message, "interaction", 0.1f);
            }
        }

        /// <summary>
        /// Attempts to pick up the item
        /// </summary>
        public bool TryPickupItem(PlayerController player)
        {
            if (_isPickedUp || player == null) return false;
            
            // Get player's inventory through GameManager
            var gameManager = GetTree().GetFirstNodeInGroup("game_manager") as GameManager;
            Inventory inventory = null;
            
            if (gameManager != null)
            {
                inventory = gameManager.GetInventory();
            }
            
            if (inventory == null)
            {
                // Fallback: try to find inventory directly in the scene tree
                inventory = GetTree().GetFirstNodeInGroup("inventory") as Inventory;
                if (inventory == null)
                {
                    GD.PrintErr("ItemPickup: Could not find player inventory");
                    return false;
                }
            }
            
            // Check if inventory can accept the item
            if (!inventory.CanAddItem(ItemId, Quantity, ItemMetadata))
            {
                // Show inventory full message
                var item = ItemDatabase.Instance?.GetItem(ItemId);
                string message = $"Inventory full! Cannot pick up {item?.Name ?? ItemId}";
                EventBus.Instance?.EmitNotificationRequested(message, "error", 2.0f);
                
                // Emit pickup failed event
                EventBus.Instance?.EmitSignal(EventBus.SignalName.ItemPickupFailed, ItemId, Quantity, "inventory_full");
                
                GD.Print($"Cannot pick up {ItemId}: inventory full");
                return false;
            }
            
            // Add item to inventory
            if (inventory.AddItem(ItemId, Quantity, ItemMetadata))
            {
                // Mark as picked up
                _isPickedUp = true;
                
                // Emit pickup event
                EmitSignal(SignalName.ItemPickedUp, ItemId, Quantity, player);
                
                // Emit global events
                EventBus.Instance?.EmitSignal(EventBus.SignalName.PlayerInteracted, "item_pickup", ItemId, GlobalPosition);
                EventBus.Instance?.EmitSignal(EventBus.SignalName.ItemPickedUpFromWorld, ItemId, Quantity, GlobalPosition);
                
                // Show pickup message
                var item = ItemDatabase.Instance?.GetItem(ItemId);
                string message = $"Picked up {item?.Name ?? ItemId}";
                if (Quantity > 1)
                {
                    message += $" x{Quantity}";
                }
                EventBus.Instance?.EmitNotificationRequested(message, "success", 1.5f);
                
                // Play pickup animation/effect
                PlayPickupEffect();
                
                GD.Print($"Successfully picked up {ItemId} x{Quantity}");
                return true;
            }
            
            return false;
        }

        /// <summary>
        /// Plays the pickup effect and removes the item from the world
        /// </summary>
        private void PlayPickupEffect()
        {
            // Play pickup animation if available
            if (_animationPlayer != null && _animationPlayer.HasAnimation("pickup"))
            {
                _animationPlayer.Play("pickup");
                _animationPlayer.AnimationFinished += OnPickupAnimationFinished;
            }
            else
            {
                // Simple fade out effect
                var tween = CreateTween();
                tween.TweenProperty(this, "modulate", Colors.Transparent, 0.3f);
                tween.TweenCallback(Callable.From(QueueFree));
            }
        }

        /// <summary>
        /// Called when pickup animation finishes
        /// </summary>
        private void OnPickupAnimationFinished(StringName animName)
        {
            if (animName == "pickup")
            {
                QueueFree();
            }
        }

        /// <summary>
        /// Handles interaction input from the player
        /// </summary>
        public void OnInteractionInput(PlayerController player)
        {
            if (_isPlayerNearby && player == _nearbyPlayer)
            {
                TryPickupItem(player);
            }
        }

        /// <summary>
        /// Sets the item data for this pickup
        /// </summary>
        public void SetItemData(string itemId, int quantity, Dictionary<string, object> metadata = null)
        {
            ItemId = itemId;
            Quantity = quantity;
            ItemMetadata = metadata ?? new Dictionary<string, object>();
            
            ValidateItemData();
            UpdateVisualDisplay();
        }

        /// <summary>
        /// Gets the item data for this pickup
        /// </summary>
        public (string itemId, int quantity, Dictionary<string, object> metadata) GetItemData()
        {
            return (ItemId, Quantity, ItemMetadata);
        }

        /// <summary>
        /// Creates an ItemPickup instance in the world
        /// </summary>
        public static ItemPickup CreateItemPickup(string itemId, int quantity, Vector2 position, 
                                                 Dictionary<string, object> metadata = null, Node parent = null)
        {
            // Load the ItemPickup scene
            var itemPickupScene = GD.Load<PackedScene>("res://Scenes/ItemPickup.tscn");
            if (itemPickupScene == null)
            {
                GD.PrintErr("ItemPickup: Could not load ItemPickup.tscn scene");
                return null;
            }
            
            // Instantiate the pickup
            var pickup = itemPickupScene.Instantiate<ItemPickup>();
            pickup.SetItemData(itemId, quantity, metadata);
            pickup.GlobalPosition = position;
            
            // Add to parent or current scene
            if (parent != null)
            {
                parent.AddChild(pickup);
            }
            else
            {
                pickup.GetTree()?.CurrentScene?.AddChild(pickup);
            }
            
            // Emit creation event
            EventBus.Instance?.EmitItemPickupCreated(itemId, quantity, position);
            
            return pickup;
        }

        public override void _ExitTree()
        {
            // Clean up event connections
            if (_animationPlayer != null)
            {
                _animationPlayer.AnimationFinished -= OnPickupAnimationFinished;
            }
            
            if (_autoPickupTimer != null)
            {
                _autoPickupTimer.Timeout -= OnAutoPickupTimeout;
            }
        }
    }
}