using Godot;
using System;

public partial class GraphicsSettings : Node
{
    private static GraphicsSettings _instance;
    public static GraphicsSettings Instance => _instance;

    [Signal]
    public delegate void SettingsChangedEventHandler();

    // Graphics quality levels
    public enum QualityLevel
    {
        Low,
        Medium,
        High,
        Ultra
    }

    // Current settings
    public QualityLevel CurrentQuality { get; private set; } = QualityLevel.Medium;
    public bool VsyncEnabled { get; private set; } = true;
    public int TargetFPS { get; private set; } = 60;
    public float RenderScale { get; private set; } = 1.0f;
    public bool ShadowsEnabled { get; private set; } = true;
    public int ShadowQuality { get; private set; } = 2; // 0-3
    public bool AntiAliasingEnabled { get; private set; } = true;
    public int TextureQuality { get; private set; } = 2; // 0-3

    private const string SETTINGS_FILE = "user://graphics_settings.cfg";

    public override void _Ready()
    {
        _instance = this;
        LoadSettings();
        ApplySettings();
    }

    public void SetQualityLevel(QualityLevel quality)
    {
        CurrentQuality = quality;
        
        switch (quality)
        {
            case QualityLevel.Low:
                RenderScale = 0.75f;
                ShadowsEnabled = false;
                ShadowQuality = 0;
                AntiAliasingEnabled = false;
                TextureQuality = 0;
                TargetFPS = 30;
                break;
                
            case QualityLevel.Medium:
                RenderScale = 1.0f;
                ShadowsEnabled = true;
                ShadowQuality = 1;
                AntiAliasingEnabled = false;
                TextureQuality = 1;
                TargetFPS = 60;
                break;
                
            case QualityLevel.High:
                RenderScale = 1.0f;
                ShadowsEnabled = true;
                ShadowQuality = 2;
                AntiAliasingEnabled = true;
                TextureQuality = 2;
                TargetFPS = 60;
                break;
                
            case QualityLevel.Ultra:
                RenderScale = 1.0f;
                ShadowsEnabled = true;
                ShadowQuality = 3;
                AntiAliasingEnabled = true;
                TextureQuality = 3;
                TargetFPS = 120;
                break;
        }
        
        ApplySettings();
        SaveSettings();
        EmitSignal(SignalName.SettingsChanged);
    }

    public void SetVsync(bool enabled)
    {
        VsyncEnabled = enabled;
        ApplySettings();
        SaveSettings();
        EmitSignal(SignalName.SettingsChanged);
    }

    public void SetTargetFPS(int fps)
    {
        TargetFPS = fps;
        ApplySettings();
        SaveSettings();
        EmitSignal(SignalName.SettingsChanged);
    }

    public void SetRenderScale(float scale)
    {
        RenderScale = Mathf.Clamp(scale, 0.5f, 2.0f);
        ApplySettings();
        SaveSettings();
        EmitSignal(SignalName.SettingsChanged);
    }

    private void ApplySettings()
    {
        // Apply VSync
        if (VsyncEnabled)
        {
            DisplayServer.WindowSetVsyncMode(DisplayServer.VSyncMode.Enabled);
        }
        else
        {
            DisplayServer.WindowSetVsyncMode(DisplayServer.VSyncMode.Disabled);
            Engine.MaxFps = TargetFPS;
        }

        // Apply render scale
        GetViewport().Scaling3DScale = RenderScale;

        // Apply shadow settings
        var renderingServer = RenderingServer.Singleton;
        if (ShadowsEnabled)
        {
            // Enable shadows and set quality
            ProjectSettings.SetSetting("rendering/lights_and_shadows/directional_shadow/size", GetShadowSize());
            ProjectSettings.SetSetting("rendering/lights_and_shadows/positional_shadow/atlas_size", GetShadowSize());
        }

        // Apply anti-aliasing
        if (AntiAliasingEnabled)
        {
            GetViewport().Msaa3D = Viewport.Msaa.Msaa4X;
        }
        else
        {
            GetViewport().Msaa3D = Viewport.Msaa.Disabled;
        }

        // Apply texture filtering
        var textureFilter = GetTextureFilter();
        ProjectSettings.SetSetting("rendering/textures/canvas_textures/default_texture_filter", textureFilter);

        GD.Print($"Graphics settings applied: Quality={CurrentQuality}, VSync={VsyncEnabled}, FPS={TargetFPS}, Scale={RenderScale}");
    }

    private int GetShadowSize()
    {
        return ShadowQuality switch
        {
            0 => 512,
            1 => 1024,
            2 => 2048,
            3 => 4096,
            _ => 2048
        };
    }

    private int GetTextureFilter()
    {
        return TextureQuality switch
        {
            0 => 0, // Nearest
            1 => 1, // Linear
            2 => 2, // Linear Mipmap
            3 => 3, // Linear Mipmap Anisotropic
            _ => 2
        };
    }

    private void SaveSettings()
    {
        var config = new ConfigFile();
        
        config.SetValue("graphics", "quality_level", (int)CurrentQuality);
        config.SetValue("graphics", "vsync_enabled", VsyncEnabled);
        config.SetValue("graphics", "target_fps", TargetFPS);
        config.SetValue("graphics", "render_scale", RenderScale);
        config.SetValue("graphics", "shadows_enabled", ShadowsEnabled);
        config.SetValue("graphics", "shadow_quality", ShadowQuality);
        config.SetValue("graphics", "antialiasing_enabled", AntiAliasingEnabled);
        config.SetValue("graphics", "texture_quality", TextureQuality);
        
        config.Save(SETTINGS_FILE);
    }

    private void LoadSettings()
    {
        var config = new ConfigFile();
        var error = config.Load(SETTINGS_FILE);
        
        if (error != Error.Ok)
        {
            GD.Print("No graphics settings file found, using defaults");
            return;
        }

        CurrentQuality = (QualityLevel)config.GetValue("graphics", "quality_level", (int)QualityLevel.Medium).AsInt32();
        VsyncEnabled = config.GetValue("graphics", "vsync_enabled", true).AsBool();
        TargetFPS = config.GetValue("graphics", "target_fps", 60).AsInt32();
        RenderScale = config.GetValue("graphics", "render_scale", 1.0f).AsSingle();
        ShadowsEnabled = config.GetValue("graphics", "shadows_enabled", true).AsBool();
        ShadowQuality = config.GetValue("graphics", "shadow_quality", 2).AsInt32();
        AntiAliasingEnabled = config.GetValue("graphics", "antialiasing_enabled", true).AsBool();
        TextureQuality = config.GetValue("graphics", "texture_quality", 2).AsInt32();
    }

    public void ResetToDefaults()
    {
        SetQualityLevel(QualityLevel.Medium);
    }
}