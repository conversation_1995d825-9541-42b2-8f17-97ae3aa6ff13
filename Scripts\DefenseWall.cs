using Godot;
using System;

namespace SurvivalLooterShooter
{
    /// <summary>
    /// Defensive wall structure that blocks enemy movement and provides cover
    /// Handles damage absorption and structural integrity
    /// </summary>
    public partial class DefenseWall : Node2D
    {
        [Export] public int MaxHealth { get; set; } = 200;
        [Export] public int CurrentHealth { get; set; } = 200;
        [Export] public float RepairCostMultiplier { get; set; } = 0.5f;

        private Structure _parentStructure;
        private StaticBody2D _collisionBody;
        private CollisionShape2D _collisionShape;
        private Sprite2D _wallSprite;
        private ProgressBar _healthBar;
        private Area2D _repairArea;
        
        // Wall state
        private bool _isDestroyed = false;
        private float _lastDamageTime = 0f;
        private const float DAMAGE_FLASH_DURATION = 0.2f;

        // Visual states
        private Texture2D _intactTexture;
        private Texture2D _damagedTexture;
        private Texture2D _heavilyDamagedTexture;

        public bool IsDestroyed => _isDestroyed || CurrentHealth <= 0;
        public float HealthPercentage => MaxHealth > 0 ? (float)CurrentHealth / MaxHealth : 0f;
        public bool NeedsRepair => CurrentHealth < MaxHealth;
        public bool CanBeRepaired => !IsDestroyed && CurrentHealth > 0;

        // Events
        [Signal] public delegate void WallDamagedEventHandler(DefenseWall wall, int damage);
        [Signal] public delegate void WallDestroyedEventHandler(DefenseWall wall);
        [Signal] public delegate void WallRepairedEventHandler(DefenseWall wall, int repairAmount);
        [Signal] public delegate void RepairStartedEventHandler(DefenseWall wall);
        [Signal] public delegate void RepairCompletedEventHandler(DefenseWall wall);

        public override void _Ready()
        {
            SetupComponents();
            LoadTextures();
            UpdateVisualState();
            
            // Register with defense system
            DefenseSystem.Instance?.RegisterDefenseWall(this);
            
            Logger.LogInfo("DefenseWall", $"Defense wall initialized with {MaxHealth} health");
        }

        /// <summary>
        /// Sets up wall components and collision
        /// </summary>
        private void SetupComponents()
        {
            // Create collision body
            _collisionBody = new StaticBody2D();
            _collisionBody.Name = "CollisionBody";
            AddChild(_collisionBody);

            // Create collision shape
            _collisionShape = new CollisionShape2D();
            _collisionShape.Name = "CollisionShape";
            var rectShape = new RectangleShape2D();
            rectShape.Size = new Vector2(32, 64); // Default wall size
            _collisionShape.Shape = rectShape;
            _collisionBody.AddChild(_collisionShape);

            // Create wall sprite
            _wallSprite = new Sprite2D();
            _wallSprite.Name = "WallSprite";
            AddChild(_wallSprite);

            // Create health bar
            _healthBar = new ProgressBar();
            _healthBar.Name = "HealthBar";
            _healthBar.Size = new Vector2(40, 6);
            _healthBar.Position = new Vector2(-20, -40);
            _healthBar.MinValue = 0;
            _healthBar.MaxValue = MaxHealth;
            _healthBar.Value = CurrentHealth;
            _healthBar.ShowPercentage = false;
            
            // Style health bar
            var fillStyle = new StyleBoxFlat();
            fillStyle.BgColor = Colors.Green;
            _healthBar.AddThemeStyleboxOverride("fill", fillStyle);
            
            var bgStyle = new StyleBoxFlat();
            bgStyle.BgColor = Colors.DarkRed;
            _healthBar.AddThemeStyleboxOverride("background", bgStyle);
            
            AddChild(_healthBar);

            // Create repair interaction area
            _repairArea = new Area2D();
            _repairArea.Name = "RepairArea";
            var repairShape = new CollisionShape2D();
            var repairCircle = new CircleShape2D();
            repairCircle.Radius = 50f;
            repairShape.Shape = repairCircle;
            _repairArea.AddChild(repairShape);
            AddChild(_repairArea);

            // Connect repair area signals
            _repairArea.InputEvent += OnRepairAreaInputEvent;
        }

        /// <summary>
        /// Loads wall textures for different damage states
        /// </summary>
        private void LoadTextures()
        {
            // Try to load specific textures
            if (ResourceLoader.Exists("res://Assets/Structures/wall_intact.png"))
            {
                _intactTexture = GD.Load<Texture2D>("res://Assets/Structures/wall_intact.png");
            }
            else
            {
                _intactTexture = CreateWallTexture(Colors.Brown);
            }

            if (ResourceLoader.Exists("res://Assets/Structures/wall_damaged.png"))
            {
                _damagedTexture = GD.Load<Texture2D>("res://Assets/Structures/wall_damaged.png");
            }
            else
            {
                _damagedTexture = CreateWallTexture(Colors.DarkGoldenrod);
            }

            if (ResourceLoader.Exists("res://Assets/Structures/wall_heavily_damaged.png"))
            {
                _heavilyDamagedTexture = GD.Load<Texture2D>("res://Assets/Structures/wall_heavily_damaged.png");
            }
            else
            {
                _heavilyDamagedTexture = CreateWallTexture(Colors.DarkRed);
            }
        }

        /// <summary>
        /// Creates a simple wall texture with the specified color
        /// </summary>
        private ImageTexture CreateWallTexture(Color color)
        {
            var image = Image.CreateEmpty(32, 64, false, Image.Format.Rgb8);
            image.Fill(color);
            
            // Add some texture details
            for (int x = 0; x < 32; x += 8)
            {
                for (int y = 0; y < 64; y += 16)
                {
                    // Draw brick pattern
                    var brickColor = color.Darkened(0.2f);
                    for (int bx = 0; bx < 7 && x + bx < 32; bx++)
                    {
                        for (int by = 0; by < 15 && y + by < 64; by++)
                        {
                            if (bx == 0 || bx == 6 || by == 0 || by == 14)
                            {
                                image.SetPixel(x + bx, y + by, brickColor);
                            }
                        }
                    }
                }
            }
            
            return ImageTexture.CreateFromImage(image);
        }

        /// <summary>
        /// Initializes the wall with a parent structure
        /// </summary>
        public void Initialize(Structure parentStructure)
        {
            _parentStructure = parentStructure;
            
            if (parentStructure != null)
            {
                MaxHealth = parentStructure.MaxHealth;
                CurrentHealth = parentStructure.CurrentHealth;
                
                // Update health bar
                _healthBar.MaxValue = MaxHealth;
                _healthBar.Value = CurrentHealth;
                
                // Connect to structure events
                parentStructure.StructureDestroyed += OnParentStructureDestroyed;
                parentStructure.StructureDamaged += OnParentStructureDamaged;
                
                // Adjust collision shape based on structure size
                if (parentStructure.Blueprint?.Size != null)
                {
                    var size = parentStructure.Blueprint.Size;
                    if (_collisionShape.Shape is RectangleShape2D rect)
                    {
                        rect.Size = new Vector2(size.Width * 32, size.Height * 32);
                    }
                }
            }
            
            UpdateVisualState();
        }

        /// <summary>
        /// Updates the visual appearance based on current health
        /// </summary>
        private void UpdateVisualState()
        {
            if (_wallSprite == null) return;

            float healthPercent = HealthPercentage;
            
            if (healthPercent > 0.7f)
            {
                _wallSprite.Texture = _intactTexture;
                UpdateHealthBarColor(Colors.Green);
            }
            else if (healthPercent > 0.3f)
            {
                _wallSprite.Texture = _damagedTexture;
                UpdateHealthBarColor(Colors.Yellow);
            }
            else if (healthPercent > 0f)
            {
                _wallSprite.Texture = _heavilyDamagedTexture;
                UpdateHealthBarColor(Colors.Red);
            }
            else
            {
                // Destroyed state
                _wallSprite.Modulate = Colors.Gray;
                _healthBar.Visible = false;
                DisableCollision();
            }
        }

        /// <summary>
        /// Updates health bar color based on health percentage
        /// </summary>
        private void UpdateHealthBarColor(Color color)
        {
            var fillStyle = new StyleBoxFlat();
            fillStyle.BgColor = color;
            _healthBar.AddThemeStyleboxOverride("fill", fillStyle);
        }

        /// <summary>
        /// Applies damage to the wall
        /// </summary>
        public void TakeDamage(int damage)
        {
            if (IsDestroyed) return;

            int actualDamage = Math.Max(0, damage);
            CurrentHealth = Math.Max(0, CurrentHealth - actualDamage);
            
            // Update health bar
            _healthBar.Value = CurrentHealth;
            
            // Flash effect
            FlashDamage();
            
            // Update visual state
            UpdateVisualState();
            
            // Emit damage event
            EmitSignal(SignalName.WallDamaged, this, actualDamage);
            
            Logger.LogInfo("DefenseWall", $"Wall took {actualDamage} damage. Health: {CurrentHealth}/{MaxHealth}");
            
            // Check for destruction
            if (CurrentHealth <= 0 && !_isDestroyed)
            {
                DestroyWall();
            }
        }

        /// <summary>
        /// Creates a damage flash effect
        /// </summary>
        private void FlashDamage()
        {
            if (_wallSprite == null) return;

            _lastDamageTime = (float)Time.GetUnixTimeFromSystem();
            _wallSprite.Modulate = Colors.Red;
            
            // Create tween to fade back to normal
            var tween = CreateTween();
            tween.TweenProperty(_wallSprite, "modulate", Colors.White, DAMAGE_FLASH_DURATION);
        }

        /// <summary>
        /// Destroys the wall
        /// </summary>
        private void DestroyWall()
        {
            if (_isDestroyed) return;

            _isDestroyed = true;
            
            // Disable collision
            DisableCollision();
            
            // Update visuals
            _wallSprite.Modulate = Colors.Gray;
            _healthBar.Visible = false;
            
            // Emit destruction event
            EmitSignal(SignalName.WallDestroyed, this);
            
            Logger.LogInfo("DefenseWall", "Defense wall destroyed");
            
            // Create destruction effects
            CreateDestructionEffects();
        }

        /// <summary>
        /// Disables wall collision
        /// </summary>
        private void DisableCollision()
        {
            if (_collisionBody != null)
            {
                _collisionBody.SetCollisionLayerValue(1, false);
                _collisionBody.SetCollisionMaskValue(1, false);
            }
        }

        /// <summary>
        /// Creates destruction visual effects
        /// </summary>
        private void CreateDestructionEffects()
        {
            // Create debris particles
            var particles = new GpuParticles2D();
            particles.Name = "DestructionParticles";
            particles.Texture = CreateDebrisTexture();
            particles.Amount = 20;
            particles.Lifetime = 2.0f;
            particles.OneShot = true;
            particles.Emitting = true;
            
            AddChild(particles);
            
            // Remove particles after emission
            var timer = new Timer();
            timer.WaitTime = 3.0f;
            timer.OneShot = true;
            timer.Timeout += () => {
                particles.QueueFree();
                timer.QueueFree();
            };
            AddChild(timer);
            timer.Start();
        }

        /// <summary>
        /// Creates a simple debris texture
        /// </summary>
        private ImageTexture CreateDebrisTexture()
        {
            var image = Image.CreateEmpty(4, 4, false, Image.Format.Rgb8);
            image.Fill(Colors.Brown);
            return ImageTexture.CreateFromImage(image);
        }

        /// <summary>
        /// Repairs the wall by the specified amount
        /// </summary>
        public bool Repair(int repairAmount)
        {
            if (!CanBeRepaired) return false;

            int actualRepair = Math.Min(repairAmount, MaxHealth - CurrentHealth);
            if (actualRepair <= 0) return false;

            CurrentHealth += actualRepair;
            _healthBar.Value = CurrentHealth;
            
            // Update visual state
            UpdateVisualState();
            
            // Emit repair event
            EmitSignal(SignalName.WallRepaired, this, actualRepair);
            
            Logger.LogInfo("DefenseWall", $"Wall repaired by {actualRepair}. Health: {CurrentHealth}/{MaxHealth}");
            
            return true;
        }

        /// <summary>
        /// Gets the materials required to repair the wall
        /// </summary>
        public System.Collections.Generic.List<BuildCost> GetRepairCost()
        {
            if (!NeedsRepair || _parentStructure?.Blueprint == null) 
                return [];

            var repairCosts = new System.Collections.Generic.List<BuildCost>();
            float damagePercent = 1f - HealthPercentage;
            
            foreach (var cost in _parentStructure.Blueprint.BuildCost)
            {
                int repairAmount = Mathf.RoundToInt(cost.Amount * damagePercent * RepairCostMultiplier);
                if (repairAmount > 0)
                {
                    repairCosts.Add(new BuildCost 
                    { 
                        Item = cost.Item, 
                        Amount = repairAmount 
                    });
                }
            }
            
            return repairCosts;
        }

        /// <summary>
        /// Attempts to repair the wall using materials from inventory
        /// </summary>
        public bool TryRepairWithInventory(Inventory inventory)
        {
            if (!CanBeRepaired || inventory == null) return false;

            var repairCosts = GetRepairCost();
            if (repairCosts.Count == 0) return false;

            // Check if player has all required materials
            foreach (var cost in repairCosts)
            {
                if (!inventory.HasItem(cost.Item, cost.Amount))
                {
                    Logger.LogInfo("DefenseWall", $"Missing {cost.Item} x{cost.Amount} for repair");
                    return false;
                }
            }

            // Consume materials
            foreach (var cost in repairCosts)
            {
                inventory.RemoveItem(cost.Item, cost.Amount);
            }

            // Calculate repair amount based on materials used
            int repairAmount = Mathf.RoundToInt((MaxHealth - CurrentHealth) * 0.8f);
            
            EmitSignal(SignalName.RepairStarted, this);
            
            // Start repair process (could be instant or take time)
            StartRepairProcess(repairAmount);
            
            return true;
        }

        /// <summary>
        /// Starts the repair process
        /// </summary>
        private void StartRepairProcess(int repairAmount)
        {
            // For now, repair is instant. Could add a timer for gradual repair
            Repair(repairAmount);
            EmitSignal(SignalName.RepairCompleted, this);
        }

        /// <summary>
        /// Handles repair area input events
        /// </summary>
        private void OnRepairAreaInputEvent(Node viewport, InputEvent @event, long shapeIdx)
        {
            if (@event is InputEventMouseButton mouseEvent && 
                mouseEvent.Pressed && 
                mouseEvent.ButtonIndex == MouseButton.Right &&
                NeedsRepair)
            {
                // Try to repair with player inventory
                var inventory = GetNode<Inventory>("/root/GameManager/Inventory");
                if (inventory != null)
                {
                    TryRepairWithInventory(inventory);
                }
            }
        }

        /// <summary>
        /// Handles parent structure destruction
        /// </summary>
        private void OnParentStructureDestroyed(Structure structure)
        {
            DestroyWall();
        }

        /// <summary>
        /// Handles parent structure damage
        /// </summary>
        private void OnParentStructureDamaged(Structure structure, int damage)
        {
            // Sync health with parent structure
            CurrentHealth = structure.CurrentHealth;
            MaxHealth = structure.MaxHealth;
            
            _healthBar.MaxValue = MaxHealth;
            _healthBar.Value = CurrentHealth;
            
            UpdateVisualState();
        }

        /// <summary>
        /// Gets the parent structure
        /// </summary>
        public Structure GetStructure()
        {
            return _parentStructure;
        }

        /// <summary>
        /// Gets wall status information
        /// </summary>
        public System.Collections.Generic.Dictionary<string, object> GetStatus()
        {
            return new System.Collections.Generic.Dictionary<string, object>
            {
                ["current_health"] = CurrentHealth,
                ["max_health"] = MaxHealth,
                ["health_percentage"] = HealthPercentage,
                ["is_destroyed"] = IsDestroyed,
                ["needs_repair"] = NeedsRepair,
                ["can_be_repaired"] = CanBeRepaired,
                ["repair_cost"] = GetRepairCost()
            };
        }

        public override void _ExitTree()
        {
            DefenseSystem.Instance?.UnregisterDefenseWall(this);
        }
    }
}