using Godot;

namespace SurvivalLooterShooter
{
    /// <summary>
    /// UI component for displaying current weather information
    /// </summary>
    public partial class WeatherUI : Control
    {
        // UI elements
        private Label _weatherLabel;
        private Label _temperatureLabel;
        private Label _seasonLabel;
        private VBoxContainer _forecastContainer;
        private ProgressBar _weatherIntensityBar;
        
        // Weather icons (optional - can be added later)
        private TextureRect _weatherIcon;

        public override void _Ready()
        {
            SetupUI();
            ConnectToWeatherSystem();
            UpdateWeatherDisplay();
        }

        /// <summary>
        /// Sets up the UI elements
        /// </summary>
        private void SetupUI()
        {
            // Create main container
            var mainContainer = new VBoxContainer();
            AddChild(mainContainer);
            
            // Weather status section
            var weatherContainer = new HBoxContainer();
            mainContainer.AddChild(weatherContainer);
            
            // Weather icon (placeholder)
            _weatherIcon = new TextureRect();
            _weatherIcon.CustomMinimumSize = new Vector2(32, 32);
            weatherContainer.AddChild(_weatherIcon);
            
            // Weather info
            var infoContainer = new VBoxContainer();
            weatherContainer.AddChild(infoContainer);
            
            _weatherLabel = new Label();
            _weatherLabel.Text = "Clear Weather";
            infoContainer.AddChild(_weatherLabel);
            
            _temperatureLabel = new Label();
            _temperatureLabel.Text = "20°C";
            infoContainer.AddChild(_temperatureLabel);
            
            _seasonLabel = new Label();
            _seasonLabel.Text = "Spring";
            infoContainer.AddChild(_seasonLabel);
            
            // Weather intensity bar
            _weatherIntensityBar = new ProgressBar();
            _weatherIntensityBar.MaxValue = 1.0;
            _weatherIntensityBar.Value = 0.5;
            _weatherIntensityBar.ShowPercentage = false;
            mainContainer.AddChild(_weatherIntensityBar);
            
            // Forecast section
            var forecastLabel = new Label();
            forecastLabel.Text = "3-Day Forecast:";
            mainContainer.AddChild(forecastLabel);
            
            _forecastContainer = new VBoxContainer();
            mainContainer.AddChild(_forecastContainer);
        }

        /// <summary>
        /// Connects to the weather system events
        /// </summary>
        private void ConnectToWeatherSystem()
        {
            if (WeatherManager.Instance != null)
            {
                WeatherManager.Instance.WeatherChanged += OnWeatherChanged;
                WeatherManager.Instance.SeasonChanged += OnSeasonChanged;
                WeatherManager.Instance.WeatherForecastUpdated += OnForecastUpdated;
            }
        }

        /// <summary>
        /// Updates the weather display with current information
        /// </summary>
        private void UpdateWeatherDisplay()
        {
            if (WeatherManager.Instance == null) return;
            
            // Update current weather
            _weatherLabel.Text = WeatherManager.Instance.GetWeatherDescription();
            _temperatureLabel.Text = $"{WeatherManager.Instance.GetCurrentTemperature():F1}°C";
            _seasonLabel.Text = $"Season: {WeatherManager.Instance.CurrentSeason}";
            _weatherIntensityBar.Value = WeatherManager.Instance.WeatherIntensity;
            
            // Update forecast
            UpdateForecastDisplay();
        }

        /// <summary>
        /// Updates the weather forecast display
        /// </summary>
        private void UpdateForecastDisplay()
        {
            if (WeatherManager.Instance == null) return;
            
            // Clear existing forecast
            foreach (Node child in _forecastContainer.GetChildren())
            {
                child.QueueFree();
            }
            
            // Add forecast items
            var forecast = WeatherManager.Instance.GetWeatherForecast();
            for (int i = 0; i < forecast.Length; i++)
            {
                var forecastLabel = new Label();
                forecastLabel.Text = $"Day {i + 1}: {forecast[i]}";
                _forecastContainer.AddChild(forecastLabel);
            }
        }

        /// <summary>
        /// Called when weather changes
        /// </summary>
        private void OnWeatherChanged(WeatherManager.WeatherType oldWeather, WeatherManager.WeatherType newWeather, float intensity)
        {
            UpdateWeatherDisplay();
        }

        /// <summary>
        /// Called when season changes
        /// </summary>
        private void OnSeasonChanged(WeatherManager.Season oldSeason, WeatherManager.Season newSeason)
        {
            UpdateWeatherDisplay();
        }

        /// <summary>
        /// Called when weather forecast is updated
        /// </summary>
        private void OnForecastUpdated()
        {
            UpdateForecastDisplay();
        }

        public override void _ExitTree()
        {
            // Disconnect from weather system
            if (WeatherManager.Instance != null)
            {
                WeatherManager.Instance.WeatherChanged -= OnWeatherChanged;
                WeatherManager.Instance.SeasonChanged -= OnSeasonChanged;
                WeatherManager.Instance.WeatherForecastUpdated -= OnForecastUpdated;
            }
        }
    }
}