using Godot;
using System;

namespace SurvivalLooterShooter
{
    /// <summary>
    /// Manages the day/night cycle system with configurable time progression
    /// Provides time-based gameplay mechanics and lighting changes
    /// </summary>
    public partial class DayNightCycle : Node
    {
        private static DayNightCycle _instance;
        public static DayNightCycle Instance => _instance;

        // Time configuration
        [Export] public float DayLengthMinutes { get; set; } = 20.0f; // Real minutes for a full day
        [Export] public float DawnTime { get; set; } = 6.0f; // 6 AM
        [Export] public float DuskTime { get; set; } = 18.0f; // 6 PM
        
        // Current time state
        public float CurrentTime { get; private set; } = 12.0f; // Start at noon
        public bool IsNightTime { get; private set; } = false;
        public bool IsDawnTime { get; private set; } = false;
        public bool IsDuskTime { get; private set; } = false;
        
        // Time progression
        private float _timeSpeed;
        private float _lastTimeUpdate;
        
        // Events
        [Signal] public delegate void TimeChangedEventHandler(float currentTime, bool isNight);
        [Signal] public delegate void DayStartedEventHandler();
        [Signal] public delegate void NightStartedEventHandler();
        [Signal] public delegate void DawnEventHandler();
        [Signal] public delegate void DuskEventHandler();

        public override void _Ready()
        {
            // Singleton setup
            if (_instance == null)
            {
                _instance = this;
                AddToGroup("day_night_cycle");
                Logger.LogInfo("DayNightCycle", "DayNightCycle singleton initialized");
            }
            else
            {
                Logger.LogError("DayNightCycle", "Multiple DayNightCycle instances detected! Removing duplicate.");
                QueueFree();
                return;
            }

            // Calculate time speed (24 hours in DayLengthMinutes real minutes)
            _timeSpeed = 24.0f / (DayLengthMinutes * 60.0f); // Hours per second
            _lastTimeUpdate = CurrentTime;

            // Connect to EventBus if available
            if (EventBus.Instance != null)
            {
                TimeChanged += EventBus.Instance.OnDayNightChanged;
            }

            Logger.LogInfo("DayNightCycle", $"Day/Night cycle initialized - Day length: {DayLengthMinutes} minutes");
        }

        public override void _Process(double delta)
        {
            UpdateTime((float)delta);
        }

        /// <summary>
        /// Updates the current time and checks for day/night transitions
        /// </summary>
        private void UpdateTime(float delta)
        {
            float previousTime = CurrentTime;
            CurrentTime += _timeSpeed * delta;

            // Wrap around 24 hours
            if (CurrentTime >= 24.0f)
            {
                CurrentTime -= 24.0f;
            }

            // Check for time-based events
            CheckTimeTransitions(previousTime, CurrentTime);

            // Emit time changed event every second (approximately)
            if (Mathf.Abs(CurrentTime - _lastTimeUpdate) >= 0.1f) // Every 6 minutes game time
            {
                EmitSignal(SignalName.TimeChanged, CurrentTime, IsNightTime);
                _lastTimeUpdate = CurrentTime;
            }
        }

        /// <summary>
        /// Checks for transitions between day/night and special times
        /// </summary>
        private void CheckTimeTransitions(float previousTime, float currentTime)
        {
            bool wasNight = IsNightTime;
            bool wasDawn = IsDawnTime;
            bool wasDusk = IsDuskTime;

            // Update time states
            IsNightTime = currentTime < DawnTime || currentTime >= DuskTime;
            IsDawnTime = currentTime >= DawnTime && currentTime < DawnTime + 1.0f; // 1 hour dawn period
            IsDuskTime = currentTime >= DuskTime && currentTime < DuskTime + 1.0f; // 1 hour dusk period

            // Check for transitions
            if (!wasNight && IsNightTime)
            {
                EmitSignal(SignalName.NightStarted);
                Logger.LogInfo("DayNightCycle", "Night has begun");
            }
            else if (wasNight && !IsNightTime)
            {
                EmitSignal(SignalName.DayStarted);
                Logger.LogInfo("DayNightCycle", "Day has begun");
            }

            if (!wasDawn && IsDawnTime)
            {
                EmitSignal(SignalName.Dawn);
                Logger.LogInfo("DayNightCycle", "Dawn is breaking");
            }

            if (!wasDusk && IsDuskTime)
            {
                EmitSignal(SignalName.Dusk);
                Logger.LogInfo("DayNightCycle", "Dusk is falling");
            }
        }

        /// <summary>
        /// Gets the current time as a formatted string
        /// </summary>
        public string GetFormattedTime()
        {
            int hours = (int)CurrentTime;
            int minutes = (int)((CurrentTime - hours) * 60);
            return $"{hours:D2}:{minutes:D2}";
        }

        /// <summary>
        /// Gets the current time period as a string
        /// </summary>
        public string GetTimePeriod()
        {
            if (IsDawnTime) return "Dawn";
            if (IsDuskTime) return "Dusk";
            if (IsNightTime) return "Night";
            return "Day";
        }

        /// <summary>
        /// Gets the lighting intensity based on current time
        /// </summary>
        public float GetLightingIntensity()
        {
            if (IsNightTime)
            {
                return 0.3f; // Dark night
            }
            else if (IsDawnTime || IsDuskTime)
            {
                return 0.7f; // Twilight
            }
            else
            {
                return 1.0f; // Full daylight
            }
        }

        /// <summary>
        /// Gets the ambient color based on current time
        /// </summary>
        public Color GetAmbientColor()
        {
            if (IsNightTime)
            {
                return new Color(0.2f, 0.2f, 0.4f); // Blue night tint
            }
            else if (IsDawnTime)
            {
                return new Color(1.0f, 0.8f, 0.6f); // Orange dawn
            }
            else if (IsDuskTime)
            {
                return new Color(1.0f, 0.6f, 0.4f); // Red dusk
            }
            else
            {
                return Colors.White; // Normal daylight
            }
        }

        /// <summary>
        /// Sets the current time (useful for save/load or debugging)
        /// </summary>
        public void SetTime(float time)
        {
            float previousTime = CurrentTime;
            CurrentTime = Mathf.Clamp(time, 0.0f, 24.0f);
            CheckTimeTransitions(previousTime, CurrentTime);
            EmitSignal(SignalName.TimeChanged, CurrentTime, IsNightTime);
            Logger.LogInfo("DayNightCycle", $"Time set to {GetFormattedTime()} ({GetTimePeriod()})");
        }

        /// <summary>
        /// Advances time by a specified number of hours
        /// </summary>
        public void AdvanceTime(float hours)
        {
            SetTime(CurrentTime + hours);
        }

        /// <summary>
        /// Gets the progress through the current day (0.0 to 1.0)
        /// </summary>
        public float GetDayProgress()
        {
            return CurrentTime / 24.0f;
        }

        /// <summary>
        /// Checks if it's currently a specific time period
        /// </summary>
        public bool IsTimePeriod(string period)
        {
            return period.ToLower() switch
            {
                "dawn" => IsDawnTime,
                "day" => !IsNightTime && !IsDawnTime && !IsDuskTime,
                "dusk" => IsDuskTime,
                "night" => IsNightTime,
                _ => false
            };
        }

        public override void _ExitTree()
        {
            if (_instance == this)
            {
                _instance = null;
            }
        }
    }
}