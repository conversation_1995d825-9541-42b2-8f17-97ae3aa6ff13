using Godot;
using System;
using System.Collections.Generic;
using System.Linq;

namespace SurvivalLooterShooter
{
    /// <summary>
    /// Manages rare natural disasters and extreme weather events
    /// Provides catastrophic events that significantly impact gameplay
    /// </summary>
    public partial class NaturalDisasterSystem : Node
    {
        private static NaturalDisasterSystem _instance;
        public static NaturalDisasterSystem Instance => _instance;

        // Active disasters
        private List<NaturalDisaster> _activeDisasters = new List<NaturalDisaster>();
        
        // Disaster spawning
        private Timer _disasterCheckTimer;
        private const float DISASTER_CHECK_INTERVAL = 1800f; // Check every 30 minutes
        
        // Disaster probabilities (per check) - very rare events
        private readonly Dictionary<DisasterType, float> _disasterProbabilities = new Dictionary<DisasterType, float>
        {
            [DisasterType.MegaStorm] = 0.0001f,      // 0.01% chance
            [DisasterType.VolcanicEruption] = 0.00005f, // 0.005% chance
            [DisasterType.MeteorShower] = 0.00008f,   // 0.008% chance
            [DisasterType.SolarFlare] = 0.00003f,     // 0.003% chance
            [DisasterType.Tsunami] = 0.00002f,        // 0.002% chance
            [DisasterType.SuperBlizzard] = 0.0002f,   // 0.02% chance in winter
            [DisasterType.Drought] = 0.0001f,         // 0.01% chance in summer
            [DisasterType.PlagueLocus] = 0.00015f     // 0.015% chance
        };

        // Events
        [Signal] public delegate void DisasterStartedEventHandler(string disasterType, float magnitude, float duration);
        [Signal] public delegate void DisasterEndedEventHandler(string disasterType);
        [Signal] public delegate void DisasterWarningEventHandler(string disasterType, float timeToImpact);
        [Signal] public delegate void CatastrophicEventEventHandler(string eventType, float severity);

        public enum DisasterType
        {
            MegaStorm,        // Extreme weather combining multiple hazards
            VolcanicEruption, // Ash, lava, toxic gases
            MeteorShower,     // Falling meteors causing fires and damage
            SolarFlare,       // Electromagnetic disruption
            Tsunami,          // Massive flooding
            SuperBlizzard,    // Extreme cold and snow
            Drought,          // Extended dry period
            PlagueLocus      // Swarms of destructive insects
        }

        public override void _Ready()
        {
            // Singleton setup
            if (_instance == null)
            {
                _instance = this;
                AddToGroup("natural_disaster_system");
                Logger.LogInfo("NaturalDisasterSystem", "NaturalDisasterSystem singleton initialized");
            }
            else
            {
                Logger.LogError("NaturalDisasterSystem", "Multiple NaturalDisasterSystem instances detected! Removing duplicate.");
                QueueFree();
                return;
            }

            SetupDisasterChecking();
            ConnectToSystems();
            
            Logger.LogInfo("NaturalDisasterSystem", "Natural disaster system initialized");
        }

        public override void _Process(double delta)
        {
            UpdateActiveDisasters((float)delta);
        }

        /// <summary>
        /// Sets up disaster checking timer
        /// </summary>
        private void SetupDisasterChecking()
        {
            _disasterCheckTimer = new Timer();
            AddChild(_disasterCheckTimer);
            _disasterCheckTimer.WaitTime = DISASTER_CHECK_INTERVAL;
            _disasterCheckTimer.Autostart = true;
            _disasterCheckTimer.Timeout += CheckForDisasters;
        }

        /// <summary>
        /// Connects to other systems for disaster triggering
        /// </summary>
        private void ConnectToSystems()
        {
            if (WeatherManager.Instance != null)
            {
                WeatherManager.Instance.WeatherChanged += OnWeatherChanged;
                WeatherManager.Instance.SeasonChanged += OnSeasonChanged;
            }
        }

        private void OnSeasonChanged(WeatherManager.Season oldSeason, WeatherManager.Season newSeason)
        {
            throw new NotImplementedException();
        }

        private void OnWeatherChanged(WeatherManager.WeatherType oldWeather, WeatherManager.WeatherType newWeather, float intensity)
        {
            throw new NotImplementedException();
        }

        /// <summary>
        /// Updates all active disasters
        /// </summary>
        private void UpdateActiveDisasters(float delta)
        {
            for (int i = _activeDisasters.Count - 1; i >= 0; i--)
            {
                var disaster = _activeDisasters[i];
                disaster.Update(delta);
                
                if (disaster.IsExpired)
                {
                    EndDisaster(disaster);
                    _activeDisasters.RemoveAt(i);
                }
            }
        }

        /// <summary>
        /// Checks for new disasters based on current conditions
        /// </summary>
        private void CheckForDisasters()
        {
            if (WeatherManager.Instance == null) return;

            var currentWeather = WeatherManager.Instance.CurrentWeather;
            var currentSeason = WeatherManager.Instance.CurrentSeason;
            var weatherIntensity = WeatherManager.Instance.WeatherIntensity;

            // Check each disaster type
            foreach (var disasterType in Enum.GetValues<DisasterType>())
            {
                if (ShouldSpawnDisaster(disasterType, currentWeather, currentSeason, weatherIntensity))
                {
                    SpawnDisaster(disasterType);
                }
            }
        }

        /// <summary>
        /// Determines if a disaster should spawn based on conditions
        /// </summary>
        private bool ShouldSpawnDisaster(DisasterType disasterType, WeatherManager.WeatherType weather, 
            WeatherManager.Season season, float intensity)
        {
            // Don't spawn if disaster is already active
            if (_activeDisasters.Any(d => d.Type == disasterType))
                return false;

            float baseChance = _disasterProbabilities[disasterType];
            float modifiedChance = baseChance;

            // Modify chance based on conditions
            switch (disasterType)
            {
                case DisasterType.MegaStorm:
                    if (weather == WeatherManager.WeatherType.Thunderstorm && intensity > 0.9f)
                        modifiedChance *= 20f;
                    else
                        modifiedChance = 0f;
                    break;

                case DisasterType.SuperBlizzard:
                    if (season == WeatherManager.Season.Winter && 
                        weather == WeatherManager.WeatherType.Blizzard && intensity > 0.8f)
                        modifiedChance *= 15f;
                    else
                        modifiedChance = 0f;
                    break;

                case DisasterType.Drought:
                    if (season == WeatherManager.Season.Summer && 
                        weather == WeatherManager.WeatherType.Clear)
                        modifiedChance *= 10f;
                    else
                        modifiedChance = 0f;
                    break;

                case DisasterType.Tsunami:
                    if (weather == WeatherManager.WeatherType.HeavyRain && intensity > 0.8f)
                        modifiedChance *= 5f;
                    break;

                // Other disasters are random and not weather-dependent
                case DisasterType.VolcanicEruption:
                case DisasterType.MeteorShower:
                case DisasterType.SolarFlare:
                case DisasterType.PlagueLocus:
                    // Base chance only
                    break;
            }

            return GD.Randf() < modifiedChance;
        }

        /// <summary>
        /// Spawns a new natural disaster
        /// </summary>
        private void SpawnDisaster(DisasterType disasterType)
        {
            var disaster = CreateDisaster(disasterType);
            _activeDisasters.Add(disaster);
            
            // Emit warning first
            EmitSignal(SignalName.DisasterWarning, disasterType.ToString(), disaster.WarningTime);
            
            // Start disaster after warning period
            GetTree().CreateTimer(disaster.WarningTime).Timeout += () => {
                EmitSignal(SignalName.DisasterStarted, disasterType.ToString(), disaster.Magnitude, disaster.Duration);
                EmitSignal(SignalName.CatastrophicEvent, disasterType.ToString(), disaster.Magnitude);
                
                EventBus.Instance?.EmitNaturalDisaster(disasterType.ToString(), disaster.Magnitude);
                
                Logger.LogInfo("NaturalDisasterSystem", 
                    $"DISASTER STARTED: {disasterType} - Magnitude: {disaster.Magnitude:F2}, Duration: {disaster.Duration:F1}s");
            };
            
            Logger.LogInfo("NaturalDisasterSystem", 
                $"DISASTER WARNING: {disasterType} incoming in {disaster.WarningTime:F1} seconds!");
        }

        /// <summary>
        /// Creates a disaster instance based on type
        /// </summary>
        private NaturalDisaster CreateDisaster(DisasterType disasterType)
        {
            return disasterType switch
            {
                DisasterType.MegaStorm => new MegaStormDisaster(),
                DisasterType.VolcanicEruption => new VolcanicEruptionDisaster(),
                DisasterType.MeteorShower => new MeteorShowerDisaster(),
                DisasterType.SolarFlare => new SolarFlareDisaster(),
                DisasterType.Tsunami => new TsunamiDisaster(),
                DisasterType.SuperBlizzard => new SuperBlizzardDisaster(),
                DisasterType.Drought => new DroughtDisaster(),
                DisasterType.PlagueLocus => new PlagueLocusDisaster(),
                _ => new NaturalDisaster(disasterType, 0.8f, 600f, 30f)
            };
        }

        /// <summary>
        /// Ends a disaster and cleans up its effects
        /// </summary>
        private void EndDisaster(NaturalDisaster disaster)
        {
            EmitSignal(SignalName.DisasterEnded, disaster.Type.ToString());
            
            Logger.LogInfo("NaturalDisasterSystem", $"DISASTER ENDED: {disaster.Type}");
        }

        /// <summary>
        /// Handles weather changes that might trigger disasters
        /// </summary>
        private void OnWeatherChanged(int oldWeather, int newWeather, float intensity)
        {
            // Extreme weather changes can trigger immediate disaster checks
            var weather = (WeatherManager.WeatherType)newWeather;
            
            if (intensity > 0.9f && (weather == WeatherManager.WeatherType.Thunderstorm || 
                                   weather == WeatherManager.WeatherType.Blizzard))
            {
                // Small chance of immediate disaster during extreme weather
                if (GD.Randf() < 0.01f)
                {
                    CheckForDisasters();
                }
            }
        }

        /// <summary>
        /// Handles seasonal changes that affect disaster probabilities
        /// </summary>
        private void OnSeasonChanged(int oldSeason, int newSeason)
        {
            Logger.LogInfo("NaturalDisasterSystem", $"Season changed, disaster probabilities updated");
        }

        #region Public API

        /// <summary>
        /// Gets all currently active disasters
        /// </summary>
        public List<NaturalDisaster> GetActiveDisasters()
        {
            return new List<NaturalDisaster>(_activeDisasters);
        }

        /// <summary>
        /// Checks if a specific disaster type is currently active
        /// </summary>
        public bool IsDisasterActive(DisasterType disasterType)
        {
            return _activeDisasters.Any(d => d.Type == disasterType);
        }

        /// <summary>
        /// Forces a specific disaster to spawn (for testing or scripted events)
        /// </summary>
        public void ForceSpawnDisaster(DisasterType disasterType, float magnitude = 0.8f)
        {
            var disaster = CreateDisaster(disasterType);
            disaster.Magnitude = magnitude;
            _activeDisasters.Add(disaster);
            
            EmitSignal(SignalName.DisasterStarted, disasterType.ToString(), disaster.Magnitude, disaster.Duration);
            EmitSignal(SignalName.CatastrophicEvent, disasterType.ToString(), disaster.Magnitude);
            
            Logger.LogInfo("NaturalDisasterSystem", $"Force spawned {disasterType} disaster");
        }

        /// <summary>
        /// Ends all active disasters (for testing or emergency situations)
        /// </summary>
        public void EndAllDisasters()
        {
            foreach (var disaster in _activeDisasters.ToList())
            {
                EndDisaster(disaster);
            }
            _activeDisasters.Clear();
            
            Logger.LogInfo("NaturalDisasterSystem", "All disasters ended");
        }

        #endregion

        public override void _ExitTree()
        {
            if (_instance == this)
            {
                _instance = null;
            }
        }
    }

    /// <summary>
    /// Base class for natural disasters
    /// </summary>
    public class NaturalDisaster
    {
        public NaturalDisasterSystem.DisasterType Type { get; protected set; }
        public float Magnitude { get; set; } // 0.0 to 1.0
        public float Duration { get; protected set; } // Duration in seconds
        public float WarningTime { get; protected set; } // Warning time before disaster starts
        public float TimeRemaining { get; protected set; }
        public bool IsExpired => TimeRemaining <= 0f;

        protected float _effectTimer;
        protected float _effectInterval = 10f; // Apply effects every 10 seconds

        public NaturalDisaster(NaturalDisasterSystem.DisasterType type, float magnitude, float duration, float warningTime)
        {
            Type = type;
            Magnitude = Mathf.Clamp(magnitude, 0f, 1f);
            Duration = duration;
            WarningTime = warningTime;
            TimeRemaining = duration;
            _effectTimer = _effectInterval;
        }

        public virtual void Update(float delta)
        {
            TimeRemaining -= delta;
            
            // Apply periodic effects
            _effectTimer -= delta;
            if (_effectTimer <= 0f)
            {
                ApplyEffects();
                _effectTimer = _effectInterval;
            }
        }

        protected virtual void ApplyEffects()
        {
            // Base implementation - override in derived classes
        }
    }

    /// <summary>
    /// Mega storm - combines multiple weather hazards
    /// </summary>
    public class MegaStormDisaster : NaturalDisaster
    {
        public MegaStormDisaster() : base(NaturalDisasterSystem.DisasterType.MegaStorm, 
            GD.Randf() * 0.3f + 0.7f, 1800f, 60f) // 30 minutes duration, 1 minute warning
        {
            _effectInterval = 5f;
        }

        protected override void ApplyEffects()
        {
            if (SurvivalStatsSystem.Instance != null)
            {
                // Extreme damage from combined hazards
                float damage = 15f * Magnitude;
                float staminaDrain = 25f * Magnitude;
                float thirstDrain = 10f * Magnitude;
                
                SurvivalStatsSystem.Instance.Health.ModifyValue(-damage);
                SurvivalStatsSystem.Instance.Stamina.ModifyValue(-staminaDrain);
                SurvivalStatsSystem.Instance.Thirst.ModifyValue(-thirstDrain);
                
                Logger.LogInfo("MegaStormDisaster", $"Mega storm effects applied - Damage: {damage:F1}");
            }
        }
    }

    /// <summary>
    /// Volcanic eruption - ash, heat, and toxic gases
    /// </summary>
    public class VolcanicEruptionDisaster : NaturalDisaster
    {
        public VolcanicEruptionDisaster() : base(NaturalDisasterSystem.DisasterType.VolcanicEruption, 
            GD.Randf() * 0.4f + 0.6f, 3600f, 120f) // 1 hour duration, 2 minute warning
        {
            _effectInterval = 8f;
        }

        protected override void ApplyEffects()
        {
            if (SurvivalStatsSystem.Instance != null)
            {
                // Heat damage and respiratory issues
                float damage = 12f * Magnitude;
                float thirstDrain = 15f * Magnitude;
                
                SurvivalStatsSystem.Instance.Health.ModifyValue(-damage);
                SurvivalStatsSystem.Instance.Thirst.ModifyValue(-thirstDrain);
                
                Logger.LogInfo("VolcanicEruptionDisaster", $"Volcanic effects applied - Damage: {damage:F1}");
            }
        }
    }

    /// <summary>
    /// Meteor shower - falling meteors causing fires and impact damage
    /// </summary>
    public class MeteorShowerDisaster : NaturalDisaster
    {
        public MeteorShowerDisaster() : base(NaturalDisasterSystem.DisasterType.MeteorShower, 
            GD.Randf() * 0.5f + 0.5f, 900f, 30f) // 15 minutes duration, 30 second warning
        {
            _effectInterval = 3f;
        }

        protected override void ApplyEffects()
        {
            if (SurvivalStatsSystem.Instance != null && GD.Randf() < 0.3f * Magnitude)
            {
                // Chance of meteor impact
                float damage = 25f * Magnitude;
                SurvivalStatsSystem.Instance.Health.ModifyValue(-damage);
                
                Logger.LogInfo("MeteorShowerDisaster", $"Meteor impact! Damage: {damage:F1}");
            }
        }
    }

    /// <summary>
    /// Solar flare - electromagnetic disruption
    /// </summary>
    public class SolarFlareDisaster : NaturalDisaster
    {
        public SolarFlareDisaster() : base(NaturalDisasterSystem.DisasterType.SolarFlare, 
            GD.Randf() * 0.6f + 0.4f, 1200f, 180f) // 20 minutes duration, 3 minute warning
        {
            _effectInterval = 15f;
        }

        protected override void ApplyEffects()
        {
            // Solar flares primarily affect electronic systems
            // For now, just minor health effects from radiation
            if (SurvivalStatsSystem.Instance != null)
            {
                float damage = 3f * Magnitude;
                SurvivalStatsSystem.Instance.Health.ModifyValue(-damage);
                
                Logger.LogInfo("SolarFlareDisaster", $"Solar radiation effects applied - Damage: {damage:F1}");
            }
        }
    }

    /// <summary>
    /// Tsunami - massive flooding
    /// </summary>
    public class TsunamiDisaster : NaturalDisaster
    {
        public TsunamiDisaster() : base(NaturalDisasterSystem.DisasterType.Tsunami, 
            GD.Randf() * 0.4f + 0.6f, 2400f, 300f) // 40 minutes duration, 5 minute warning
        {
            _effectInterval = 6f;
        }

        protected override void ApplyEffects()
        {
            if (SurvivalStatsSystem.Instance != null)
            {
                // Drowning and hypothermia effects
                float damage = 10f * Magnitude;
                float staminaDrain = 20f * Magnitude;
                
                SurvivalStatsSystem.Instance.Health.ModifyValue(-damage);
                SurvivalStatsSystem.Instance.Stamina.ModifyValue(-staminaDrain);
                
                Logger.LogInfo("TsunamiDisaster", $"Tsunami effects applied - Damage: {damage:F1}");
            }
        }
    }

    /// <summary>
    /// Super blizzard - extreme cold and snow
    /// </summary>
    public class SuperBlizzardDisaster : NaturalDisaster
    {
        public SuperBlizzardDisaster() : base(NaturalDisasterSystem.DisasterType.SuperBlizzard, 
            GD.Randf() * 0.3f + 0.7f, 2700f, 90f) // 45 minutes duration, 1.5 minute warning
        {
            _effectInterval = 4f;
        }

        protected override void ApplyEffects()
        {
            if (SurvivalStatsSystem.Instance != null)
            {
                // Extreme cold effects
                float damage = 18f * Magnitude;
                float hungerDrain = 12f * Magnitude;
                float staminaDrain = 15f * Magnitude;
                
                SurvivalStatsSystem.Instance.Health.ModifyValue(-damage);
                SurvivalStatsSystem.Instance.Hunger.ModifyValue(-hungerDrain);
                SurvivalStatsSystem.Instance.Stamina.ModifyValue(-staminaDrain);
                
                Logger.LogInfo("SuperBlizzardDisaster", $"Super blizzard effects applied - Damage: {damage:F1}");
            }
        }
    }

    /// <summary>
    /// Drought - extended dry period affecting resources
    /// </summary>
    public class DroughtDisaster : NaturalDisaster
    {
        public DroughtDisaster() : base(NaturalDisasterSystem.DisasterType.Drought, 
            GD.Randf() * 0.4f + 0.6f, 7200f, 600f) // 2 hours duration, 10 minute warning
        {
            _effectInterval = 20f;
        }

        protected override void ApplyEffects()
        {
            if (SurvivalStatsSystem.Instance != null)
            {
                // Increased thirst and reduced water sources
                float thirstDrain = 8f * Magnitude;
                SurvivalStatsSystem.Instance.Thirst.ModifyValue(-thirstDrain);
                
                Logger.LogInfo("DroughtDisaster", $"Drought effects applied - Thirst drain: {thirstDrain:F1}");
            }
        }
    }

    /// <summary>
    /// Plague locus - swarms of destructive insects
    /// </summary>
    public class PlagueLocusDisaster : NaturalDisaster
    {
        public PlagueLocusDisaster() : base(NaturalDisasterSystem.DisasterType.PlagueLocus, 
            GD.Randf() * 0.5f + 0.5f, 1800f, 45f) // 30 minutes duration, 45 second warning
        {
            _effectInterval = 7f;
        }

        protected override void ApplyEffects()
        {
            if (SurvivalStatsSystem.Instance != null)
            {
                // Swarm attacks and crop destruction
                float damage = 8f * Magnitude;
                float hungerDrain = 5f * Magnitude; // Crops destroyed
                
                SurvivalStatsSystem.Instance.Health.ModifyValue(-damage);
                SurvivalStatsSystem.Instance.Hunger.ModifyValue(-hungerDrain);
                
                Logger.LogInfo("PlagueLocusDisaster", $"Locust swarm effects applied - Damage: {damage:F1}");
            }
        }
    }
}