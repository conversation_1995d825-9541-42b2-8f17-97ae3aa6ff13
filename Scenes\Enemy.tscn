[gd_scene load_steps=4 format=3 uid="uid://pl4qu5mfgqud"]

[ext_resource type="Script" uid="uid://tqa6w28pmana" path="res://Scripts/Enemy.cs" id="1_0h8vx"]
[ext_resource type="Script" uid="uid://3uvdjkr3o1rl" path="res://Scripts/AIController.cs" id="2_ai_ctrl"]

[sub_resource type="CircleShape2D" id="CircleShape2D_1h8vx"]
radius = 16.0

[node name="Enemy" type="CharacterBody2D"]
script = ExtResource("1_0h8vx")

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
position = Vector2(1, 2)
shape = SubResource("CircleShape2D_1h8vx")

[node name="Sprite2D" type="Sprite2D" parent="."]

[node name="AIController" type="Node2D" parent="."]
script = ExtResource("2_ai_ctrl")
