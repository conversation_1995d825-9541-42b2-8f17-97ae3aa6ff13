using Godot;
using System;
using System.Collections.Generic;
using System.Linq;

namespace SurvivalLooterShooter
{
    /// <summary>
    /// Manages seasonal changes in resource availability and spawning
    /// Integrates with world generation and weather systems
    /// </summary>
    public partial class SeasonalResourceSystem : Node
    {
        private static SeasonalResourceSystem _instance;
        public static SeasonalResourceSystem Instance => _instance;

        // Resource availability by season
        private Dictionary<WeatherManager.Season, SeasonalResourceData> _seasonalResources;
        
        // Current season state
        private WeatherManager.Season _currentSeason = WeatherManager.Season.Spring;
        private Dictionary<string, float> _currentResourceModifiers = new Dictionary<string, float>();
        
        // Resource spawn tracking
        private Dictionary<string, List<Vector2>> _activeResourceNodes = new Dictionary<string, List<Vector2>>();
        private Timer _resourceUpdateTimer;
        private const float RESOURCE_UPDATE_INTERVAL = 300f; // Update resources every 5 minutes

        // Events
        [Signal] public delegate void SeasonalResourcesChangedEventHandler(int season, string resourceModifiersJson);
        [Signal] public delegate void ResourceAvailabilityChangedEventHandler(string resourceId, float availabilityModifier);

        public override void _Ready()
        {
            // Singleton setup
            if (_instance == null)
            {
                _instance = this;
                AddToGroup("seasonal_resource_system");
                Logger.LogInfo("SeasonalResourceSystem", "SeasonalResourceSystem singleton initialized");
            }
            else
            {
                Logger.LogError("SeasonalResourceSystem", "Multiple SeasonalResourceSystem instances detected! Removing duplicate.");
                QueueFree();
                return;
            }

            InitializeSeasonalResources();
            SetupResourceUpdateTimer();
            ConnectToWeatherSystem();
            
            Logger.LogInfo("SeasonalResourceSystem", "Seasonal resource system initialized");
        }

        /// <summary>
        /// Initializes seasonal resource availability data
        /// </summary>
        private void InitializeSeasonalResources()
        {
            _seasonalResources = new Dictionary<WeatherManager.Season, SeasonalResourceData>
            {
                [WeatherManager.Season.Spring] = new SeasonalResourceData
                {
                    ResourceModifiers = new Dictionary<string, float>
                    {
                        ["berries"] = 1.5f,        // More berries in spring
                        ["mushrooms"] = 1.3f,      // More mushrooms
                        ["herbs"] = 1.4f,          // More medicinal herbs
                        ["flowers"] = 2.0f,        // Peak flower season
                        ["young_wood"] = 1.2f,     // New growth wood
                        ["fresh_water"] = 1.1f,    // Spring water sources
                        ["fish"] = 1.3f,           // Fish spawning season
                        ["small_game"] = 1.2f      // Active small animals
                    },
                    Description = "Spring brings abundant plant growth and active wildlife"
                },
                
                [WeatherManager.Season.Summer] = new SeasonalResourceData
                {
                    ResourceModifiers = new Dictionary<string, float>
                    {
                        ["berries"] = 2.0f,        // Peak berry season
                        ["fruits"] = 1.8f,         // Summer fruits
                        ["vegetables"] = 1.6f,     // Garden vegetables
                        ["herbs"] = 1.2f,          // Still good for herbs
                        ["hardwood"] = 1.1f,       // Mature wood
                        ["stone"] = 1.0f,          // Normal stone availability
                        ["metal_ore"] = 1.0f,      // Normal ore availability
                        ["fish"] = 1.1f,           // Good fishing weather
                        ["large_game"] = 1.3f      // Active large animals
                    },
                    Description = "Summer provides peak fruit and vegetable harvests"
                },
                
                [WeatherManager.Season.Autumn] = new SeasonalResourceData
                {
                    ResourceModifiers = new Dictionary<string, float>
                    {
                        ["nuts"] = 2.5f,           // Peak nut season
                        ["mushrooms"] = 1.8f,      // Excellent mushroom season
                        ["root_vegetables"] = 1.6f, // Root harvest time
                        ["hardwood"] = 1.3f,       // Good wood cutting season
                        ["berries"] = 0.8f,        // Fewer berries
                        ["herbs"] = 0.9f,          // Herbs starting to die back
                        ["game_meat"] = 1.4f,      // Animals preparing for winter
                        ["fur"] = 1.5f             // Better fur quality
                    },
                    Description = "Autumn offers nuts, mushrooms, and animals preparing for winter"
                },
                
                [WeatherManager.Season.Winter] = new SeasonalResourceData
                {
                    ResourceModifiers = new Dictionary<string, float>
                    {
                        ["evergreen_wood"] = 1.2f, // Evergreen trees still available
                        ["ice"] = 2.0f,            // Abundant ice
                        ["stone"] = 1.1f,          // Easier to find exposed stone
                        ["metal_ore"] = 1.2f,      // Exposed ore deposits
                        ["berries"] = 0.3f,        // Very few berries
                        ["mushrooms"] = 0.4f,      // Few mushrooms
                        ["herbs"] = 0.2f,          // Almost no herbs
                        ["fish"] = 0.7f,           // Ice fishing limitations
                        ["large_game"] = 0.8f,     // Animals hibernating/migrating
                        ["fur"] = 1.8f             // Premium winter fur
                    },
                    Description = "Winter limits plant resources but provides ice and exposed minerals"
                }
            };

            // Initialize current modifiers with spring values
            UpdateCurrentResourceModifiers(_currentSeason);
        }

        /// <summary>
        /// Sets up the timer for periodic resource updates
        /// </summary>
        private void SetupResourceUpdateTimer()
        {
            _resourceUpdateTimer = new Timer();
            AddChild(_resourceUpdateTimer);
            _resourceUpdateTimer.WaitTime = RESOURCE_UPDATE_INTERVAL;
            _resourceUpdateTimer.Autostart = true;
            _resourceUpdateTimer.Timeout += UpdateResourceAvailability;
        }

        /// <summary>
        /// Connects to weather system for seasonal changes
        /// </summary>
        private void ConnectToWeatherSystem()
        {
            if (WeatherManager.Instance != null)
            {
                WeatherManager.Instance.SeasonChanged += OnSeasonChanged;
                
                // Get current season
                _currentSeason = WeatherManager.Instance.CurrentSeason;
                UpdateCurrentResourceModifiers(_currentSeason);
            }
        }

        private void OnSeasonChanged(WeatherManager.Season oldSeason, WeatherManager.Season newSeason)
        {
            throw new NotImplementedException();
        }

        /// <summary>
        /// Handles seasonal changes
        /// </summary>
        private void OnSeasonChanged(int oldSeason, int newSeason)
        {
            var season = (WeatherManager.Season)newSeason;
            _currentSeason = season;
            
            UpdateCurrentResourceModifiers(season);
            UpdateResourceAvailability();
            
            Logger.LogInfo("SeasonalResourceSystem", $"Season changed to {season}, updating resource availability");
        }

        /// <summary>
        /// Updates current resource modifiers based on season
        /// </summary>
        private void UpdateCurrentResourceModifiers(WeatherManager.Season season)
        {
            if (_seasonalResources.ContainsKey(season))
            {
                _currentResourceModifiers = new Dictionary<string, float>(_seasonalResources[season].ResourceModifiers);
                
                // Emit events
                string modifiersJson = System.Text.Json.JsonSerializer.Serialize(_currentResourceModifiers);
                EmitSignal(SignalName.SeasonalResourcesChanged, (int)season, modifiersJson);
                EventBus.Instance?.EmitSeasonalResourcesChanged((int)season, modifiersJson);
            }
        }

        /// <summary>
        /// Updates resource availability in the world
        /// </summary>
        private void UpdateResourceAvailability()
        {
            foreach (var modifier in _currentResourceModifiers)
            {
                EmitSignal(SignalName.ResourceAvailabilityChanged, modifier.Key, modifier.Value);
                
                Logger.LogInfo("SeasonalResourceSystem", 
                    $"Resource {modifier.Key} availability: {modifier.Value:F2}x");
            }
        }

        #region Public API

        /// <summary>
        /// Gets the current resource availability modifier for a specific resource
        /// </summary>
        public float GetResourceAvailability(string resourceId)
        {
            return _currentResourceModifiers.GetValueOrDefault(resourceId, 1.0f);
        }

        /// <summary>
        /// Gets all current resource modifiers
        /// </summary>
        public Dictionary<string, float> GetAllResourceModifiers()
        {
            return new Dictionary<string, float>(_currentResourceModifiers);
        }

        /// <summary>
        /// Gets seasonal resource data for a specific season
        /// </summary>
        public SeasonalResourceData GetSeasonalData(WeatherManager.Season season)
        {
            return _seasonalResources.GetValueOrDefault(season, new SeasonalResourceData());
        }

        /// <summary>
        /// Checks if a resource is abundant in the current season (>1.5x modifier)
        /// </summary>
        public bool IsResourceAbundant(string resourceId)
        {
            return GetResourceAvailability(resourceId) >= 1.5f;
        }

        /// <summary>
        /// Checks if a resource is scarce in the current season (<0.5x modifier)
        /// </summary>
        public bool IsResourceScarce(string resourceId)
        {
            return GetResourceAvailability(resourceId) <= 0.5f;
        }

        /// <summary>
        /// Gets a description of the current season's resource availability
        /// </summary>
        public string GetSeasonalDescription()
        {
            if (_seasonalResources.ContainsKey(_currentSeason))
            {
                return _seasonalResources[_currentSeason].Description;
            }
            return "Normal resource availability";
        }

        /// <summary>
        /// Forces a resource availability update (useful for testing)
        /// </summary>
        public void ForceResourceUpdate()
        {
            UpdateResourceAvailability();
        }

        #endregion

        public override void _ExitTree()
        {
            if (_instance == this)
            {
                _instance = null;
            }
        }
    }

    /// <summary>
    /// Data structure for seasonal resource information
    /// </summary>
    public class SeasonalResourceData
    {
        public Dictionary<string, float> ResourceModifiers { get; set; } = new Dictionary<string, float>();
        public string Description { get; set; } = "";
    }
}