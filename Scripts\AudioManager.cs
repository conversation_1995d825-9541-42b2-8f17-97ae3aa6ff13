using Godot;
using System;
using System.Collections.Generic;

public partial class AudioManager : Node
{
    private static AudioManager _instance;
    public static AudioManager Instance => _instance;

    [Signal]
    public delegate void VolumeChangedEventHandler();

    // Audio buses
    private const string MASTER_BUS = "Master";
    private const string SFX_BUS = "SFX";
    private const string MUSIC_BUS = "Music";
    private const string AMBIENT_BUS = "Ambient";

    // Volume settings
    public float MasterVolume { get; private set; } = 1.0f;
    public float SFXVolume { get; private set; } = 1.0f;
    public float MusicVolume { get; private set; } = 1.0f;
    public float AmbientVolume { get; private set; } = 1.0f;

    // Audio players
    private AudioStreamPlayer _musicPlayer;
    private AudioStreamPlayer _ambientPlayer;
    private List<AudioStreamPlayer> _sfxPlayers = new List<AudioStreamPlayer>();
    private int _maxSFXPlayers = 10;

    // Audio resources
    private Dictionary<string, AudioStream> _sfxLibrary = new Dictionary<string, AudioStream>();
    private Dictionary<string, AudioStream> _musicLibrary = new Dictionary<string, AudioStream>();
    private Dictionary<string, AudioStream> _ambientLibrary = new Dictionary<string, AudioStream>();

    private const string SETTINGS_FILE = "user://audio_settings.cfg";

    public override void _Ready()
    {
        _instance = this;
        SetupAudioPlayers();
        LoadAudioResources();
        LoadSettings();
        ApplyVolumeSettings();
    }

    private void SetupAudioPlayers()
    {
        // Music player
        _musicPlayer = new AudioStreamPlayer();
        _musicPlayer.Bus = MUSIC_BUS;
        AddChild(_musicPlayer);

        // Ambient player
        _ambientPlayer = new AudioStreamPlayer();
        _ambientPlayer.Bus = AMBIENT_BUS;
        AddChild(_ambientPlayer);

        // SFX players pool
        for (int i = 0; i < _maxSFXPlayers; i++)
        {
            var sfxPlayer = new AudioStreamPlayer();
            sfxPlayer.Bus = SFX_BUS;
            AddChild(sfxPlayer);
            _sfxPlayers.Add(sfxPlayer);
        }
    }

    private void LoadAudioResources()
    {
        // Load SFX
        LoadAudioFromDirectory("res://Audio/SFX/", _sfxLibrary);
        
        // Load Music
        LoadAudioFromDirectory("res://Audio/Music/", _musicLibrary);
        
        // Load Ambient
        LoadAudioFromDirectory("res://Audio/Ambient/", _ambientLibrary);
    }

    private void LoadAudioFromDirectory(string path, Dictionary<string, AudioStream> library)
    {
        if (!DirAccess.DirExistsAbsolute(path))
        {
            GD.Print($"Audio directory not found: {path}");
            return;
        }

        var dir = DirAccess.Open(path);
        if (dir != null)
        {
            dir.ListDirBegin();
            string fileName = dir.GetNext();
            
            while (fileName != "")
            {
                if (!dir.CurrentIsDir() && (fileName.EndsWith(".ogg") || fileName.EndsWith(".wav") || fileName.EndsWith(".mp3")))
                {
                    var audioStream = GD.Load<AudioStream>(path + fileName);
                    if (audioStream != null)
                    {
                        string key = fileName.GetBaseName();
                        library[key] = audioStream;
                        GD.Print($"Loaded audio: {key}");
                    }
                }
                fileName = dir.GetNext();
            }
        }
    }

    public void PlaySFX(string soundName, float volume = 1.0f, float pitch = 1.0f)
    {
        if (!_sfxLibrary.ContainsKey(soundName))
        {
            GD.PrintErr($"SFX not found: {soundName}");
            return;
        }

        var availablePlayer = GetAvailableSFXPlayer();
        if (availablePlayer != null)
        {
            availablePlayer.Stream = _sfxLibrary[soundName];
            availablePlayer.VolumeDb = Mathf.LinearToDb(volume * SFXVolume);
            availablePlayer.PitchScale = pitch;
            availablePlayer.Play();
        }
    }

    public void PlayMusic(string musicName, bool loop = true, float fadeInTime = 1.0f)
    {
        if (!_musicLibrary.ContainsKey(musicName))
        {
            GD.PrintErr($"Music not found: {musicName}");
            return;
        }

        if (_musicPlayer.Playing)
        {
            FadeOutMusic(fadeInTime, () => StartMusic(musicName, loop, fadeInTime));
        }
        else
        {
            StartMusic(musicName, loop, fadeInTime);
        }
    }

    private void StartMusic(string musicName, bool loop, float fadeInTime)
    {
        _musicPlayer.Stream = _musicLibrary[musicName];
        if (_musicPlayer.Stream is AudioStreamOggVorbis oggStream)
        {
            oggStream.Loop = loop;
        }
        
        _musicPlayer.VolumeDb = Mathf.LinearToDb(0.01f); // Start very quiet
        _musicPlayer.Play();
        
        if (fadeInTime > 0)
        {
            FadeInMusic(fadeInTime);
        }
        else
        {
            _musicPlayer.VolumeDb = Mathf.LinearToDb(MusicVolume);
        }
    }

    public void StopMusic(float fadeOutTime = 1.0f)
    {
        if (fadeOutTime > 0)
        {
            FadeOutMusic(fadeOutTime, () => _musicPlayer.Stop());
        }
        else
        {
            _musicPlayer.Stop();
        }
    }

    public void PlayAmbient(string ambientName, bool loop = true, float volume = 1.0f)
    {
        if (!_ambientLibrary.ContainsKey(ambientName))
        {
            GD.PrintErr($"Ambient sound not found: {ambientName}");
            return;
        }

        _ambientPlayer.Stream = _ambientLibrary[ambientName];
        if (_ambientPlayer.Stream is AudioStreamOggVorbis oggStream)
        {
            oggStream.Loop = loop;
        }
        
        _ambientPlayer.VolumeDb = Mathf.LinearToDb(volume * AmbientVolume);
        _ambientPlayer.Play();
    }

    public void StopAmbient()
    {
        _ambientPlayer.Stop();
    }

    private AudioStreamPlayer GetAvailableSFXPlayer()
    {
        foreach (var player in _sfxPlayers)
        {
            if (!player.Playing)
            {
                return player;
            }
        }
        
        // If all players are busy, use the first one (interrupt)
        return _sfxPlayers[0];
    }

    private void FadeInMusic(float duration)
    {
        var tween = CreateTween();
        tween.TweenMethod(Callable.From<float>(SetMusicVolumeInternal), 0.01f, MusicVolume, duration);
    }

    private void FadeOutMusic(float duration, Action onComplete = null)
    {
        var currentVolume = Mathf.DbToLinear(_musicPlayer.VolumeDb);
        var tween = CreateTween();
        tween.TweenMethod(Callable.From<float>(SetMusicVolumeInternal), currentVolume, 0.01f, duration);
        if (onComplete != null)
        {
            tween.TweenCallback(Callable.From(onComplete));
        }
    }

    private void SetMusicVolumeInternal(float volume)
    {
        _musicPlayer.VolumeDb = Mathf.LinearToDb(volume);
    }

    public void SetMasterVolume(float volume)
    {
        MasterVolume = Mathf.Clamp(volume, 0.0f, 1.0f);
        ApplyVolumeSettings();
        SaveSettings();
        EmitSignal(SignalName.VolumeChanged);
    }

    public void SetSFXVolume(float volume)
    {
        SFXVolume = Mathf.Clamp(volume, 0.0f, 1.0f);
        ApplyVolumeSettings();
        SaveSettings();
        EmitSignal(SignalName.VolumeChanged);
    }

    public void SetMusicVolume(float volume)
    {
        MusicVolume = Mathf.Clamp(volume, 0.0f, 1.0f);
        ApplyVolumeSettings();
        SaveSettings();
        EmitSignal(SignalName.VolumeChanged);
    }

    public void SetAmbientVolume(float volume)
    {
        AmbientVolume = Mathf.Clamp(volume, 0.0f, 1.0f);
        ApplyVolumeSettings();
        SaveSettings();
        EmitSignal(SignalName.VolumeChanged);
    }

    private void ApplyVolumeSettings()
    {
        AudioServer.SetBusVolumeDb(AudioServer.GetBusIndex(MASTER_BUS), Mathf.LinearToDb(MasterVolume));
        AudioServer.SetBusVolumeDb(AudioServer.GetBusIndex(SFX_BUS), Mathf.LinearToDb(SFXVolume));
        AudioServer.SetBusVolumeDb(AudioServer.GetBusIndex(MUSIC_BUS), Mathf.LinearToDb(MusicVolume));
        AudioServer.SetBusVolumeDb(AudioServer.GetBusIndex(AMBIENT_BUS), Mathf.LinearToDb(AmbientVolume));
    }

    private void SaveSettings()
    {
        var config = new ConfigFile();
        config.SetValue("audio", "master_volume", MasterVolume);
        config.SetValue("audio", "sfx_volume", SFXVolume);
        config.SetValue("audio", "music_volume", MusicVolume);
        config.SetValue("audio", "ambient_volume", AmbientVolume);
        config.Save(SETTINGS_FILE);
    }

    private void LoadSettings()
    {
        var config = new ConfigFile();
        var error = config.Load(SETTINGS_FILE);
        
        if (error != Error.Ok)
        {
            GD.Print("No audio settings file found, using defaults");
            return;
        }

        MasterVolume = config.GetValue("audio", "master_volume", 1.0f).AsSingle();
        SFXVolume = config.GetValue("audio", "sfx_volume", 1.0f).AsSingle();
        MusicVolume = config.GetValue("audio", "music_volume", 1.0f).AsSingle();
        AmbientVolume = config.GetValue("audio", "ambient_volume", 1.0f).AsSingle();
    }

    public void ResetToDefaults()
    {
        SetMasterVolume(1.0f);
        SetSFXVolume(1.0f);
        SetMusicVolume(1.0f);
        SetAmbientVolume(1.0f);
    }

    // Convenience methods for common game sounds
    public void PlayUISound(string soundName) => PlaySFX(soundName, 0.8f);
    public void PlayWeaponSound(string soundName) => PlaySFX(soundName, 1.0f);
    public void PlayFootstepSound(string soundName) => PlaySFX(soundName, 0.6f, (float)GD.RandRange(0.9, 1.1));
    public void PlayImpactSound(string soundName) => PlaySFX(soundName, 0.9f);
}