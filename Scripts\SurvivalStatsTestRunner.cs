using Godot;

namespace SurvivalLooterShooter
{
    /// <summary>
    /// Simple test runner to verify survival stats system functionality
    /// </summary>
    public partial class SurvivalStatsTestRunner : Node
    {
        private SurvivalStatsSystem _statsSystem;

        public override void _Ready()
        {
            GD.Print("=== Survival Stats System Test Runner ===");
            
            // Create and initialize the survival stats system
            _statsSystem = new SurvivalStatsSystem();
            AddChild(_statsSystem);
            
            // Wait a moment for initialization, then run tests
            GetTree().CreateTimer(1.0f).Timeout += RunBasicTests;
        }

        private void RunBasicTests()
        {
            GD.Print("\n--- Testing Basic Functionality ---");
            
            // Test initial state
            GD.Print($"Health: {_statsSystem.Health}");
            GD.Print($"Hunger: {_statsSystem.Hunger}");
            GD.Print($"Thirst: {_statsSystem.Thirst}");
            GD.Print($"Stamina: {_statsSystem.Stamina}");
            GD.Print($"Is Dead: {_statsSystem.IsDead}");
            
            // Test consumable usage
            GD.Print("\n--- Testing Consumables ---");
            
            // Lower some stats first
            _statsSystem.Health.SetValue(50f);
            _statsSystem.Hunger.SetValue(30f);
            _statsSystem.Thirst.SetValue(40f);
            _statsSystem.Stamina.SetValue(60f);
            
            GD.Print("After lowering stats:");
            GD.Print($"Health: {_statsSystem.Health.CurrentValue:F1}");
            GD.Print($"Hunger: {_statsSystem.Hunger.CurrentValue:F1}");
            GD.Print($"Thirst: {_statsSystem.Thirst.CurrentValue:F1}");
            GD.Print($"Stamina: {_statsSystem.Stamina.CurrentValue:F1}");
            
            // Test consumables
            bool result = _statsSystem.ConsumeItem("health_potion");
            GD.Print($"Health potion consumed: {result}");
            GD.Print($"Health after potion: {_statsSystem.Health.CurrentValue:F1}");
            
            result = _statsSystem.ConsumeItem("water");
            GD.Print($"Water consumed: {result}");
            GD.Print($"Thirst after water: {_statsSystem.Thirst.CurrentValue:F1}");
            
            result = _statsSystem.ConsumeItem("canned_food");
            GD.Print($"Canned food consumed: {result}");
            GD.Print($"Hunger after food: {_statsSystem.Hunger.CurrentValue:F1}");
            
            result = _statsSystem.ConsumeItem("energy_drink");
            GD.Print($"Energy drink consumed: {result}");
            GD.Print($"Stamina after drink: {_statsSystem.Stamina.CurrentValue:F1}");
            GD.Print($"Thirst after drink: {_statsSystem.Thirst.CurrentValue:F1}");
            
            // Test MRE (multiple effects)
            result = _statsSystem.ConsumeItem("mre");
            GD.Print($"MRE consumed: {result}");
            GD.Print($"Health after MRE: {_statsSystem.Health.CurrentValue:F1}");
            GD.Print($"Hunger after MRE: {_statsSystem.Hunger.CurrentValue:F1}");
            GD.Print($"Thirst after MRE: {_statsSystem.Thirst.CurrentValue:F1}");
            
            // Test save/load
            GD.Print("\n--- Testing Save/Load ---");
            var saveData = _statsSystem.GetAllStatsData();
            GD.Print("Save data created");
            
            // Modify stats
            _statsSystem.Health.SetValue(25f);
            _statsSystem.Hunger.SetValue(10f);
            GD.Print($"Modified - Health: {_statsSystem.Health.CurrentValue:F1}, Hunger: {_statsSystem.Hunger.CurrentValue:F1}");
            
            // Load save data
            _statsSystem.LoadAllStatsData(saveData);
            GD.Print($"Loaded - Health: {_statsSystem.Health.CurrentValue:F1}, Hunger: {_statsSystem.Hunger.CurrentValue:F1}");
            
            // Test death mechanics
            GD.Print("\n--- Testing Death Mechanics ---");
            _statsSystem.Health.SetValue(0f);
            GD.Print($"Set health to 0, Is Dead: {_statsSystem.IsDead}");
            
            GD.Print("\n=== Test Runner Complete ===");
        }
    }
}