using Godot;
using System.Collections.Generic;

namespace SurvivalLooterShooter
{
    /// <summary>
    /// Test runner for the progression reward system
    /// </summary>
    public partial class ProgressionSystemTestRunner : Node
    {
        private ProgressionRewardSystem _progressionSystem;
        private AbilitySystem _abilitySystem;
        private SkillManager _skillManager;
        
        public override void _Ready()
        {
            GD.Print("=== Progression System Test Runner Started ===");
            
            // Initialize systems
            InitializeSystems();
            
            // Run tests
            RunProgressionTests();
        }
        
        private void InitializeSystems()
        {
            // Add SkillManager
            _skillManager = new SkillManager();
            AddChild(_skillManager);
            
            // Add ProgressionRewardSystem
            _progressionSystem = new ProgressionRewardSystem();
            AddChild(_progressionSystem);
            
            // Add AbilitySystem
            _abilitySystem = new AbilitySystem();
            AddChild(_abilitySystem);
            
            // Wait a frame for systems to initialize
            GetTree().ProcessFrame += OnSystemsInitialized;
        }
        
        private void OnSystemsInitialized()
        {
            GetTree().ProcessFrame -= OnSystemsInitialized;
            
            // Connect to progression events
            if (_progressionSystem != null)
            {
                _progressionSystem.RecipeUnlocked += OnRecipeUnlocked;
                _progressionSystem.MilestoneAchieved += OnMilestoneAchieved;
                _progressionSystem.PrestigeLevelUnlocked += OnPrestigeLevelUnlocked;
                _progressionSystem.DeathPenaltyApplied += OnDeathPenaltyApplied;
            }
            
            if (_abilitySystem != null)
            {
                _abilitySystem.AbilityUnlocked += OnAbilityUnlocked;
                _abilitySystem.AbilityActivated += OnAbilityActivated;
                _abilitySystem.CraftingEnhancementUnlocked += OnCraftingEnhancementUnlocked;
            }
        }
        
        private void RunProgressionTests()
        {
            GD.Print("\n--- Testing Recipe Unlocks ---");
            TestRecipeUnlocks();
            
            GD.Print("\n--- Testing Milestone Achievements ---");
            TestMilestoneAchievements();
            
            GD.Print("\n--- Testing Ability System ---");
            TestAbilitySystem();
            
            GD.Print("\n--- Testing Death Penalties ---");
            TestDeathPenalties();
            
            GD.Print("\n--- Testing Save/Load ---");
            TestSaveLoad();
            
            GD.Print("\n=== Progression System Tests Complete ===");
        }
        
        private void TestRecipeUnlocks()
        {
            if (_skillManager == null || _progressionSystem == null)
            {
                GD.PrintErr("Systems not initialized for recipe unlock test");
                return;
            }
            
            // Test weapon proficiency skill progression
            GD.Print("Testing weapon proficiency progression...");
            
            // Simulate gaining experience and leveling up
            for (int level = 1; level <= 15; level++)
            {
                // Simulate skill level up
                EventBus.Instance?.EmitSignal(EventBus.SignalName.SkillLevelUp, "weapon_proficiency", level);
                
                // Check for unlocked recipes
                var unlockedRecipes = _progressionSystem.GetAvailableRecipes();
                if (unlockedRecipes.Count > 0)
                {
                    GD.Print($"  Level {level}: {unlockedRecipes.Count} recipes unlocked");
                }
            }
        }
        
        private void TestMilestoneAchievements()
        {
            if (_skillManager == null || _progressionSystem == null)
            {
                GD.PrintErr("Systems not initialized for milestone test");
                return;
            }
            
            // Test combat milestone
            GD.Print("Testing combat milestone achievement...");
            EventBus.Instance?.EmitSignal(EventBus.SignalName.SkillLevelUp, "weapon_proficiency", 10);
            
            // Test crafting milestone
            GD.Print("Testing crafting milestone achievement...");
            EventBus.Instance?.EmitSignal(EventBus.SignalName.SkillLevelUp, "crafting_efficiency", 20);
        }
        
        private void TestAbilitySystem()
        {
            if (_abilitySystem == null)
            {
                GD.PrintErr("AbilitySystem not initialized for ability test");
                return;
            }
            
            // Test ability unlocks through skill progression
            GD.Print("Testing ability unlocks...");
            EventBus.Instance?.EmitSignal(EventBus.SignalName.SkillLevelUp, "weapon_proficiency", 25);
            EventBus.Instance?.EmitSignal(EventBus.SignalName.SkillLevelUp, "critical_hit", 15);
            
            // Test ability activation
            GD.Print("Testing ability activation...");
            bool activated = _abilitySystem.ActivateAbility("berserker_mode");
            GD.Print($"  Berserker mode activation: {activated}");
            
            // Test crafting enhancement unlocks
            GD.Print("Testing crafting enhancement unlocks...");
            EventBus.Instance?.EmitSignal(EventBus.SignalName.SkillLevelUp, "crafting_efficiency", 10);
            EventBus.Instance?.EmitSignal(EventBus.SignalName.SkillLevelUp, "quality_bonus", 12);
        }
        
        private void TestDeathPenalties()
        {
            if (_progressionSystem == null)
            {
                GD.PrintErr("ProgressionRewardSystem not initialized for death penalty test");
                return;
            }
            
            // Simulate player death
            GD.Print("Testing death penalties...");
            EventBus.Instance?.EmitSignal(EventBus.SignalName.PlayerDied, "enemy_attack");
            
            // Test experience debt recovery
            GD.Print("Testing experience debt recovery...");
            EventBus.Instance?.EmitSignal(EventBus.SignalName.ExperienceGained, "test_action", (int)SkillType.Combat, 100f);
        }
        
        private void TestSaveLoad()
        {
            if (_progressionSystem == null || _abilitySystem == null || _skillManager == null)
            {
                GD.PrintErr("Systems not initialized for save/load test");
                return;
            }
            
            // Get save data from all systems
            var progressionSaveData = _progressionSystem.GetSaveData();
            var abilitySaveData = _abilitySystem.GetSaveData();
            var skillSaveData = _skillManager.GetSaveData();
            
            GD.Print($"Progression save data keys: {progressionSaveData.Count}");
            GD.Print($"Ability save data keys: {abilitySaveData.Count}");
            GD.Print($"Skill save data keys: {skillSaveData.Count}");
            
            // Test loading the data back
            _progressionSystem.LoadSaveData(progressionSaveData);
            _abilitySystem.LoadSaveData(abilitySaveData);
            _skillManager.LoadSaveData(skillSaveData);
            
            GD.Print("Save/Load test completed successfully");
        }
        
        // Event handlers
        private void OnRecipeUnlocked(string recipeId, string skillId, int skillLevel)
        {
            GD.Print($"  ✓ Recipe unlocked: {recipeId} (Skill: {skillId}, Level: {skillLevel})");
        }
        
        private void OnMilestoneAchieved(string milestoneId, string skillId, int level)
        {
            GD.Print($"  ✓ Milestone achieved: {milestoneId} (Skill: {skillId}, Level: {level})");
        }
        
        private void OnPrestigeLevelUnlocked(string skillId, int prestigeLevel)
        {
            GD.Print($"  ✓ Prestige level unlocked: {skillId} Level {prestigeLevel}");
        }
        
        private void OnDeathPenaltyApplied(string penaltyInfo)
        {
            GD.Print($"  ✓ {penaltyInfo}");
        }
        
        private void OnAbilityUnlocked(string abilityId, string abilityName)
        {
            GD.Print($"  ✓ Ability unlocked: {abilityName} ({abilityId})");
        }
        
        private void OnAbilityActivated(string abilityId, float duration)
        {
            GD.Print($"  ✓ Ability activated: {abilityId} (Duration: {duration}s)");
        }
        
        private void OnCraftingEnhancementUnlocked(string enhancementId, string description)
        {
            GD.Print($"  ✓ Crafting enhancement unlocked: {enhancementId} - {description}");
        }
        
        public override void _Input(InputEvent @event)
        {
            if (@event is InputEventKey keyEvent && keyEvent.Pressed)
            {
                switch (keyEvent.Keycode)
                {
                    case Key.Key1:
                        GD.Print("Manual test: Weapon proficiency level up");
                        EventBus.Instance?.EmitSignal(EventBus.SignalName.SkillLevelUp, "weapon_proficiency", 5);
                        break;
                        
                    case Key.Key2:
                        GD.Print("Manual test: Crafting efficiency level up");
                        EventBus.Instance?.EmitSignal(EventBus.SignalName.SkillLevelUp, "crafting_efficiency", 10);
                        break;
                        
                    case Key.Key3:
                        GD.Print("Manual test: Activate berserker mode");
                        _abilitySystem?.ActivateAbility("berserker_mode");
                        break;
                        
                    case Key.Key4:
                        GD.Print("Manual test: Player death");
                        EventBus.Instance?.EmitSignal(EventBus.SignalName.PlayerDied, "manual_test");
                        break;
                        
                    case Key.Key5:
                        GD.Print("Manual test: Gain experience");
                        EventBus.Instance?.EmitSignal(EventBus.SignalName.ExperienceGained, "manual_test", (int)SkillType.Combat, 50f);
                        break;
                }
            }
        }
    }
}