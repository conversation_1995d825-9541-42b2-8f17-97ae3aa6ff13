[folding]

node_unfolds=[Node<PERSON><PERSON>("."), PackedStringArray("Layout"), NodePath("Background"), PackedStringArray("Layout"), NodePath("Background/CloseButton"), PackedStringArray("Layout"), NodePath("Background/VBoxContainer"), PackedStringArray("Layout"), NodePath("Background/VBoxContainer/Header"), PackedStringArray("Layout"), NodePath("Background/VBoxContainer/Header/StationInfo"), PackedStringArray("Layout"), NodePath("Background/VBoxContainer/Header/StationInfo/StationNameLabel"), PackedStringArray("Layout"), NodePath("Background/VBoxContainer/Header/StationInfo/StationLevelLabel"), PackedStringArray("Layout"), NodePath("Background/VBoxContainer/Header/CraftingProgress"), PackedStringArray("Visibility", "Layout"), Node<PERSON>ath("Background/VBoxContainer/Header/QueueTimeLabel"), PackedStringArray("Layout"), NodePath("Background/VBoxContainer/MainContainer"), PackedStringArray("Layout"), NodePath("Background/VBoxContainer/MainContainer/RecipeList"), PackedStringArray("Layout"), NodePath("Background/VBoxContainer/MainContainer/RecipeList/Label"), PackedStringArray("Layout"), NodePath("Background/VBoxContainer/MainContainer/RecipeList/RecipeScrollContainer"), PackedStringArray("Layout"), NodePath("Background/VBoxContainer/MainContainer/RecipeList/RecipeScrollContainer/RecipeContainer"), PackedStringArray("Layout"), NodePath("Background/VBoxContainer/MainContainer/QueuePanel"), PackedStringArray("Layout"), NodePath("Background/VBoxContainer/MainContainer/QueuePanel/Label"), PackedStringArray("Layout"), NodePath("Background/VBoxContainer/MainContainer/QueuePanel/QueueScrollContainer"), PackedStringArray("Layout"), NodePath("Background/VBoxContainer/MainContainer/QueuePanel/QueueScrollContainer/QueueContainer"), PackedStringArray("Layout"), NodePath("Background/VBoxContainer/MainContainer/RecipeDetails"), PackedStringArray("Layout"), NodePath("Background/VBoxContainer/MainContainer/RecipeDetails/Label"), PackedStringArray("Layout"), NodePath("Background/VBoxContainer/MainContainer/RecipeDetails/SelectedRecipePanel"), PackedStringArray("Layout"), NodePath("Background/VBoxContainer/MainContainer/RecipeDetails/SelectedRecipePanel/SelectedRecipeContainer"), PackedStringArray("Layout"), NodePath("Background/VBoxContainer/MainContainer/RecipeDetails/SelectedRecipePanel/SelectedRecipeContainer/RecipeNameLabel"), PackedStringArray("Layout"), NodePath("Background/VBoxContainer/MainContainer/RecipeDetails/SelectedRecipePanel/SelectedRecipeContainer/CraftingControls"), PackedStringArray("Layout"), NodePath("Background/VBoxContainer/MainContainer/RecipeDetails/SelectedRecipePanel/SelectedRecipeContainer/CraftingControls/Label"), PackedStringArray("Layout"), NodePath("Background/VBoxContainer/MainContainer/RecipeDetails/SelectedRecipePanel/SelectedRecipeContainer/CraftingControls/QuantitySpinBox"), PackedStringArray("Layout"), NodePath("Background/VBoxContainer/MainContainer/RecipeDetails/SelectedRecipePanel/SelectedRecipeContainer/CraftingControls/QueueButton"), PackedStringArray("Layout"), NodePath("Background/VBoxContainer/MainContainer/RecipeDetails/SelectedRecipePanel/SelectedRecipeContainer/UpgradeButton"), PackedStringArray("Visibility", "Layout")]
resource_unfolds=[]
nodes_folded=[]
