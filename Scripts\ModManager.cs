using Godot;
using SurvivalLooterShooter;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;

[System.Serializable]
public class ModInfo
{
    public string ModId { get; set; }
    public string Name { get; set; }
    public string Version { get; set; }
    public string Author { get; set; }
    public string Description { get; set; }
    public List<string> Dependencies { get; set; } = new();
    public bool IsEnabled { get; set; } = true;
    public string ModPath { get; set; }
    public DateTime LastModified { get; set; }
}

[System.Serializable]
public class CustomContent
{
    public string ContentId { get; set; }
    public string ContentType { get; set; } // "item", "recipe", "enemy", "structure", etc.
    public string Name { get; set; }
    public Dictionary<string, object> Properties { get; set; } = new();
    public string ModId { get; set; }
}

public partial class ModManager : Node
{
    public static ModManager Instance { get; private set; }
    
    [Export] public string ModsDirectory { get; set; } = "user://mods/";
    [Export] public bool AllowScriptMods { get; set; } = false; // Security consideration
    
    private List<ModInfo> _loadedMods = new();
    private Dictionary<string, CustomContent> _customItems = new();
    private Dictionary<string, CustomContent> _customRecipes = new();
    private Dictionary<string, CustomContent> _customEnemies = new();
    private Dictionary<string, CustomContent> _customStructures = new();
    
    private const string ModConfigFile = "mod.json";
    private const string ModSettingsFile = "user://mod_settings.save";

    public override void _Ready()
    {
        if (Instance == null)
        {
            Instance = this;
            CreateModsDirectory();
            LoadModSettings();
            ScanAndLoadMods();
        }
        else
        {
            QueueFree();
        }
    }

    private void CreateModsDirectory()
    {
        if (!DirAccess.DirExistsAbsolute(ModsDirectory))
        {
            DirAccess.MakeDirRecursiveAbsolute(ModsDirectory);
            GD.Print($"Created mods directory: {ModsDirectory}");
        }
    }

    private void ScanAndLoadMods()
    {
        var dir = DirAccess.Open(ModsDirectory);
        if (dir == null) return;

        dir.ListDirBegin();
        var fileName = dir.GetNext();
        
        while (fileName != "")
        {
            if (dir.CurrentIsDir() && !fileName.StartsWith("."))
            {
                var modPath = Path.Combine(ModsDirectory, fileName);
                LoadMod(modPath);
            }
            fileName = dir.GetNext();
        }
        
        dir.ListDirEnd();
        
        // Sort mods by dependencies
        _loadedMods = ResolveDependencies(_loadedMods);
        
        // Apply loaded mods
        foreach (var mod in _loadedMods.Where(m => m.IsEnabled))
        {
            ApplyMod(mod);
        }
        
        GD.Print($"Loaded {_loadedMods.Count} mods");
    }

    private void LoadMod(string modPath)
    {
        var configPath = Path.Combine(modPath, ModConfigFile);
        
        if (!Godot.FileAccess.FileExists(configPath))
        {
            GD.PrintErr($"Mod config not found: {configPath}");
            return;
        }

        try
        {
            using var configFile = Godot.FileAccess.Open(configPath, Godot.FileAccess.ModeFlags.Read);
            if (configFile != null)
            {
                var jsonString = configFile.GetAsText();
                var json = new Json();
                var parseResult = json.Parse(jsonString);
                
                if (parseResult == Error.Ok)
                {
                    var modData = json.Data.AsGodotDictionary();
                    var modInfo = DeserializeModInfo(modData, modPath);
                    
                    if (ValidateMod(modInfo))
                    {
                        _loadedMods.Add(modInfo);
                        GD.Print($"Loaded mod: {modInfo.Name} v{modInfo.Version}");
                    }
                }
            }
        }
        catch (Exception ex)
        {
            GD.PrintErr($"Failed to load mod from {modPath}: {ex.Message}");
        }
    }

    private ModInfo DeserializeModInfo(Godot.Collections.Dictionary data, string modPath)
    {
        var modInfo = new ModInfo
        {
            ModPath = modPath,
            LastModified = DateTime.Now
        };
        
        if (data.ContainsKey("mod_id"))
            modInfo.ModId = data["mod_id"].AsString();
        if (data.ContainsKey("name"))
            modInfo.Name = data["name"].AsString();
        if (data.ContainsKey("version"))
            modInfo.Version = data["version"].AsString();
        if (data.ContainsKey("author"))
            modInfo.Author = data["author"].AsString();
        if (data.ContainsKey("description"))
            modInfo.Description = data["description"].AsString();
        
        if (data.ContainsKey("dependencies"))
        {
            var depsArray = data["dependencies"].AsGodotArray();
            foreach (var dep in depsArray)
            {
                modInfo.Dependencies.Add(dep.AsString());
            }
        }
        
        return modInfo;
    }

    private bool ValidateMod(ModInfo modInfo)
    {
        // Basic validation
        if (string.IsNullOrEmpty(modInfo.ModId) || string.IsNullOrEmpty(modInfo.Name))
        {
            GD.PrintErr($"Invalid mod: Missing required fields");
            return false;
        }
        
        // Check for duplicate mod IDs
        if (_loadedMods.Any(m => m.ModId == modInfo.ModId))
        {
            GD.PrintErr($"Duplicate mod ID: {modInfo.ModId}");
            return false;
        }
        
        return true;
    }

    private List<ModInfo> ResolveDependencies(List<ModInfo> mods)
    {
        var resolved = new List<ModInfo>();
        var visited = new HashSet<string>();
        
        foreach (var mod in mods)
        {
            ResolveDependenciesRecursive(mod, mods, resolved, visited);
        }
        
        return resolved;
    }

    private void ResolveDependenciesRecursive(ModInfo mod, List<ModInfo> allMods, List<ModInfo> resolved, HashSet<string> visited)
    {
        if (visited.Contains(mod.ModId)) return;
        visited.Add(mod.ModId);
        
        // Resolve dependencies first
        foreach (var depId in mod.Dependencies)
        {
            var dependency = allMods.FirstOrDefault(m => m.ModId == depId);
            if (dependency != null)
            {
                ResolveDependenciesRecursive(dependency, allMods, resolved, visited);
            }
            else
            {
                GD.PrintErr($"Missing dependency: {depId} for mod {mod.ModId}");
            }
        }
        
        if (!resolved.Contains(mod))
        {
            resolved.Add(mod);
        }
    }

    private void ApplyMod(ModInfo mod)
    {
        // Load custom content from mod
        LoadCustomItems(mod);
        LoadCustomRecipes(mod);
        LoadCustomEnemies(mod);
        LoadCustomStructures(mod);
        LoadCustomScripts(mod);
        
        GD.Print($"Applied mod: {mod.Name}");
    }

    private void LoadCustomItems(ModInfo mod)
    {
        var itemsPath = Path.Combine(mod.ModPath, "items.json");
        if (!Godot.FileAccess.FileExists(itemsPath)) return;

        try
        {
            using var itemsFile = Godot.FileAccess.Open(itemsPath, Godot.FileAccess.ModeFlags.Read);
            if (itemsFile != null)
            {
                var jsonString = itemsFile.GetAsText();
                var json = new Json();
                var parseResult = json.Parse(jsonString);
                
                if (parseResult == Error.Ok)
                {
                    var itemsArray = json.Data.AsGodotArray();
                    foreach (var itemData in itemsArray)
                    {
                        var itemDict = itemData.AsGodotDictionary();
                        var customItem = CreateCustomContent(itemDict, "item", mod.ModId);
                        _customItems[customItem.ContentId] = customItem;
                        
                        // Register with ItemDatabase
                        SurvivalLooterShooter.ItemDatabase.Instance?.RegisterCustomItem(customItem);
                    }
                }
            }
        }
        catch (Exception ex)
        {
            GD.PrintErr($"Failed to load custom items from {mod.Name}: {ex.Message}");
        }
    }

    private void LoadCustomRecipes(ModInfo mod)
    {
        var recipesPath = Path.Combine(mod.ModPath, "recipes.json");
        if (!Godot.FileAccess.FileExists(recipesPath)) return;

        try
        {
            using var recipesFile = Godot.FileAccess.Open(recipesPath, Godot.FileAccess.ModeFlags.Read);
            if (recipesFile != null)
            {
                var jsonString = recipesFile.GetAsText();
                var json = new Json();
                var parseResult = json.Parse(jsonString);
                
                if (parseResult == Error.Ok)
                {
                    var recipesArray = json.Data.AsGodotArray();
                    foreach (var recipeData in recipesArray)
                    {
                        var recipeDict = recipeData.AsGodotDictionary();
                        var customRecipe = CreateCustomContent(recipeDict, "recipe", mod.ModId);
                        _customRecipes[customRecipe.ContentId] = customRecipe;
                        
                        // Register with CraftingSystem
                        SurvivalLooterShooter.CraftingSystem.Instance?.RegisterCustomRecipe(customRecipe);
                    }
                }
            }
        }
        catch (Exception ex)
        {
            GD.PrintErr($"Failed to load custom recipes from {mod.Name}: {ex.Message}");
        }
    }

    private void LoadCustomEnemies(ModInfo mod)
    {
        var enemiesPath = Path.Combine(mod.ModPath, "enemies.json");
        if (!Godot.FileAccess.FileExists(enemiesPath)) return;

        try
        {
            using var enemiesFile = Godot.FileAccess.Open(enemiesPath, Godot.FileAccess.ModeFlags.Read);
            if (enemiesFile != null)
            {
                var jsonString = enemiesFile.GetAsText();
                var json = new Json();
                var parseResult = json.Parse(jsonString);
                
                if (parseResult == Error.Ok)
                {
                    var enemiesArray = json.Data.AsGodotArray();
                    foreach (var enemyData in enemiesArray)
                    {
                        var enemyDict = enemyData.AsGodotDictionary();
                        var customEnemy = CreateCustomContent(enemyDict, "enemy", mod.ModId);
                        _customEnemies[customEnemy.ContentId] = customEnemy;
                        
                        // Register with EnemyManager
                        EnemyManager.Instance?.RegisterCustomEnemy(customEnemy);
                    }
                }
            }
        }
        catch (Exception ex)
        {
            GD.PrintErr($"Failed to load custom enemies from {mod.Name}: {ex.Message}");
        }
    }

    private void LoadCustomStructures(ModInfo mod)
    {
        var structuresPath = Path.Combine(mod.ModPath, "structures.json");
        if (!Godot.FileAccess.FileExists(structuresPath)) return;

        try
        {
            using var structuresFile = Godot.FileAccess.Open(structuresPath, Godot.FileAccess.ModeFlags.Read);
            if (structuresFile != null)
            {
                var jsonString = structuresFile.GetAsText();
                var json = new Json();
                var parseResult = json.Parse(jsonString);
                
                if (parseResult == Error.Ok)
                {
                    var structuresArray = json.Data.AsGodotArray();
                    foreach (var structureData in structuresArray)
                    {
                        var structureDict = structureData.AsGodotDictionary();
                        var customStructure = CreateCustomContent(structureDict, "structure", mod.ModId);
                        _customStructures[customStructure.ContentId] = customStructure;
                        
                        // Register with BuildingManager
                        BuildingManager.Instance?.RegisterCustomStructure(customStructure);
                    }
                }
            }
        }
        catch (Exception ex)
        {
            GD.PrintErr($"Failed to load custom structures from {mod.Name}: {ex.Message}");
        }
    }

    private void LoadCustomScripts(ModInfo mod)
    {
        if (!AllowScriptMods) return;
        
        var scriptsPath = Path.Combine(mod.ModPath, "scripts");
        if (!DirAccess.DirExistsAbsolute(scriptsPath)) return;

        // This would load and compile custom C# scripts
        // For security reasons, this is disabled by default
        GD.Print($"Script loading disabled for security. Mod: {mod.Name}");
    }

    private CustomContent CreateCustomContent(Godot.Collections.Dictionary data, string contentType, string modId)
    {
        var content = new CustomContent
        {
            ContentType = contentType,
            ModId = modId
        };
        
        if (data.ContainsKey("id"))
            content.ContentId = data["id"].AsString();
        if (data.ContainsKey("name"))
            content.Name = data["name"].AsString();
        
        // Convert all other properties
        foreach (var item in data)
        {
            if (item.Key.AsString() != "id" && item.Key.AsString() != "name")
            {
                content.Properties[item.Key.AsString()] = item.Value;
            }
        }
        
        return content;
    }

    public void EnableMod(string modId, bool enabled)
    {
        var mod = _loadedMods.FirstOrDefault(m => m.ModId == modId);
        if (mod != null)
        {
            mod.IsEnabled = enabled;
            SaveModSettings();
            
            if (enabled)
            {
                ApplyMod(mod);
            }
            else
            {
                RemoveMod(mod);
            }
            
            GD.Print($"Mod {mod.Name} {(enabled ? "enabled" : "disabled")}");
        }
    }

    private void RemoveMod(ModInfo mod)
    {
        // Remove custom content added by this mod
        var itemsToRemove = _customItems.Where(i => i.Value.ModId == mod.ModId).Select(i => i.Key).ToList();
        foreach (var itemId in itemsToRemove)
        {
            _customItems.Remove(itemId);
            ItemDatabase.Instance?.UnregisterCustomItem(itemId);
        }
        
        var recipesToRemove = _customRecipes.Where(r => r.Value.ModId == mod.ModId).Select(r => r.Key).ToList();
        foreach (var recipeId in recipesToRemove)
        {
            _customRecipes.Remove(recipeId);
            CraftingSystem.Instance?.UnregisterCustomRecipe(recipeId);
        }
        
        var enemiesToRemove = _customEnemies.Where(e => e.Value.ModId == mod.ModId).Select(e => e.Key).ToList();
        foreach (var enemyId in enemiesToRemove)
        {
            _customEnemies.Remove(enemyId);
            EnemyManager.Instance?.UnregisterCustomEnemy(enemyId);
        }
        
        var structuresToRemove = _customStructures.Where(s => s.Value.ModId == mod.ModId).Select(s => s.Key).ToList();
        foreach (var structureId in structuresToRemove)
        {
            _customStructures.Remove(structureId);
            BuildingManager.Instance?.UnregisterCustomStructure(structureId);
        }
    }

    public List<ModInfo> GetLoadedMods()
    {
        return new List<ModInfo>(_loadedMods);
    }

    public ModInfo GetMod(string modId)
    {
        return _loadedMods.FirstOrDefault(m => m.ModId == modId);
    }

    public Dictionary<string, CustomContent> GetCustomContent(string contentType)
    {
        return contentType switch
        {
            "item" => new Dictionary<string, CustomContent>(_customItems),
            "recipe" => new Dictionary<string, CustomContent>(_customRecipes),
            "enemy" => new Dictionary<string, CustomContent>(_customEnemies),
            "structure" => new Dictionary<string, CustomContent>(_customStructures),
            _ => new Dictionary<string, CustomContent>()
        };
    }

    public void CreateModTemplate(string modId, string modName, string author)
    {
        var modPath = Path.Combine(ModsDirectory, modId);
        
        if (DirAccess.DirExistsAbsolute(modPath))
        {
            GD.PrintErr($"Mod directory already exists: {modPath}");
            return;
        }
        
        DirAccess.MakeDirRecursiveAbsolute(modPath);
        
        // Create mod.json
        var modConfig = new Godot.Collections.Dictionary
        {
            ["mod_id"] = modId,
            ["name"] = modName,
            ["version"] = "1.0.0",
            ["author"] = author,
            ["description"] = "A custom mod for Survival Looter Shooter",
            ["dependencies"] = new Godot.Collections.Array()
        };
        
        var configPath = Path.Combine(modPath, ModConfigFile);
        using var configFile = Godot.FileAccess.Open(configPath, Godot.FileAccess.ModeFlags.Write);
        if (configFile != null)
        {
            configFile.StoreString(Json.Stringify(modConfig));
        }
        
        // Create template files
        CreateTemplateFile(Path.Combine(modPath, "items.json"), "[]");
        CreateTemplateFile(Path.Combine(modPath, "recipes.json"), "[]");
        CreateTemplateFile(Path.Combine(modPath, "enemies.json"), "[]");
        CreateTemplateFile(Path.Combine(modPath, "structures.json"), "[]");
        
        // Create README
        var readmeContent = $@"# {modName}

Author: {author}
Version: 1.0.0

## Description
A custom mod for Survival Looter Shooter.

## Files
- `mod.json`: Mod configuration
- `items.json`: Custom items
- `recipes.json`: Custom recipes
- `enemies.json`: Custom enemies
- `structures.json`: Custom structures

## Installation
Place this folder in the mods directory and restart the game.
";
        
        CreateTemplateFile(Path.Combine(modPath, "README.md"), readmeContent);
        
        GD.Print($"Mod template created: {modPath}");
    }

    private void CreateTemplateFile(string filePath, string content)
    {
        using var file = Godot.FileAccess.Open(filePath, Godot.FileAccess.ModeFlags.Write);
        if (file != null)
        {
            file.StoreString(content);
        }
    }

    private void SaveModSettings()
    {
        try
        {
            var settings = new Godot.Collections.Dictionary();
            
            foreach (var mod in _loadedMods)
            {
                settings[mod.ModId] = mod.IsEnabled;
            }
            
            using var saveFile = Godot.FileAccess.Open(ModSettingsFile, Godot.FileAccess.ModeFlags.Write);
            if (saveFile != null)
            {
                var jsonString = Json.Stringify(settings);
                saveFile.StoreString(jsonString);
            }
        }
        catch (Exception ex)
        {
            GD.PrintErr($"Failed to save mod settings: {ex.Message}");
        }
    }

    private void LoadModSettings()
    {
        try
        {
            if (Godot.FileAccess.FileExists(ModSettingsFile))
            {
                using var saveFile = Godot.FileAccess.Open(ModSettingsFile, Godot.FileAccess.ModeFlags.Read);
                if (saveFile != null)
                {
                    var jsonString = saveFile.GetAsText();
                    var json = new Json();
                    var parseResult = json.Parse(jsonString);
                    
                    if (parseResult == Error.Ok)
                    {
                        var settings = json.Data.AsGodotDictionary();
                        
                        foreach (var setting in settings)
                        {
                            var modId = setting.Key.AsString();
                            var enabled = setting.Value.AsBool();
                            
                            var mod = _loadedMods.FirstOrDefault(m => m.ModId == modId);
                            if (mod != null)
                            {
                                mod.IsEnabled = enabled;
                            }
                        }
                    }
                }
            }
        }
        catch (Exception ex)
        {
            GD.PrintErr($"Failed to load mod settings: {ex.Message}");
        }
    }

    public void RefreshMods()
    {
        // Clear current mods
        _loadedMods.Clear();
        _customItems.Clear();
        _customRecipes.Clear();
        _customEnemies.Clear();
        _customStructures.Clear();
        
        // Reload all mods
        ScanAndLoadMods();
        
        GD.Print("Mods refreshed");
    }
}
