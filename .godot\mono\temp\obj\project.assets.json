{"version": 3, "targets": {"net8.0": {"Godot.SourceGenerators/4.4.1": {"type": "package", "build": {"build/Godot.SourceGenerators.props": {}}}, "GodotSharp/4.4.1": {"type": "package", "compile": {"lib/net8.0/GodotSharp.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/GodotSharp.dll": {"related": ".xml"}}}, "GodotSharpEditor/4.4.1": {"type": "package", "dependencies": {"GodotSharp": "4.4.1"}, "compile": {"lib/net8.0/GodotSharpEditor.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/GodotSharpEditor.dll": {"related": ".xml"}}}}}, "libraries": {"Godot.SourceGenerators/4.4.1": {"sha512": "V/cuX41BxippWGD79zrP2bhqfXkuyiy9OFuCkTu3flo7I6STSJca637TL2phe7rzROIFre0vQR1+PAMdsjO3zg==", "type": "package", "path": "godot.sourcegenerators/4.4.1", "files": [".nupkg.metadata", ".signature.p7s", "analyzers/dotnet/cs/Godot.SourceGenerators.dll", "build/Godot.SourceGenerators.props", "godot.sourcegenerators.4.4.1.nupkg.sha512", "godot.sourcegenerators.nuspec"]}, "GodotSharp/4.4.1": {"sha512": "ghnQEo5LikQPfbCYcVxje8epffNCiyNG4zvGWUDRZRC1O+653+yqG3wdxk3+5RZsA3jaRuGKRavsGcnhLKe12g==", "type": "package", "path": "godotsharp/4.4.1", "files": [".nupkg.metadata", ".signature.p7s", "godotsharp.4.4.1.nupkg.sha512", "godotsharp.nuspec", "lib/net8.0/GodotSharp.dll", "lib/net8.0/GodotSharp.xml"]}, "GodotSharpEditor/4.4.1": {"sha512": "cc3nA24r/sjW8KxrNqfSQk4CQMQYcg/kYk5935R88IhGBdRuTH4miaBXPlzR/td5GR8i1oGtCMWyW8axpQ0DxA==", "type": "package", "path": "godotsharpeditor/4.4.1", "files": [".nupkg.metadata", ".signature.p7s", "godotsharpeditor.4.4.1.nupkg.sha512", "godotsharpeditor.nuspec", "lib/net8.0/GodotSharpEditor.dll", "lib/net8.0/GodotSharpEditor.xml"]}}, "projectFileDependencyGroups": {"net8.0": ["Godot.SourceGenerators >= 4.4.1", "GodotSharp >= 4.4.1", "GodotSharpEditor >= 4.4.1"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\project1\\SurvivalLooterShooter.csproj", "projectName": "SurvivalLooterShooter", "projectPath": "C:\\Users\\<USER>\\Desktop\\project1\\SurvivalLooterShooter.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\project1\\.godot\\mono\\temp\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Godot.SourceGenerators": {"target": "Package", "version": "[4.4.1, )"}, "GodotSharp": {"target": "Package", "version": "[4.4.1, )"}, "GodotSharpEditor": {"target": "Package", "version": "[4.4.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[8.0.17, 8.0.17]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[8.0.17, 8.0.17]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[8.0.17, 8.0.17]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.205/PortableRuntimeIdentifierGraph.json"}}}}