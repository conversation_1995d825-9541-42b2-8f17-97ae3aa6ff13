using Godot;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;

namespace SurvivalLooterShooter
{
    /// <summary>
    /// Manages building placement, validation, and structure lifecycle
    /// </summary>
    public partial class BuildingManager : Node
    {
        private static BuildingManager _instance;
        public static BuildingManager Instance => _instance;

        // Structure blueprints loaded from JSON
        private Dictionary<string, StructureBlueprint> _blueprints = new Dictionary<string, StructureBlueprint>();
        
        // Placed structures in the world
        private Dictionary<Vector2, Structure> _placedStructures = new Dictionary<Vector2, Structure>();
        
        // Foundation tracking for placement validation
        private HashSet<Vector2> _foundationPositions = new HashSet<Vector2>();
        
        // Building preview
        private BuildingPreview _buildingPreview;
        private string _selectedStructureId;
        private bool _buildingMode = false;
        
        // Grid settings
        private const int GRID_SIZE = 32;
        
        // Events
        [Signal]
        public delegate void StructurePlacedEventHandler(Structure structure);
        
        [Signal]
        public delegate void StructureRemovedEventHandler(Vector2 position, string structureId);
        
        [Signal]
        public delegate void BuildingModeChangedEventHandler(bool enabled, string structureId);

        public bool IsBuildingMode => _buildingMode;
        public string SelectedStructureId => _selectedStructureId;

        public override void _Ready()
        {
            if (_instance == null)
            {
                _instance = this;
                AddToGroup("building_manager");
                
                // Load structure blueprints
                LoadStructureBlueprints();
                
                // Initialize building preview
                InitializeBuildingPreview();
                
                Logger.LogInfo("BuildingManager", "BuildingManager initialized");
            }
            else
            {
                GD.PrintErr("Multiple BuildingManager instances detected! Removing duplicate.");
                QueueFree();
            }
        }

        public override void _Input(InputEvent @event)
        {
            if (!_buildingMode) return;
            
            if (@event is InputEventMouseButton mouseEvent)
            {
                if (mouseEvent.Pressed && mouseEvent.ButtonIndex == MouseButton.Left)
                {
                    // Attempt to place structure
                    var worldPosition = GetViewport().GetMousePosition();
                    var gridPosition = WorldToGrid(worldPosition);
                    TryPlaceStructure(_selectedStructureId, gridPosition);
                }
                else if (mouseEvent.Pressed && mouseEvent.ButtonIndex == MouseButton.Right)
                {
                    // Cancel building mode
                    SetBuildingMode(false);
                }
            }
            else if (@event is InputEventKey keyEvent && keyEvent.Pressed)
            {
                if (keyEvent.Keycode == Key.Escape)
                {
                    // Cancel building mode
                    SetBuildingMode(false);
                }
            }
        }

        public override void _Process(double delta)
        {
            if (_buildingMode && _buildingPreview != null)
            {
                // Update preview position to follow mouse
                var worldPosition = GetViewport().GetMousePosition();
                var gridPosition = WorldToGrid(worldPosition);
                var worldGridPosition = GridToWorld(gridPosition);
                
                _buildingPreview.Position = worldGridPosition;
                
                // Update preview validity
                var blueprint = GetBlueprint(_selectedStructureId);
                bool canPlace = blueprint != null && CanPlaceStructure(_selectedStructureId, gridPosition);
                _buildingPreview.SetValid(canPlace);
            }
        }

        /// <summary>
        /// Loads structure blueprints from JSON file
        /// </summary>
        private void LoadStructureBlueprints()
        {
            try
            {
                string jsonPath = "res://Data/Structures.json";
                if (!FileAccess.FileExists(jsonPath))
                {
                    Logger.LogError("BuildingManager", $"Structures.json not found at {jsonPath}");
                    return;
                }

                using var file = FileAccess.Open(jsonPath, FileAccess.ModeFlags.Read);
                string jsonContent = file.GetAsText();
                
                var blueprints = JsonSerializer.Deserialize<List<StructureBlueprint>>(jsonContent);
                
                foreach (var blueprint in blueprints)
                {
                    _blueprints[blueprint.Id] = blueprint;
                    Logger.LogInfo("BuildingManager", $"Loaded structure blueprint: {blueprint.Name}");
                }
                
                Logger.LogInfo("BuildingManager", $"Loaded {_blueprints.Count} structure blueprints");
            }
            catch (Exception ex)
            {
                Logger.LogError("BuildingManager", $"Failed to load structure blueprints: {ex.Message}");
            }
        }

        /// <summary>
        /// Initializes the building preview system
        /// </summary>
        private void InitializeBuildingPreview()
        {
            _buildingPreview = new BuildingPreview();
            AddChild(_buildingPreview);
            _buildingPreview.Visible = false;
        }

        /// <summary>
        /// Gets a structure blueprint by ID
        /// </summary>
        public StructureBlueprint GetBlueprint(string structureId)
        {
            return _blueprints.TryGetValue(structureId, out var blueprint) ? blueprint : null;
        }

        /// <summary>
        /// Gets all available structure blueprints
        /// </summary>
        public Dictionary<string, StructureBlueprint> GetAllBlueprints()
        {
            return new Dictionary<string, StructureBlueprint>(_blueprints);
        }

        /// <summary>
        /// Attempts to place a structure at the specified grid position
        /// </summary>
        public bool TryPlaceStructure(string structureId, Vector2 gridPosition)
        {
            if (!CanPlaceStructure(structureId, gridPosition))
                return false;

            var blueprint = GetBlueprint(structureId);
            if (blueprint == null)
                return false;

            // Check if player has required materials
            var inventory = GetNode<Inventory>("/root/GameManager/Inventory");
            if (inventory == null)
            {
                Logger.LogError("BuildingManager", "Inventory not found");
                return false;
            }

            if (!HasRequiredMaterials(blueprint, inventory))
            {
                Logger.LogInfo("BuildingManager", "Insufficient materials to build structure");
                return false;
            }

            // Consume materials
            ConsumeBuildingMaterials(blueprint, inventory);

            // Create and place structure
            var structure = CreateStructure(blueprint, gridPosition);
            PlaceStructure(structure, gridPosition);

            Logger.LogInfo("BuildingManager", $"Successfully placed {blueprint.Name} at {gridPosition}");
            return true;
        }

        /// <summary>
        /// Checks if a structure can be placed at the specified position
        /// </summary>
        public bool CanPlaceStructure(string structureId, Vector2 gridPosition)
        {
            var blueprint = GetBlueprint(structureId);
            if (blueprint == null)
                return false;

            // Use blueprint's placement validation
            return blueprint.CanPlaceAt(gridPosition, this);
        }

        /// <summary>
        /// Checks if the specified area is clear of structures
        /// </summary>
        public bool IsAreaClear(Vector2 gridPosition, StructureSize size)
        {
            for (int x = 0; x < size.Width; x++)
            {
                for (int y = 0; y < size.Height; y++)
                {
                    var checkPosition = gridPosition + new Vector2(x, y);
                    if (_placedStructures.ContainsKey(checkPosition))
                        return false;
                }
            }
            return true;
        }

        /// <summary>
        /// Checks if there's a foundation at the specified position
        /// </summary>
        public bool HasFoundationAt(Vector2 gridPosition)
        {
            return _foundationPositions.Contains(gridPosition) || 
                   (_placedStructures.TryGetValue(gridPosition, out var structure) && 
                    structure.Blueprint.Type == "foundation");
        }

        /// <summary>
        /// Gets structures within a specified radius
        /// </summary>
        public List<Structure> GetStructuresInRadius(Vector2 centerPosition, float radius)
        {
            var structures = new List<Structure>();
            
            foreach (var kvp in _placedStructures)
            {
                var distance = kvp.Key.DistanceTo(centerPosition);
                if (distance <= radius)
                    structures.Add(kvp.Value);
            }
            
            return structures;
        }

        /// <summary>
        /// Gets the structure at a specific grid position
        /// </summary>
        public Structure GetStructureAt(Vector2 gridPosition)
        {
            return _placedStructures.TryGetValue(gridPosition, out var structure) ? structure : null;
        }

        /// <summary>
        /// Removes a structure from the specified position
        /// </summary>
        public bool RemoveStructure(Vector2 gridPosition)
        {
            if (!_placedStructures.TryGetValue(gridPosition, out var structure))
                return false;

            // Remove from tracking
            _placedStructures.Remove(gridPosition);
            
            if (structure.Blueprint.Type == "foundation")
                _foundationPositions.Remove(gridPosition);

            // Emit event
            EmitSignal(SignalName.StructureRemoved, gridPosition, structure.StructureId);

            // Remove from scene
            structure.QueueFree();

            Logger.LogInfo("BuildingManager", $"Removed structure {structure.StructureId} from {gridPosition}");
            return true;
        }

        /// <summary>
        /// Sets building mode on/off
        /// </summary>
        public void SetBuildingMode(bool enabled, string structureId = "")
        {
            _buildingMode = enabled;
            _selectedStructureId = enabled ? structureId : "";
            
            if (_buildingPreview != null)
            {
                _buildingPreview.Visible = enabled;
                if (enabled && !string.IsNullOrEmpty(structureId))
                {
                    var blueprint = GetBlueprint(structureId);
                    if (blueprint != null)
                        _buildingPreview.SetBlueprint(blueprint);
                }
            }

            EmitSignal(SignalName.BuildingModeChanged, enabled, structureId);
            Logger.LogInfo("BuildingManager", $"Building mode {(enabled ? "enabled" : "disabled")} for {structureId}");
        }

        /// <summary>
        /// Converts world position to grid position
        /// </summary>
        public Vector2 WorldToGrid(Vector2 worldPosition)
        {
            return new Vector2(
                Mathf.FloorToInt(worldPosition.X / GRID_SIZE),
                Mathf.FloorToInt(worldPosition.Y / GRID_SIZE)
            );
        }

        /// <summary>
        /// Converts grid position to world position
        /// </summary>
        public Vector2 GridToWorld(Vector2 gridPosition)
        {
            return gridPosition * GRID_SIZE + Vector2.One * (GRID_SIZE / 2);
        }

        private bool HasRequiredMaterials(StructureBlueprint blueprint, Inventory inventory)
        {
            foreach (var cost in blueprint.BuildCost)
            {
                if (!inventory.HasItem(cost.Item, cost.Amount))
                    return false;
            }
            return true;
        }

        private void ConsumeBuildingMaterials(StructureBlueprint blueprint, Inventory inventory)
        {
            foreach (var cost in blueprint.BuildCost)
            {
                inventory.RemoveItem(cost.Item, cost.Amount);
            }
        }

        private Structure CreateStructure(StructureBlueprint blueprint, Vector2 gridPosition)
        {
            // Load structure scene or create programmatically
            var structureScene = GD.Load<PackedScene>("res://Scenes/Structure.tscn");
            Structure structure;
            
            if (structureScene != null)
            {
                structure = structureScene.Instantiate<Structure>();
            }
            else
            {
                // Create structure programmatically
                structure = new Structure();
                
                // Add required child nodes
                var sprite = new Sprite2D();
                sprite.Name = "Sprite2D";
                structure.AddChild(sprite);
                
                var collisionShape = new CollisionShape2D();
                collisionShape.Name = "CollisionShape2D";
                structure.AddChild(collisionShape);
                
                var interactionArea = new Area2D();
                interactionArea.Name = "InteractionArea";
                var areaCollision = new CollisionShape2D();
                interactionArea.AddChild(areaCollision);
                structure.AddChild(interactionArea);
            }
            
            structure.Initialize(blueprint, gridPosition);
            return structure;
        }

        private void PlaceStructure(Structure structure, Vector2 gridPosition)
        {
            // Add to scene
            GetTree().CurrentScene.AddChild(structure);
            
            // Set world position
            structure.Position = GridToWorld(gridPosition);
            
            // Track structure
            _placedStructures[gridPosition] = structure;
            
            // Track foundation if applicable
            if (structure.Blueprint.Type == "foundation")
                _foundationPositions.Add(gridPosition);

            // Connect structure events
            structure.StructureDestroyed += OnStructureDestroyed;

            // Emit event
            EmitSignal(SignalName.StructurePlaced, structure);
        }

        private void OnStructureDestroyed(Structure structure)
        {
            // Find and remove structure from tracking
            var positionToRemove = Vector2.Zero;
            foreach (var kvp in _placedStructures)
            {
                if (kvp.Value == structure)
                {
                    positionToRemove = kvp.Key;
                    break;
                }
            }
            
            if (positionToRemove != Vector2.Zero)
            {
                RemoveStructure(positionToRemove);
            }
        }

        // Mod support and endgame methods
        public void RegisterCustomStructure(CustomContent customStructure)
        {
            GD.Print($"Registered custom structure: {customStructure.Name}");
        }

        public void UnregisterCustomStructure(string structureId)
        {
            GD.Print($"Unregistered custom structure: {structureId}");
        }

        public List<string> GetUnlockedBlueprints()
        {
            // Return list of unlocked blueprint IDs
            return new List<string>();
        }

        public void UnlockBlueprint(string blueprintId)
        {
            GD.Print($"Unlocked blueprint: {blueprintId}");
        }

        public void UnlockLegendaryWeapons()
        {
            GD.Print("Legendary weapon blueprints unlocked");
        }

        public void UnlockMegaStructures()
        {
            GD.Print("Mega structures unlocked");
        }
    }
}