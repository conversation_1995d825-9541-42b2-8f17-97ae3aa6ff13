[folding]

node_unfolds=[Node<PERSON>ath("."), PackedStringArray("Layout", "Mouse"), NodePath("Background"), PackedStringArray("Layout", "Theme Overrides"), NodePath("Background/VBoxContainer"), PackedStringArray("Layout"), NodePath("Background/VBoxContainer/TitleLabel"), PackedStringArray("Layout"), NodePath("Background/VBoxContainer/HSeparator"), PackedStringArray("Layout"), NodePath("Background/VBoxContainer/ScrollContainer"), PackedStringArray("Layout"), NodePath("Background/VBoxContainer/ScrollContainer/InventoryGrid"), PackedStringArray("Layout"), NodePath("Background/CloseButton"), PackedStringArray("Layout"), NodePath("ItemTooltip"), PackedStringArray("Visibility", "Layout", "Theme Overrides"), NodePath("ItemTooltip/TooltipLabel"), PackedStringArray("Layout")]
resource_unfolds=["res://Scenes/InventoryUI.tscn::StyleBoxFlat_1", PackedStringArray("Resource", "Border Width", "Border", "Corner Radius")]
nodes_folded=[]
