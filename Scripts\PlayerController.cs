using Godot;
using System;

namespace SurvivalLooterShooter
{
    /// <summary>
    /// PlayerController coordinates all game systems and handles player input
    /// Integrates inventory, crafting, combat, and survival stats with player actions
    /// </summary>
    public partial class PlayerController : CharacterBody2D
    {
        // System references
        private Inventory _inventory;
        private CraftingSystem _craftingSystem;
        private WeaponController _weaponController;
        private SurvivalStatsSystem _survivalStatsSystem;
        private SaveManager _saveManager;
        private POIInteractionSystem _poiInteractionSystem;
        private ExplorationTracker _explorationTracker;
        
        // UI references
        private InventoryUI _inventoryUI;
        private CraftingUI _craftingUI;
        private SurvivalHUD _survivalHUD;
        
        // Player state
        private Vector2 _velocity = Vector2.Zero;
        private bool _isAlive = true;
        private float _lastActionTime = 0f;
        
        // Movement configuration
        [Export] public float MoveSpeed = 300f;
        [Export] public float SprintMultiplier = 1.5f;
        [Export] public float StaminaCostPerSecond = 10f;
        [Export] public float ActionStaminaCost = 5f;
        
        // Input state
        private bool _isSprinting = false;
        private Vector2 _inputVector = Vector2.Zero;
        
        // Events for player actions
        [Signal]
        public delegate void PlayerMovedEventHandler(Vector2 position, Vector2 velocity);
        
        [Signal]
        public delegate void PlayerActionPerformedEventHandler(string actionType, float staminaCost);
        
        [Signal]
        public delegate void PlayerInteractedEventHandler(string interactionType, string targetId);

        public bool IsAlive => _isAlive;
        public Vector2 InputVector => _inputVector;
        public bool IsSprinting => _isSprinting;

        public override void _Ready()
        {
            // Initialize player controller
            InitializePlayerController();
            
            GD.Print("PlayerController initialized");
        }

        /// <summary>
        /// Initializes the player controller with system references
        /// </summary>
        public void Initialize(Inventory inventory, CraftingSystem craftingSystem, WeaponController weaponController, 
                              SurvivalStatsSystem survivalStatsSystem, SaveManager saveManager,
                              InventoryUI inventoryUI = null, CraftingUI craftingUI = null, SurvivalHUD survivalHUD = null)
        {
            _inventory = inventory ?? throw new ArgumentNullException(nameof(inventory));
            _craftingSystem = craftingSystem ?? throw new ArgumentNullException(nameof(craftingSystem));
            _weaponController = weaponController ?? throw new ArgumentNullException(nameof(weaponController));
            _survivalStatsSystem = survivalStatsSystem ?? throw new ArgumentNullException(nameof(survivalStatsSystem));
            _saveManager = saveManager ?? throw new ArgumentNullException(nameof(saveManager));
            _inventoryUI = inventoryUI; // Allow null for testing
            _craftingUI = craftingUI; // Allow null for testing
            _survivalHUD = survivalHUD; // Allow null for testing
            
            // Initialize POI and exploration systems
            _poiInteractionSystem = POIInteractionSystem.Instance;
            _explorationTracker = ExplorationTracker.Instance;
            
            // Connect to survival stats events
            _survivalStatsSystem.PlayerDied += OnPlayerDied;
            _survivalStatsSystem.PlayerRespawned += OnPlayerRespawned;
            
            GD.Print("PlayerController systems initialized");
        }

        /// <summary>
        /// Sets up the player controller's initial state
        /// </summary>
        private void InitializePlayerController()
        {
            // Set initial position if needed
            if (GlobalPosition == Vector2.Zero)
            {
                GlobalPosition = new Vector2(960, 540); // Center of 1920x1080 screen
            }
        }

        public override void _PhysicsProcess(double delta)
        {
            if (!_isAlive) return;
            
            HandleMovement((float)delta);
            HandleStaminaConsumption((float)delta);
            UpdateWorldLoading();
            UpdatePOIAndExploration();
        }

        public override void _Input(InputEvent @event)
        {
            if (!_isAlive) return;
            
            HandleInputEvent(@event);
        }

        /// <summary>
        /// Handles all input events for the player
        /// </summary>
        private void HandleInputEvent(InputEvent @event)
        {
            // Handle input actions defined in project settings
            if (@event.IsActionPressed("open_inventory"))
            {
                ToggleInventory();
            }
            else if (@event.IsActionPressed("open_crafting"))
            {
                ToggleCrafting();
            }
            else if (@event.IsActionPressed("interact"))
            {
                PerformInteraction();
            }
            else if (@event.IsActionPressed("reload"))
            {
                PerformReload();
            }
            else if (@event.IsActionPressed("fire"))
            {
                PerformFire();
            }
            
            // Handle key press events for additional functionality
            if (@event is InputEventKey keyEvent && keyEvent.Pressed)
            {
                switch (keyEvent.Keycode)
                {
                    case Key.F5:
                        PerformQuickSave();
                        break;
                    case Key.F9:
                        PerformQuickLoad();
                        break;
                    case Key.Key1:
                        UseConsumable("canned_food");
                        break;
                    case Key.Key2:
                        UseConsumable("energy_drink");
                        break;
                    case Key.Key3:
                        UseConsumable("bandage");
                        break;
                    case Key.Key4:
                        UseConsumable("herbal_tea");
                        break;
                }
            }
        }

        /// <summary>
        /// Handles player movement and input processing
        /// </summary>
        private void HandleMovement(float delta)
        {
            // Get input vector using configured input actions
            _inputVector = Vector2.Zero;
            
            if (Input.IsActionPressed("move_left"))
                _inputVector.X -= 1;
            if (Input.IsActionPressed("move_right"))
                _inputVector.X += 1;
            if (Input.IsActionPressed("move_up"))
                _inputVector.Y -= 1;
            if (Input.IsActionPressed("move_down"))
                _inputVector.Y += 1;
            
            // Normalize diagonal movement
            if (_inputVector.Length() > 0)
            {
                _inputVector = _inputVector.Normalized();
            }
            
            // Check for sprinting (using Shift key)
            _isSprinting = Input.IsKeyPressed(Key.Shift) && _inputVector.Length() > 0 && 
                          _survivalStatsSystem.Stamina.CurrentValue > 0;
            
            // Calculate movement speed with skill bonuses
            float currentSpeed = MoveSpeed;
            
            // Apply endurance skill bonus to movement speed
            if (SkillManager.Instance != null)
            {
                float speedBonus = SkillManager.Instance.GetSkillBonus("endurance", "movement_speed");
                currentSpeed *= (1f + speedBonus);
            }
            
            if (_isSprinting)
            {
                currentSpeed *= SprintMultiplier;
            }
            
            // Apply movement
            Velocity = _inputVector * currentSpeed;
            MoveAndSlide();
            
            // Emit movement event if player moved
            if (Velocity.Length() > 0)
            {
                EmitSignal(SignalName.PlayerMoved, GlobalPosition, Velocity);
                EventBus.Instance?.EmitSignal(EventBus.SignalName.PlayerMoved, GlobalPosition, Velocity, _isSprinting);
            }
        }

        /// <summary>
        /// Handles stamina consumption based on player actions
        /// </summary>
        private void HandleStaminaConsumption(float delta)
        {
            if (_isSprinting && _inputVector.Length() > 0)
            {
                // Calculate stamina cost with skill bonuses
                float staminaCost = StaminaCostPerSecond * delta;
                
                // Apply metabolism skill bonus to reduce stamina consumption
                if (SkillManager.Instance != null)
                {
                    float staminaEfficiency = SkillManager.Instance.GetSkillBonus("metabolism", "stamina_efficiency");
                    staminaCost *= (1f - staminaEfficiency);
                }
                
                _survivalStatsSystem.Stamina.ModifyValue(-staminaCost);
                
                // Stop sprinting if out of stamina
                if (_survivalStatsSystem.Stamina.CurrentValue <= 0)
                {
                    _isSprinting = false;
                }
            }
        }

        #region UI Actions

        /// <summary>
        /// Toggles the inventory UI
        /// </summary>
        private void ToggleInventory()
        {
            if (CanPerformAction())
            {
                _inventoryUI?.ToggleInventory();
                ConsumeActionStamina("toggle_inventory");
            }
        }

        /// <summary>
        /// Toggles the crafting UI
        /// </summary>
        private void ToggleCrafting()
        {
            if (CanPerformAction())
            {
                _craftingUI?.ToggleCrafting();
                ConsumeActionStamina("toggle_crafting");
            }
        }

        #endregion

        #region Combat Actions

        /// <summary>
        /// Performs weapon firing
        /// </summary>
        private void PerformFire()
        {
            if (CanPerformAction())
            {
                bool fired = _weaponController?.Fire() ?? false;
                if (fired)
                {
                    ConsumeActionStamina("fire_weapon");
                }
            }
        }

        /// <summary>
        /// Performs weapon reload
        /// </summary>
        private void PerformReload()
        {
            if (CanPerformAction())
            {
                bool reloadStarted = _weaponController?.StartReload() ?? false;
                if (reloadStarted)
                {
                    ConsumeActionStamina("reload_weapon");
                }
            }
        }

        #endregion

        #region Interaction Actions

        /// <summary>
        /// Performs general interaction - handles resource harvesting, item pickups, POI interactions, and other interactions
        /// </summary>
        private void PerformInteraction()
        {
            if (CanPerformAction())
            {
                // Check for resource harvesting first
                bool interactionPerformed = CheckForResourceHarvesting();
                
                // If no resource harvesting, check for POI interactions
                if (!interactionPerformed)
                {
                    interactionPerformed = CheckForPOIInteractions();
                }
                
                // If no POI interaction, check for nearby item pickups
                if (!interactionPerformed)
                {
                    interactionPerformed = CheckForItemPickups();
                }
                
                // If no item pickup was found, perform general interaction
                if (!interactionPerformed)
                {
                    EmitSignal(SignalName.PlayerInteracted, "general", "");
                    EventBus.Instance?.EmitSignal(EventBus.SignalName.PlayerInteracted, "general", "", GlobalPosition);
                    GD.Print("Player performed general interaction");
                }
                
                ConsumeActionStamina("interact");
            }
        }

        /// <summary>
        /// Checks for nearby resource nodes and attempts to harvest them
        /// </summary>
        private bool CheckForResourceHarvesting()
        {
            var resourceSystem = ResourceHarvestingSystem.Instance;
            if (resourceSystem == null) return false;

            // Try to harvest resource at current position
            return resourceSystem.TryHarvestResourceAt(GlobalPosition, this, 75f);
        }

        /// <summary>
        /// Checks for nearby item pickups and attempts to pick them up
        /// </summary>
        private bool CheckForItemPickups()
        {
            // Get all ItemPickup nodes in the scene
            var itemPickups = GetTree().GetNodesInGroup("item_pickups");
            
            ItemPickup closestPickup = null;
            float closestDistance = float.MaxValue;
            
            // Find the closest item pickup within interaction range
            foreach (Node node in itemPickups)
            {
                if (node is ItemPickup pickup)
                {
                    float distance = GlobalPosition.DistanceTo(pickup.GlobalPosition);
                    if (distance <= pickup.InteractionRange && distance < closestDistance)
                    {
                        closestDistance = distance;
                        closestPickup = pickup;
                    }
                }
            }
            
            // Try to pick up the closest item
            if (closestPickup != null)
            {
                return closestPickup.TryPickupItem(this);
            }
            
            return false;
        }

        #endregion

        #region Consumable Actions

        /// <summary>
        /// Uses a consumable item from inventory
        /// </summary>
        private void UseConsumable(string itemId)
        {
            if (!CanPerformAction()) return;
            
            if (_inventory?.HasItem(itemId) == true)
            {
                // Remove item from inventory
                if (_inventory.RemoveItem(itemId, 1))
                {
                    // Apply consumable effects
                    bool consumed = _survivalStatsSystem?.ConsumeItem(itemId) ?? false;
                    if (consumed)
                    {
                        ConsumeActionStamina("use_consumable");
                        
                        var item = ItemDatabase.Instance?.GetItem(itemId);
                        GD.Print($"Used {item?.Name ?? itemId}");
                        
                        // Emit event
                        EmitSignal(SignalName.PlayerActionPerformed, "use_consumable", ActionStaminaCost);
                        EventBus.Instance?.EmitSignal(EventBus.SignalName.PlayerActionPerformed, "use_consumable", itemId, ActionStaminaCost);
                    }
                    else
                    {
                        // If consumption failed, add the item back
                        _inventory.AddItem(itemId, 1);
                        GD.Print($"Failed to consume {itemId}");
                    }
                }
            }
            else
            {
                var item = ItemDatabase.Instance?.GetItem(itemId);
                GD.Print($"No {item?.Name ?? itemId} available");
            }
        }

        #endregion

        #region Save/Load Actions

        /// <summary>
        /// Performs quick save
        /// </summary>
        private void PerformQuickSave()
        {
            if (CanPerformAction())
            {
                _saveManager?.SaveGame();
                ConsumeActionStamina("quick_save");
                
                EmitSignal(SignalName.PlayerActionPerformed, "quick_save", ActionStaminaCost);
                EventBus.Instance?.EmitSignal(EventBus.SignalName.PlayerActionPerformed, "quick_save", "", ActionStaminaCost);
            }
        }

        /// <summary>
        /// Performs quick load
        /// </summary>
        private void PerformQuickLoad()
        {
            if (CanPerformAction())
            {
                _saveManager?.LoadGame();
                ConsumeActionStamina("quick_load");
                
                EmitSignal(SignalName.PlayerActionPerformed, "quick_load", ActionStaminaCost);
                EventBus.Instance?.EmitSignal(EventBus.SignalName.PlayerActionPerformed, "quick_load", "", ActionStaminaCost);
            }
        }

        #endregion

        #region Action Validation and Stamina Management

        /// <summary>
        /// Checks if the player can perform an action
        /// </summary>
        private bool CanPerformAction()
        {
            if (!_isAlive) return false;
            
            // Check if enough time has passed since last action (prevent spam)
            float currentTime = (float)Time.GetUnixTimeFromSystem();
            if (currentTime - _lastActionTime < 0.1f) // 100ms cooldown
            {
                return false;
            }
            
            // Check if player has enough stamina for actions that require it
            if (_survivalStatsSystem?.Stamina.CurrentValue < ActionStaminaCost)
            {
                return false;
            }
            
            return true;
        }

        /// <summary>
        /// Consumes stamina for performing an action
        /// </summary>
        private void ConsumeActionStamina(string actionType)
        {
            _survivalStatsSystem?.Stamina.ModifyValue(-ActionStaminaCost);
            _lastActionTime = (float)Time.GetUnixTimeFromSystem();
            
            EmitSignal(SignalName.PlayerActionPerformed, actionType, ActionStaminaCost);
        }

        #endregion

        #region Event Handlers

        /// <summary>
        /// Handles player death
        /// </summary>
        private void OnPlayerDied()
        {
            _isAlive = false;
            Velocity = Vector2.Zero;
            
            // Close all UI panels
            _inventoryUI?.HideInventory();
            _craftingUI?.HideCrafting();
            
            GD.Print("PlayerController: Player died, disabling controls");
        }

        /// <summary>
        /// Handles player respawn
        /// </summary>
        private void OnPlayerRespawned()
        {
            _isAlive = true;
            
            // Reset player state
            _velocity = Vector2.Zero;
            _inputVector = Vector2.Zero;
            _isSprinting = false;
            
            GD.Print("PlayerController: Player respawned, enabling controls");
        }

        #endregion

        #region Public Interface

        /// <summary>
        /// Gets the player's current position
        /// </summary>
        public Vector2 GetPlayerPosition()
        {
            return GlobalPosition;
        }

        /// <summary>
        /// Sets the player's position (for loading saves, teleporting, etc.)
        /// </summary>
        public void SetPlayerPosition(Vector2 position)
        {
            GlobalPosition = position;
        }

        /// <summary>
        /// Gets player movement data for saving
        /// </summary>
        public PlayerMovementData GetMovementData()
        {
            return new PlayerMovementData
            {
                Position = GlobalPosition,
                Velocity = Velocity,
                IsSprinting = _isSprinting,
                IsAlive = _isAlive
            };
        }

        /// <summary>
        /// Loads player movement data from save
        /// </summary>
        public void LoadMovementData(PlayerMovementData data)
        {
            GlobalPosition = data.Position;
            Velocity = data.Velocity;
            _isSprinting = data.IsSprinting;
            _isAlive = data.IsAlive;
        }

        /// <summary>
        /// Forces the player to stop all actions (for cutscenes, menus, etc.)
        /// </summary>
        public void DisablePlayerControl()
        {
            _inputVector = Vector2.Zero;
            Velocity = Vector2.Zero;
            _isSprinting = false;
        }

        /// <summary>
        /// Re-enables player control
        /// </summary>
        public void EnablePlayerControl()
        {
            // Player control is automatically enabled when _isAlive is true
            // This method exists for consistency and future expansion
        }

        #endregion

        #region World Integration

        /// <summary>
        /// Updates world loading based on player position
        /// </summary>
        private void UpdateWorldLoading()
        {
            WorldManager.Instance?.UpdateWorldLoading(GlobalPosition);
        }

        /// <summary>
        /// Gets the current biome the player is in
        /// </summary>
        public BiomeType GetCurrentBiome()
        {
            return WorldManager.Instance?.GetBiomeAt(GlobalPosition) ?? BiomeType.Plains;
        }

        /// <summary>
        /// Gets the current chunk the player is in
        /// </summary>
        public WorldChunk GetCurrentChunk()
        {
            return WorldManager.Instance?.GetChunkAt(GlobalPosition);
        }

        #endregion

        public override void _ExitTree()
        {
            // Unsubscribe from events
            if (_survivalStatsSystem != null)
            {
                _survivalStatsSystem.PlayerDied -= OnPlayerDied;
                _survivalStatsSystem.PlayerRespawned -= OnPlayerRespawned;
            }
        }

        #region POI and Exploration Methods

        /// <summary>
        /// Updates POI interaction system and exploration tracker with current player position
        /// </summary>
        private void UpdatePOIAndExploration()
        {
            _poiInteractionSystem?.UpdatePlayerPosition(GlobalPosition);
            _explorationTracker?.UpdateExploration(GlobalPosition);
        }

        /// <summary>
        /// Checks for POI interactions and handles them
        /// </summary>
        private bool CheckForPOIInteractions()
        {
            if (_poiInteractionSystem == null) return false;

            // Handle POI interactions
            _poiInteractionSystem.HandleInteractionInput();
            
            // Check if we're near any POIs
            var nearbyPOIs = _poiInteractionSystem.GetNearbyInteractablePOIs();
            if (nearbyPOIs.Count > 0)
            {
                EmitSignal(SignalName.PlayerInteracted, "poi", nearbyPOIs[0].Id);
                EventBus.Instance?.EmitSignal(EventBus.SignalName.PlayerInteracted, "poi", nearbyPOIs[0].Id, GlobalPosition);
                return true;
            }

            return false;
        }

        #endregion
    }

    /// <summary>
    /// Data structure for player movement state
    /// </summary>
    [System.Serializable]
    public class PlayerMovementData
    {
        public Vector2 Position { get; set; }
        public Vector2 Velocity { get; set; }
        public bool IsSprinting { get; set; }
        public bool IsAlive { get; set; }
    }
}