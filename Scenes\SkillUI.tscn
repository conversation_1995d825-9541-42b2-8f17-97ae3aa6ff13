[gd_scene load_steps=2 format=3 uid="uid://bvqxm8n7qkxhj"]

[ext_resource type="Script" uid="uid://dqwk2ei285yt6" path="res://Scripts/SkillUI.cs" id="1_0k8vx"]

[node name="SkillUI" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("1_0k8vx")

[node name="Background" type="ColorRect" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
color = Color(0, 0, 0, 0.7)

[node name="VBox" type="VBoxContainer" parent="."]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -400.0
offset_top = -300.0
offset_right = 400.0
offset_bottom = 300.0
grow_horizontal = 2
grow_vertical = 2

[node name="Header" type="HBoxContainer" parent="VBox"]
layout_mode = 2

[node name="TitleLabel" type="Label" parent="VBox/Header"]
layout_mode = 2
text = "Skills"
horizontal_alignment = 1

[node name="SkillPointsLabel" type="Label" parent="VBox/Header"]
layout_mode = 2
size_flags_horizontal = 3
text = "Skill Points: 0"
horizontal_alignment = 2

[node name="CloseButton" type="Button" parent="VBox/Header"]
layout_mode = 2
text = "X"

[node name="SkillTabs" type="TabContainer" parent="VBox"]
layout_mode = 2
size_flags_vertical = 3
