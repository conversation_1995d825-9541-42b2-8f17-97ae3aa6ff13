using Godot;
using System;
using System.Collections.Generic;

[System.Serializable]
public enum QuestType
{
    Kill,
    Collect,
    Craft,
    Explore,
    Survive,
    Build
}

[System.Serializable]
public enum QuestDifficulty
{
    Easy,
    Medium,
    Hard,
    Extreme
}

[System.Serializable]
public class QuestObjective
{
    public string Description { get; set; }
    public string TargetId { get; set; }
    public int RequiredAmount { get; set; }
    public int CurrentAmount { get; set; }
    public bool IsCompleted => CurrentAmount >= RequiredAmount;
}

[System.Serializable]
public class QuestReward
{
    public string ItemId { get; set; }
    public int Amount { get; set; }
    public int ExperiencePoints { get; set; }
}

public partial class Quest : Node
{
    [Export] public string QuestId { get; set; }
    [Export] public string Title { get; set; }
    [Export] public string Description { get; set; }
    [Export] public QuestType Type { get; set; }
    [Export] public QuestDifficulty Difficulty { get; set; }
    public List<QuestObjective> Objectives { get; set; } = new();
    public List<QuestReward> Rewards { get; set; } = new();
    [Export] public float TimeLimit { get; set; } = 0f; // 0 = no time limit
    [Export] public bool IsActive { get; set; } = false;
    [Export] public bool IsCompleted { get; set; } = false;
    public DateTime StartTime { get; set; }
    
    public bool AllObjectivesCompleted => Objectives.TrueForAll(obj => obj.IsCompleted);
    public float RemainingTime => TimeLimit > 0 ? Math.Max(0, TimeLimit - (float)(DateTime.Now - StartTime).TotalSeconds) : float.MaxValue;
    public bool IsExpired => TimeLimit > 0 && RemainingTime <= 0;

    public override void _Ready()
    {
        if (Objectives == null)
            Objectives = new List<QuestObjective>();
        if (Rewards == null)
            Rewards = new List<QuestReward>();
    }

    public void StartQuest()
    {
        IsActive = true;
        StartTime = DateTime.Now;
        GD.Print($"Quest started: {Title}");
    }

    public void UpdateObjective(string targetId, int amount = 1)
    {
        if (!IsActive || IsCompleted) return;

        foreach (var objective in Objectives)
        {
            if (objective.TargetId == targetId)
            {
                objective.CurrentAmount = Math.Min(objective.CurrentAmount + amount, objective.RequiredAmount);
                GD.Print($"Quest objective updated: {objective.Description} ({objective.CurrentAmount}/{objective.RequiredAmount})");
                
                if (objective.IsCompleted)
                {
                    GD.Print($"Quest objective completed: {objective.Description}");
                }
                break;
            }
        }

        CheckCompletion();
    }

    public void CheckCompletion()
    {
        if (!IsActive || IsCompleted) return;

        if (AllObjectivesCompleted)
        {
            CompleteQuest();
        }
        else if (IsExpired)
        {
            FailQuest();
        }
    }

    private void CompleteQuest()
    {
        IsCompleted = true;
        IsActive = false;
        GD.Print($"Quest completed: {Title}");
        
        // Award rewards through EventBus
        SurvivalLooterShooter.EventBus.Instance?.EmitSignal(SurvivalLooterShooter.EventBus.SignalName.QuestCompleted, this);
    }

    private void FailQuest()
    {
        IsActive = false;
        GD.Print($"Quest failed: {Title}");
        
        SurvivalLooterShooter.EventBus.Instance?.EmitSignal(SurvivalLooterShooter.EventBus.SignalName.QuestFailed, this);
    }
}