using Godot;
using System;

namespace SurvivalLooterShooter
{
    /// <summary>
    /// Demo scene for testing resource harvesting mechanics
    /// </summary>
    public partial class ResourceHarvestingDemo : Node2D
    {
        private GameManager _gameManager;
        private ResourceHarvestingSystem _resourceSystem;
        private PlayerController _player;
        private Camera2D _camera;
        private Label _infoLabel;

        public override void _Ready()
        {
            GD.Print("ResourceHarvestingDemo starting...");
            
            // Initialize the demo
            InitializeDemo();
        }

        /// <summary>
        /// Initializes the demo scene
        /// </summary>
        private void InitializeDemo()
        {
            // Get UI elements
            _camera = GetNode<Camera2D>("Camera2D");
            _infoLabel = GetNode<Label>("UI/InfoLabel");
            
            // Wait for ItemDatabase to be ready
            CallDeferred(nameof(InitializeDemoDeferred));
        }

        private void InitializeDemoDeferred()
        {
            // Create and initialize GameManager
            _gameManager = new GameManager();
            AddChild(_gameManager);
            
            // Wait for GameManager to initialize
            CallDeferred(nameof(SetupDemoScene));
        }

        private void SetupDemoScene()
        {
            // Get systems from GameManager
            _resourceSystem = _gameManager.GetResourceHarvestingSystem();
            _player = _gameManager.GetPlayerController();
            
            if (_player != null)
            {
                // Position player in the center
                _player.GlobalPosition = new Vector2(960, 540);
                
                // Make camera follow player
                _camera.Reparent(_player);
                _camera.Position = Vector2.Zero;
            }

            // Spawn some test resource nodes around the player
            SpawnTestResources();
            
            // Add some tools to the player's inventory
            AddTestTools();
            
            GD.Print("ResourceHarvestingDemo initialized successfully");
        }

        /// <summary>
        /// Spawns test resource nodes around the player
        /// </summary>
        private void SpawnTestResources()
        {
            if (_resourceSystem == null) return;

            Vector2 playerPos = new Vector2(960, 540);
            
            // Spawn different types of resources in a circle around the player
            var resourceTypes = new[] { "wood", "stone", "berries", "mushrooms", "metal_ore" };
            float radius = 200f;
            
            for (int i = 0; i < resourceTypes.Length; i++)
            {
                float angle = (float)(i * 2 * Math.PI / resourceTypes.Length);
                Vector2 spawnPos = playerPos + new Vector2(
                    Mathf.Cos(angle) * radius,
                    Mathf.Sin(angle) * radius
                );
                
                // Create resource node manually for testing
                CreateTestResourceNode(resourceTypes[i], spawnPos);
            }
            
            // Spawn additional random resources
            for (int i = 0; i < 10; i++)
            {
                Vector2 randomPos = playerPos + new Vector2(
                    GD.RandRange(-400, 400),
                    GD.RandRange(-400, 400)
                );
                
                string randomResource = resourceTypes[GD.RandRange(0, resourceTypes.Length)];
                CreateTestResourceNode(randomResource, randomPos);
            }
        }

        /// <summary>
        /// Creates a test resource node at the specified position
        /// </summary>
        private void CreateTestResourceNode(string resourceId, Vector2 position)
        {
            var resourceNode = new ResourceNode();
            resourceNode.ResourceId = resourceId;
            resourceNode.GlobalPosition = position;
            
            // Set properties based on resource type
            var item = ItemDatabase.Instance?.GetItem(resourceId);
            if (item != null)
            {
                resourceNode.ResourceType = item.Metadata.ContainsKey("resource_type") ? 
                    item.Metadata["resource_type"].ToString() : "generic";
                resourceNode.RequiredTool = item.Metadata.ContainsKey("tool_required") ? 
                    item.Metadata["tool_required"].ToString() : "none";
            }
            
            // Set default values
            resourceNode.MinYield = 1;
            resourceNode.MaxYield = 5;
            resourceNode.HarvestTime = 2.0f;
            resourceNode.RegenerationTime = 60f; // 1 minute for testing
            resourceNode.InteractionRange = 75f;
            
            // Add to scene
            AddChild(resourceNode);
            
            GD.Print($"Created test resource node: {resourceId} at {position}");
        }

        /// <summary>
        /// Adds test tools to the player's inventory
        /// </summary>
        private void AddTestTools()
        {
            var inventory = _gameManager?.GetInventory();
            if (inventory == null) return;

            // Add harvesting tools
            inventory.AddItem("axe", 1);
            inventory.AddItem("pickaxe", 1);
            
            // Add some space for harvested resources
            GD.Print("Added harvesting tools to player inventory");
        }

        public override void _Input(InputEvent @event)
        {
            if (@event is InputEventKey keyEvent && keyEvent.Pressed)
            {
                switch (keyEvent.Keycode)
                {
                    case Key.R:
                        // Regenerate all resources
                        _resourceSystem?.ForceRegenerateAllResources();
                        GD.Print("Forced regeneration of all resources");
                        break;
                    case Key.C:
                        // Clear all resources
                        _resourceSystem?.ClearAllResources();
                        GD.Print("Cleared all resources");
                        break;
                    case Key.T:
                        // Spawn more test resources
                        SpawnTestResources();
                        GD.Print("Spawned more test resources");
                        break;
                    case Key.I:
                        // Show resource system stats
                        ShowResourceStats();
                        break;
                }
            }
        }

        /// <summary>
        /// Shows resource system statistics
        /// </summary>
        private void ShowResourceStats()
        {
            if (_resourceSystem == null) return;

            var stats = _resourceSystem.GetStats();
            string statsText = $@"Resource System Stats:
Total Nodes: {stats.TotalResourceNodes}
Active Nodes: {stats.ActiveResourceNodes}
Depleted Nodes: {stats.DepletedResourceNodes}
Active Chunks: {stats.ActiveChunks}
Resource Types: {stats.ResourceTypes}

Controls:
R - Regenerate all resources
C - Clear all resources  
T - Spawn more test resources
I - Show stats";

            _infoLabel.Text = statsText;
            
            GD.Print("=== Resource System Stats ===");
            GD.Print($"Total Nodes: {stats.TotalResourceNodes}");
            GD.Print($"Active Nodes: {stats.ActiveResourceNodes}");
            GD.Print($"Depleted Nodes: {stats.DepletedResourceNodes}");
            GD.Print($"Active Chunks: {stats.ActiveChunks}");
            GD.Print($"Resource Types: {stats.ResourceTypes}");
        }
    }
}