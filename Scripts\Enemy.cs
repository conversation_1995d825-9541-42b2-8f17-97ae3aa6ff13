using Godot;
using System;

namespace SurvivalLooterShooter
{
    public partial class Enemy : CharacterBody2D
    {
        [Export] public string EnemyType { get; set; } = "basic";
        [Export] public string EnemyId { get; set; } = "";
        [Export] public string EnemyName { get; set; } = "Enemy";
        [Export] public float MaxHealth { get; set; } = 100f;
        [Export] public float CurrentHealth { get; set; } = 100f;
        [Export] public float Damage { get; set; } = 10f;
        [Export] public float Speed { get; set; } = 100f;
        [Export] public float DetectionRange { get; set; } = 200f;
        [Export] public float AttackRange { get; set; } = 50f;
        [Export] public int ExperienceReward { get; set; } = 10;
        [Export] public string[] ValidBiomes { get; set; } = new string[] { "all" };
        [Export] public string LootTable { get; set; } = "";

        public Node Target { get; set; }
        public AIState CurrentState { get; set; } = AIState.Idle;
        public bool IsDead => CurrentHealth <= 0;

        // Events
        [Signal] public delegate void EnemyDiedEventHandler();
        [Signal] public delegate void EnemyTookDamageEventHandler(float damage);
        [Signal] public delegate void EnemyAttackedEventHandler();

        public virtual void TakeDamage(float damage)
        {
            CurrentHealth -= damage;
            EmitSignal(SignalName.EnemyTookDamage, damage);
            if (CurrentHealth <= 0)
            {
                Die();
            }
        }

        public virtual void Die()
        {
            EmitSignal(SignalName.EnemyDied);
            EventBus.Instance?.EmitEnemyKilled(this);
            QueueFree();
        }

        public virtual void Attack()
        {
            EmitSignal(SignalName.EnemyAttacked);
            // Attack logic here
        }

        public virtual void Heal(float amount)
        {
            CurrentHealth = Mathf.Min(CurrentHealth + amount, MaxHealth);
        }

        public virtual void StopMovement()
        {
            Velocity = Vector2.Zero;
        }

        public virtual AIController GetAIController()
        {
            return GetNode<AIController>("AIController");
        }

        public virtual bool IsTargetInAttackRange(Node target)
        {
            if (target == null) return false;
            if (target is Node2D node2D)
            {
                var distance = GlobalPosition.DistanceTo(node2D.GlobalPosition);
                return distance <= AttackRange;
            }
            return false;
        }

        public virtual bool IsTargetInDetectionRange(Node target)
        {
            if (target == null) return false;
            if (target is Node2D node2D)
            {
                var distance = GlobalPosition.DistanceTo(node2D.GlobalPosition);
                return distance <= DetectionRange;
            }
            return false;
        }

        public virtual float GetDistanceTo(Node target)
        {
            if (target == null) return float.MaxValue;
            if (target is Node2D node2D)
            {
                return GlobalPosition.DistanceTo(node2D.GlobalPosition);
            }
            return float.MaxValue;
        }

        public float GetHealthPercentage()
        {
            return CurrentHealth / MaxHealth;
        }

        public virtual void Initialize(EnemyData data)
        {
            EnemyType = data.Id;
            MaxHealth = data.MaxHealth;
            CurrentHealth = data.Health;
            Damage = data.Damage;
            Speed = data.Speed;
            DetectionRange = data.DetectionRange;
            AttackRange = data.AttackRange;
            ExperienceReward = (int)data.ExperienceReward;
        }

        internal void Attack(Node2D target)
        {
            throw new NotImplementedException();
        }

        public enum AIState
        {
            Idle,
            Patrolling,
            Chasing,
            Attacking,
            Returning
        }
    }
}