using Godot;
using System.Collections.Generic;

namespace SurvivalLooterShooter
{
    /// <summary>
    /// Unit tests for the storage container system
    /// </summary>
    public partial class StorageContainerTests : Node
    {
        private StorageContainer _testContainer;
        private int _testsPassed = 0;
        private int _totalTests = 0;

        public override void _Ready()
        {
            Logger.LogInfo("StorageContainerTests", "Starting storage container tests...");
            RunAllTests();
        }

        private void RunAllTests()
        {
            SetupTestContainer();
            
            TestBasicItemAddition();
            TestItemStacking();
            TestCapacityLimits();
            TestItemRemoval();
            TestItemFilters();
            TestSorting();
            TestAccessControl();
            TestContainerStats();
            
            Logger.LogInfo("StorageContainerTests", $"Storage container tests completed: {_testsPassed}/{_totalTests} passed");
        }

        private void SetupTestContainer()
        {
            _testContainer = new StorageContainer();
            AddChild(_testContainer);
            _testContainer.Initialize("test_container", "Test Container", 50, ContainerType.Basic);
        }

        private void TestBasicItemAddition()
        {
            _totalTests++;
            Logger.LogInfo("StorageContainerTests", "Testing basic item addition...");
            
            // Test adding a single item
            bool result = _testContainer.TryAddItem("wood", 10);
            
            if (result && _testContainer.GetItemQuantity("wood") == 10)
            {
                _testsPassed++;
                Logger.LogInfo("StorageContainerTests", "✓ Basic item addition test passed");
            }
            else
            {
                Logger.LogError("StorageContainerTests", "✗ Basic item addition test failed");
            }
        }

        private void TestItemStacking()
        {
            _totalTests++;
            Logger.LogInfo("StorageContainerTests", "Testing item stacking...");
            
            // Add more of the same item
            bool result = _testContainer.TryAddItem("wood", 5);
            
            if (result && _testContainer.GetItemQuantity("wood") == 15)
            {
                _testsPassed++;
                Logger.LogInfo("StorageContainerTests", "✓ Item stacking test passed");
            }
            else
            {
                Logger.LogError("StorageContainerTests", "✗ Item stacking test failed");
            }
        }

        private void TestCapacityLimits()
        {
            _totalTests++;
            Logger.LogInfo("StorageContainerTests", "Testing capacity limits...");
            
            // Try to add more items than capacity allows
            bool result = _testContainer.TryAddItem("stone", 40); // Should fail as we already have 15 wood
            
            if (!result && _testContainer.GetItemQuantity("stone") == 0)
            {
                _testsPassed++;
                Logger.LogInfo("StorageContainerTests", "✓ Capacity limits test passed");
            }
            else
            {
                Logger.LogError("StorageContainerTests", "✗ Capacity limits test failed");
            }
        }

        private void TestItemRemoval()
        {
            _totalTests++;
            Logger.LogInfo("StorageContainerTests", "Testing item removal...");
            
            // Remove some wood
            bool result = _testContainer.TryRemoveItem("wood", 5);
            
            if (result && _testContainer.GetItemQuantity("wood") == 10)
            {
                _testsPassed++;
                Logger.LogInfo("StorageContainerTests", "✓ Item removal test passed");
            }
            else
            {
                Logger.LogError("StorageContainerTests", "✗ Item removal test failed");
            }
        }

        private void TestItemFilters()
        {
            _totalTests++;
            Logger.LogInfo("StorageContainerTests", "Testing item filters...");
            
            // Set up a filter for only materials
            _testContainer.SetItemFilters("material");
            
            // Try to add a weapon (should fail)
            bool weaponResult = _testContainer.TryAddItem("assault_rifle", 1);
            
            // Try to add a material (should succeed)
            bool materialResult = _testContainer.TryAddItem("metal_scrap", 5);
            
            if (!weaponResult && materialResult && _testContainer.GetItemQuantity("metal_scrap") == 5)
            {
                _testsPassed++;
                Logger.LogInfo("StorageContainerTests", "✓ Item filters test passed");
            }
            else
            {
                Logger.LogError("StorageContainerTests", "✗ Item filters test failed");
            }
            
            // Clear filters for other tests
            _testContainer.SetItemFilters();
        }

        private void TestSorting()
        {
            _totalTests++;
            Logger.LogInfo("StorageContainerTests", "Testing container sorting...");
            
            // Add some different items
            _testContainer.TryAddItem("bandage", 3);
            _testContainer.TryAddItem("ammunition", 20);
            
            // Test sorting by name
            _testContainer.SortContainer(ContainerSortMode.Name);
            var sortedItems = _testContainer.GetItems();
            
            if (sortedItems.Count > 0)
            {
                _testsPassed++;
                Logger.LogInfo("StorageContainerTests", "✓ Container sorting test passed");
            }
            else
            {
                Logger.LogError("StorageContainerTests", "✗ Container sorting test failed");
            }
        }

        private void TestAccessControl()
        {
            _totalTests++;
            Logger.LogInfo("StorageContainerTests", "Testing access control...");
            
            // Lock the container
            _testContainer.Lock("1234");
            
            // Test permission checking
            bool hasPermission = _testContainer.HasPermission("unauthorized_player", ContainerPermission.View);
            
            // Test unlocking
            bool unlockResult = _testContainer.TryUnlock("1234");
            
            if (!hasPermission && unlockResult && !_testContainer.IsLocked)
            {
                _testsPassed++;
                Logger.LogInfo("StorageContainerTests", "✓ Access control test passed");
            }
            else
            {
                Logger.LogError("StorageContainerTests", "✗ Access control test failed");
            }
        }

        private void TestContainerStats()
        {
            _totalTests++;
            Logger.LogInfo("StorageContainerTests", "Testing container statistics...");
            
            var stats = _testContainer.GetStats();
            
            if (stats.TotalItems > 0 && stats.Capacity == 50 && stats.UtilizationPercentage > 0)
            {
                _testsPassed++;
                Logger.LogInfo("StorageContainerTests", "✓ Container statistics test passed");
            }
            else
            {
                Logger.LogError("StorageContainerTests", "✗ Container statistics test failed");
            }
        }
    }
}