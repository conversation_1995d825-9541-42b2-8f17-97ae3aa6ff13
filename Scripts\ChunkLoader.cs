using Godot;
using System;
using System.Collections.Generic;

namespace SurvivalLooterShooter
{
    /// <summary>
    /// ChunkLoader manages efficient loading and unloading of world chunks
    /// Handles chunk streaming based on player position and render distance
    /// </summary>
    public partial class ChunkLoader : Node
    {
        // Configuration
        private int _chunkSize;
        private int _renderDistance;
        private float _updateInterval = 1.0f; // How often to check for chunk updates (seconds)

        // State tracking
        private float _lastUpdateTime = 0f;
        private Vector2I _lastPlayerChunk = Vector2I.Zero;
        private HashSet<Vector2I> _requestedChunks = new HashSet<Vector2I>();
        private Queue<Vector2I> _loadQueue = new Queue<Vector2I>();
        private Queue<Vector2I> _unloadQueue = new Queue<Vector2I>();

        // Performance settings
        [Export] public int MaxChunksPerFrame { get; set; } = 1; // Limit chunk operations per frame
        [Export] public bool EnableAsyncLoading { get; set; } = true;

        // Events
        [Signal]
        public delegate void ChunkRequestedEventHandler(Vector2I chunkCoords);
        
        [Signal]
        public delegate void ChunkUnloadRequestedEventHandler(Vector2I chunkCoords);

        public int ChunkSize => _chunkSize;
        public int RenderDistance => _renderDistance;

        /// <summary>
        /// Initializes the chunk loader with configuration
        /// </summary>
        public void Initialize(int chunkSize, int renderDistance)
        {
            _chunkSize = chunkSize;
            _renderDistance = renderDistance;
            
            GD.Print($"ChunkLoader initialized - ChunkSize: {chunkSize}, RenderDistance: {renderDistance}");
        }

        public override void _Process(double delta)
        {
            _lastUpdateTime += (float)delta;
            
            if (_lastUpdateTime >= _updateInterval)
            {
                _lastUpdateTime = 0f;
                ProcessChunkQueues();
            }
        }

        /// <summary>
        /// Updates chunk loading based on player position
        /// </summary>
        public void UpdatePlayerPosition(Vector2 playerPosition)
        {
            Vector2I currentChunk = WorldToChunkCoords(playerPosition);
            
            if (currentChunk != _lastPlayerChunk)
            {
                _lastPlayerChunk = currentChunk;
                QueueChunkUpdates(currentChunk);
            }
        }

        /// <summary>
        /// Queues chunks for loading and unloading based on player position
        /// </summary>
        private void QueueChunkUpdates(Vector2I playerChunk)
        {
            // Queue chunks for loading
            for (int x = -_renderDistance; x <= _renderDistance; x++)
            {
                for (int y = -_renderDistance; y <= _renderDistance; y++)
                {
                    Vector2I chunkCoords = playerChunk + new Vector2I(x, y);
                    
                    if (!_requestedChunks.Contains(chunkCoords))
                    {
                        _loadQueue.Enqueue(chunkCoords);
                        _requestedChunks.Add(chunkCoords);
                    }
                }
            }

            // Queue distant chunks for unloading
            var chunksToUnload = new List<Vector2I>();
            foreach (var chunkCoords in _requestedChunks)
            {
                float distance = chunkCoords.DistanceTo(playerChunk);
                if (distance > _renderDistance + 1) // Buffer to prevent thrashing
                {
                    chunksToUnload.Add(chunkCoords);
                }
            }

            foreach (var chunkCoords in chunksToUnload)
            {
                _unloadQueue.Enqueue(chunkCoords);
                _requestedChunks.Remove(chunkCoords);
            }
        }

        /// <summary>
        /// Processes chunk loading and unloading queues
        /// </summary>
        private void ProcessChunkQueues()
        {
            int operationsThisFrame = 0;

            // Process unload queue first to free memory
            while (_unloadQueue.Count > 0 && operationsThisFrame < MaxChunksPerFrame)
            {
                Vector2I chunkCoords = _unloadQueue.Dequeue();
                EmitSignal(SignalName.ChunkUnloadRequested, chunkCoords);
                operationsThisFrame++;
            }

            // Process load queue
            while (_loadQueue.Count > 0 && operationsThisFrame < MaxChunksPerFrame)
            {
                Vector2I chunkCoords = _loadQueue.Dequeue();
                
                if (EnableAsyncLoading)
                {
                    // For now, we'll do synchronous loading
                    // In a full implementation, this could use threading
                    EmitSignal(SignalName.ChunkRequested, chunkCoords);
                }
                else
                {
                    EmitSignal(SignalName.ChunkRequested, chunkCoords);
                }
                
                operationsThisFrame++;
            }
        }

        /// <summary>
        /// Converts world coordinates to chunk coordinates
        /// </summary>
        private Vector2I WorldToChunkCoords(Vector2 worldPosition)
        {
            return new Vector2I(
                Mathf.FloorToInt(worldPosition.X / _chunkSize),
                Mathf.FloorToInt(worldPosition.Y / _chunkSize)
            );
        }

        /// <summary>
        /// Forces loading of a specific chunk
        /// </summary>
        public void ForceLoadChunk(Vector2I chunkCoords)
        {
            if (!_requestedChunks.Contains(chunkCoords))
            {
                _loadQueue.Enqueue(chunkCoords);
                _requestedChunks.Add(chunkCoords);
            }
        }

        /// <summary>
        /// Forces unloading of a specific chunk
        /// </summary>
        public void ForceUnloadChunk(Vector2I chunkCoords)
        {
            if (_requestedChunks.Contains(chunkCoords))
            {
                _unloadQueue.Enqueue(chunkCoords);
                _requestedChunks.Remove(chunkCoords);
            }
        }

        /// <summary>
        /// Gets the current load queue size
        /// </summary>
        public int GetLoadQueueSize()
        {
            return _loadQueue.Count;
        }

        /// <summary>
        /// Gets the current unload queue size
        /// </summary>
        public int GetUnloadQueueSize()
        {
            return _unloadQueue.Count;
        }

        /// <summary>
        /// Clears all queues and requested chunks
        /// </summary>
        public void ClearQueues()
        {
            _loadQueue.Clear();
            _unloadQueue.Clear();
            _requestedChunks.Clear();
        }

        /// <summary>
        /// Gets performance statistics
        /// </summary>
        public ChunkLoaderStats GetStats()
        {
            return new ChunkLoaderStats
            {
                LoadQueueSize = _loadQueue.Count,
                UnloadQueueSize = _unloadQueue.Count,
                RequestedChunksCount = _requestedChunks.Count,
                LastPlayerChunk = _lastPlayerChunk
            };
        }
    }

    /// <summary>
    /// Statistics for chunk loader performance monitoring
    /// </summary>
    public class ChunkLoaderStats
    {
        public int LoadQueueSize { get; set; }
        public int UnloadQueueSize { get; set; }
        public int RequestedChunksCount { get; set; }
        public Vector2I LastPlayerChunk { get; set; }
    }
}