using Godot;
using System;
using System.Collections.Generic;
using System.Linq;

namespace SurvivalLooterShooter
{
    /// <summary>
    /// Manages progression rewards, recipe unlocks, milestone achievements, and prestige system
    /// </summary>
    public partial class ProgressionRewardSystem : Node
    {
        public static ProgressionRewardSystem Instance { get; private set; }
        
        private Dictionary<string, List<string>> _skillRecipeUnlocks = new();
        private Dictionary<string, SkillMilestone> _skillMilestones = new();
        private Dictionary<string, PrestigeLevel> _prestigeLevels = new();
        private HashSet<string> _unlockedRecipes = new();
        private HashSet<string> _achievedMilestones = new();
        private Dictionary<string, int> _prestigePoints = new();
        private Dictionary<string, float> _deathPenalties = new();
        private Dictionary<string, float> _experienceDebt = new();
        
        // Death penalty settings
        private const float BASE_DEATH_PENALTY = 0.1f; // 10% XP loss
        private const float MAX_DEATH_PENALTY = 0.25f; // 25% max XP loss
        private const float DEBT_RECOVERY_RATE = 0.05f; // 5% of gained XP goes to debt recovery
        
        [Signal]
        public delegate void RecipeUnlockedEventHandler(string recipeId, string skillId, int skillLevel);
        
        [Signal]
        public delegate void MilestoneAchievedEventHandler(string milestoneId, string skillId, int level);
        
        [Signal]
        public delegate void PrestigeLevelUnlockedEventHandler(string skillId, int prestigeLevel);
        
        [Signal]
        public delegate void DeathPenaltyAppliedEventHandler(string penaltyInfo);
        
        public override void _Ready()
        {
            if (Instance == null)
            {
                Instance = this;
                InitializeProgressionData();
                ConnectToEventBus();
            }
            else
            {
                QueueFree();
            }
        }
        
        private void ConnectToEventBus()
        {
            if (EventBus.Instance != null)
            {
                EventBus.Instance.SkillLevelUp += OnSkillLevelUp;
                EventBus.Instance.PlayerDied += OnPlayerDied;
                EventBus.Instance.ExperienceGained += OnExperienceGained;
            }
        }
        
        private void InitializeProgressionData()
        {
            InitializeSkillRecipeUnlocks();
            InitializeSkillMilestones();
            InitializePrestigeLevels();
        }
        
        private void InitializeSkillRecipeUnlocks()
        {
            // Combat skill recipe unlocks
            _skillRecipeUnlocks["weapon_proficiency"] = new List<string>
            {
                "craft_improved_rifle", // Level 5
                "craft_sniper_rifle", // Level 10
                "craft_assault_rifle" // Level 15
            };
            
            _skillRecipeUnlocks["critical_hit"] = new List<string>
            {
                "craft_precision_ammo", // Level 3
                "craft_armor_piercing_ammo" // Level 8
            };
            
            // Crafting skill recipe unlocks
            _skillRecipeUnlocks["crafting_efficiency"] = new List<string>
            {
                "craft_advanced_workbench", // Level 5
                "craft_automated_crafter", // Level 12
                "craft_master_toolkit" // Level 20
            };
            
            _skillRecipeUnlocks["quality_bonus"] = new List<string>
            {
                "craft_quality_materials", // Level 4
                "craft_masterwork_items", // Level 10
                "craft_legendary_gear" // Level 18
            };
            
            _skillRecipeUnlocks["advanced_recipes"] = new List<string>
            {
                "craft_advanced_rifle", // Level 1
                "craft_health_serum", // Level 3
                "craft_explosive_ammo", // Level 5
                "craft_power_core", // Level 8
                "craft_advanced_armor" // Level 10
            };
            
            // Survival skill recipe unlocks
            _skillRecipeUnlocks["medicine"] = new List<string>
            {
                "craft_advanced_bandage", // Level 3
                "craft_antidote", // Level 6
                "craft_regeneration_serum" // Level 12
            };
            
            _skillRecipeUnlocks["foraging"] = new List<string>
            {
                "craft_nutrient_paste", // Level 4
                "craft_energy_bar", // Level 8
                "craft_survival_kit" // Level 15
            };
            
            // Building skill recipe unlocks
            _skillRecipeUnlocks["construction"] = new List<string>
            {
                "craft_reinforced_walls", // Level 5
                "craft_automated_defenses", // Level 10
                "craft_fortress_gate" // Level 18
            };
            
            _skillRecipeUnlocks["advanced_structures"] = new List<string>
            {
                "craft_solar_panel", // Level 2
                "craft_water_purifier", // Level 5
                "craft_command_center" // Level 12
            };
        }
        
        private void InitializeSkillMilestones()
        {
            // Combat milestones
            _skillMilestones["combat_novice"] = new SkillMilestone
            {
                Id = "combat_novice",
                Name = "Combat Novice",
                Description = "Reach level 10 in any combat skill",
                SkillCategory = SkillType.Combat,
                RequiredLevel = 10,
                Rewards = new List<MilestoneReward>
                {
                    new() { Type = "skill_points", Amount = 2 },
                    new() { Type = "recipe_unlock", Target = "craft_combat_stimulant" }
                }
            };
            
            _skillMilestones["combat_expert"] = new SkillMilestone
            {
                Id = "combat_expert",
                Name = "Combat Expert",
                Description = "Reach level 25 in any combat skill",
                SkillCategory = SkillType.Combat,
                RequiredLevel = 25,
                Rewards = new List<MilestoneReward>
                {
                    new() { Type = "skill_points", Amount = 5 },
                    new() { Type = "prestige_points", Amount = 1 },
                    new() { Type = "ability_unlock", Target = "berserker_mode" }
                }
            };
            
            // Crafting milestones
            _skillMilestones["master_crafter"] = new SkillMilestone
            {
                Id = "master_crafter",
                Name = "Master Crafter",
                Description = "Reach level 20 in crafting efficiency",
                SkillCategory = SkillType.Crafting,
                RequiredLevel = 20,
                Rewards = new List<MilestoneReward>
                {
                    new() { Type = "skill_points", Amount = 3 },
                    new() { Type = "crafting_bonus", Amount = 0.15f },
                    new() { Type = "recipe_unlock", Target = "craft_legendary_toolkit" }
                }
            };
            
            // Survival milestones
            _skillMilestones["survivor"] = new SkillMilestone
            {
                Id = "survivor",
                Name = "Hardened Survivor",
                Description = "Reach level 15 in metabolism skill",
                SkillCategory = SkillType.Survival,
                RequiredLevel = 15,
                Rewards = new List<MilestoneReward>
                {
                    new() { Type = "skill_points", Amount = 2 },
                    new() { Type = "stat_bonus", Target = "max_health", Amount = 25f }
                }
            };
            
            // Building milestones
            _skillMilestones["architect"] = new SkillMilestone
            {
                Id = "architect",
                Name = "Master Architect",
                Description = "Reach level 30 in construction skill",
                SkillCategory = SkillType.Building,
                RequiredLevel = 30,
                Rewards = new List<MilestoneReward>
                {
                    new() { Type = "skill_points", Amount = 4 },
                    new() { Type = "prestige_points", Amount = 2 },
                    new() { Type = "building_bonus", Amount = 0.25f }
                }
            };
        }
        
        private void InitializePrestigeLevels()
        {
            // Combat prestige levels
            _prestigeLevels["combat_prestige_1"] = new PrestigeLevel
            {
                Id = "combat_prestige_1",
                Name = "Weapon Master",
                Description = "Unlock advanced weapon modifications",
                SkillCategory = SkillType.Combat,
                RequiredPrestigePoints = 1,
                Benefits = new List<PrestigeBenefit>
                {
                    new() { Type = "damage_multiplier", Amount = 0.1f },
                    new() { Type = "crit_chance_bonus", Amount = 0.05f }
                }
            };
            
            _prestigeLevels["combat_prestige_2"] = new PrestigeLevel
            {
                Id = "combat_prestige_2",
                Name = "Combat Veteran",
                Description = "Reduced death penalties and combat bonuses",
                SkillCategory = SkillType.Combat,
                RequiredPrestigePoints = 3,
                Benefits = new List<PrestigeBenefit>
                {
                    new() { Type = "death_penalty_reduction", Amount = 0.5f },
                    new() { Type = "reload_speed_bonus", Amount = 0.2f }
                }
            };
            
            // Crafting prestige levels
            _prestigeLevels["crafting_prestige_1"] = new PrestigeLevel
            {
                Id = "crafting_prestige_1",
                Name = "Artisan",
                Description = "Enhanced crafting efficiency and quality",
                SkillCategory = SkillType.Crafting,
                RequiredPrestigePoints = 1,
                Benefits = new List<PrestigeBenefit>
                {
                    new() { Type = "material_efficiency", Amount = 0.15f },
                    new() { Type = "quality_chance_bonus", Amount = 0.1f }
                }
            };
            
            // Survival prestige levels
            _prestigeLevels["survival_prestige_1"] = new PrestigeLevel
            {
                Id = "survival_prestige_1",
                Name = "Apex Survivor",
                Description = "Improved survival stats and recovery",
                SkillCategory = SkillType.Survival,
                RequiredPrestigePoints = 2,
                Benefits = new List<PrestigeBenefit>
                {
                    new() { Type = "stat_decay_reduction", Amount = 0.3f },
                    new() { Type = "healing_bonus", Amount = 0.25f }
                }
            };
        }
        
        private void OnSkillLevelUp(string skillId, int newLevel)
        {
            CheckForRecipeUnlocks(skillId, newLevel);
            CheckForMilestones(skillId, newLevel);
            CheckForPrestigeUnlocks(skillId);
        }
        
        private void CheckForRecipeUnlocks(string skillId, int skillLevel)
        {
            if (!_skillRecipeUnlocks.TryGetValue(skillId, out var recipes)) return;
            
            // Calculate which recipes should be unlocked at this level
            int recipesPerLevel = Math.Max(1, recipes.Count / 20); // Spread recipes across 20 levels
            int unlocksAtThisLevel = Math.Min(recipesPerLevel, recipes.Count);
            
            for (int i = 0; i < unlocksAtThisLevel; i++)
            {
                int recipeIndex = ((skillLevel - 1) / (20 / recipes.Count)) + i;
                if (recipeIndex < recipes.Count)
                {
                    string recipeId = recipes[recipeIndex];
                    if (_unlockedRecipes.Add(recipeId)) // Returns true if newly added
                    {
                        EmitSignal(SignalName.RecipeUnlocked, recipeId, skillId, skillLevel);
                        GD.Print($"Recipe unlocked: {recipeId} (Skill: {skillId}, Level: {skillLevel})");
                    }
                }
            }
        }
        
        private void CheckForMilestones(string skillId, int skillLevel)
        {
            var skill = SkillManager.Instance?.GetSkill(skillId);
            if (skill == null) return;
            
            foreach (var milestone in _skillMilestones.Values)
            {
                if (milestone.SkillCategory == skill.Category && 
                    skillLevel >= milestone.RequiredLevel &&
                    _achievedMilestones.Add(milestone.Id)) // Returns true if newly added
                {
                    ApplyMilestoneRewards(milestone);
                    EmitSignal(SignalName.MilestoneAchieved, milestone.Id, skillId, skillLevel);
                    GD.Print($"Milestone achieved: {milestone.Name}");
                }
            }
        }
        
        private void ApplyMilestoneRewards(SkillMilestone milestone)
        {
            foreach (var reward in milestone.Rewards)
            {
                switch (reward.Type)
                {
                    case "skill_points":
                        SkillManager.Instance?.AddSkillPoints((int)reward.Amount);
                        break;
                        
                    case "recipe_unlock":
                        if (!string.IsNullOrEmpty(reward.Target))
                        {
                            _unlockedRecipes.Add(reward.Target);
                        }
                        break;
                        
                    case "prestige_points":
                        string skillCategory = milestone.SkillCategory.ToString().ToLower();
                        _prestigePoints[skillCategory] = _prestigePoints.GetValueOrDefault(skillCategory) + (int)reward.Amount;
                        break;
                        
                    case "ability_unlock":
                        // This would integrate with an ability system
                        GD.Print($"Ability unlocked: {reward.Target}");
                        break;
                        
                    case "crafting_bonus":
                    case "stat_bonus":
                    case "building_bonus":
                        // These would be applied as permanent bonuses
                        GD.Print($"Permanent bonus applied: {reward.Type} +{reward.Amount}");
                        break;
                }
            }
        }
        
        private void CheckForPrestigeUnlocks(string skillId)
        {
            var skill = SkillManager.Instance?.GetSkill(skillId);
            if (skill == null) return;
            
            string categoryKey = skill.Category.ToString().ToLower();
            int availablePoints = _prestigePoints.GetValueOrDefault(categoryKey);
            
            foreach (var prestige in _prestigeLevels.Values)
            {
                if (prestige.SkillCategory == skill.Category &&
                    availablePoints >= prestige.RequiredPrestigePoints &&
                    !IsPrestigeLevelUnlocked(prestige.Id))
                {
                    UnlockPrestigeLevel(prestige);
                }
            }
        }
        
        private void UnlockPrestigeLevel(PrestigeLevel prestige)
        {
            string categoryKey = prestige.SkillCategory.ToString().ToLower();
            _prestigePoints[categoryKey] -= prestige.RequiredPrestigePoints;
            
            // Apply prestige benefits
            foreach (var benefit in prestige.Benefits)
            {
                ApplyPrestigeBenefit(benefit);
            }
            
            EmitSignal(SignalName.PrestigeLevelUnlocked, categoryKey, prestige.RequiredPrestigePoints);
            GD.Print($"Prestige level unlocked: {prestige.Name}");
        }
        
        private void ApplyPrestigeBenefit(PrestigeBenefit benefit)
        {
            // These would integrate with the appropriate systems
            switch (benefit.Type)
            {
                case "damage_multiplier":
                case "crit_chance_bonus":
                case "reload_speed_bonus":
                    // Apply to combat system
                    break;
                    
                case "material_efficiency":
                case "quality_chance_bonus":
                    // Apply to crafting system
                    break;
                    
                case "stat_decay_reduction":
                case "healing_bonus":
                    // Apply to survival system
                    break;
                    
                case "death_penalty_reduction":
                    // Reduce death penalties
                    break;
            }
            
            GD.Print($"Prestige benefit applied: {benefit.Type} +{benefit.Amount}");
        }
        
        private void OnPlayerDied(string cause)
        {
            ApplyDeathPenalties();
        }
        
        private void ApplyDeathPenalties()
        {
            var penalties = new Dictionary<string, float>();
            
            if (SkillManager.Instance == null) return;
            
            // Calculate death penalty based on prestige levels
            float penaltyReduction = GetPrestigeBenefit("death_penalty_reduction");
            float actualPenalty = Math.Max(0.05f, BASE_DEATH_PENALTY * (1f - penaltyReduction));
            
            // Apply penalties to all skills
            foreach (SkillType category in Enum.GetValues<SkillType>())
            {
                var categorySkills = SkillManager.Instance.GetSkillsByCategory(category);
                foreach (var skill in categorySkills)
                {
                    if (skill.Level > 0)
                    {
                        float experienceLoss = skill.Experience * actualPenalty;
                        skill.Experience = Math.Max(0, skill.Experience - experienceLoss);
                        
                        // Add to experience debt for recovery
                        _experienceDebt[skill.Id] = _experienceDebt.GetValueOrDefault(skill.Id) + experienceLoss;
                        
                        penalties[skill.Id] = experienceLoss;
                    }
                }
            }
            
            string penaltyInfo = $"Death penalties applied to {penalties.Count} skills. Penalty rate: {actualPenalty:P1}";
            EmitSignal(SignalName.DeathPenaltyApplied, penaltyInfo);
            GD.Print(penaltyInfo);
        }
        
        private void OnExperienceGained(string source, int categoryInt, float amount)
        {
            // Handle experience debt recovery
            SkillType category = (SkillType)categoryInt;
            var categorySkills = SkillManager.Instance?.GetSkillsByCategory(category);
            
            if (categorySkills == null) return;
            
            foreach (var skill in categorySkills)
            {
                if (_experienceDebt.TryGetValue(skill.Id, out float debt) && debt > 0)
                {
                    float recovery = amount * DEBT_RECOVERY_RATE;
                    float actualRecovery = Math.Min(recovery, debt);
                    
                    _experienceDebt[skill.Id] = debt - actualRecovery;
                    skill.Experience += actualRecovery;
                    
                    if (_experienceDebt[skill.Id] <= 0)
                    {
                        _experienceDebt.Remove(skill.Id);
                        GD.Print($"Experience debt recovered for skill: {skill.Id}");
                    }
                }
            }
        }
        
        // Public API methods
        public bool IsRecipeUnlocked(string recipeId)
        {
            return _unlockedRecipes.Contains(recipeId);
        }
        
        public bool IsMilestoneAchieved(string milestoneId)
        {
            return _achievedMilestones.Contains(milestoneId);
        }
        
        public bool IsPrestigeLevelUnlocked(string prestigeId)
        {
            // This would check against unlocked prestige levels
            return false; // Placeholder
        }
        
        public int GetPrestigePoints(SkillType category)
        {
            string key = category.ToString().ToLower();
            return _prestigePoints.GetValueOrDefault(key);
        }
        
        public float GetPrestigeBenefit(string benefitType)
        {
            // Calculate total prestige benefits of a specific type
            float totalBenefit = 0f;
            
            foreach (var prestige in _prestigeLevels.Values)
            {
                if (IsPrestigeLevelUnlocked(prestige.Id))
                {
                    foreach (var benefit in prestige.Benefits)
                    {
                        if (benefit.Type == benefitType)
                        {
                            totalBenefit += benefit.Amount;
                        }
                    }
                }
            }
            
            return totalBenefit;
        }
        
        public List<string> GetAvailableRecipes()
        {
            return _unlockedRecipes.ToList();
        }
        
        public Dictionary<string, float> GetExperienceDebt()
        {
            return new Dictionary<string, float>(_experienceDebt);
        }
        
        public Dictionary<string, object> GetSaveData()
        {
            return new Dictionary<string, object>
            {
                ["unlocked_recipes"] = _unlockedRecipes.ToList(),
                ["achieved_milestones"] = _achievedMilestones.ToList(),
                ["prestige_points"] = _prestigePoints,
                ["experience_debt"] = _experienceDebt
            };
        }
        
        public void LoadSaveData(Dictionary<string, object> saveData)
        {
            if (saveData.TryGetValue("unlocked_recipes", out var recipes))
            {
                _unlockedRecipes = new HashSet<string>((List<string>)recipes);
            }
            
            if (saveData.TryGetValue("achieved_milestones", out var milestones))
            {
                _achievedMilestones = new HashSet<string>((List<string>)milestones);
            }
            
            if (saveData.TryGetValue("prestige_points", out var points))
            {
                _prestigePoints = (Dictionary<string, int>)points;
            }
            
            if (saveData.TryGetValue("experience_debt", out var debt))
            {
                _experienceDebt = (Dictionary<string, float>)debt;
            }
        }
    }
    
    // Supporting data structures
    [Serializable]
    public class SkillMilestone
    {
        public string Id { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public SkillType SkillCategory { get; set; }
        public int RequiredLevel { get; set; }
        public List<MilestoneReward> Rewards { get; set; } = new();
    }
    
    [Serializable]
    public class MilestoneReward
    {
        public string Type { get; set; } // "skill_points", "recipe_unlock", "prestige_points", etc.
        public string Target { get; set; } // Specific target for the reward
        public float Amount { get; set; }
    }
    
    [Serializable]
    public class PrestigeLevel
    {
        public string Id { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public SkillType SkillCategory { get; set; }
        public int RequiredPrestigePoints { get; set; }
        public List<PrestigeBenefit> Benefits { get; set; } = new();
    }
    
    [Serializable]
    public class PrestigeBenefit
    {
        public string Type { get; set; }
        public float Amount { get; set; }
    }
}