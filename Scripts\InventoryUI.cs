using Godot;
using System.Collections.Generic;
using System.Linq;

namespace SurvivalLooterShooter
{
    /// <summary>
    /// Main inventory UI controller that manages the inventory display and interactions
    /// </summary>
    public partial class InventoryUI : Control
    {
        private Panel _background;
        private GridContainer _inventoryGrid;
        private Button _closeButton;
        private Panel _itemTooltip;
        private RichTextLabel _tooltipLabel;
        
        private Inventory _inventory;
        private Dictionary<string, InventorySlotUI> _slotUIs = new Dictionary<string, InventorySlotUI>();
        
        // Preload the slot UI scene
        private PackedScene _slotUIScene;
        
        private bool _isVisible = false;

        public override void _Ready()
        {
            // Get references to child nodes
            _background = GetNode<Panel>("Background");
            _inventoryGrid = GetNode<GridContainer>("Background/VBoxContainer/ScrollContainer/InventoryGrid");
            _closeButton = GetNode<Button>("Background/CloseButton");
            _itemTooltip = GetNode<Panel>("ItemTooltip");
            _tooltipLabel = GetNode<RichTextLabel>("ItemTooltip/TooltipLabel");
            
            // Load the slot UI scene
            _slotUIScene = GD.Load<PackedScene>("res://Scenes/InventorySlotUI.tscn");
            
            // Initially hide the inventory
            Visible = false;
            _isVisible = false;
            
            // Hide tooltip initially
            _itemTooltip.Visible = false;
        }

        /// <summary>
        /// Initializes the inventory UI with an inventory reference
        /// </summary>
        /// <param name="inventory">The inventory system to display</param>
        public void Initialize(Inventory inventory)
        {
            _inventory = inventory;
            
            if (_inventory != null)
            {
                // Connect to inventory signals (keep for backward compatibility)
                _inventory.InventoryChanged += OnInventoryChanged;
                _inventory.ItemEquipped += OnItemEquipped;
                
                // Connect to EventBus events for more responsive updates
                if (EventBus.Instance != null)
                {
                    EventBus.Instance.ItemAdded += OnEventBusItemAdded;
                    EventBus.Instance.ItemRemoved += OnEventBusItemRemoved;
                    EventBus.Instance.ItemEquipped += OnEventBusItemEquipped;
                }
                
                // Initial update
                UpdateInventoryDisplay();
            }
        }

        /// <summary>
        /// Toggles the inventory visibility
        /// </summary>
        public void ToggleInventory()
        {
            _isVisible = !_isVisible;
            Visible = _isVisible;
            
            if (_isVisible)
            {
                UpdateInventoryDisplay();
            }
            else
            {
                HideTooltip();
            }
        }

        /// <summary>
        /// Shows the inventory
        /// </summary>
        public void ShowInventory()
        {
            _isVisible = true;
            Visible = true;
            UpdateInventoryDisplay();
        }

        /// <summary>
        /// Hides the inventory
        /// </summary>
        public void HideInventory()
        {
            _isVisible = false;
            Visible = false;
            HideTooltip();
        }

        /// <summary>
        /// Updates the entire inventory display
        /// </summary>
        public void UpdateInventoryDisplay()
        {
            if (_inventory == null)
                return;

            // Get all items from inventory
            var allItems = _inventory.GetAllItems();
            
            // Clear existing slot UIs that are no longer needed
            var slotsToRemove = new List<string>();
            foreach (var kvp in _slotUIs)
            {
                if (!allItems.ContainsKey(kvp.Key))
                {
                    slotsToRemove.Add(kvp.Key);
                }
            }
            
            foreach (var slotKey in slotsToRemove)
            {
                _slotUIs[slotKey].QueueFree();
                _slotUIs.Remove(slotKey);
            }

            // Update or create slot UIs for current items
            foreach (var kvp in allItems)
            {
                string slotKey = kvp.Key;
                InventorySlot slot = kvp.Value;
                
                if (_slotUIs.ContainsKey(slotKey))
                {
                    // Update existing slot
                    _slotUIs[slotKey].UpdateSlot(slot, slotKey);
                }
                else
                {
                    // Create new slot UI
                    CreateSlotUI(slotKey, slot);
                }
            }

            // Add empty slots to fill the grid (optional - for visual consistency)
            int currentSlots = _slotUIs.Count;
            int minSlots = 32; // Minimum number of slots to show
            
            for (int i = currentSlots; i < minSlots; i++)
            {
                string emptySlotKey = $"empty_{i}";
                if (!_slotUIs.ContainsKey(emptySlotKey))
                {
                    CreateSlotUI(emptySlotKey, null);
                }
            }
        }

        /// <summary>
        /// Creates a new slot UI for the given slot data
        /// </summary>
        /// <param name="slotKey">The key identifying the slot</param>
        /// <param name="slot">The inventory slot data (can be null for empty slots)</param>
        private void CreateSlotUI(string slotKey, InventorySlot slot)
        {
            var slotUI = _slotUIScene.Instantiate<InventorySlotUI>();
            _inventoryGrid.AddChild(slotUI);
            
            // Connect signals
            slotUI.SlotClicked += OnSlotClicked;
            slotUI.SlotHovered += OnSlotHovered;
            slotUI.SlotUnhovered += OnSlotUnhovered;
            
            // Update the slot
            slotUI.UpdateSlot(slot, slotKey);
            
            // Store reference
            _slotUIs[slotKey] = slotUI;
        }

        /// <summary>
        /// Handles inventory change events
        /// </summary>
        private void OnInventoryChanged(string itemId, int quantityChange)
        {
            // Update the display when inventory changes
            if (_isVisible)
            {
                UpdateInventoryDisplay();
            }
        }

        /// <summary>
        /// Handles item equipped events
        /// </summary>
        private void OnItemEquipped(string slotType, string itemId)
        {
            // Update the display to show equipped status
            if (_isVisible)
            {
                UpdateInventoryDisplay();
            }
        }

        /// <summary>
        /// Handles slot click events
        /// </summary>
        private void OnSlotClicked(string slotKey)
        {
            if (_inventory == null || !_slotUIs.ContainsKey(slotKey))
                return;

            var slotUI = _slotUIs[slotKey];
            var slot = slotUI.GetSlot();
            
            if (slot != null && !slot.IsEmpty)
            {
                // Get item data
                var item = ItemDatabase.Instance?.GetItem(slot.ItemId);
                if (item != null)
                {
                    // Handle different item types
                    switch (item.Type.ToLower())
                    {
                        case "weapon":
                        case "armor":
                        case "tool":
                            // Try to equip the item
                            _inventory.EquipItem(slot.ItemId);
                            break;
                        case "consumable":
                            // TODO: Use consumable item
                            GD.Print($"Using consumable: {item.Name}");
                            break;
                        default:
                            GD.Print($"Clicked on {item.Name}");
                            break;
                    }
                }
            }
        }

        /// <summary>
        /// Handles slot hover events to show tooltips
        /// </summary>
        private void OnSlotHovered(string slotKey, Vector2 globalPosition)
        {
            if (_inventory == null || !_slotUIs.ContainsKey(slotKey))
                return;

            var slotUI = _slotUIs[slotKey];
            var slot = slotUI.GetSlot();
            
            if (slot != null && !slot.IsEmpty)
            {
                ShowTooltip(slot, globalPosition);
            }
        }

        /// <summary>
        /// Handles slot unhover events to hide tooltips
        /// </summary>
        private void OnSlotUnhovered()
        {
            HideTooltip();
        }

        /// <summary>
        /// Shows the item tooltip with metadata and durability information
        /// </summary>
        private void ShowTooltip(InventorySlot slot, Vector2 position)
        {
            var item = ItemDatabase.Instance?.GetItem(slot.ItemId);
            if (item == null)
                return;

            // Build tooltip text
            string tooltipText = $"[b]{item.Name}[/b]\n";
            tooltipText += $"Type: {item.Type}\n";
            tooltipText += $"Quantity: {slot.Quantity}";
            
            if (item.MaxStack > 1)
            {
                tooltipText += $"/{item.MaxStack}";
            }
            tooltipText += "\n";

            // Add metadata information
            if (slot.Metadata.Count > 0 || item.Metadata.Count > 0)
            {
                tooltipText += "\n[b]Properties:[/b]\n";
                
                // Show item base properties
                foreach (var kvp in item.Metadata)
                {
                    string value = kvp.Value?.ToString() ?? "N/A";
                    tooltipText += $"{FormatMetadataKey(kvp.Key)}: {value}\n";
                }
                
                // Show instance-specific metadata (like current durability)
                foreach (var kvp in slot.Metadata)
                {
                    if (!item.Metadata.ContainsKey(kvp.Key))
                    {
                        string value = kvp.Value?.ToString() ?? "N/A";
                        tooltipText += $"{FormatMetadataKey(kvp.Key)}: {value}\n";
                    }
                }
                
                // Special handling for durability
                if (slot.Metadata.ContainsKey("durability") && item.Metadata.ContainsKey("durability"))
                {
                    var currentDurability = slot.GetMetadata<float>("durability", 100f);
                    var maxDurability = item.GetMetadata<float>("durability", 100f);
                    tooltipText += $"Durability: {currentDurability:F0}/{maxDurability:F0}\n";
                }
            }

            // Set tooltip text and position
            _tooltipLabel.Text = tooltipText;
            
            // Position tooltip near cursor but keep it on screen
            Vector2 tooltipSize = new Vector2(200, 100); // Approximate size
            Vector2 screenSize = GetViewportRect().Size;
            
            Vector2 tooltipPos = position + new Vector2(10, 10);
            
            // Keep tooltip on screen
            if (tooltipPos.X + tooltipSize.X > screenSize.X)
                tooltipPos.X = position.X - tooltipSize.X - 10;
            if (tooltipPos.Y + tooltipSize.Y > screenSize.Y)
                tooltipPos.Y = position.Y - tooltipSize.Y - 10;
            
            _itemTooltip.Position = tooltipPos;
            _itemTooltip.Visible = true;
        }

        /// <summary>
        /// Hides the item tooltip
        /// </summary>
        private void HideTooltip()
        {
            _itemTooltip.Visible = false;
        }

        /// <summary>
        /// Formats metadata keys for display
        /// </summary>
        private string FormatMetadataKey(string key)
        {
            return key.Replace("_", " ").ToTitleCase();
        }

        /// <summary>
        /// Handles the close button press
        /// </summary>
        private void _on_close_button_pressed()
        {
            HideInventory();
        }

        /// <summary>
        /// Handles input events for keyboard shortcuts
        /// </summary>
        public override void _Input(InputEvent @event)
        {
            if (@event.IsActionPressed("open_inventory"))
            {
                ToggleInventory();
                GetViewport().SetInputAsHandled();
            }
        }

        /// <summary>
        /// Cleanup when the node is removed
        /// </summary>
        public override void _ExitTree()
        {
            if (_inventory != null)
            {
                _inventory.InventoryChanged -= OnInventoryChanged;
                _inventory.ItemEquipped -= OnItemEquipped;
            }
            
            // Disconnect from EventBus events
            if (EventBus.Instance != null)
            {
                EventBus.Instance.ItemAdded -= OnEventBusItemAdded;
                EventBus.Instance.ItemRemoved -= OnEventBusItemRemoved;
                EventBus.Instance.ItemEquipped -= OnEventBusItemEquipped;
            }
        }

        #region EventBus Event Handlers

        /// <summary>
        /// Handles EventBus item added events
        /// </summary>
        private void OnEventBusItemAdded(string itemId, int quantity)
        {
            if (_isVisible)
            {
                UpdateInventoryDisplay();
            }
        }

        /// <summary>
        /// Handles EventBus item removed events
        /// </summary>
        private void OnEventBusItemRemoved(string itemId, int quantity)
        {
            if (_isVisible)
            {
                UpdateInventoryDisplay();
            }
        }

        /// <summary>
        /// Handles EventBus item equipped events
        /// </summary>
        private void OnEventBusItemEquipped(string slotType, string itemId, string previousItemId)
        {
            if (_isVisible)
            {
                UpdateInventoryDisplay();
            }
        }

        #endregion
    }
}

/// <summary>
/// Extension method for string title case conversion
/// </summary>
public static class StringExtensions
{
    public static string ToTitleCase(this string input)
    {
        if (string.IsNullOrEmpty(input))
            return input;
        
        var words = input.Split(' ');
        for (int i = 0; i < words.Length; i++)
        {
            if (words[i].Length > 0)
            {
                words[i] = char.ToUpper(words[i][0]) + words[i].Substring(1).ToLower();
            }
        }
        return string.Join(" ", words);
    }
}