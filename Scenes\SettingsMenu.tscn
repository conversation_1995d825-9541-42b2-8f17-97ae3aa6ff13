[gd_scene load_steps=2 format=3 uid="uid://burs1ab2njrgl"]

[ext_resource type="Script" uid="uid://f0jippjvajpg" path="res://Scripts/SettingsMenuUI.cs" id="1_abc123"]

[node name="SettingsMenu" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("1_abc123")

[node name="Background" type="ColorRect" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
color = Color(0, 0, 0, 0.8)

[node name="CenterContainer" type="CenterContainer" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2

[node name="Panel" type="Panel" parent="CenterContainer"]
custom_minimum_size = Vector2(600, 500)
layout_mode = 2

[node name="VBoxContainer" type="VBoxContainer" parent="CenterContainer/Panel"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 20.0
offset_top = 20.0
offset_right = -20.0
offset_bottom = -20.0
grow_horizontal = 2
grow_vertical = 2

[node name="Title" type="Label" parent="CenterContainer/Panel/VBoxContainer"]
layout_mode = 2
text = "SETTINGS"
horizontal_alignment = 1

[node name="HSeparator" type="HSeparator" parent="CenterContainer/Panel/VBoxContainer"]
layout_mode = 2

[node name="GraphicsSection" type="VBoxContainer" parent="CenterContainer/Panel/VBoxContainer"]
layout_mode = 2

[node name="GraphicsLabel" type="Label" parent="CenterContainer/Panel/VBoxContainer/GraphicsSection"]
layout_mode = 2
text = "GRAPHICS"

[node name="QualityContainer" type="HBoxContainer" parent="CenterContainer/Panel/VBoxContainer/GraphicsSection"]
layout_mode = 2

[node name="QualityLabel" type="Label" parent="CenterContainer/Panel/VBoxContainer/GraphicsSection/QualityContainer"]
custom_minimum_size = Vector2(150, 0)
layout_mode = 2
text = "Quality:"

[node name="QualityOption" type="OptionButton" parent="CenterContainer/Panel/VBoxContainer/GraphicsSection/QualityContainer"]
layout_mode = 2
size_flags_horizontal = 3

[node name="VsyncContainer" type="HBoxContainer" parent="CenterContainer/Panel/VBoxContainer/GraphicsSection"]
layout_mode = 2

[node name="VsyncLabel" type="Label" parent="CenterContainer/Panel/VBoxContainer/GraphicsSection/VsyncContainer"]
custom_minimum_size = Vector2(150, 0)
layout_mode = 2
text = "VSync:"

[node name="VsyncCheck" type="CheckBox" parent="CenterContainer/Panel/VBoxContainer/GraphicsSection/VsyncContainer"]
layout_mode = 2

[node name="FPSContainer" type="HBoxContainer" parent="CenterContainer/Panel/VBoxContainer/GraphicsSection"]
layout_mode = 2

[node name="FPSLabel" type="Label" parent="CenterContainer/Panel/VBoxContainer/GraphicsSection/FPSContainer"]
custom_minimum_size = Vector2(150, 0)
layout_mode = 2
text = "Target FPS:"

[node name="FPSSpinBox" type="SpinBox" parent="CenterContainer/Panel/VBoxContainer/GraphicsSection/FPSContainer"]
layout_mode = 2
min_value = 30.0
max_value = 144.0
step = 30.0
value = 60.0

[node name="AudioSection" type="VBoxContainer" parent="CenterContainer/Panel/VBoxContainer"]
layout_mode = 2

[node name="AudioLabel" type="Label" parent="CenterContainer/Panel/VBoxContainer/AudioSection"]
layout_mode = 2
text = "AUDIO"

[node name="MasterVolumeContainer" type="HBoxContainer" parent="CenterContainer/Panel/VBoxContainer/AudioSection"]
layout_mode = 2

[node name="MasterVolumeLabel" type="Label" parent="CenterContainer/Panel/VBoxContainer/AudioSection/MasterVolumeContainer"]
custom_minimum_size = Vector2(150, 0)
layout_mode = 2
text = "Master Volume:"

[node name="MasterVolumeSlider" type="HSlider" parent="CenterContainer/Panel/VBoxContainer/AudioSection/MasterVolumeContainer"]
layout_mode = 2
size_flags_horizontal = 3
value = 100.0

[node name="MasterVolumeValue" type="Label" parent="CenterContainer/Panel/VBoxContainer/AudioSection/MasterVolumeContainer"]
custom_minimum_size = Vector2(50, 0)
layout_mode = 2
text = "100%"

[node name="SFXVolumeContainer" type="HBoxContainer" parent="CenterContainer/Panel/VBoxContainer/AudioSection"]
layout_mode = 2

[node name="SFXVolumeLabel" type="Label" parent="CenterContainer/Panel/VBoxContainer/AudioSection/SFXVolumeContainer"]
custom_minimum_size = Vector2(150, 0)
layout_mode = 2
text = "SFX Volume:"

[node name="SFXVolumeSlider" type="HSlider" parent="CenterContainer/Panel/VBoxContainer/AudioSection/SFXVolumeContainer"]
layout_mode = 2
size_flags_horizontal = 3
value = 100.0

[node name="SFXVolumeValue" type="Label" parent="CenterContainer/Panel/VBoxContainer/AudioSection/SFXVolumeContainer"]
custom_minimum_size = Vector2(50, 0)
layout_mode = 2
text = "100%"

[node name="MusicVolumeContainer" type="HBoxContainer" parent="CenterContainer/Panel/VBoxContainer/AudioSection"]
layout_mode = 2

[node name="MusicVolumeLabel" type="Label" parent="CenterContainer/Panel/VBoxContainer/AudioSection/MusicVolumeContainer"]
custom_minimum_size = Vector2(150, 0)
layout_mode = 2
text = "Music Volume:"

[node name="MusicVolumeSlider" type="HSlider" parent="CenterContainer/Panel/VBoxContainer/AudioSection/MusicVolumeContainer"]
layout_mode = 2
size_flags_horizontal = 3
value = 100.0

[node name="MusicVolumeValue" type="Label" parent="CenterContainer/Panel/VBoxContainer/AudioSection/MusicVolumeContainer"]
custom_minimum_size = Vector2(50, 0)
layout_mode = 2
text = "100%"

[node name="ButtonContainer" type="HBoxContainer" parent="CenterContainer/Panel/VBoxContainer"]
layout_mode = 2
alignment = 1

[node name="ResetButton" type="Button" parent="CenterContainer/Panel/VBoxContainer/ButtonContainer"]
layout_mode = 2
text = "Reset to Defaults"

[node name="ApplyButton" type="Button" parent="CenterContainer/Panel/VBoxContainer/ButtonContainer"]
layout_mode = 2
text = "Apply"

[node name="CloseButton" type="Button" parent="CenterContainer/Panel/VBoxContainer/ButtonContainer"]
layout_mode = 2
text = "Close"
