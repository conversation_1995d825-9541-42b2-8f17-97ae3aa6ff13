using Godot;
using System;
using System.Collections.Generic;
using System.Linq;

namespace SurvivalLooterShooter
{
    public partial class QuestManager : Node
{
    public static QuestManager Instance { get; private set; }
    
    [Export] public int MaxActiveQuests { get; set; } = 5;
    [Export] public float QuestGenerationInterval { get; set; } = 300f; // 5 minutes
    
    private List<Quest> _activeQuests = new();
    private List<Quest> _completedQuests = new();
    private List<Quest> _availableQuests = new();
    private Timer _questGenerationTimer;
    private Random _random = new();

    // Quest templates for procedural generation
    private readonly Dictionary<QuestType, List<string>> _questTemplates = new()
    {
        [QuestType.Kill] = new() 
        { 
            "Eliminate {0} {1}",
            "Hunt down {0} {1}",
            "Clear the area of {0} {1}"
        },
        [QuestType.Collect] = new() 
        { 
            "Gather {0} {1}",
            "Collect {0} {1}",
            "Scavenge {0} {1}"
        },
        [QuestType.Craft] = new() 
        { 
            "Craft {0} {1}",
            "Create {0} {1}",
            "Manufacture {0} {1}"
        },
        [QuestType.Explore] = new() 
        { 
            "Explore {0} locations",
            "Discover {0} new areas",
            "Scout {0} points of interest"
        },
        [QuestType.Survive] = new() 
        { 
            "Survive for {0} minutes",
            "Stay alive for {0} minutes",
            "Endure {0} minutes in the wilderness"
        },
        [QuestType.Build] = new() 
        { 
            "Build {0} {1}",
            "Construct {0} {1}",
            "Establish {0} {1}"
        }
    };

    public override void _Ready()
    {
        if (Instance == null)
        {
            Instance = this;
            SetupQuestGeneration();
            ConnectToEventBus();
        }
        else
        {
            QueueFree();
        }
    }

    private void SetupQuestGeneration()
    {
        _questGenerationTimer = new Timer();
        _questGenerationTimer.WaitTime = QuestGenerationInterval;
        _questGenerationTimer.Timeout += GenerateRandomQuest;
        _questGenerationTimer.Autostart = true;
        AddChild(_questGenerationTimer);
    }

    private void ConnectToEventBus()
    {
        if (EventBus.Instance != null)
        {
            EventBus.Instance.EnemyKilled += OnEnemyKilled;
            EventBus.Instance.ItemPickedUp += OnItemPickedUp;
            EventBus.Instance.ItemCrafted += OnItemCrafted;
            EventBus.Instance.POIDiscovered += OnPOIDiscovered;
            EventBus.Instance.StructureBuilt += OnStructureBuilt;
        }
    }

        private void OnItemCrafted(string recipeId, string outputItemId, int outputQuantity)
        {
            throw new NotImplementedException();
        }

        private void OnItemPickedUp(string itemId)
        {
            throw new NotImplementedException();
        }

        private void OnEnemyKilled(string enemyType, bool wasHeadshot)
        {
            throw new NotImplementedException();
        }

        public void GenerateRandomQuest()
    {
        if (_activeQuests.Count >= MaxActiveQuests) return;

        var questType = (QuestType)_random.Next(Enum.GetValues<QuestType>().Length);
        var difficulty = GenerateQuestDifficulty();
        
        var quest = CreateQuestByType(questType, difficulty);
        if (quest != null)
        {
            _availableQuests.Add(quest);
            GD.Print($"New quest available: {quest.Title}");
        }
    }

    private QuestDifficulty GenerateQuestDifficulty()
    {
        // Weight difficulty based on player progression
        var playerLevel = SkillManager.Instance?.GetAverageSkillLevel() ?? 1f;
        
        if (playerLevel < 10) return QuestDifficulty.Easy;
        if (playerLevel < 25) return _random.NextDouble() < 0.7 ? QuestDifficulty.Easy : QuestDifficulty.Medium;
        if (playerLevel < 50) return _random.NextDouble() < 0.5 ? QuestDifficulty.Medium : QuestDifficulty.Hard;
        
        return _random.NextDouble() < 0.3 ? QuestDifficulty.Extreme : QuestDifficulty.Hard;
    }

    private Quest CreateQuestByType(QuestType type, QuestDifficulty difficulty)
    {
        var quest = new Quest();
        quest.QuestId = Guid.NewGuid().ToString();
        quest.Type = type;
        quest.Difficulty = difficulty;

        var difficultyMultiplier = difficulty switch
        {
            QuestDifficulty.Easy => 1f,
            QuestDifficulty.Medium => 2f,
            QuestDifficulty.Hard => 4f,
            QuestDifficulty.Extreme => 8f,
            _ => 1f
        };

        switch (type)
        {
            case QuestType.Kill:
                CreateKillQuest(quest, difficultyMultiplier);
                break;
            case QuestType.Collect:
                CreateCollectQuest(quest, difficultyMultiplier);
                break;
            case QuestType.Craft:
                CreateCraftQuest(quest, difficultyMultiplier);
                break;
            case QuestType.Explore:
                CreateExploreQuest(quest, difficultyMultiplier);
                break;
            case QuestType.Survive:
                CreateSurviveQuest(quest, difficultyMultiplier);
                break;
            case QuestType.Build:
                CreateBuildQuest(quest, difficultyMultiplier);
                break;
        }

        GenerateQuestRewards(quest, difficultyMultiplier);
        return quest;
    }

    private void CreateKillQuest(Quest quest, float multiplier)
    {
        var enemies = new[] { "forest_wolf", "wild_boar", "bandit", "zombie" };
        var enemyType = enemies[_random.Next(enemies.Length)];
        var amount = Math.Max(1, (int)(5 * multiplier));

        quest.Title = string.Format(_questTemplates[QuestType.Kill][_random.Next(_questTemplates[QuestType.Kill].Count)], amount, enemyType);
        quest.Description = $"Eliminate {amount} {enemyType} enemies to complete this quest.";
        
        quest.Objectives.Add(new QuestObjective
        {
            Description = $"Kill {enemyType}",
            TargetId = enemyType,
            RequiredAmount = amount,
            CurrentAmount = 0
        });
    }

    private void CreateCollectQuest(Quest quest, float multiplier)
    {
        var items = new[] { "wood", "stone", "metal_scrap", "cloth", "berries" };
        var itemType = items[_random.Next(items.Length)];
        var amount = Math.Max(1, (int)(10 * multiplier));

        quest.Title = string.Format(_questTemplates[QuestType.Collect][_random.Next(_questTemplates[QuestType.Collect].Count)], amount, itemType);
        quest.Description = $"Collect {amount} {itemType} to complete this quest.";
        
        quest.Objectives.Add(new QuestObjective
        {
            Description = $"Collect {itemType}",
            TargetId = itemType,
            RequiredAmount = amount,
            CurrentAmount = 0
        });
    }

    private void CreateCraftQuest(Quest quest, float multiplier)
    {
        var items = new[] { "bandage", "wooden_spear", "campfire", "storage_box" };
        var itemType = items[_random.Next(items.Length)];
        var amount = Math.Max(1, (int)(3 * multiplier));

        quest.Title = string.Format(_questTemplates[QuestType.Craft][_random.Next(_questTemplates[QuestType.Craft].Count)], amount, itemType);
        quest.Description = $"Craft {amount} {itemType} to complete this quest.";
        
        quest.Objectives.Add(new QuestObjective
        {
            Description = $"Craft {itemType}",
            TargetId = itemType,
            RequiredAmount = amount,
            CurrentAmount = 0
        });
    }

    private void CreateExploreQuest(Quest quest, float multiplier)
    {
        var amount = Math.Max(1, (int)(3 * multiplier));

        quest.Title = string.Format(_questTemplates[QuestType.Explore][_random.Next(_questTemplates[QuestType.Explore].Count)], amount);
        quest.Description = $"Discover {amount} new points of interest to complete this quest.";
        
        quest.Objectives.Add(new QuestObjective
        {
            Description = "Discover POI",
            TargetId = "poi_discovered",
            RequiredAmount = amount,
            CurrentAmount = 0
        });
    }

    private void CreateSurviveQuest(Quest quest, float multiplier)
    {
        var minutes = Math.Max(5, (int)(15 * multiplier));

        quest.Title = string.Format(_questTemplates[QuestType.Survive][_random.Next(_questTemplates[QuestType.Survive].Count)], minutes);
        quest.Description = $"Stay alive for {minutes} minutes to complete this quest.";
        quest.TimeLimit = minutes * 60f; // Convert to seconds
        
        quest.Objectives.Add(new QuestObjective
        {
            Description = $"Survive {minutes} minutes",
            TargetId = "survive_time",
            RequiredAmount = 1,
            CurrentAmount = 0
        });
    }

    private void CreateBuildQuest(Quest quest, float multiplier)
    {
        var structures = new[] { "wooden_wall", "storage_container", "crafting_station", "defensive_turret" };
        var structureType = structures[_random.Next(structures.Length)];
        var amount = Math.Max(1, (int)(2 * multiplier));

        quest.Title = string.Format(_questTemplates[QuestType.Build][_random.Next(_questTemplates[QuestType.Build].Count)], amount, structureType);
        quest.Description = $"Build {amount} {structureType} structures to complete this quest.";
        
        quest.Objectives.Add(new QuestObjective
        {
            Description = $"Build {structureType}",
            TargetId = structureType,
            RequiredAmount = amount,
            CurrentAmount = 0
        });
    }

    private void GenerateQuestRewards(Quest quest, float multiplier)
    {
        var baseXP = 100 * multiplier;
        quest.Rewards.Add(new QuestReward
        {
            ExperiencePoints = (int)baseXP
        });

        // Add item rewards based on quest type
        var rewardItems = quest.Type switch
        {
            QuestType.Kill => new[] { "ammo", "weapon_parts", "medical_supplies" },
            QuestType.Collect => new[] { "rare_materials", "tools", "blueprints" },
            QuestType.Craft => new[] { "advanced_materials", "skill_books", "recipes" },
            QuestType.Explore => new[] { "maps", "treasure", "rare_items" },
            QuestType.Survive => new[] { "survival_gear", "food", "medicine" },
            QuestType.Build => new[] { "building_materials", "blueprints", "tools" },
            _ => new[] { "misc_reward" }
        };

        var rewardItem = rewardItems[_random.Next(rewardItems.Length)];
        var rewardAmount = Math.Max(1, (int)(3 * multiplier));

        quest.Rewards.Add(new QuestReward
        {
            ItemId = rewardItem,
            Amount = rewardAmount
        });
    }

    public void AcceptQuest(Quest quest)
    {
        if (_activeQuests.Count >= MaxActiveQuests) return;
        if (!_availableQuests.Contains(quest)) return;

        _availableQuests.Remove(quest);
        _activeQuests.Add(quest);
        quest.StartQuest();
    }

    public void AbandonQuest(Quest quest)
    {
        if (_activeQuests.Contains(quest))
        {
            _activeQuests.Remove(quest);
            quest.IsActive = false;
        }
    }

    // Event handlers for quest progress tracking
    private void OnEnemyKilled(Enemy enemy)
    {
        foreach (var quest in _activeQuests.Where(q => q.Type == QuestType.Kill))
        {
            quest.UpdateObjective(enemy.EnemyType);
        }
    }

    private void OnItemPickedUp(string itemId, int amount)
    {
        foreach (var quest in _activeQuests.Where(q => q.Type == QuestType.Collect))
        {
            quest.UpdateObjective(itemId, amount);
        }
    }

    private void OnItemCrafted(string itemId, int amount)
    {
        foreach (var quest in _activeQuests.Where(q => q.Type == QuestType.Craft))
        {
            quest.UpdateObjective(itemId, amount);
        }
    }

    private void OnPOIDiscovered(string poiId)
    {
        foreach (var quest in _activeQuests.Where(q => q.Type == QuestType.Explore))
        {
            quest.UpdateObjective("poi_discovered");
        }
    }

    private void OnStructureBuilt(string structureType)
    {
        foreach (var quest in _activeQuests.Where(q => q.Type == QuestType.Build))
        {
            quest.UpdateObjective(structureType);
        }
    }

    public override void _Process(double delta)
    {
        // Update survive quests
        foreach (var quest in _activeQuests.Where(q => q.Type == QuestType.Survive && q.IsActive))
        {
            if (quest.RemainingTime <= 0 && quest.TimeLimit > 0)
            {
                quest.UpdateObjective("survive_time");
            }
        }

        // Check for expired quests
        var expiredQuests = _activeQuests.Where(q => q.IsExpired).ToList();
        foreach (var quest in expiredQuests)
        {
            quest.CheckCompletion();
            _activeQuests.Remove(quest);
        }
    }

    public List<Quest> GetActiveQuests() => new(_activeQuests);
    public List<Quest> GetAvailableQuests() => new(_availableQuests);
    public List<Quest> GetCompletedQuests() => new(_completedQuests);
    }
}