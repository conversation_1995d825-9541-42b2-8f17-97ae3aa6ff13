using Godot;
using System;
using System.Collections.Generic;
using System.IO;

namespace SurvivalLooterShooter
{
    /// <summary>
    /// Integration tests for error handling and data validation across all systems
    /// </summary>
    public partial class ErrorHandlingIntegrationTests : Node
    {
        private int _testsRun = 0;
        private int _testsPassed = 0;
        private int _testsFailed = 0;

        public override void _Ready()
        {
            Logger.LogInfo("ErrorHandlingTests", "Starting error handling integration tests");
            
            // Wait a frame to ensure all systems are initialized
            CallDeferred(nameof(RunTests));
        }

        private void RunTests()
        {
            try
            {
                // Test data loading error recovery
                TestCorruptedDataRecovery();
                
                // Test inventory error handling
                TestInventoryErrorHandling();
                
                // Test save/load error handling
                TestSaveLoadErrorHandling();
                
                // Test system integration with errors
                TestSystemIntegrationErrors();
                
                // Test fallback mechanisms
                TestFallbackMechanisms();
                
                // Print results
                Logger.LogInfo("ErrorHandlingTests", $"Integration tests completed - Run: {_testsRun}, Passed: {_testsPassed}, Failed: {_testsFailed}");
                
                if (_testsFailed > 0)
                {
                    Logger.LogError("ErrorHandlingTests", $"{_testsFailed} integration tests failed!");
                }
                else
                {
                    Logger.LogInfo("ErrorHandlingTests", "All integration tests passed!");
                }
            }
            catch (Exception ex)
            {
                Logger.LogException("ErrorHandlingTests", ex, "RunTests");
            }
        }

        private void TestCorruptedDataRecovery()
        {
            Logger.LogInfo("ErrorHandlingTests", "Testing corrupted data recovery");

            // Test with corrupted JSON files
            TestCorruptedItemsFile();
            TestCorruptedRecipesFile();
            TestMissingDataFiles();
        }

        private void TestCorruptedItemsFile()
        {
            // Create a temporary corrupted items file
            string tempPath = "user://temp_corrupted_items.json";
            string corruptedJson = "{\"items\": [invalid json}";
            
            try
            {
                var file = Godot.FileAccess.Open(tempPath, Godot.FileAccess.ModeFlags.Write);
                if (file != null)
                {
                    file.StoreString(corruptedJson);
                    file.Close();
                }

                // Test JSON validation
                bool isValid = DataValidator.ValidateJsonContent(corruptedJson, out string error);
                AssertFalse("Corrupted JSON should fail validation", isValid);
                AssertFalse("Corrupted JSON should have error message", string.IsNullOrEmpty(error));

                // Clean up
                if (Godot.FileAccess.FileExists(tempPath))
                {
                    DirAccess.RemoveAbsolute(tempPath);
                }
            }
            catch (Exception ex)
            {
                Logger.LogException("ErrorHandlingTests", ex, "TestCorruptedItemsFile");
            }
        }

        private void TestCorruptedRecipesFile()
        {
            // Test recipe validation with missing required fields
            var invalidRecipe = new Recipe();
            invalidRecipe.Id = ""; // Invalid empty ID
            invalidRecipe.Inputs = null; // Invalid null inputs
            
            bool isValid = DataValidator.ValidateRecipe(invalidRecipe, out List<string> errors);
            AssertFalse("Recipe with empty ID and null inputs should fail validation", isValid);
            AssertTrue("Invalid recipe should have errors", errors.Count > 0);
        }

        private void TestMissingDataFiles()
        {
            // Test behavior when data files are missing
            // This would normally be handled by the ItemDatabase fallback system
            var fallbackItems = DataValidator.CreateFallbackItems();
            AssertTrue("Should create fallback items when data is missing", fallbackItems.Count > 0);
            
            var fallbackRecipes = DataValidator.CreateFallbackRecipes();
            AssertTrue("Should create fallback recipes when data is missing", fallbackRecipes.Count > 0);
        }

        private void TestInventoryErrorHandling()
        {
            Logger.LogInfo("ErrorHandlingTests", "Testing inventory error handling");

            var inventory = new Inventory();

            // Test adding invalid items
            bool result = inventory.AddItem("", 5);
            AssertFalse("Adding item with empty ID should fail", result);

            result = inventory.AddItem("valid_item", -5);
            AssertFalse("Adding negative quantity should fail", result);

            result = inventory.AddItem("nonexistent_item", 1);
            AssertFalse("Adding non-existent item should fail", result);

            // Test removing invalid items
            result = inventory.RemoveItem("", 1);
            AssertFalse("Removing item with empty ID should fail", result);

            result = inventory.RemoveItem("valid_item", -1);
            AssertFalse("Removing negative quantity should fail", result);

            result = inventory.RemoveItem("nonexistent_item", 1);
            AssertFalse("Removing non-existent item should fail", result);

            // Test removing more items than available
            inventory.AddItem("bandage", 3);
            result = inventory.RemoveItem("bandage", 5);
            AssertFalse("Removing more items than available should fail", result);

            // Verify inventory state is still consistent
            int quantity = inventory.GetItemQuantity("bandage");
            AssertTrue("Inventory should still have original quantity after failed removal", quantity == 3);
        }

        private void TestSaveLoadErrorHandling()
        {
            Logger.LogInfo("ErrorHandlingTests", "Testing save/load error handling");

            // Test save data validation
            var invalidSaveData = new GameSaveData();
            invalidSaveData.InventoryItems = null;
            invalidSaveData.LastSaveTime = default(DateTime);
            
            bool isValid = DataValidator.ValidateSaveData(invalidSaveData, out List<string> errors);
            
            // Should be corrected, not failed
            AssertTrue("Save data validation should correct null collections", invalidSaveData.InventoryItems != null);
            AssertTrue("Save data validation should correct invalid timestamp", invalidSaveData.LastSaveTime != default(DateTime));

            // Test corrupted save file content
            string corruptedSaveJson = "{\"inventoryItems\": invalid}";
            isValid = DataValidator.ValidateJsonContent(corruptedSaveJson, out string jsonError);
            AssertFalse("Corrupted save JSON should fail validation", isValid);
        }

        private void TestSystemIntegrationErrors()
        {
            Logger.LogInfo("ErrorHandlingTests", "Testing system integration error handling");

            // Test crafting with missing items
            var craftingSystem = new CraftingSystem();
            var inventory = new Inventory();
            craftingSystem.SetInventory(inventory);

            // Try to craft without required materials
            var recipe = ItemDatabase.Instance?.GetRecipe("craft_bandage");
            if (recipe != null)
            {
                bool canCraft = craftingSystem.CanCraft(recipe);
                AssertFalse("Should not be able to craft without materials", canCraft);

                bool craftResult = craftingSystem.CraftItem(recipe);
                AssertFalse("Crafting should fail without materials", craftResult);
            }

            // Test weapon system with invalid items
            var weaponController = new WeaponController();
            weaponController.Initialize(inventory);

            // Try to equip non-existent weapon
            bool equipResult = inventory.EquipItem("nonexistent_weapon", "weapon");
            AssertFalse("Should not be able to equip non-existent weapon", equipResult);
        }

        private void TestFallbackMechanisms()
        {
            Logger.LogInfo("ErrorHandlingTests", "Testing fallback mechanisms");

            // Test item validation with auto-correction
            var itemMissingName = new Item("test_item", "", "consumable", 5);
            bool isValid = DataValidator.ValidateItem(itemMissingName, out List<string> errors);
            
            AssertFalse("Item name should be auto-corrected", string.IsNullOrEmpty(itemMissingName.Name));

            // Test item with invalid stack size
            var itemInvalidStack = new Item("test_item_2", "Test Item", "material", -1);
            isValid = DataValidator.ValidateItem(itemInvalidStack, out errors);
            
            AssertTrue("Invalid stack size should be corrected", itemInvalidStack.MaxStack > 0);

            // Test weapon without required metadata
            var weaponNoMetadata = new Item("test_weapon", "Test Weapon", "weapon", 1);
            isValid = DataValidator.ValidateItem(weaponNoMetadata, out errors);
            
            AssertTrue("Weapon should have damage metadata added", weaponNoMetadata.Metadata.ContainsKey("damage"));
            AssertTrue("Weapon should have ammo_type metadata added", weaponNoMetadata.Metadata.ContainsKey("ammo_type"));

            // Test recipe with invalid crafting time
            var recipeInvalidTime = new Recipe("test_recipe", 
                new List<RecipeInput> { new RecipeInput("input_item", 1) },
                new RecipeOutput("output_item", 1),
                -1.0f);
            
            isValid = DataValidator.ValidateRecipe(recipeInvalidTime, out errors);
            AssertTrue("Invalid crafting time should be corrected", recipeInvalidTime.CraftingTime > 0);
        }

        // Test assertion helpers
        private void AssertTrue(string message, bool condition)
        {
            _testsRun++;
            if (condition)
            {
                _testsPassed++;
                Logger.LogDebug("ErrorHandlingTests", $"✓ {message}");
            }
            else
            {
                _testsFailed++;
                Logger.LogError("ErrorHandlingTests", $"✗ {message}");
            }
        }

        private void AssertFalse(string message, bool condition)
        {
            AssertTrue(message, !condition);
        }
    }
}