using Godot;
using System;
using System.Collections.Generic;

namespace SurvivalLooterShooter
{
    public partial class TutorialSystem : Node
{
    private static TutorialSystem _instance;
    public static TutorialSystem Instance => _instance;

    [Signal]
    public delegate void TutorialStepCompletedEventHandler(string stepId);

    [Signal]
    public delegate void TutorialCompletedEventHandler();

    private Dictionary<string, TutorialStep> _tutorialSteps = new Dictionary<string, TutorialStep>();
    private Queue<string> _activeSteps = new Queue<string>();
    private string _currentStepId = null;
    private bool _tutorialActive = false;
    private bool _tutorialCompleted = false;
    private DateTime _tutorialStartTime;

    // UI Elements
    private Control _tutorialUI;
    private Label _instructionLabel;
    private Label _progressLabel;
    private Button _skipButton;
    private Button _nextButton;
    private AnimationPlayer _highlightAnimation;

    private const string TUTORIAL_PROGRESS_FILE = "user://tutorial_progress.cfg";

    public class TutorialStep
    {
        public string Id { get; set; }
        public string Title { get; set; }
        public string Instruction { get; set; }
        public string TargetNodePath { get; set; }
        public TutorialTrigger Trigger { get; set; }
        public Dictionary<string, object> TriggerData { get; set; }
        public bool IsCompleted { get; set; }
        public bool IsOptional { get; set; }
        public Action OnComplete { get; set; }

        public TutorialStep()
        {
            TriggerData = new Dictionary<string, object>();
        }
    }

    public enum TutorialTrigger
    {
        Manual,           // Player clicks next
        KeyPress,         // Specific key pressed
        UIInteraction,    // UI element clicked
        GameEvent,        // Game event occurred
        Timer,            // Time elapsed
        PlayerAction      // Specific player action
    }

    public override void _Ready()
    {
        _instance = this;
        SetupTutorialUI();
        InitializeTutorialSteps();
        LoadTutorialProgress();

        // Connect to game events
        ConnectToGameEvents();
    }

    private void SetupTutorialUI()
    {
        // Create tutorial UI overlay
        _tutorialUI = new Control();
        _tutorialUI.SetAnchorsAndOffsetsPreset(Control.LayoutPreset.FullRect);
        _tutorialUI.Visible = false;
        AddChild(_tutorialUI);

        // Background overlay
        var background = new ColorRect();
        background.SetAnchorsAndOffsetsPreset(Control.LayoutPreset.FullRect);
        background.Color = new Color(0, 0, 0, 0.5f);
        _tutorialUI.AddChild(background);

        // Tutorial panel
        var panel = new Panel();
        panel.SetAnchorsAndOffsetsPreset(Control.LayoutPreset.CenterBottom);
        panel.Size = new Vector2(800, 200);
        panel.Position = new Vector2(-400, -220);
        _tutorialUI.AddChild(panel);

        // Instruction text
        _instructionLabel = new Label();
        _instructionLabel.SetAnchorsAndOffsetsPreset(Control.LayoutPreset.FullRect);
        _instructionLabel.AutowrapMode = TextServer.AutowrapMode.WordSmart;
        _instructionLabel.VerticalAlignment = VerticalAlignment.Center;
        _instructionLabel.HorizontalAlignment = HorizontalAlignment.Center;
        _instructionLabel.AddThemeStyleboxOverride("normal", new StyleBoxFlat());
        panel.AddChild(_instructionLabel);

        // Progress label
        _progressLabel = new Label();
        _progressLabel.SetAnchorsAndOffsetsPreset(Control.LayoutPreset.TopRight);
        _progressLabel.Position = new Vector2(-100, 10);
        _progressLabel.Size = new Vector2(90, 30);
        _progressLabel.HorizontalAlignment = HorizontalAlignment.Right;
        panel.AddChild(_progressLabel);

        // Buttons
        var buttonContainer = new HBoxContainer();
        buttonContainer.SetAnchorsAndOffsetsPreset(Control.LayoutPreset.BottomRight);
        buttonContainer.Position = new Vector2(-200, -40);
        buttonContainer.Size = new Vector2(180, 30);
        panel.AddChild(buttonContainer);

        _skipButton = new Button();
        _skipButton.Text = "Skip Tutorial";
        _skipButton.Pressed += OnSkipPressed;
        buttonContainer.AddChild(_skipButton);

        _nextButton = new Button();
        _nextButton.Text = "Next";
        _nextButton.Pressed += OnNextPressed;
        buttonContainer.AddChild(_nextButton);

        // Highlight animation
        _highlightAnimation = new AnimationPlayer();
        _tutorialUI.AddChild(_highlightAnimation);
    }

    private void InitializeTutorialSteps()
    {
        // Movement tutorial
        AddTutorialStep(new TutorialStep
        {
            Id = "movement",
            Title = "Movement",
            Instruction = "Use WASD keys to move your character around. Try moving in different directions.",
            Trigger = TutorialTrigger.KeyPress,
            TriggerData = { ["keys"] = new[] { "w", "a", "s", "d" } }
        });

        // Inventory tutorial
        AddTutorialStep(new TutorialStep
        {
            Id = "open_inventory",
            Title = "Inventory",
            Instruction = "Press 'I' to open your inventory. This is where you'll manage your items and equipment.",
            Trigger = TutorialTrigger.KeyPress,
            TriggerData = { ["key"] = "i" }
        });

        // Item pickup tutorial
        AddTutorialStep(new TutorialStep
        {
            Id = "pickup_item",
            Title = "Item Pickup",
            Instruction = "Walk over to the item on the ground and press 'E' to pick it up.",
            Trigger = TutorialTrigger.GameEvent,
            TriggerData = { ["event"] = "item_picked_up" }
        });

        // Crafting tutorial
        AddTutorialStep(new TutorialStep
        {
            Id = "open_crafting",
            Title = "Crafting",
            Instruction = "Press 'C' to open the crafting menu. Here you can create new items from materials.",
            Trigger = TutorialTrigger.KeyPress,
            TriggerData = { ["key"] = "c" }
        });

        // First craft tutorial
        AddTutorialStep(new TutorialStep
        {
            Id = "first_craft",
            Title = "First Craft",
            Instruction = "Craft a bandage using cloth and alcohol. Click on the recipe and then 'Craft'.",
            Trigger = TutorialTrigger.GameEvent,
            TriggerData = { ["event"] = "item_crafted", ["item"] = "bandage" }
        });

        // Combat tutorial
        AddTutorialStep(new TutorialStep
        {
            Id = "equip_weapon",
            Title = "Equip Weapon",
            Instruction = "Right-click on a weapon in your inventory to equip it.",
            Trigger = TutorialTrigger.GameEvent,
            TriggerData = { ["event"] = "weapon_equipped" }
        });

        // Shooting tutorial
        AddTutorialStep(new TutorialStep
        {
            Id = "shoot_weapon",
            Title = "Combat",
            Instruction = "Left-click to fire your weapon. Aim at the target dummy to practice.",
            Trigger = TutorialTrigger.GameEvent,
            TriggerData = { ["event"] = "weapon_fired" }
        });

        // Reload tutorial
        AddTutorialStep(new TutorialStep
        {
            Id = "reload_weapon",
            Title = "Reloading",
            Instruction = "Press 'R' to reload your weapon when you run out of ammo.",
            Trigger = TutorialTrigger.KeyPress,
            TriggerData = { ["key"] = "r" }
        });

        // Survival stats tutorial
        AddTutorialStep(new TutorialStep
        {
            Id = "survival_stats",
            Title = "Survival Stats",
            Instruction = "Watch your health, hunger, thirst, and stamina bars in the top-left corner. Keep them high to survive!",
            Trigger = TutorialTrigger.Timer,
            TriggerData = { ["duration"] = 5.0f }
        });

        // Building tutorial
        AddTutorialStep(new TutorialStep
        {
            Id = "building_basics",
            Title = "Building",
            Instruction = "Press 'B' to open the building menu. You can construct structures to create your base.",
            Trigger = TutorialTrigger.KeyPress,
            TriggerData = { ["key"] = "b" },
            IsOptional = true
        });

        // Tutorial completion
        AddTutorialStep(new TutorialStep
        {
            Id = "tutorial_complete",
            Title = "Tutorial Complete!",
            Instruction = "Congratulations! You've learned the basics. Now go explore and survive!",
            Trigger = TutorialTrigger.Manual
        });
    }

    private void AddTutorialStep(TutorialStep step)
    {
        _tutorialSteps[step.Id] = step;
    }

    public void StartTutorial()
    {
        if (_tutorialCompleted)
        {
            GD.Print("Tutorial already completed");
            return;
        }

        _tutorialActive = true;
        _tutorialStartTime = DateTime.Now;
        _tutorialUI.Visible = true;

        // Queue all tutorial steps
        foreach (var step in _tutorialSteps.Values)
        {
            if (!step.IsCompleted)
            {
                _activeSteps.Enqueue(step.Id);
            }
        }

        StartNextStep();
        AudioManager.Instance?.PlaySFX("tutorial_start");
    }

    private void StartNextStep()
    {
        if (_activeSteps.Count == 0)
        {
            CompleteTutorial();
            return;
        }

        _currentStepId = _activeSteps.Dequeue();
        var step = _tutorialSteps[_currentStepId];

        DisplayStep(step);
        SetupStepTrigger(step);

        GD.Print($"Tutorial step started: {step.Title}");
    }

    private void DisplayStep(TutorialStep step)
    {
        _instructionLabel.Text = $"{step.Title}\n\n{step.Instruction}";

        var completedSteps = 0;
        foreach (var s in _tutorialSteps.Values)
        {
            if (s.IsCompleted) completedSteps++;
        }

        _progressLabel.Text = $"{completedSteps + 1}/{_tutorialSteps.Count}";

        // Show/hide next button based on trigger type
        _nextButton.Visible = step.Trigger == TutorialTrigger.Manual || step.Trigger == TutorialTrigger.Timer;

        // Highlight target if specified
        if (!string.IsNullOrEmpty(step.TargetNodePath))
        {
            HighlightTarget(step.TargetNodePath);
        }
    }

    private void SetupStepTrigger(TutorialStep step)
    {
        switch (step.Trigger)
        {
            case TutorialTrigger.Timer:
                var duration = step.TriggerData.ContainsKey("duration") ?
                    Convert.ToSingle(step.TriggerData["duration"]) : 3.0f;
                GetTree().CreateTimer(duration).Timeout += () => CompleteCurrentStep();
                break;

            case TutorialTrigger.Manual:
                // Next button handles this
                break;

                // Other triggers are handled by game events
        }
    }

    private void HighlightTarget(string nodePath)
    {
        var targetNode = GetTree().CurrentScene.GetNode(nodePath);
        if (targetNode is Control control)
        {
            // Create highlight effect
            var highlight = new ColorRect();
            highlight.Color = new Color(1, 1, 0, 0.3f);
            highlight.SetAnchorsAndOffsetsPreset(Control.LayoutPreset.FullRect);
            control.AddChild(highlight);

            // Animate highlight
            var tween = CreateTween();
            tween.SetLoops();
            tween.TweenProperty(highlight, "modulate:a", 0.1f, 0.5f);
            tween.TweenProperty(highlight, "modulate:a", 0.3f, 0.5f);
        }
    }

    private void ConnectToGameEvents()
    {
        if (EventBus.Instance != null)
        {
            EventBus.Instance.ItemPickedUp += OnItemPickedUp;
            EventBus.Instance.ItemCrafted += OnItemCrafted;
            EventBus.Instance.WeaponEquipped += OnWeaponEquipped;
            EventBus.Instance.WeaponFired += OnWeaponFired;
        }
    }

        private void OnWeaponFired(string weaponId, string weaponName, float damage, int remainingAmmo)
        {
            throw new NotImplementedException();
        }

        private void OnItemCrafted(string recipeId, string outputItemId, int outputQuantity)
        {
            throw new NotImplementedException();
        }

        public override void _Input(InputEvent @event)
    {
        if (!_tutorialActive || _currentStepId == null) return;

        var currentStep = _tutorialSteps[_currentStepId];

        if (currentStep.Trigger == TutorialTrigger.KeyPress && @event is InputEventKey keyEvent && keyEvent.Pressed)
        {
            var targetKey = currentStep.TriggerData.ContainsKey("key") ?
                currentStep.TriggerData["key"].ToString().ToLower() : "";
            var targetKeys = currentStep.TriggerData.ContainsKey("keys") ?
                currentStep.TriggerData["keys"] as string[] : null;

            var pressedKey = keyEvent.AsTextKeycode().ToLower();

            if (pressedKey == targetKey || (targetKeys != null && Array.Exists(targetKeys, k => k == pressedKey)))
            {
                CompleteCurrentStep();
            }
        }
    }

    private void OnItemPickedUp(string itemId)
    {
        CheckGameEventTrigger("item_picked_up", itemId);
    }

    private void OnItemCrafted(string itemId, string itemType)
    {
        CheckGameEventTrigger("item_crafted", itemId);
    }

    private void OnWeaponEquipped(string weaponId)
    {
        CheckGameEventTrigger("weapon_equipped", weaponId);
    }

    private void OnWeaponFired(string weaponId)
    {
        CheckGameEventTrigger("weapon_fired", weaponId);
    }

    private void CheckGameEventTrigger(string eventName, string data = null)
    {
        if (!_tutorialActive || _currentStepId == null) return;

        var currentStep = _tutorialSteps[_currentStepId];

        if (currentStep.Trigger == TutorialTrigger.GameEvent)
        {
            var targetEvent = currentStep.TriggerData.ContainsKey("event") ?
                currentStep.TriggerData["event"].ToString() : "";
            var targetItem = currentStep.TriggerData.ContainsKey("item") ?
                currentStep.TriggerData["item"].ToString() : "";

            if (eventName == targetEvent && (string.IsNullOrEmpty(targetItem) || data == targetItem))
            {
                CompleteCurrentStep();
            }
        }
    }

    private void CompleteCurrentStep()
    {
        if (_currentStepId == null) return;

        var step = _tutorialSteps[_currentStepId];
        step.IsCompleted = true;
        step.OnComplete?.Invoke();

        EmitSignal(SignalName.TutorialStepCompleted, _currentStepId);
        AudioManager.Instance?.PlaySFX("tutorial_step_complete");

        SaveTutorialProgress();
        StartNextStep();
    }

    private void CompleteTutorial()
    {
        _tutorialActive = false;
        _tutorialCompleted = true;
        _tutorialUI.Visible = false;

        var tutorialTime = (float)(DateTime.Now - _tutorialStartTime).TotalSeconds;
        PlayerStatistics.Instance?.SetStat("tutorial_time", tutorialTime);

        EmitSignal(SignalName.TutorialCompleted);
        AudioManager.Instance?.PlaySFX("tutorial_complete");

        // Show completion message
        var dialog = new AcceptDialog();
        dialog.Title = "Tutorial Complete!";
        dialog.DialogText = $"Great job! You completed the tutorial in {tutorialTime:F1} seconds.\nNow you're ready to survive on your own!";
        dialog.PopupCentered();
        GetTree().CurrentScene.AddChild(dialog);

        SaveTutorialProgress();
        GD.Print("Tutorial completed successfully!");
    }

    private void OnSkipPressed()
    {
        var confirmDialog = new ConfirmationDialog();
        confirmDialog.DialogText = "Are you sure you want to skip the tutorial?";
        confirmDialog.Confirmed += () =>
        {
            _tutorialCompleted = true;
            CompleteTutorial();
        };
        confirmDialog.PopupCentered();
        GetTree().CurrentScene.AddChild(confirmDialog);
    }

    private void OnNextPressed()
    {
        CompleteCurrentStep();
    }

    public void RestartTutorial()
    {
        foreach (var step in _tutorialSteps.Values)
        {
            step.IsCompleted = false;
        }
        _tutorialCompleted = false;
        _activeSteps.Clear();
        SaveTutorialProgress();
        StartTutorial();
    }

    private void SaveTutorialProgress()
    {
        var config = new ConfigFile();

        config.SetValue("tutorial", "completed", _tutorialCompleted);

        foreach (var kvp in _tutorialSteps)
        {
            config.SetValue("steps", kvp.Key, kvp.Value.IsCompleted);
        }

        config.Save(TUTORIAL_PROGRESS_FILE);
    }

    private void LoadTutorialProgress()
    {
        var config = new ConfigFile();
        var error = config.Load(TUTORIAL_PROGRESS_FILE);

        if (error != Error.Ok)
        {
            GD.Print("No tutorial progress found, starting fresh");
            return;
        }

        _tutorialCompleted = config.GetValue("tutorial", "completed", false).AsBool();

        foreach (var stepId in _tutorialSteps.Keys)
        {
            _tutorialSteps[stepId].IsCompleted = config.GetValue("steps", stepId, false).AsBool();
        }
    }

    public bool IsTutorialCompleted() => _tutorialCompleted;
    public bool IsTutorialActive() => _tutorialActive;
    }
}