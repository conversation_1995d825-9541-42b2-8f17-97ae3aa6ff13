using Godot;

namespace SurvivalLooterShooter
{
    /// <summary>
    /// Simple test runner for save/load functionality
    /// </summary>
    public partial class SaveLoadTestRunner : Node
    {
        public override void _Ready()
        {
            GD.Print("=== Save/Load System Test ===");
            
            // Wait a frame for systems to initialize
            CallDeferred(nameof(RunTests));
        }

        private void RunTests()
        {
            try
            {
                // Test GameSaveData creation
                var saveData = new GameSaveData();
                saveData.InventoryItems["test"] = new InventorySlotData
                {
                    ItemId = "bandage",
                    Quantity = 5
                };
                
                GD.Print("✓ GameSaveData creation test passed");
                
                // Test InventorySlotData conversion
                var slot = new InventorySlot("cloth", 10);
                var slotData = new InventorySlotData(slot);
                var convertedSlot = slotData.ToInventorySlot();
                
                if (convertedSlot.ItemId == "cloth" && convertedSlot.Quantity == 10)
                {
                    GD.Print("✓ InventorySlotData conversion test passed");
                }
                else
                {
                    GD.PrintErr("✗ InventorySlotData conversion test failed");
                }
                
                // Test SaveManager creation
                var saveManager = new SaveManager();
                AddChild(saveManager);
                
                GD.Print("✓ SaveManager creation test passed");
                
                GD.Print("=== Save/Load System Tests Complete ===");
                GD.Print("Save/Load system is ready for use!");
                GD.Print("Press F5 to save, F9 to load");
                
                // Clean up test node
                saveManager.QueueFree();
            }
            catch (System.Exception ex)
            {
                GD.PrintErr($"✗ Save/Load test failed: {ex.Message}");
            }
        }
    }
}