[gd_scene load_steps=3 format=3 uid="uid://b8dad0e1f2g3h"]

[ext_resource type="Script" uid="uid://dwc3k3rl3s3ms" path="res://Scripts/CraftingUI.cs" id="1_4d5e6"]

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_1"]
bg_color = Color(0.2, 0.2, 0.2, 0.9)
border_width_left = 2
border_width_top = 2
border_width_right = 2
border_width_bottom = 2
border_color = Color(0.5, 0.5, 0.5, 1)
corner_radius_top_left = 5
corner_radius_top_right = 5
corner_radius_bottom_right = 5
corner_radius_bottom_left = 5

[node name="CraftingUI" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2
script = ExtResource("1_4d5e6")

[node name="Background" type="Panel" parent="."]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -500.0
offset_top = -350.0
offset_right = 500.0
offset_bottom = 350.0
grow_horizontal = 2
grow_vertical = 2
theme_override_styles/panel = SubResource("StyleBoxFlat_1")

[node name="VBoxContainer" type="VBoxContainer" parent="Background"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 10.0
offset_top = 10.0
offset_right = -10.0
offset_bottom = -10.0
grow_horizontal = 2
grow_vertical = 2

[node name="TitleLabel" type="Label" parent="Background/VBoxContainer"]
layout_mode = 2
text = "Crafting"
horizontal_alignment = 1
vertical_alignment = 1

[node name="HSeparator" type="HSeparator" parent="Background/VBoxContainer"]
layout_mode = 2

[node name="MainContainer" type="HSplitContainer" parent="Background/VBoxContainer"]
layout_mode = 2
size_flags_vertical = 3

[node name="RecipeList" type="VBoxContainer" parent="Background/VBoxContainer/MainContainer"]
layout_mode = 2
size_flags_horizontal = 3

[node name="RecipeListLabel" type="Label" parent="Background/VBoxContainer/MainContainer/RecipeList"]
layout_mode = 2
text = "Available Recipes"
horizontal_alignment = 1

[node name="RecipeScrollContainer" type="ScrollContainer" parent="Background/VBoxContainer/MainContainer/RecipeList"]
layout_mode = 2
size_flags_vertical = 3

[node name="RecipeContainer" type="VBoxContainer" parent="Background/VBoxContainer/MainContainer/RecipeList/RecipeScrollContainer"]
layout_mode = 2
size_flags_horizontal = 3

[node name="RecipeDetails" type="VBoxContainer" parent="Background/VBoxContainer/MainContainer"]
layout_mode = 2
size_flags_horizontal = 3

[node name="RecipeDetailsLabel" type="Label" parent="Background/VBoxContainer/MainContainer/RecipeDetails"]
layout_mode = 2
text = "Recipe Details"
horizontal_alignment = 1

[node name="SelectedRecipePanel" type="Panel" parent="Background/VBoxContainer/MainContainer/RecipeDetails"]
layout_mode = 2
size_flags_vertical = 3

[node name="SelectedRecipeContainer" type="VBoxContainer" parent="Background/VBoxContainer/MainContainer/RecipeDetails/SelectedRecipePanel"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 10.0
offset_top = 10.0
offset_right = -10.0
offset_bottom = -10.0
grow_horizontal = 2
grow_vertical = 2

[node name="RecipeNameLabel" type="Label" parent="Background/VBoxContainer/MainContainer/RecipeDetails/SelectedRecipePanel/SelectedRecipeContainer"]
layout_mode = 2
text = "Select a recipe"
horizontal_alignment = 1

[node name="HSeparator2" type="HSeparator" parent="Background/VBoxContainer/MainContainer/RecipeDetails/SelectedRecipePanel/SelectedRecipeContainer"]
layout_mode = 2

[node name="MaterialsLabel" type="Label" parent="Background/VBoxContainer/MainContainer/RecipeDetails/SelectedRecipePanel/SelectedRecipeContainer"]
layout_mode = 2
text = "Required Materials:"

[node name="MaterialsContainer" type="VBoxContainer" parent="Background/VBoxContainer/MainContainer/RecipeDetails/SelectedRecipePanel/SelectedRecipeContainer"]
layout_mode = 2
size_flags_vertical = 3

[node name="HSeparator3" type="HSeparator" parent="Background/VBoxContainer/MainContainer/RecipeDetails/SelectedRecipePanel/SelectedRecipeContainer"]
layout_mode = 2

[node name="OutputLabel" type="Label" parent="Background/VBoxContainer/MainContainer/RecipeDetails/SelectedRecipePanel/SelectedRecipeContainer"]
layout_mode = 2
text = "Output:"

[node name="OutputContainer" type="HBoxContainer" parent="Background/VBoxContainer/MainContainer/RecipeDetails/SelectedRecipePanel/SelectedRecipeContainer"]
layout_mode = 2

[node name="HSeparator4" type="HSeparator" parent="Background/VBoxContainer/MainContainer/RecipeDetails/SelectedRecipePanel/SelectedRecipeContainer"]
layout_mode = 2

[node name="CraftButton" type="Button" parent="Background/VBoxContainer/MainContainer/RecipeDetails/SelectedRecipePanel/SelectedRecipeContainer"]
layout_mode = 2
disabled = true
text = "Craft Item"

[node name="CloseButton" type="Button" parent="Background"]
layout_mode = 1
anchors_preset = 1
anchor_left = 1.0
anchor_right = 1.0
offset_left = -30.0
offset_top = 5.0
offset_right = -5.0
offset_bottom = 30.0
grow_horizontal = 0
text = "X"

[connection signal="pressed" from="Background/VBoxContainer/MainContainer/RecipeDetails/SelectedRecipePanel/SelectedRecipeContainer/CraftButton" to="." method="_on_craft_button_pressed"]
[connection signal="pressed" from="Background/CloseButton" to="." method="_on_close_button_pressed"]
