using Godot;
using System;
using System.Collections.Generic;
using System.Linq;

namespace SurvivalLooterShooter
{
    /// <summary>
    /// Unit tests for the POI (Point of Interest) generation and interaction system
    /// Tests POI placement, interior generation, loot distribution, and exploration tracking
    /// </summary>
    public partial class POISystemTests : Node
    {
        private POIGenerator _poiGenerator;
        private ExplorationTracker _explorationTracker;
        private POIInteractionSystem _poiInteractionSystem;
        private WorldChunk _testChunk;
        
        private int _testsRun = 0;
        private int _testsPassed = 0;

        public override void _Ready()
        {
            GD.Print("=== POI System Tests Starting ===");
            RunAllTests();
        }

        private void RunAllTests()
        {
            SetupTestEnvironment();
            
            // POI Generation Tests
            TestPOIGeneration();
            TestPOIPlacement();
            TestInteriorGeneration();
            TestLootGeneration();
            TestPOITemplateLoading();
            
            // Exploration Tests
            TestExplorationTracking();
            TestMapRevealation();
            TestBiomeDiscovery();
            TestExplorationMilestones();
            
            // Interaction Tests
            TestPOIInteraction();
            TestLootCollection();
            TestRoomNavigation();
            TestPOIDiscovery();
            
            // Reset and Persistence Tests
            TestPOIReset();
            TestSaveLoadSystem();
            
            PrintTestResults();
        }

        private void SetupTestEnvironment()
        {
            // Create test chunk
            _testChunk = new WorldChunk(new Vector2I(0, 0), 64);
            AddChild(_testChunk);
            
            // Initialize POI generator
            _poiGenerator = new POIGenerator();
            AddChild(_poiGenerator);
            _poiGenerator.Initialize(12345);
            
            // Initialize exploration tracker
            _explorationTracker = new ExplorationTracker();
            AddChild(_explorationTracker);
            
            // Initialize POI interaction system
            _poiInteractionSystem = new POIInteractionSystem();
            AddChild(_poiInteractionSystem);
            
            GD.Print("Test environment setup complete");
        }

        #region POI Generation Tests

        private void TestPOIGeneration()
        {
            GD.Print("Testing POI generation...");
            _testsRun++;

            try
            {
                // Generate POIs for test chunk
                _poiGenerator.GeneratePOIsForChunk(_testChunk);
                
                // Get generated POIs
                var pois = _poiGenerator.GetPOIsInChunk(_testChunk.ChunkCoords);
                
                // Verify POIs were generated
                Assert(pois.Count >= 0, "POIs should be generated (can be 0 due to randomness)");
                
                if (pois.Count > 0)
                {
                    var poi = pois[0];
                    Assert(!string.IsNullOrEmpty(poi.Id), "POI should have valid ID");
                    Assert(!string.IsNullOrEmpty(poi.Name), "POI should have valid name");
                    Assert(poi.WorldPosition != Vector2.Zero, "POI should have valid world position");
                    Assert(poi.Template != null, "POI should have valid template");
                }
                
                _testsPassed++;
                GD.Print("✓ POI generation test passed");
            }
            catch (Exception ex)
            {
                GD.PrintErr($"✗ POI generation test failed: {ex.Message}");
            }
        }

        private void TestPOIPlacement()
        {
            GD.Print("Testing POI placement validation...");
            _testsRun++;

            try
            {
                // Generate multiple chunks to test placement
                var chunks = new List<WorldChunk>();
                for (int x = 0; x < 3; x++)
                {
                    for (int y = 0; y < 3; y++)
                    {
                        var chunk = new WorldChunk(new Vector2I(x, y), 64);
                        AddChild(chunk);
                        chunks.Add(chunk);
                        _poiGenerator.GeneratePOIsForChunk(chunk);
                    }
                }

                // Collect all POIs
                var allPOIs = new List<PointOfInterest>();
                foreach (var chunk in chunks)
                {
                    allPOIs.AddRange(_poiGenerator.GetPOIsInChunk(chunk.ChunkCoords));
                }

                // Verify minimum distance between POIs
                bool validPlacement = true;
                for (int i = 0; i < allPOIs.Count; i++)
                {
                    for (int j = i + 1; j < allPOIs.Count; j++)
                    {
                        float distance = allPOIs[i].WorldPosition.DistanceTo(allPOIs[j].WorldPosition);
                        if (distance < _poiGenerator.MinDistanceBetweenPOIs)
                        {
                            validPlacement = false;
                            break;
                        }
                    }
                    if (!validPlacement) break;
                }

                Assert(validPlacement, "POIs should maintain minimum distance between each other");
                
                // Clean up test chunks
                foreach (var chunk in chunks)
                {
                    chunk.QueueFree();
                }
                
                _testsPassed++;
                GD.Print("✓ POI placement test passed");
            }
            catch (Exception ex)
            {
                GD.PrintErr($"✗ POI placement test failed: {ex.Message}");
            }
        }

        private void TestInteriorGeneration()
        {
            GD.Print("Testing POI interior generation...");
            _testsRun++;

            try
            {
                // Create a test POI template
                var template = new POIData
                {
                    Id = "test_cabin",
                    Name = "Test Cabin",
                    Type = POIType.AbandonedCabin,
                    InteriorSize = new Vector2(20, 15),
                    MinRooms = 2,
                    MaxRooms = 4,
                    LootTableId = "cabin_loot",
                    ResetTime = 3600f
                };

                // Generate interior using reflection to access private method
                var random = new Random(12345);
                var generateInteriorMethod = typeof(POIGenerator).GetMethod("GenerateInterior", 
                    System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                
                var interior = (POIInterior)generateInteriorMethod.Invoke(_poiGenerator, new object[] { template, random });

                Assert(interior != null, "Interior should be generated");
                Assert(interior.Rooms.Count >= template.MinRooms, $"Should have at least {template.MinRooms} rooms");
                Assert(interior.Rooms.Count <= template.MaxRooms, $"Should have at most {template.MaxRooms} rooms");
                Assert(interior.Size == template.InteriorSize, "Interior size should match template");

                // Verify room connections
                if (interior.Rooms.Count > 1)
                {
                    Assert(interior.Connections.Count > 0, "Multi-room interiors should have connections");
                }

                _testsPassed++;
                GD.Print("✓ Interior generation test passed");
            }
            catch (Exception ex)
            {
                GD.PrintErr($"✗ Interior generation test failed: {ex.Message}");
            }
        }

        private void TestLootGeneration()
        {
            GD.Print("Testing loot generation...");
            _testsRun++;

            try
            {
                // Generate POIs and check for loot
                _poiGenerator.GeneratePOIsForChunk(_testChunk);
                var pois = _poiGenerator.GetPOIsInChunk(_testChunk.ChunkCoords);

                if (pois.Count > 0)
                {
                    var poi = pois[0];
                    bool hasLoot = false;

                    foreach (var room in poi.Interior.Rooms)
                    {
                        if (room.LootSpawns.Count > 0)
                        {
                            hasLoot = true;
                            
                            // Verify loot spawn properties
                            var loot = room.LootSpawns[0];
                            Assert(!string.IsNullOrEmpty(loot.ItemId), "Loot should have valid item ID");
                            Assert(loot.Quantity > 0, "Loot should have positive quantity");
                            Assert(loot.RespawnTime > 0, "Loot should have positive respawn time");
                            break;
                        }
                    }

                    // Note: Loot generation is probabilistic, so we don't assert hasLoot
                    GD.Print($"POI has loot: {hasLoot}");
                }

                _testsPassed++;
                GD.Print("✓ Loot generation test passed");
            }
            catch (Exception ex)
            {
                GD.PrintErr($"✗ Loot generation test failed: {ex.Message}");
            }
        }

        private void TestPOITemplateLoading()
        {
            GD.Print("Testing POI template loading...");
            _testsRun++;

            try
            {
                // Templates should be loaded during POI generator initialization
                // We can't directly access private fields, so we'll test by generating POIs
                _poiGenerator.GeneratePOIsForChunk(_testChunk);
                var pois = _poiGenerator.GetPOIsInChunk(_testChunk.ChunkCoords);

                // If POIs are generated successfully, templates were loaded
                Assert(true, "POI templates loaded successfully (inferred from successful generation)");

                _testsPassed++;
                GD.Print("✓ POI template loading test passed");
            }
            catch (Exception ex)
            {
                GD.PrintErr($"✗ POI template loading test failed: {ex.Message}");
            }
        }

        #endregion

        #region Exploration Tests

        private void TestExplorationTracking()
        {
            GD.Print("Testing exploration tracking...");
            _testsRun++;

            try
            {
                Vector2 testPosition = new Vector2(100, 100);
                
                // Update exploration
                _explorationTracker.UpdateExploration(testPosition);
                
                // Check if position is marked as explored
                bool isExplored = _explorationTracker.IsPositionExplored(testPosition);
                Assert(isExplored, "Position should be marked as explored");
                
                // Check exploration stats
                var stats = _explorationTracker.GetExplorationStats();
                Assert(stats.TotalAreasExplored > 0, "Should have explored areas");

                _testsPassed++;
                GD.Print("✓ Exploration tracking test passed");
            }
            catch (Exception ex)
            {
                GD.PrintErr($"✗ Exploration tracking test failed: {ex.Message}");
            }
        }

        private void TestMapRevealation()
        {
            GD.Print("Testing map revelation...");
            _testsRun++;

            try
            {
                Vector2 testPosition = new Vector2(200, 200);
                
                // Update exploration to reveal map
                _explorationTracker.UpdateExploration(testPosition);
                
                // Check fog of war
                float fogValue = _explorationTracker.GetFogOfWarAt(testPosition);
                Assert(fogValue > 0f, "Fog of war should be reduced at explored position");
                
                // Check explored cells
                var exploredCells = _explorationTracker.GetExploredCells();
                Assert(exploredCells.Count > 0, "Should have explored cells");

                _testsPassed++;
                GD.Print("✓ Map revelation test passed");
            }
            catch (Exception ex)
            {
                GD.PrintErr($"✗ Map revelation test failed: {ex.Message}");
            }
        }

        private void TestBiomeDiscovery()
        {
            GD.Print("Testing biome discovery...");
            _testsRun++;

            try
            {
                // Simulate exploring different biome areas
                Vector2[] positions = {
                    new Vector2(0, 0),      // Plains
                    new Vector2(500, 500),  // Different biome
                    new Vector2(-300, 200)  // Another biome
                };

                foreach (var pos in positions)
                {
                    _explorationTracker.UpdateExploration(pos);
                }

                var stats = _explorationTracker.GetExplorationStats();
                Assert(stats.BiomesDiscovered > 0, "Should have discovered at least one biome");

                _testsPassed++;
                GD.Print("✓ Biome discovery test passed");
            }
            catch (Exception ex)
            {
                GD.PrintErr($"✗ Biome discovery test failed: {ex.Message}");
            }
        }

        private void TestExplorationMilestones()
        {
            GD.Print("Testing exploration milestones...");
            _testsRun++;

            try
            {
                // Connect to milestone signal to track achievements
                _explorationTracker.ExplorationMilestone += (milestone, progress) => {
                    GD.Print($"Milestone reached: {milestone} ({progress:F2})");
                };

                // Explore many positions to trigger milestones
                for (int i = 0; i < 150; i++)
                {
                    Vector2 pos = new Vector2(i * 20, i * 15);
                    _explorationTracker.UpdateExploration(pos);
                }

                // Note: Milestone achievement depends on specific thresholds
                // We'll consider the test passed if no errors occur
                Assert(true, "Exploration milestone system functioning");

                _testsPassed++;
                GD.Print("✓ Exploration milestones test passed");
            }
            catch (Exception ex)
            {
                GD.PrintErr($"✗ Exploration milestones test failed: {ex.Message}");
            }
        }

        #endregion

        #region Interaction Tests

        private void TestPOIInteraction()
        {
            GD.Print("Testing POI interaction...");
            _testsRun++;

            try
            {
                // Generate POIs
                _poiGenerator.GeneratePOIsForChunk(_testChunk);
                var pois = _poiGenerator.GetPOIsInChunk(_testChunk.ChunkCoords);

                if (pois.Count > 0)
                {
                    var poi = pois[0];
                    
                    // Update player position near POI
                    _poiInteractionSystem.UpdatePlayerPosition(poi.WorldPosition);
                    
                    // Try to enter POI
                    bool entered = _poiInteractionSystem.TryEnterPOI(poi.Id);
                    Assert(entered, "Should be able to enter nearby POI");
                    
                    // Check interaction info
                    var info = _poiInteractionSystem.GetCurrentPOIInfo();
                    Assert(info.IsInsidePOI, "Should be inside POI");
                    Assert(info.CurrentPOI != null, "Should have current POI");
                    
                    // Exit POI
                    _poiInteractionSystem.ExitPOI();
                    
                    var exitInfo = _poiInteractionSystem.GetCurrentPOIInfo();
                    Assert(!exitInfo.IsInsidePOI, "Should not be inside POI after exit");
                }

                _testsPassed++;
                GD.Print("✓ POI interaction test passed");
            }
            catch (Exception ex)
            {
                GD.PrintErr($"✗ POI interaction test failed: {ex.Message}");
            }
        }

        private void TestLootCollection()
        {
            GD.Print("Testing loot collection...");
            _testsRun++;

            try
            {
                // This test requires inventory system to be available
                // For now, we'll test the loot availability checking
                
                _poiGenerator.GeneratePOIsForChunk(_testChunk);
                var pois = _poiGenerator.GetPOIsInChunk(_testChunk.ChunkCoords);

                if (pois.Count > 0)
                {
                    var poi = pois[0];
                    _poiInteractionSystem.UpdatePlayerPosition(poi.WorldPosition);
                    _poiInteractionSystem.TryEnterPOI(poi.Id);
                    
                    var availableLoot = _poiInteractionSystem.GetAvailableLootInCurrentRoom();
                    
                    // Loot availability is probabilistic, so we just check the system works
                    Assert(availableLoot != null, "Should return loot list (can be empty)");
                    
                    GD.Print($"Available loot items: {availableLoot.Count}");
                }

                _testsPassed++;
                GD.Print("✓ Loot collection test passed");
            }
            catch (Exception ex)
            {
                GD.PrintErr($"✗ Loot collection test failed: {ex.Message}");
            }
        }

        private void TestRoomNavigation()
        {
            GD.Print("Testing room navigation...");
            _testsRun++;

            try
            {
                _poiGenerator.GeneratePOIsForChunk(_testChunk);
                var pois = _poiGenerator.GetPOIsInChunk(_testChunk.ChunkCoords);

                if (pois.Count > 0)
                {
                    var poi = pois[0];
                    if (poi.Interior.Rooms.Count > 1)
                    {
                        _poiInteractionSystem.UpdatePlayerPosition(poi.WorldPosition);
                        _poiInteractionSystem.TryEnterPOI(poi.Id);
                        
                        var connectedRooms = _poiInteractionSystem.GetConnectedRooms();
                        Assert(connectedRooms != null, "Should return connected rooms list");
                        
                        if (connectedRooms.Count > 0)
                        {
                            bool moved = _poiInteractionSystem.MoveToRoom(connectedRooms[0].Id);
                            Assert(moved, "Should be able to move to connected room");
                        }
                    }
                }

                _testsPassed++;
                GD.Print("✓ Room navigation test passed");
            }
            catch (Exception ex)
            {
                GD.PrintErr($"✗ Room navigation test failed: {ex.Message}");
            }
        }

        private void TestPOIDiscovery()
        {
            GD.Print("Testing POI discovery...");
            _testsRun++;

            try
            {
                _poiGenerator.GeneratePOIsForChunk(_testChunk);
                var pois = _poiGenerator.GetPOIsInChunk(_testChunk.ChunkCoords);

                if (pois.Count > 0)
                {
                    var poi = pois[0];
                    
                    // Initially not discovered
                    Assert(!poi.IsDiscovered, "POI should not be discovered initially");
                    
                    // Discover POI
                    _poiGenerator.DiscoverPOI(poi.Id, poi.WorldPosition);
                    
                    // Should now be discovered
                    Assert(poi.IsDiscovered, "POI should be discovered after discovery call");
                }

                _testsPassed++;
                GD.Print("✓ POI discovery test passed");
            }
            catch (Exception ex)
            {
                GD.PrintErr($"✗ POI discovery test failed: {ex.Message}");
            }
        }

        #endregion

        #region Reset and Persistence Tests

        private void TestPOIReset()
        {
            GD.Print("Testing POI reset mechanics...");
            _testsRun++;

            try
            {
                _poiGenerator.GeneratePOIsForChunk(_testChunk);
                var pois = _poiGenerator.GetPOIsInChunk(_testChunk.ChunkCoords);

                if (pois.Count > 0)
                {
                    var poi = pois[0];
                    
                    // Mark some loot as looted
                    foreach (var room in poi.Interior.Rooms)
                    {
                        foreach (var loot in room.LootSpawns)
                        {
                            loot.IsLooted = true;
                            loot.LastLootTime = Time.GetUnixTimeFromSystem();
                            break; // Just mark one item
                        }
                        break; // Just one room
                    }
                    
                    // Reset POI
                    _poiGenerator.ResetPOI(poi.Id);
                    
                    // Check if loot was reset
                    bool allLootReset = true;
                    foreach (var room in poi.Interior.Rooms)
                    {
                        foreach (var loot in room.LootSpawns)
                        {
                            if (loot.IsLooted)
                            {
                                allLootReset = false;
                                break;
                            }
                        }
                        if (!allLootReset) break;
                    }
                    
                    Assert(allLootReset, "All loot should be reset after POI reset");
                }

                _testsPassed++;
                GD.Print("✓ POI reset test passed");
            }
            catch (Exception ex)
            {
                GD.PrintErr($"✗ POI reset test failed: {ex.Message}");
            }
        }

        private void TestSaveLoadSystem()
        {
            GD.Print("Testing POI save/load system...");
            _testsRun++;

            try
            {
                // Generate and discover some POIs
                _poiGenerator.GeneratePOIsForChunk(_testChunk);
                var pois = _poiGenerator.GetPOIsInChunk(_testChunk.ChunkCoords);

                if (pois.Count > 0)
                {
                    var poi = pois[0];
                    _poiGenerator.DiscoverPOI(poi.Id, poi.WorldPosition);
                }

                // Get save data
                var poiSaveData = _poiGenerator.GetSaveData();
                var explorationSaveData = _explorationTracker.GetSaveData();

                Assert(poiSaveData != null, "POI save data should not be null");
                Assert(explorationSaveData != null, "Exploration save data should not be null");

                // Test loading (we can't fully test without resetting state)
                _poiGenerator.LoadSaveData(poiSaveData);
                _explorationTracker.LoadSaveData(explorationSaveData);

                _testsPassed++;
                GD.Print("✓ Save/load system test passed");
            }
            catch (Exception ex)
            {
                GD.PrintErr($"✗ Save/load system test failed: {ex.Message}");
            }
        }

        #endregion

        private void Assert(bool condition, string message)
        {
            if (!condition)
            {
                throw new Exception($"Assertion failed: {message}");
            }
        }

        private void PrintTestResults()
        {
            GD.Print("=== POI System Test Results ===");
            GD.Print($"Tests Run: {_testsRun}");
            GD.Print($"Tests Passed: {_testsPassed}");
            GD.Print($"Tests Failed: {_testsRun - _testsPassed}");
            GD.Print($"Success Rate: {(float)_testsPassed / _testsRun * 100:F1}%");
            
            if (_testsPassed == _testsRun)
            {
                GD.Print("🎉 All POI system tests passed!");
            }
            else
            {
                GD.PrintErr($"❌ {_testsRun - _testsPassed} tests failed");
            }
        }
    }
}