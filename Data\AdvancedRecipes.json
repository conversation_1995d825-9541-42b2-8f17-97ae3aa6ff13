[{"id": "craft_advanced_rifle", "recipe_type": "advanced", "inputs": [{"id": "metal_scrap", "amount": 15}, {"id": "advanced_components", "amount": 3}, {"id": "rifle_barrel", "amount": 1}], "output": {"id": "advanced_rifle", "amount": 1}, "crafting_time": 30.0, "required_station": "workbench", "required_station_level": 2, "efficiency_bonus": 0.2, "batch_size": 1, "skill_requirements": {"crafting": 5, "engineering": 3}, "intermediate_steps": [{"step_name": "Forge Rifle Barrel", "inputs": [{"id": "steel_ingot", "amount": 2}, {"id": "carbon", "amount": 1}], "output": {"id": "rifle_barrel", "amount": 1}, "processing_time": 15.0, "required_tool": "forge"}], "unlock_conditions": [{"type": "recipe_discovered", "target": "craft_rifle_ammo", "value": 1}]}, {"id": "craft_health_serum", "recipe_type": "advanced", "inputs": [{"id": "purified_extract", "amount": 1}, {"id": "stabilizer", "amount": 2}, {"id": "glass_vial", "amount": 1}], "output": {"id": "health_serum", "amount": 3}, "crafting_time": 20.0, "required_station": "chemistry_lab", "required_station_level": 1, "efficiency_bonus": 0.15, "batch_size": 3, "skill_requirements": {"chemistry": 4}, "intermediate_steps": [{"step_name": "Extract Plant Essence", "inputs": [{"id": "medicinal_herbs", "amount": 5}, {"id": "distilled_water", "amount": 2}], "output": {"id": "plant_extract", "amount": 1}, "processing_time": 8.0, "required_tool": "extractor"}, {"step_name": "Purify Extract", "inputs": [{"id": "plant_extract", "amount": 1}, {"id": "purification_agent", "amount": 1}], "output": {"id": "purified_extract", "amount": 1}, "processing_time": 12.0, "required_tool": "purifier"}]}, {"id": "craft_explosive_ammo", "recipe_type": "advanced", "inputs": [{"id": "bullet_casing", "amount": 10}, {"id": "explosive_compound", "amount": 5}, {"id": "primer", "amount": 10}], "output": {"id": "explosive_ammo", "amount": 10}, "crafting_time": 25.0, "required_station": "forge", "required_station_level": 2, "efficiency_bonus": 0.25, "batch_size": 10, "skill_requirements": {"explosives": 6, "crafting": 4}, "intermediate_steps": [{"step_name": "Mix Explosive Compound", "inputs": [{"id": "gunpowder", "amount": 3}, {"id": "chemical_catalyst", "amount": 2}], "output": {"id": "explosive_compound", "amount": 5}, "processing_time": 10.0, "required_tool": "mixer"}], "unlock_conditions": [{"type": "skill_level", "target": "explosives", "value": 5}]}, {"id": "craft_power_core", "recipe_type": "advanced", "inputs": [{"id": "energy_crystal", "amount": 1}, {"id": "conductive_wire", "amount": 8}, {"id": "insulation_material", "amount": 4}, {"id": "metal_housing", "amount": 1}], "output": {"id": "power_core", "amount": 1}, "crafting_time": 45.0, "required_station": "forge", "required_station_level": 3, "efficiency_bonus": 0.3, "batch_size": 1, "skill_requirements": {"engineering": 8, "electronics": 6}, "intermediate_steps": [{"step_name": "Prepare Energy Crystal", "inputs": [{"id": "raw_crystal", "amount": 1}, {"id": "crystal_polish", "amount": 2}], "output": {"id": "energy_crystal", "amount": 1}, "processing_time": 15.0, "required_tool": "crystal_cutter"}, {"step_name": "Create Metal Housing", "inputs": [{"id": "titanium_alloy", "amount": 3}, {"id": "precision_tools", "amount": 1}], "output": {"id": "metal_housing", "amount": 1}, "processing_time": 20.0, "required_tool": "precision_forge"}]}, {"id": "craft_advanced_armor", "recipe_type": "advanced", "inputs": [{"id": "armor_plates", "amount": 6}, {"id": "kevlar_fabric", "amount": 4}, {"id": "padding_material", "amount": 8}, {"id": "armor_joints", "amount": 12}], "output": {"id": "advanced_armor", "amount": 1}, "crafting_time": 60.0, "required_station": "workbench", "required_station_level": 3, "efficiency_bonus": 0.2, "batch_size": 1, "skill_requirements": {"crafting": 7, "armor_smithing": 5}, "intermediate_steps": [{"step_name": "Forge Armor Plates", "inputs": [{"id": "steel_ingot", "amount": 4}, {"id": "carbon_fiber", "amount": 2}], "output": {"id": "armor_plates", "amount": 6}, "processing_time": 25.0, "required_tool": "armor_forge"}, {"step_name": "Craft Armor Joints", "inputs": [{"id": "flexible_metal", "amount": 3}, {"id": "lubricant", "amount": 2}], "output": {"id": "armor_joints", "amount": 12}, "processing_time": 15.0, "required_tool": "precision_tools"}]}]