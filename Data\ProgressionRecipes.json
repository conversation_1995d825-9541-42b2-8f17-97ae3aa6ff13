[{"id": "craft_improved_rifle", "recipe_type": "progression", "inputs": [{"id": "metal_scrap", "amount": 8}, {"id": "rifle_parts", "amount": 2}, {"id": "weapon_oil", "amount": 1}], "output": {"id": "improved_rifle", "amount": 1}, "crafting_time": 15.0, "unlock_requirement": {"skill": "weapon_proficiency", "level": 5}}, {"id": "craft_sniper_rifle", "recipe_type": "progression", "inputs": [{"id": "precision_barrel", "amount": 1}, {"id": "scope", "amount": 1}, {"id": "rifle_stock", "amount": 1}, {"id": "advanced_components", "amount": 2}], "output": {"id": "sniper_rifle", "amount": 1}, "crafting_time": 25.0, "unlock_requirement": {"skill": "weapon_proficiency", "level": 10}}, {"id": "craft_precision_ammo", "recipe_type": "progression", "inputs": [{"id": "bullet_casing", "amount": 5}, {"id": "precision_powder", "amount": 3}, {"id": "primer", "amount": 5}], "output": {"id": "precision_ammo", "amount": 5}, "crafting_time": 8.0, "unlock_requirement": {"skill": "critical_hit", "level": 3}}, {"id": "craft_armor_piercing_ammo", "recipe_type": "progression", "inputs": [{"id": "hardened_core", "amount": 3}, {"id": "bullet_casing", "amount": 8}, {"id": "high_grade_powder", "amount": 5}], "output": {"id": "armor_piercing_ammo", "amount": 8}, "crafting_time": 12.0, "unlock_requirement": {"skill": "critical_hit", "level": 8}}, {"id": "craft_advanced_workbench", "recipe_type": "progression", "inputs": [{"id": "steel_frame", "amount": 4}, {"id": "precision_tools", "amount": 2}, {"id": "work_surface", "amount": 1}, {"id": "storage_compartment", "amount": 3}], "output": {"id": "advanced_workbench", "amount": 1}, "crafting_time": 45.0, "unlock_requirement": {"skill": "crafting_efficiency", "level": 5}}, {"id": "craft_automated_crafter", "recipe_type": "progression", "inputs": [{"id": "automation_core", "amount": 1}, {"id": "conveyor_belt", "amount": 4}, {"id": "robotic_arm", "amount": 2}, {"id": "control_panel", "amount": 1}], "output": {"id": "automated_crafter", "amount": 1}, "crafting_time": 90.0, "unlock_requirement": {"skill": "crafting_efficiency", "level": 12}}, {"id": "craft_master_toolkit", "recipe_type": "progression", "inputs": [{"id": "titanium_tools", "amount": 8}, {"id": "precision_instruments", "amount": 5}, {"id": "tool_case", "amount": 1}, {"id": "calibration_kit", "amount": 2}], "output": {"id": "master_toolkit", "amount": 1}, "crafting_time": 60.0, "unlock_requirement": {"skill": "crafting_efficiency", "level": 20}}, {"id": "craft_quality_materials", "recipe_type": "progression", "inputs": [{"id": "raw_materials", "amount": 10}, {"id": "purification_agent", "amount": 3}, {"id": "quality_catalyst", "amount": 1}], "output": {"id": "quality_materials", "amount": 8}, "crafting_time": 20.0, "unlock_requirement": {"skill": "quality_bonus", "level": 4}}, {"id": "craft_masterwork_items", "recipe_type": "progression", "inputs": [{"id": "quality_materials", "amount": 5}, {"id": "masterwork_template", "amount": 1}, {"id": "artisan_tools", "amount": 2}], "output": {"id": "masterwork_gear", "amount": 1}, "crafting_time": 40.0, "unlock_requirement": {"skill": "quality_bonus", "level": 10}}, {"id": "craft_legendary_gear", "recipe_type": "progression", "inputs": [{"id": "legendary_essence", "amount": 1}, {"id": "masterwork_gear", "amount": 1}, {"id": "divine_catalyst", "amount": 2}, {"id": "ancient_runes", "amount": 3}], "output": {"id": "legendary_equipment", "amount": 1}, "crafting_time": 120.0, "unlock_requirement": {"skill": "quality_bonus", "level": 18}}, {"id": "craft_advanced_bandage", "recipe_type": "progression", "inputs": [{"id": "sterile_cloth", "amount": 3}, {"id": "healing_salve", "amount": 2}, {"id": "antiseptic", "amount": 1}], "output": {"id": "advanced_bandage", "amount": 5}, "crafting_time": 8.0, "unlock_requirement": {"skill": "medicine", "level": 3}}, {"id": "craft_antidote", "recipe_type": "progression", "inputs": [{"id": "antitoxin_herbs", "amount": 4}, {"id": "purified_water", "amount": 2}, {"id": "stabilizer", "amount": 1}], "output": {"id": "universal_antidote", "amount": 3}, "crafting_time": 15.0, "unlock_requirement": {"skill": "medicine", "level": 6}}, {"id": "craft_regeneration_serum", "recipe_type": "progression", "inputs": [{"id": "regenerative_cells", "amount": 2}, {"id": "bio_catalyst", "amount": 3}, {"id": "medical_vial", "amount": 1}, {"id": "growth_hormone", "amount": 1}], "output": {"id": "regeneration_serum", "amount": 2}, "crafting_time": 30.0, "unlock_requirement": {"skill": "medicine", "level": 12}}, {"id": "craft_nutrient_paste", "recipe_type": "progression", "inputs": [{"id": "concentrated_nutrients", "amount": 3}, {"id": "protein_powder", "amount": 2}, {"id": "vitamin_supplement", "amount": 1}], "output": {"id": "nutrient_paste", "amount": 8}, "crafting_time": 10.0, "unlock_requirement": {"skill": "foraging", "level": 4}}, {"id": "craft_energy_bar", "recipe_type": "progression", "inputs": [{"id": "energy_crystals", "amount": 2}, {"id": "binding_agent", "amount": 1}, {"id": "flavor_enhancer", "amount": 1}], "output": {"id": "energy_bar", "amount": 6}, "crafting_time": 12.0, "unlock_requirement": {"skill": "foraging", "level": 8}}, {"id": "craft_survival_kit", "recipe_type": "progression", "inputs": [{"id": "emergency_supplies", "amount": 5}, {"id": "survival_tools", "amount": 3}, {"id": "waterproof_case", "amount": 1}, {"id": "emergency_beacon", "amount": 1}], "output": {"id": "complete_survival_kit", "amount": 1}, "crafting_time": 35.0, "unlock_requirement": {"skill": "foraging", "level": 15}}, {"id": "craft_reinforced_walls", "recipe_type": "progression", "inputs": [{"id": "steel_reinforcement", "amount": 6}, {"id": "concrete_mix", "amount": 8}, {"id": "insulation_foam", "amount": 4}], "output": {"id": "reinforced_wall_section", "amount": 4}, "crafting_time": 25.0, "unlock_requirement": {"skill": "construction", "level": 5}}, {"id": "craft_automated_defenses", "recipe_type": "progression", "inputs": [{"id": "turret_base", "amount": 1}, {"id": "targeting_system", "amount": 1}, {"id": "ammunition_feed", "amount": 2}, {"id": "power_supply", "amount": 1}], "output": {"id": "automated_turret", "amount": 1}, "crafting_time": 50.0, "unlock_requirement": {"skill": "construction", "level": 10}}, {"id": "craft_fortress_gate", "recipe_type": "progression", "inputs": [{"id": "heavy_steel_frame", "amount": 8}, {"id": "hydraulic_system", "amount": 2}, {"id": "armor_plating", "amount": 12}, {"id": "access_control", "amount": 1}], "output": {"id": "fortress_gate", "amount": 1}, "crafting_time": 80.0, "unlock_requirement": {"skill": "construction", "level": 18}}, {"id": "craft_solar_panel", "recipe_type": "progression", "inputs": [{"id": "photovoltaic_cells", "amount": 6}, {"id": "aluminum_frame", "amount": 4}, {"id": "wiring_harness", "amount": 2}, {"id": "inverter", "amount": 1}], "output": {"id": "solar_panel", "amount": 1}, "crafting_time": 30.0, "unlock_requirement": {"skill": "advanced_structures", "level": 2}}, {"id": "craft_water_purifier", "recipe_type": "progression", "inputs": [{"id": "filtration_system", "amount": 3}, {"id": "uv_sterilizer", "amount": 1}, {"id": "storage_tank", "amount": 2}, {"id": "pump_mechanism", "amount": 1}], "output": {"id": "water_purifier", "amount": 1}, "crafting_time": 40.0, "unlock_requirement": {"skill": "advanced_structures", "level": 5}}, {"id": "craft_command_center", "recipe_type": "progression", "inputs": [{"id": "command_console", "amount": 1}, {"id": "communication_array", "amount": 2}, {"id": "data_processing_unit", "amount": 3}, {"id": "backup_power", "amount": 2}], "output": {"id": "command_center", "amount": 1}, "crafting_time": 100.0, "unlock_requirement": {"skill": "advanced_structures", "level": 12}}]