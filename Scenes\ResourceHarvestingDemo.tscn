[gd_scene load_steps=2 format=3 uid="uid://bvw6rep3eskiv"]

[ext_resource type="Script" uid="uid://b4wrubxiv4soj" path="res://Scripts/ResourceHarvestingDemo.cs" id="1_abc123"]

[node name="ResourceHarvestingDemo" type="Node2D"]
script = ExtResource("1_abc123")

[node name="Camera2D" type="Camera2D" parent="."]
position = Vector2(960, 540)

[node name="UI" type="CanvasLayer" parent="."]

[node name="InfoLabel" type="Label" parent="UI"]
anchors_preset = 2
anchor_top = 1.0
anchor_bottom = 1.0
offset_left = 10.0
offset_top = -100.0
offset_right = 400.0
offset_bottom = -10.0
text = "Resource Harvesting Demo
WASD - Move
E - Interact/Harvest
F5 - Quick Save
F9 - Quick Load"
