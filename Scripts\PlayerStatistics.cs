using Godot;
using System;
using System.Collections.Generic;
using System.Linq;

namespace SurvivalLooterShooter
{
    public partial class PlayerStatistics : Node
    {
        public enum StatCategory
        {
            Combat,
            Survival,
            Crafting,
            Building,
            Exploration
        }
        public static PlayerStatistics Instance { get; private set; }

        private Dictionary<string, float> _statistics = new();

        public override void _Ready()
        {
            if (Instance == null)
            {
                Instance = this;
                InitializeStatistics();
            }
            else
            {
                QueueFree();
            }
        }

        private void InitializeStatistics()
        {
            _statistics["survival_time"] = 0f;
            _statistics["enemies_killed"] = 0f;
            _statistics["bosses_defeated"] = 0f;
            _statistics["items_crafted"] = 0f;
            _statistics["structures_built"] = 0f;
            _statistics["resources_gathered"] = 0f;
            _statistics["quests_completed"] = 0f;
            _statistics["total_playtime"] = 0f;
        }

        public float GetStat(string statName)
        {
            return _statistics.GetValueOrDefault(statName, 0f);
        }

        public void IncrementStat(string statName, float amount = 1f)
        {
            if (_statistics.ContainsKey(statName))
            {
                _statistics[statName] += amount;
            }
            else
            {
                _statistics[statName] = amount;
            }
        }

        public void SetStat(string statName, float value)
        {
            _statistics[statName] = value;
        }

        public string GetMostUsedWeapon()
        {
            return "assault_rifle";
        }

        public string GetMostCraftedItem()
        {
            return "bandage";
        }

        public int GetCraftingStationsUsed()
        {
            return 5;
        }

        public string GetMostBuiltStructure()
        {
            return "wooden_wall";
        }

        public int GetLargestBaseSize()
        {
            return 25;
        }

        public Dictionary<string, float> GetAllStatistics()
        {
            return new Dictionary<string, float>(_statistics);
        }

        public Dictionary<string, float> GetStatsByCategory(StatCategory category)
        {
            var categoryStats = new Dictionary<string, float>();
            switch (category)
            {
                case StatCategory.Combat:
                    if (_statistics.ContainsKey("enemies_killed")) categoryStats["Enemies Killed"] = _statistics["enemies_killed"];
                    if (_statistics.ContainsKey("bosses_defeated")) categoryStats["Bosses Defeated"] = _statistics["bosses_defeated"];
                    break;
                case StatCategory.Survival:
                    if (_statistics.ContainsKey("survival_time")) categoryStats["Survival Time"] = _statistics["survival_time"];
                    if (_statistics.ContainsKey("total_playtime")) categoryStats["Total Playtime"] = _statistics["total_playtime"];
                    break;
                case StatCategory.Crafting:
                    if (_statistics.ContainsKey("items_crafted")) categoryStats["Items Crafted"] = _statistics["items_crafted"];
                    break;
                case StatCategory.Building:
                    if (_statistics.ContainsKey("structures_built")) categoryStats["Structures Built"] = _statistics["structures_built"];
                    break;
                case StatCategory.Exploration:
                    if (_statistics.ContainsKey("resources_gathered")) categoryStats["Resources Gathered"] = _statistics["resources_gathered"];
                    if (_statistics.ContainsKey("quests_completed")) categoryStats["Quests Completed"] = _statistics["quests_completed"];
                    break;
            }
            return categoryStats;
        }
    }
}