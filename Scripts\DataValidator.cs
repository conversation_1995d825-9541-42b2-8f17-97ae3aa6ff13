using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using Godot;

namespace SurvivalLooterShooter
{
    /// <summary>
    /// Utility class for validating game data and providing fallback mechanisms
    /// </summary>
    public static class DataValidator
    {
        /// <summary>
        /// Validates an Item object and provides fallback values
        /// </summary>
        public static bool ValidateItem(Item item, out List<string> errors)
        {
            errors = new List<string>();
            bool isValid = true;

            if (item == null)
            {
                errors.Add("Item is null");
                return false;
            }

            // Validate ID
            if (string.IsNullOrWhiteSpace(item.Id))
            {
                errors.Add("Item ID is null or empty");
                isValid = false;
            }
            else if (item.Id.Length > 50)
            {
                errors.Add($"Item ID '{item.Id}' is too long (max 50 characters)");
                isValid = false;
            }
            else if (!IsValidIdentifier(item.Id))
            {
                errors.Add($"Item ID '{item.Id}' contains invalid characters");
                isValid = false;
            }

            // Validate Name
            if (string.IsNullOrWhiteSpace(item.Name))
            {
                errors.Add($"Item '{item.Id}' has no name");
                item.Name = item.Id?.Replace("_", " ") ?? "Unknown Item";
                Logger.LogWarning("DataValidator", $"Fixed missing name for item '{item.Id}'");
            }

            // Validate Type
            var validTypes = new[] { "weapon", "ammo", "consumable", "material", "tool", "armor" };
            if (string.IsNullOrWhiteSpace(item.Type))
            {
                errors.Add($"Item '{item.Id}' has no type");
                item.Type = "material"; // Default fallback
                Logger.LogWarning("DataValidator", $"Set default type 'material' for item '{item.Id}'");
            }
            else if (!validTypes.Contains(item.Type.ToLower()))
            {
                errors.Add($"Item '{item.Id}' has invalid type '{item.Type}'");
                Logger.LogWarning("DataValidator", $"Item '{item.Id}' has unrecognized type '{item.Type}'");
            }

            // Validate MaxStack
            if (item.MaxStack <= 0)
            {
                errors.Add($"Item '{item.Id}' has invalid max_stack value: {item.MaxStack}");
                item.MaxStack = 1; // Default fallback
                Logger.LogWarning("DataValidator", $"Set default max_stack=1 for item '{item.Id}'");
            }
            else if (item.MaxStack > 1000)
            {
                errors.Add($"Item '{item.Id}' has excessive max_stack value: {item.MaxStack}");
                item.MaxStack = 1000; // Cap at reasonable limit
                Logger.LogWarning("DataValidator", $"Capped max_stack to 1000 for item '{item.Id}'");
            }

            // Validate Metadata
            if (item.Metadata == null)
            {
                item.Metadata = new Dictionary<string, object>();
                Logger.LogWarning("DataValidator", $"Initialized empty metadata for item '{item.Id}'");
            }

            // Type-specific validation
            ValidateItemTypeSpecificData(item, errors);

            return isValid;
        }

        /// <summary>
        /// Validates type-specific item data
        /// </summary>
        private static void ValidateItemTypeSpecificData(Item item, List<string> errors)
        {
            switch (item.Type?.ToLower())
            {
                case "weapon":
                    ValidateWeaponMetadata(item, errors);
                    break;
                case "consumable":
                    ValidateConsumableMetadata(item, errors);
                    break;
                case "ammo":
                    ValidateAmmoMetadata(item, errors);
                    break;
            }
        }

        /// <summary>
        /// Validates weapon-specific metadata
        /// </summary>
        private static void ValidateWeaponMetadata(Item item, List<string> errors)
        {
            // Damage
            if (!item.Metadata.ContainsKey("damage"))
            {
                item.SetMetadata("damage", 10f);
                Logger.LogWarning("DataValidator", $"Set default damage=10 for weapon '{item.Id}'");
            }
            else
            {
                float damage = item.GetMetadata<float>("damage", 10f);
                if (damage <= 0)
                {
                    errors.Add($"Weapon '{item.Id}' has invalid damage: {damage}");
                    item.SetMetadata("damage", 10f);
                }
            }

            // Fire rate
            if (!item.Metadata.ContainsKey("fire_rate"))
            {
                item.SetMetadata("fire_rate", 1.0f);
                Logger.LogWarning("DataValidator", $"Set default fire_rate=1.0 for weapon '{item.Id}'");
            }

            // Ammo type
            if (!item.Metadata.ContainsKey("ammo_type"))
            {
                item.SetMetadata("ammo_type", "generic_ammo");
                Logger.LogWarning("DataValidator", $"Set default ammo_type for weapon '{item.Id}'");
            }

            // Durability
            if (!item.Metadata.ContainsKey("durability"))
            {
                item.SetMetadata("durability", 100f);
                item.SetMetadata("max_durability", 100f);
                Logger.LogWarning("DataValidator", $"Set default durability for weapon '{item.Id}'");
            }

            // Magazine size
            if (!item.Metadata.ContainsKey("magazine_size"))
            {
                item.SetMetadata("magazine_size", 10);
                Logger.LogWarning("DataValidator", $"Set default magazine_size=10 for weapon '{item.Id}'");
            }
        }

        /// <summary>
        /// Validates consumable-specific metadata
        /// </summary>
        private static void ValidateConsumableMetadata(Item item, List<string> errors)
        {
            // Use time
            if (!item.Metadata.ContainsKey("use_time"))
            {
                item.SetMetadata("use_time", 1.0f);
                Logger.LogWarning("DataValidator", $"Set default use_time=1.0 for consumable '{item.Id}'");
            }

            // At least one restoration effect should be present
            var restorationKeys = new[] { "health_restore", "hunger_restore", "thirst_restore", "stamina_restore" };
            bool hasRestoration = restorationKeys.Any(key => item.Metadata.ContainsKey(key));
            
            if (!hasRestoration)
            {
                item.SetMetadata("health_restore", 10f);
                Logger.LogWarning("DataValidator", $"Added default health_restore=10 for consumable '{item.Id}'");
            }
        }

        /// <summary>
        /// Validates ammo-specific metadata
        /// </summary>
        private static void ValidateAmmoMetadata(Item item, List<string> errors)
        {
            if (!item.Metadata.ContainsKey("ammo_type"))
            {
                item.SetMetadata("ammo_type", "generic");
                Logger.LogWarning("DataValidator", $"Set default ammo_type for ammo '{item.Id}'");
            }
        }

        /// <summary>
        /// Validates a Recipe object and provides fallback values
        /// </summary>
        public static bool ValidateRecipe(Recipe recipe, out List<string> errors)
        {
            errors = new List<string>();
            bool isValid = true;

            if (recipe == null)
            {
                errors.Add("Recipe is null");
                return false;
            }

            // Validate ID
            if (string.IsNullOrWhiteSpace(recipe.Id))
            {
                errors.Add("Recipe ID is null or empty");
                isValid = false;
            }
            else if (!IsValidIdentifier(recipe.Id))
            {
                errors.Add($"Recipe ID '{recipe.Id}' contains invalid characters");
                isValid = false;
            }

            // Validate Inputs
            if (recipe.Inputs == null || recipe.Inputs.Count == 0)
            {
                errors.Add($"Recipe '{recipe.Id}' has no inputs");
                isValid = false;
            }
            else
            {
                for (int i = 0; i < recipe.Inputs.Count; i++)
                {
                    var input = recipe.Inputs[i];
                    if (input == null)
                    {
                        errors.Add($"Recipe '{recipe.Id}' has null input at index {i}");
                        isValid = false;
                        continue;
                    }

                    if (string.IsNullOrWhiteSpace(input.Id))
                    {
                        errors.Add($"Recipe '{recipe.Id}' has input with empty ID at index {i}");
                        isValid = false;
                    }

                    if (input.Amount <= 0)
                    {
                        errors.Add($"Recipe '{recipe.Id}' has invalid input amount {input.Amount} for '{input.Id}'");
                        input.Amount = 1; // Fix invalid amount
                        Logger.LogWarning("DataValidator", $"Fixed input amount for '{input.Id}' in recipe '{recipe.Id}'");
                    }
                }
            }

            // Validate Output
            if (recipe.Output == null)
            {
                errors.Add($"Recipe '{recipe.Id}' has no output");
                isValid = false;
            }
            else
            {
                if (string.IsNullOrWhiteSpace(recipe.Output.Id))
                {
                    errors.Add($"Recipe '{recipe.Id}' has output with empty ID");
                    isValid = false;
                }

                if (recipe.Output.Amount <= 0)
                {
                    errors.Add($"Recipe '{recipe.Id}' has invalid output amount: {recipe.Output.Amount}");
                    recipe.Output.Amount = 1; // Fix invalid amount
                    Logger.LogWarning("DataValidator", $"Fixed output amount for recipe '{recipe.Id}'");
                }
            }

            // Validate Crafting Time
            if (recipe.CraftingTime <= 0)
            {
                errors.Add($"Recipe '{recipe.Id}' has invalid crafting time: {recipe.CraftingTime}");
                recipe.CraftingTime = 1.0f; // Default fallback
                Logger.LogWarning("DataValidator", $"Set default crafting_time=1.0 for recipe '{recipe.Id}'");
            }

            return isValid;
        }

        /// <summary>
        /// Validates save data and provides fallback values
        /// </summary>
        public static bool ValidateSaveData(GameSaveData saveData, out List<string> errors)
        {
            errors = new List<string>();
            bool isValid = true;

            if (saveData == null)
            {
                errors.Add("Save data is null");
                return false;
            }

            // Initialize null collections
            if (saveData.InventoryItems == null)
            {
                saveData.InventoryItems = new Dictionary<string, InventorySlotData>();
                Logger.LogWarning("DataValidator", "Initialized null InventoryItems in save data");
            }

            if (saveData.EquipmentSlots == null)
            {
                saveData.EquipmentSlots = new Dictionary<string, InventorySlotData>();
                Logger.LogWarning("DataValidator", "Initialized null EquipmentSlots in save data");
            }

            if (saveData.SurvivalStats == null)
            {
                saveData.SurvivalStats = new Dictionary<string, SurvivalStatData>();
                Logger.LogWarning("DataValidator", "Initialized null SurvivalStats in save data");
            }

            // Validate inventory items
            var invalidInventoryKeys = new List<string>();
            foreach (var kvp in saveData.InventoryItems)
            {
                if (string.IsNullOrWhiteSpace(kvp.Key) || kvp.Value == null || kvp.Value.IsEmpty)
                {
                    invalidInventoryKeys.Add(kvp.Key);
                    continue;
                }

                if (kvp.Value.Quantity <= 0)
                {
                    errors.Add($"Invalid quantity {kvp.Value.Quantity} for item '{kvp.Value.ItemId}' in inventory");
                    invalidInventoryKeys.Add(kvp.Key);
                }
            }

            // Remove invalid inventory items
            foreach (var key in invalidInventoryKeys)
            {
                saveData.InventoryItems.Remove(key);
                Logger.LogWarning("DataValidator", $"Removed invalid inventory item with key '{key}'");
            }

            // Validate equipment slots
            var invalidEquipmentKeys = new List<string>();
            foreach (var kvp in saveData.EquipmentSlots)
            {
                if (kvp.Value == null || kvp.Value.IsEmpty)
                {
                    invalidEquipmentKeys.Add(kvp.Key);
                }
            }

            foreach (var key in invalidEquipmentKeys)
            {
                saveData.EquipmentSlots.Remove(key);
                Logger.LogWarning("DataValidator", $"Removed invalid equipment slot '{key}'");
            }

            // Validate timestamps
            if (saveData.LastSaveTime == default(DateTime))
            {
                saveData.LastSaveTime = DateTime.UtcNow;
                Logger.LogWarning("DataValidator", "Fixed invalid LastSaveTime in save data");
            }

            // Validate version
            if (string.IsNullOrWhiteSpace(saveData.SaveVersion))
            {
                saveData.SaveVersion = "1.0.0";
                Logger.LogWarning("DataValidator", "Set default SaveVersion in save data");
            }

            return isValid;
        }

        /// <summary>
        /// Validates JSON content before deserialization
        /// </summary>
        public static bool ValidateJsonContent(string jsonContent, out string error)
        {
            error = null;

            if (string.IsNullOrWhiteSpace(jsonContent))
            {
                error = "JSON content is null or empty";
                return false;
            }

            try
            {
                using (JsonDocument doc = JsonDocument.Parse(jsonContent))
                {
                    // Basic JSON structure validation
                    if (doc.RootElement.ValueKind != JsonValueKind.Array && 
                        doc.RootElement.ValueKind != JsonValueKind.Object)
                    {
                        error = "JSON root element must be an array or object";
                        return false;
                    }
                }
                return true;
            }
            catch (JsonException ex)
            {
                error = $"Invalid JSON format: {ex.Message}";
                return false;
            }
        }

        /// <summary>
        /// Checks if a string is a valid identifier (alphanumeric and underscores)
        /// </summary>
        private static bool IsValidIdentifier(string identifier)
        {
            if (string.IsNullOrWhiteSpace(identifier))
                return false;

            return identifier.All(c => char.IsLetterOrDigit(c) || c == '_');
        }

        /// <summary>
        /// Creates default fallback items for critical game functionality
        /// </summary>
        public static List<Item> CreateFallbackItems()
        {
            Logger.LogWarning("DataValidator", "Creating fallback items due to data loading failure");

            return new List<Item>
            {
                new Item("fallback_bandage", "Emergency Bandage", "consumable", 10)
                {
                    Metadata = new Dictionary<string, object>
                    {
                        ["health_restore"] = 20f,
                        ["use_time"] = 2.0f
                    }
                },
                new Item("fallback_water", "Emergency Water", "consumable", 5)
                {
                    Metadata = new Dictionary<string, object>
                    {
                        ["thirst_restore"] = 25f,
                        ["use_time"] = 1.0f
                    }
                },
                new Item("fallback_food", "Emergency Rations", "consumable", 5)
                {
                    Metadata = new Dictionary<string, object>
                    {
                        ["hunger_restore"] = 30f,
                        ["use_time"] = 3.0f
                    }
                }
            };
        }

        /// <summary>
        /// Creates default fallback recipes
        /// </summary>
        public static List<Recipe> CreateFallbackRecipes()
        {
            Logger.LogWarning("DataValidator", "Creating fallback recipes due to data loading failure");

            return new List<Recipe>
            {
                new Recipe("fallback_basic_healing", 
                    new List<RecipeInput> { new RecipeInput("fallback_bandage", 1) },
                    new RecipeOutput("fallback_bandage", 1),
                    1.0f)
            };
        }
    }
}