using System;
using System.Collections.Generic;

namespace SurvivalLooterShooter
{
    /// <summary>
    /// Represents a single slot in the inventory that holds an item with quantity and metadata
    /// </summary>
    [Serializable]
    public class InventorySlot
    {
        public string ItemId { get; set; }
        public int Quantity { get; set; }
        public Dictionary<string, object> Metadata { get; set; }

        public InventorySlot()
        {
            Metadata = new Dictionary<string, object>();
        }

        public InventorySlot(string itemId, int quantity, Dictionary<string, object> metadata = null)
        {
            ItemId = itemId;
            Quantity = quantity;
            Metadata = metadata ?? new Dictionary<string, object>();
        }

        /// <summary>
        /// Gets a metadata value as the specified type
        /// </summary>
        public T GetMetadata<T>(string key, T defaultValue = default(T))
        {
            if (Metadata.ContainsKey(key))
            {
                try
                {
                    return (T)Convert.ChangeType(Metadata[key], typeof(T));
                }
                catch
                {
                    return defaultValue;
                }
            }
            return defaultValue;
        }

        /// <summary>
        /// Sets a metadata value
        /// </summary>
        public void SetMetadata<T>(string key, T value)
        {
            Metadata[key] = value;
        }

        /// <summary>
        /// Checks if this slot is empty
        /// </summary>
        public bool IsEmpty => Quantity <= 0 || string.IsNullOrEmpty(ItemId);

        /// <summary>
        /// Checks if this slot can stack with another item
        /// </summary>
        public bool CanStackWith(string itemId, Dictionary<string, object> metadata = null)
        {
            if (IsEmpty || ItemId != itemId)
                return false;

            // Get item definition to check max stack
            var item = ItemDatabase.Instance?.GetItem(ItemId);
            if (item == null || item.MaxStack <= 1)
                return false;

            // For items with metadata that affects stacking (like durability), check compatibility
            if (metadata != null && Metadata.Count > 0)
            {
                // Items with different durability or other critical metadata shouldn't stack
                if (metadata.ContainsKey("durability") && Metadata.ContainsKey("durability"))
                {
                    var newDurability = metadata["durability"];
                    var existingDurability = Metadata["durability"];
                    if (!newDurability.Equals(existingDurability))
                        return false;
                }
            }

            return true;
        }

        /// <summary>
        /// Gets the maximum stack size for the item in this slot
        /// </summary>
        public int GetMaxStackSize()
        {
            if (IsEmpty)
                return 0;

            var item = ItemDatabase.Instance?.GetItem(ItemId);
            return item?.MaxStack ?? 1;
        }

        /// <summary>
        /// Gets the remaining space in this stack
        /// </summary>
        public int GetRemainingStackSpace()
        {
            if (IsEmpty)
                return GetMaxStackSize();

            return Math.Max(0, GetMaxStackSize() - Quantity);
        }

        /// <summary>
        /// Creates a copy of this inventory slot
        /// </summary>
        public InventorySlot Clone()
        {
            var clonedMetadata = new Dictionary<string, object>();
            foreach (var kvp in Metadata)
            {
                clonedMetadata[kvp.Key] = kvp.Value;
            }

            return new InventorySlot(ItemId, Quantity, clonedMetadata);
        }

        public override string ToString()
        {
            if (IsEmpty)
                return "Empty Slot";

            var item = ItemDatabase.Instance?.GetItem(ItemId);
            var itemName = item?.Name ?? ItemId;
            return $"{itemName} x{Quantity}";
        }
    }
}