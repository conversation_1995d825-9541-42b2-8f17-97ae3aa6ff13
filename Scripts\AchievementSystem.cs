using Godot;
using System;
using System.Collections.Generic;
using System.Linq;

namespace SurvivalLooterShooter
{
    public partial class AchievementSystem : Node
{
    public static AchievementSystem Instance { get; private set; }
    
    private Dictionary<string, Achievement> _achievements = new();
    private HashSet<string> _completedAchievements = new();

    public override void _Ready()
    {
        if (Instance == null)
        {
            Instance = this;
        }
        else
        {
            QueueFree();
        }
    }

    public List<string> GetCompletedAchievements()
    {
        return new List<string>(_completedAchievements);
    }

    public void UnlockAchievement(string achievementId)
    {
        if (!_completedAchievements.Contains(achievementId))
        {
            _completedAchievements.Add(achievementId);
            GD.Print($"Achievement unlocked: {achievementId}");
        }
    }

    public bool IsAchievementCompleted(string achievementId)
    {
        return _completedAchievements.Contains(achievementId);
    }

    public List<Achievement> GetUnlockedAchievements()
    {
        return _achievements.Values.Where(a => _completedAchievements.Contains(a.Id)).ToList();
    }

    public List<Achievement> GetLockedAchievements()
    {
        return _achievements.Values.Where(a => !_completedAchievements.Contains(a.Id)).ToList();
    }

    [System.Serializable]
    public class Achievement
    {
        public string Id { get; set; } = "";
        public string Name { get; set; } = "";
        public string Description { get; set; } = "";
        public string IconPath { get; set; } = "";
        public bool IsUnlocked { get; set; } = false;
        public int Points { get; set; } = 10; // Default points value
    }
}
}