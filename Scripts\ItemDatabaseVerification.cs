using Godot;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;

namespace SurvivalLooterShooter
{
    /// <summary>
    /// Enhanced ItemDatabase with comprehensive validation and error handling
    /// </summary>
    public partial class ItemDatabaseVerification : Node
    {
        private static ItemDatabaseVerification _instance;
        public static ItemDatabaseVerification Instance => _instance;

        private Dictionary<string, Item> _items = new Dictionary<string, Item>();
        private Dictionary<string, Recipe> _recipes = new Dictionary<string, Recipe>();
        private Dictionary<string, List<Recipe>> _recipesByOutput = new Dictionary<string, List<Recipe>>();
        
        // Error tracking
        private List<string> _loadingErrors = new List<string>();
        private bool _itemsLoadedSuccessfully = false;
        private bool _recipesLoadedSuccessfully = false;
        private bool _fallbackDataLoaded = false;

        // Events for error reporting
        [Signal]
        public delegate void DataLoadingErrorEventHandler(string errorMessage);
        
        [Signal]
        public delegate void FallbackDataLoadedEventHandler(string reason);

        public override void _Ready()
        {
            if (_instance == null)
            {
                _instance = this;
                Logger.LogInfo("ItemDatabase", "Initializing ItemDatabase with validation");
                
                LoadItemsFromJsonWithValidation();
                LoadRecipesFromJsonWithValidation();
                
                // Verify data integrity after loading
                VerifyDataIntegrity();
                
                Logger.LogInfo("ItemDatabase", $"ItemDatabase initialized - Items: {_items.Count}, Recipes: {_recipes.Count}");
            }
            else
            {
                QueueFree(); // Ensure only one instance exists
            }
        }

        /// <summary>
        /// Loads item definitions from JSON with comprehensive validation
        /// </summary>
        public void LoadItemsFromJsonWithValidation()
        {
            const string ITEMS_FILE_PATH = "res://Data/Items.json";
            _itemsLoadedSuccessfully = false;
            
            try
            {
                Logger.LogInfo("ItemDatabase", "Loading items from JSON with validation");
                
                // Check if file exists
                if (!Godot.FileAccess.FileExists(ITEMS_FILE_PATH))
                {
                    string error = $"Items.json file not found at {ITEMS_FILE_PATH}";
                    Logger.LogError("ItemDatabase", error);
                    _loadingErrors.Add(error);
                    LoadFallbackItems();
                    return;
                }

                // Read file content
                string jsonContent;
                using (var file = Godot.FileAccess.Open(ITEMS_FILE_PATH, Godot.FileAccess.ModeFlags.Read))
                {
                    if (file == null)
                    {
                        string error = $"Failed to open Items.json file at {ITEMS_FILE_PATH}";
                        Logger.LogError("ItemDatabase", error);
                        _loadingErrors.Add(error);
                        LoadFallbackItems();
                        return;
                    }
                    jsonContent = file.GetAsText();
                }

                // Validate JSON format
                if (!DataValidator.ValidateJsonContent(jsonContent, out string jsonError))
                {
                    string error = $"Invalid JSON in Items.json: {jsonError}";
                    Logger.LogError("ItemDatabase", error);
                    _loadingErrors.Add(error);
                    LoadFallbackItems();
                    return;
                }

                // Deserialize JSON
                List<Item> items;
                try
                {
                    var options = new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true,
                        AllowTrailingCommas = true
                    };
                    items = JsonSerializer.Deserialize<List<Item>>(jsonContent, options);
                }
                catch (JsonException ex)
                {
                    string error = $"JSON deserialization failed for Items.json: {ex.Message}";
                    Logger.LogError("ItemDatabase", error);
                    _loadingErrors.Add(error);
                    LoadFallbackItems();
                    return;
                }

                if (items == null)
                {
                    string error = "Deserialized items list is null";
                    Logger.LogError("ItemDatabase", error);
                    _loadingErrors.Add(error);
                    LoadFallbackItems();
                    return;
                }

                // Clear existing items
                _items.Clear();
                
                // Validate and process each item
                int validItems = 0;
                int invalidItems = 0;
                
                foreach (var item in items)
                {
                    if (item == null)
                    {
                        invalidItems++;
                        Logger.LogWarning("ItemDatabase", "Skipping null item in Items.json");
                        continue;
                    }

                    // Validate item with error recovery
                    if (DataValidator.ValidateItem(item, out List<string> itemErrors))
                    {
                        // Item is valid or was successfully corrected
                        if (_items.ContainsKey(item.Id))
                        {
                            Logger.LogWarning("ItemDatabase", $"Duplicate item ID '{item.Id}' found, using latest definition");
                        }
                        
                        _items[item.Id] = item;
                        validItems++;
                        
                        if (itemErrors.Count > 0)
                        {
                            Logger.LogWarning("ItemDatabase", $"Item '{item.Id}' had correctable issues: {string.Join(", ", itemErrors)}");
                        }
                    }
                    else
                    {
                        // Item has critical errors that couldn't be fixed
                        invalidItems++;
                        string errorMsg = $"Item '{item?.Id ?? "unknown"}' has critical errors: {string.Join(", ", itemErrors)}";
                        Logger.LogError("ItemDatabase", errorMsg);
                        _loadingErrors.Add(errorMsg);
                    }
                }

                Logger.LogInfo("ItemDatabase", $"Items loaded - Valid: {validItems}, Invalid: {invalidItems}, Total: {_items.Count}");
                
                // Check if we have enough valid items to continue
                if (_items.Count == 0)
                {
                    Logger.LogError("ItemDatabase", "No valid items loaded, using fallback data");
                    LoadFallbackItems();
                }
                else
                {
                    _itemsLoadedSuccessfully = true;
                    
                    // Add fallback items if we're missing critical ones
                    EnsureCriticalItemsExist();
                }
            }
            catch (Exception ex)
            {
                string error = $"Unexpected error loading Items.json: {ex.Message}";
                Logger.LogException("ItemDatabase", ex, "LoadItemsFromJsonWithValidation");
                _loadingErrors.Add(error);
                LoadFallbackItems();
            }
        }

        /// <summary>
        /// Loads recipe definitions from JSON with comprehensive validation
        /// </summary>
        public void LoadRecipesFromJsonWithValidation()
        {
            const string RECIPES_FILE_PATH = "res://Data/Recipes.json";
            _recipesLoadedSuccessfully = false;
            
            try
            {
                Logger.LogInfo("ItemDatabase", "Loading recipes from JSON with validation");
                
                // Check if file exists
                if (!Godot.FileAccess.FileExists(RECIPES_FILE_PATH))
                {
                    string error = $"Recipes.json file not found at {RECIPES_FILE_PATH}";
                    Logger.LogError("ItemDatabase", error);
                    _loadingErrors.Add(error);
                    LoadFallbackRecipes();
                    return;
                }

                // Read file content
                string jsonContent;
                using (var file = Godot.FileAccess.Open(RECIPES_FILE_PATH, Godot.FileAccess.ModeFlags.Read))
                {
                    if (file == null)
                    {
                        string error = $"Failed to open Recipes.json file at {RECIPES_FILE_PATH}";
                        Logger.LogError("ItemDatabase", error);
                        _loadingErrors.Add(error);
                        LoadFallbackRecipes();
                        return;
                    }
                    jsonContent = file.GetAsText();
                }

                // Validate JSON format
                if (!DataValidator.ValidateJsonContent(jsonContent, out string jsonError))
                {
                    string error = $"Invalid JSON in Recipes.json: {jsonError}";
                    Logger.LogError("ItemDatabase", error);
                    _loadingErrors.Add(error);
                    LoadFallbackRecipes();
                    return;
                }

                // Deserialize JSON
                List<Recipe> recipes;
                try
                {
                    var options = new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true,
                        AllowTrailingCommas = true
                    };
                    recipes = JsonSerializer.Deserialize<List<Recipe>>(jsonContent, options);
                }
                catch (JsonException ex)
                {
                    string error = $"JSON deserialization failed for Recipes.json: {ex.Message}";
                    Logger.LogError("ItemDatabase", error);
                    _loadingErrors.Add(error);
                    LoadFallbackRecipes();
                    return;
                }

                if (recipes == null)
                {
                    string error = "Deserialized recipes list is null";
                    Logger.LogError("ItemDatabase", error);
                    _loadingErrors.Add(error);
                    LoadFallbackRecipes();
                    return;
                }

                // Clear existing recipes
                _recipes.Clear();
                _recipesByOutput.Clear();
                
                // Validate and process each recipe
                int validRecipes = 0;
                int invalidRecipes = 0;
                
                foreach (var recipe in recipes)
                {
                    if (recipe == null)
                    {
                        invalidRecipes++;
                        Logger.LogWarning("ItemDatabase", "Skipping null recipe in Recipes.json");
                        continue;
                    }

                    // Validate recipe with error recovery
                    if (DataValidator.ValidateRecipe(recipe, out List<string> recipeErrors))
                    {
                        // Additional validation: check if input/output items exist
                        bool hasValidItems = ValidateRecipeItemReferences(recipe);
                        
                        if (hasValidItems)
                        {
                            if (_recipes.ContainsKey(recipe.Id))
                            {
                                Logger.LogWarning("ItemDatabase", $"Duplicate recipe ID '{recipe.Id}' found, using latest definition");
                            }
                            
                            _recipes[recipe.Id] = recipe;
                            
                            // Index by output item
                            string outputId = recipe.Output.Id;
                            if (!_recipesByOutput.ContainsKey(outputId))
                            {
                                _recipesByOutput[outputId] = new List<Recipe>();
                            }
                            _recipesByOutput[outputId].Add(recipe);
                            
                            validRecipes++;
                            
                            if (recipeErrors.Count > 0)
                            {
                                Logger.LogWarning("ItemDatabase", $"Recipe '{recipe.Id}' had correctable issues: {string.Join(", ", recipeErrors)}");
                            }
                        }
                        else
                        {
                            invalidRecipes++;
                            Logger.LogError("ItemDatabase", $"Recipe '{recipe.Id}' references non-existent items");
                        }
                    }
                    else
                    {
                        // Recipe has critical errors that couldn't be fixed
                        invalidRecipes++;
                        string errorMsg = $"Recipe '{recipe?.Id ?? "unknown"}' has critical errors: {string.Join(", ", recipeErrors)}";
                        Logger.LogError("ItemDatabase", errorMsg);
                        _loadingErrors.Add(errorMsg);
                    }
                }

                Logger.LogInfo("ItemDatabase", $"Recipes loaded - Valid: {validRecipes}, Invalid: {invalidRecipes}, Total: {_recipes.Count}");
                
                if (_recipes.Count == 0)
                {
                    Logger.LogWarning("ItemDatabase", "No valid recipes loaded, using fallback data");
                    LoadFallbackRecipes();
                }
                else
                {
                    _recipesLoadedSuccessfully = true;
                }
            }
            catch (Exception ex)
            {
                string error = $"Unexpected error loading Recipes.json: {ex.Message}";
                Logger.LogException("ItemDatabase", ex, "LoadRecipesFromJsonWithValidation");
                _loadingErrors.Add(error);
                LoadFallbackRecipes();
            }
        }

        /// <summary>
        /// Validates that recipe input and output items exist in the item database
        /// </summary>
        private bool ValidateRecipeItemReferences(Recipe recipe)
        {
            // Check output item exists
            if (!_items.ContainsKey(recipe.Output.Id))
            {
                Logger.LogWarning("ItemDatabase", $"Recipe '{recipe.Id}' output item '{recipe.Output.Id}' not found in item database");
                return false;
            }

            // Check all input items exist
            foreach (var input in recipe.Inputs)
            {
                if (!_items.ContainsKey(input.Id))
                {
                    Logger.LogWarning("ItemDatabase", $"Recipe '{recipe.Id}' input item '{input.Id}' not found in item database");
                    return false;
                }
            }

            return true;
        }

        /// <summary>
        /// Loads fallback items when main data fails to load
        /// </summary>
        private void LoadFallbackItems()
        {
            Logger.LogWarning("ItemDatabase", "Loading fallback items");
            
            var fallbackItems = DataValidator.CreateFallbackItems();
            _items.Clear();
            
            foreach (var item in fallbackItems)
            {
                _items[item.Id] = item;
            }
            
            _fallbackDataLoaded = true;
            EmitSignal(SignalName.FallbackDataLoaded, "Items data corrupted or missing");
        }

        /// <summary>
        /// Loads fallback recipes when main data fails to load
        /// </summary>
        private void LoadFallbackRecipes()
        {
            Logger.LogWarning("ItemDatabase", "Loading fallback recipes");
            
            var fallbackRecipes = DataValidator.CreateFallbackRecipes();
            _recipes.Clear();
            _recipesByOutput.Clear();
            
            foreach (var recipe in fallbackRecipes)
            {
                _recipes[recipe.Id] = recipe;
                
                string outputId = recipe.Output.Id;
                if (!_recipesByOutput.ContainsKey(outputId))
                {
                    _recipesByOutput[outputId] = new List<Recipe>();
                }
                _recipesByOutput[outputId].Add(recipe);
            }
            
            _fallbackDataLoaded = true;
            EmitSignal(SignalName.FallbackDataLoaded, "Recipes data corrupted or missing");
        }

        /// <summary>
        /// Ensures critical items exist for basic game functionality
        /// </summary>
        private void EnsureCriticalItemsExist()
        {
            var criticalItemTypes = new[] { "consumable" };
            bool hasCriticalItems = _items.Values.Any(item => criticalItemTypes.Contains(item.Type));
            
            if (!hasCriticalItems)
            {
                Logger.LogWarning("ItemDatabase", "No critical items found, adding fallback consumables");
                
                var fallbackItems = DataValidator.CreateFallbackItems();
                foreach (var item in fallbackItems)
                {
                    if (!_items.ContainsKey(item.Id))
                    {
                        _items[item.Id] = item;
                    }
                }
            }
        }

        /// <summary>
        /// Verifies data integrity after loading
        /// </summary>
        private void VerifyDataIntegrity()
        {
            Logger.LogInfo("ItemDatabase", "Verifying data integrity");
            
            // Check for orphaned recipes (recipes that reference non-existent items)
            var orphanedRecipes = new List<string>();
            
            foreach (var recipe in _recipes.Values)
            {
                if (!_items.ContainsKey(recipe.Output.Id))
                {
                    orphanedRecipes.Add(recipe.Id);
                    continue;
                }
                
                foreach (var input in recipe.Inputs)
                {
                    if (!_items.ContainsKey(input.Id))
                    {
                        orphanedRecipes.Add(recipe.Id);
                        break;
                    }
                }
            }
            
            // Remove orphaned recipes
            foreach (var recipeId in orphanedRecipes)
            {
                var recipe = _recipes[recipeId];
                _recipes.Remove(recipeId);
                
                // Remove from output index
                if (_recipesByOutput.ContainsKey(recipe.Output.Id))
                {
                    _recipesByOutput[recipe.Output.Id].RemoveAll(r => r.Id == recipeId);
                    if (_recipesByOutput[recipe.Output.Id].Count == 0)
                    {
                        _recipesByOutput.Remove(recipe.Output.Id);
                    }
                }
                
                Logger.LogWarning("ItemDatabase", $"Removed orphaned recipe '{recipeId}'");
            }
            
            Logger.LogInfo("ItemDatabase", $"Data integrity verification complete - Removed {orphanedRecipes.Count} orphaned recipes");
        }

        // Public API methods with error handling

        /// <summary>
        /// Retrieves an item definition by ID with error handling
        /// </summary>
        public Item GetItem(string id)
        {
            if (string.IsNullOrEmpty(id))
            {
                Logger.LogWarning("ItemDatabase", "GetItem called with null or empty ID");
                return null;
            }

            if (_items.TryGetValue(id, out Item item))
            {
                return item;
            }
            
            Logger.LogDebug("ItemDatabase", $"Item '{id}' not found in database");
            return null;
        }

        /// <summary>
        /// Retrieves a recipe by ID with error handling
        /// </summary>
        public Recipe GetRecipe(string id)
        {
            if (string.IsNullOrEmpty(id))
            {
                Logger.LogWarning("ItemDatabase", "GetRecipe called with null or empty ID");
                return null;
            }

            if (_recipes.TryGetValue(id, out Recipe recipe))
            {
                return recipe;
            }
            
            Logger.LogDebug("ItemDatabase", $"Recipe '{id}' not found in database");
            return null;
        }

        /// <summary>
        /// Gets all recipes that produce a specific item
        /// </summary>
        public List<Recipe> GetRecipesByOutput(string itemId)
        {
            if (string.IsNullOrEmpty(itemId))
            {
                Logger.LogWarning("ItemDatabase", "GetRecipesByOutput called with null or empty itemId");
                return new List<Recipe>();
            }

            return _recipesByOutput.TryGetValue(itemId, out List<Recipe> recipes) 
                ? new List<Recipe>(recipes) // Return a copy
                : new List<Recipe>();
        }

        /// <summary>
        /// Gets all loaded items (defensive copy)
        /// </summary>
        public Dictionary<string, Item> GetAllItems()
        {
            return new Dictionary<string, Item>(_items);
        }

        /// <summary>
        /// Gets all loaded recipes (defensive copy)
        /// </summary>
        public Dictionary<string, Recipe> GetAllRecipes()
        {
            return new Dictionary<string, Recipe>(_recipes);
        }

        /// <summary>
        /// Checks if an item exists in the database
        /// </summary>
        public bool HasItem(string id)
        {
            return !string.IsNullOrEmpty(id) && _items.ContainsKey(id);
        }

        /// <summary>
        /// Checks if a recipe exists in the database
        /// </summary>
        public bool HasRecipe(string id)
        {
            return !string.IsNullOrEmpty(id) && _recipes.ContainsKey(id);
        }

        /// <summary>
        /// Gets loading status information
        /// </summary>
        public DatabaseStatus GetStatus()
        {
            return new DatabaseStatus
            {
                ItemsLoadedSuccessfully = _itemsLoadedSuccessfully,
                RecipesLoadedSuccessfully = _recipesLoadedSuccessfully,
                FallbackDataLoaded = _fallbackDataLoaded,
                ItemCount = _items.Count,
                RecipeCount = _recipes.Count,
                LoadingErrors = new List<string>(_loadingErrors)
            };
        }

        /// <summary>
        /// Forces a reload of all data
        /// </summary>
        public void ReloadData()
        {
            Logger.LogInfo("ItemDatabase", "Forcing data reload");
            
            _loadingErrors.Clear();
            _itemsLoadedSuccessfully = false;
            _recipesLoadedSuccessfully = false;
            _fallbackDataLoaded = false;
            
            LoadItemsFromJsonWithValidation();
            LoadRecipesFromJsonWithValidation();
            VerifyDataIntegrity();
        }

        // Properties for backward compatibility
        public int ItemCount => _items.Count;
        public int RecipeCount => _recipes.Count;
    }

    /// <summary>
    /// Status information for the ItemDatabase
    /// </summary>
    public class DatabaseStatus
    {
        public bool ItemsLoadedSuccessfully { get; set; }
        public bool RecipesLoadedSuccessfully { get; set; }
        public bool FallbackDataLoaded { get; set; }
        public int ItemCount { get; set; }
        public int RecipeCount { get; set; }
        public List<string> LoadingErrors { get; set; } = new List<string>();
    }
}