using Godot;
using System;
using System.Collections.Generic;
using System.Linq;

namespace SurvivalLooterShooter
{
    /// <summary>
    /// Represents a specialized crafting station that can craft specific recipes
    /// </summary>
    public partial class CraftingStation : Node2D
    {
        [Export] public string StationId { get; set; }
        [Export] public string StationType { get; set; }
        [Export] public int StationLevel { get; set; } = 1;
        [Export] public float EfficiencyMultiplier { get; set; } = 1.0f;
        [Export] public int MaxQueueSize { get; set; } = 5;
        
        private Structure _structure;
        private List<CraftingQueueItem> _craftingQueue = new List<CraftingQueueItem>();
        private CraftingQueueItem _currentCrafting;
        private Timer _craftingTimer;
        private Area2D _interactionArea;
        
        // Events
        [Signal]
        public delegate void CraftingStartedEventHandler(CraftingStation station, string recipeId);
        
        [Signal]
        public delegate void CraftingCompletedEventHandler(CraftingStation station, string recipeId, string outputItemId, int outputQuantity);
        
        [Signal]
        public delegate void CraftingQueueChangedEventHandler(CraftingStation station, int queueSize);
        
        [Signal]
        public delegate void StationUpgradedEventHandler(CraftingStation station, int newLevel);

        public bool IsCrafting => _currentCrafting != null;
        public int QueueSize => _craftingQueue.Count;
        public List<CraftingQueueItem> CraftingQueue => new List<CraftingQueueItem>(_craftingQueue);
        public CraftingQueueItem CurrentCrafting => _currentCrafting;

        public override void _Ready()
        {
            // Set up crafting timer
            _craftingTimer = new Timer();
            _craftingTimer.WaitTime = 1.0f;
            _craftingTimer.OneShot = true;
            _craftingTimer.Timeout += OnCraftingTimerTimeout;
            AddChild(_craftingTimer);
            
            // Set up interaction area
            _interactionArea = GetNode<Area2D>("InteractionArea");
            if (_interactionArea != null)
            {
                _interactionArea.InputEvent += OnInteractionAreaInputEvent;
            }
            
            // Get structure reference
            _structure = GetParent<Structure>();
            
            Logger.LogInfo("CraftingStation", $"Crafting station {StationId} initialized");
        }

        /// <summary>
        /// Initializes the crafting station with specific parameters
        /// </summary>
        public void Initialize(string stationId, string stationType, int level = 1)
        {
            StationId = stationId;
            StationType = stationType;
            StationLevel = level;
            UpdateStationProperties();
        }

        /// <summary>
        /// Gets all recipes that this station can craft
        /// </summary>
        public List<Recipe> GetAvailableRecipes()
        {
            if (ItemDatabase.Instance == null)
                return new List<Recipe>();

            var allRecipes = ItemDatabase.Instance.GetAllRecipes();
            var stationRecipes = new List<Recipe>();

            foreach (var recipe in allRecipes.Values)
            {
                if (CanCraftRecipe(recipe))
                {
                    stationRecipes.Add(recipe);
                }
            }

            return stationRecipes;
        }

        /// <summary>
        /// Checks if this station can craft a specific recipe
        /// </summary>
        public bool CanCraftRecipe(Recipe recipe)
        {
            if (recipe == null || !recipe.IsValid())
                return false;

            // Check if recipe has station requirement
            if (recipe is AdvancedRecipe advancedRecipe)
            {
                // Check station type requirement
                if (!string.IsNullOrEmpty(advancedRecipe.RequiredStation) && 
                    advancedRecipe.RequiredStation != StationType)
                    return false;

                // Check station level requirement
                if (advancedRecipe.RequiredStationLevel > StationLevel)
                    return false;
            }

            return true;
        }

        /// <summary>
        /// Adds a recipe to the crafting queue
        /// </summary>
        public bool QueueRecipe(Recipe recipe, int quantity = 1, Inventory inventory = null)
        {
            if (!CanCraftRecipe(recipe))
                return false;

            if (_craftingQueue.Count >= MaxQueueSize)
                return false;

            // Check if we have materials for at least one craft
            if (inventory != null && !HasMaterialsForRecipe(recipe, inventory))
                return false;

            // Add to queue
            var queueItem = new CraftingQueueItem
            {
                Recipe = recipe,
                Quantity = quantity,
                RemainingQuantity = quantity,
                QueuedTime = Time.GetUnixTimeFromSystem()
            };

            _craftingQueue.Add(queueItem);
            EmitSignal(SignalName.CraftingQueueChanged, this, _craftingQueue.Count);

            // Start crafting if not already crafting
            if (!IsCrafting)
            {
                StartNextCrafting(inventory);
            }

            Logger.LogInfo("CraftingStation", $"Queued recipe {recipe.Id} x{quantity} at station {StationId}");
            return true;
        }

        /// <summary>
        /// Removes a recipe from the crafting queue
        /// </summary>
        public bool RemoveFromQueue(int queueIndex)
        {
            if (queueIndex < 0 || queueIndex >= _craftingQueue.Count)
                return false;

            _craftingQueue.RemoveAt(queueIndex);
            EmitSignal(SignalName.CraftingQueueChanged, this, _craftingQueue.Count);

            Logger.LogInfo("CraftingStation", $"Removed item from queue at index {queueIndex}");
            return true;
        }

        /// <summary>
        /// Clears the entire crafting queue
        /// </summary>
        public void ClearQueue()
        {
            _craftingQueue.Clear();
            EmitSignal(SignalName.CraftingQueueChanged, this, _craftingQueue.Count);
            Logger.LogInfo("CraftingStation", $"Cleared crafting queue for station {StationId}");
        }

        /// <summary>
        /// Upgrades the crafting station to the next level
        /// </summary>
        public bool UpgradeStation(Inventory inventory = null)
        {
            if (_structure == null || !_structure.CanUpgrade())
                return false;

            // Check upgrade materials if inventory provided
            if (inventory != null)
            {
                var upgradeCost = _structure.GetUpgradeCost();
                foreach (var cost in upgradeCost)
                {
                    if (!inventory.HasItem(cost.Item, cost.Amount))
                        return false;
                }

                // Consume upgrade materials
                foreach (var cost in upgradeCost)
                {
                    inventory.RemoveItem(cost.Item, cost.Amount);
                }
            }

            // Upgrade structure
            if (_structure.TryUpgrade())
            {
                StationLevel++;
                UpdateStationProperties();
                EmitSignal(SignalName.StationUpgraded, this, StationLevel);
                Logger.LogInfo("CraftingStation", $"Upgraded station {StationId} to level {StationLevel}");
                return true;
            }

            return false;
        }

        /// <summary>
        /// Gets the estimated time remaining for current crafting
        /// </summary>
        public float GetCraftingTimeRemaining()
        {
            if (!IsCrafting || _craftingTimer == null)
                return 0f;

            return (float)_craftingTimer.TimeLeft;
        }

        /// <summary>
        /// Gets the total estimated time for all items in queue
        /// </summary>
        public float GetTotalQueueTime()
        {
            float totalTime = 0f;

            // Add current crafting time
            if (IsCrafting && _currentCrafting != null)
            {
                totalTime += GetCraftingTimeRemaining();
                totalTime += (_currentCrafting.RemainingQuantity - 1) * GetAdjustedCraftingTime(_currentCrafting.Recipe);
            }

            // Add queue times
            foreach (var queueItem in _craftingQueue)
            {
                totalTime += queueItem.RemainingQuantity * GetAdjustedCraftingTime(queueItem.Recipe);
            }

            return totalTime;
        }

        private void StartNextCrafting(Inventory inventory)
        {
            if (IsCrafting || _craftingQueue.Count == 0)
                return;

            var nextItem = _craftingQueue[0];
            
            // Check if we have materials
            if (inventory != null && !HasMaterialsForRecipe(nextItem.Recipe, inventory))
            {
                Logger.LogWarning("CraftingStation", $"Insufficient materials for recipe {nextItem.Recipe.Id}");
                return;
            }

            // Consume materials if inventory provided
            if (inventory != null)
            {
                ConsumeMaterials(nextItem.Recipe, inventory);
            }

            // Start crafting
            _currentCrafting = nextItem;
            _craftingQueue.RemoveAt(0);
            
            float craftingTime = GetAdjustedCraftingTime(nextItem.Recipe);
            _craftingTimer.WaitTime = craftingTime;
            _craftingTimer.Start();

            EmitSignal(SignalName.CraftingStarted, this, nextItem.Recipe.Id);
            EmitSignal(SignalName.CraftingQueueChanged, this, _craftingQueue.Count);

            Logger.LogInfo("CraftingStation", $"Started crafting {nextItem.Recipe.Id} at station {StationId} (time: {craftingTime}s)");
        }

        private void OnCraftingTimerTimeout()
        {
            if (_currentCrafting == null)
                return;

            var recipe = _currentCrafting.Recipe;
            
            // Add output to inventory (if available) or drop in world
            var inventory = GetNode<Inventory>("/root/GameManager/Inventory");
            if (inventory != null && inventory.CanAddItem(recipe.Output.Id, recipe.Output.Amount))
            {
                inventory.AddItem(recipe.Output.Id, recipe.Output.Amount);
            }
            else
            {
                // Drop items in world near the station
                DropItemsInWorld(recipe.Output.Id, recipe.Output.Amount);
            }

            EmitSignal(SignalName.CraftingCompleted, this, recipe.Id, recipe.Output.Id, recipe.Output.Amount);
            Logger.LogInfo("CraftingStation", $"Completed crafting {recipe.Output.Id} x{recipe.Output.Amount}");

            // Reduce remaining quantity
            _currentCrafting.RemainingQuantity--;

            // Check if we need to craft more of this recipe
            if (_currentCrafting.RemainingQuantity > 0)
            {
                // Continue crafting the same recipe
                float craftingTime = GetAdjustedCraftingTime(recipe);
                _craftingTimer.WaitTime = craftingTime;
                _craftingTimer.Start();
            }
            else
            {
                // Finished with this recipe, move to next
                _currentCrafting = null;
                StartNextCrafting(inventory);
            }
        }

        private bool HasMaterialsForRecipe(Recipe recipe, Inventory inventory)
        {
            foreach (var input in recipe.Inputs)
            {
                if (!inventory.HasItem(input.Id, input.Amount))
                    return false;
            }
            return true;
        }

        private void ConsumeMaterials(Recipe recipe, Inventory inventory)
        {
            foreach (var input in recipe.Inputs)
            {
                inventory.RemoveItem(input.Id, input.Amount);
            }
        }

        private float GetAdjustedCraftingTime(Recipe recipe)
        {
            float baseTime = recipe.CraftingTime;
            return baseTime / EfficiencyMultiplier;
        }

        private void UpdateStationProperties()
        {
            // Update efficiency based on level
            EfficiencyMultiplier = 1.0f + (StationLevel - 1) * 0.25f; // 25% efficiency increase per level
            
            // Update queue size based on level
            MaxQueueSize = 5 + (StationLevel - 1) * 2; // +2 queue slots per level
        }

        private void DropItemsInWorld(string itemId, int quantity)
        {
            // Create item pickup near the station
            var itemPickupScene = GD.Load<PackedScene>("res://Scenes/ItemPickup.tscn");
            if (itemPickupScene != null)
            {
                var itemPickup = itemPickupScene.Instantiate<ItemPickup>();
                GetTree().CurrentScene.AddChild(itemPickup);
                
                // Position near the station with some randomness
                var dropPosition = GlobalPosition + new Vector2(
                    GD.RandRange(-50, 50),
                    GD.RandRange(-50, 50)
                );
                itemPickup.GlobalPosition = dropPosition;
                itemPickup.SetItemData(itemId, quantity);
            }
        }

        private void OnInteractionAreaInputEvent(Node viewport, InputEvent @event, long shapeIdx)
        {
            if (@event is InputEventMouseButton mouseEvent && 
                mouseEvent.Pressed && 
                mouseEvent.ButtonIndex == MouseButton.Left)
            {
                // Open crafting station UI
                var craftingStationUI = GetNode<CraftingStationUI>("/root/GameManager/UI/CraftingStationUI");
                if (craftingStationUI != null)
                {
                    craftingStationUI.OpenStation(this);
                }
            }
        }
    }

    /// <summary>
    /// Represents an item in the crafting queue
    /// </summary>
    public class CraftingQueueItem
    {
        public Recipe Recipe { get; set; }
        public int Quantity { get; set; }
        public int RemainingQuantity { get; set; }
        public double QueuedTime { get; set; }
        
        public float Progress => Quantity > 0 ? (float)(Quantity - RemainingQuantity) / Quantity : 0f;
    }
}