using Godot;
using System;
using System.Collections.Generic;
using System.Linq;

namespace SurvivalLooterShooter
{
    public enum SpecialEventType
    {
        Blood<PERSON><PERSON>,
        MeteorShower,
        BossInvasion,
        ResourceBoom,
        TreasureHunt,
        Plague,
        SuperStorm,
        AncientRuins
    }

    [System.Serializable]
    public class SpecialEvent
    {
        public string EventId { get; set; }
        public SpecialEventType Type { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public float Duration { get; set; }
        public bool IsActive { get; set; }
        public DateTime StartTime { get; set; }
        public Dictionary<string, object> Parameters { get; set; } = new();
    }

    public partial class SpecialEventManager : Node
    {
        public static SpecialEventManager Instance { get; private set; }

        [Export] public float EventCheckInterval { get; set; } = 600f; // Check every 10 minutes
        [Export] public float BaseEventChance { get; set; } = 0.1f; // 10% chance per check

        private List<SpecialEvent> _activeEvents = new();
        private Timer _eventCheckTimer;
        private Random _random = new();

        public override void _Ready()
        {
            if (Instance == null)
            {
                Instance = this;
                SetupEventTimer();
            }
            else
            {
                QueueFree();
            }
        }

        private void SetupEventTimer()
        {
            _eventCheckTimer = new Timer();
            _eventCheckTimer.WaitTime = EventCheckInterval;
            _eventCheckTimer.Timeout += CheckForSpecialEvents;
            _eventCheckTimer.Autostart = true;
            AddChild(_eventCheckTimer);
        }

        private void CheckForSpecialEvents()
        {
            // Don't start new events if one is already active
            if (_activeEvents.Any(e => e.IsActive)) return;

            if (_random.NextDouble() < BaseEventChance)
            {
                var eventType = (SpecialEventType)_random.Next(Enum.GetValues<SpecialEventType>().Length);
                StartSpecialEvent(eventType);
            }
        }

        public void StartSpecialEvent(SpecialEventType eventType)
        {
            var specialEvent = CreateSpecialEvent(eventType);
            if (specialEvent != null)
            {
                _activeEvents.Add(specialEvent);
                specialEvent.IsActive = true;
                specialEvent.StartTime = DateTime.Now;

                ExecuteEventStart(specialEvent);

                // Schedule event end
                GetTree().CreateTimer(specialEvent.Duration).Timeout += () => EndSpecialEvent(specialEvent);

                GD.Print($"Special event started: {specialEvent.Name}");
                EventBus.Instance?.EmitSignal(EventBus.SignalName.SpecialEventStarted, specialEvent.EventId);
            }
        }

        private SpecialEvent CreateSpecialEvent(SpecialEventType type)
        {
            var specialEvent = new SpecialEvent
            {
                EventId = Guid.NewGuid().ToString(),
                Type = type
            };

            switch (type)
            {
                case SpecialEventType.BloodMoon:
                    specialEvent.Name = "Blood Moon";
                    specialEvent.Description = "The moon turns red, spawning more dangerous enemies with better loot.";
                    specialEvent.Duration = 300f; // 5 minutes
                    specialEvent.Parameters["enemy_spawn_multiplier"] = 3.0f;
                    specialEvent.Parameters["enemy_damage_multiplier"] = 1.5f;
                    specialEvent.Parameters["loot_quality_multiplier"] = 2.0f;
                    break;

                case SpecialEventType.MeteorShower:
                    specialEvent.Name = "Meteor Shower";
                    specialEvent.Description = "Meteors rain from the sky, bringing rare materials but also danger.";
                    specialEvent.Duration = 180f; // 3 minutes
                    specialEvent.Parameters["meteor_spawn_rate"] = 5f; // Every 5 seconds
                    specialEvent.Parameters["rare_material_chance"] = 0.8f;
                    break;

                case SpecialEventType.BossInvasion:
                    specialEvent.Name = "Boss Invasion";
                    specialEvent.Description = "Multiple boss enemies appear across the world.";
                    specialEvent.Duration = 600f; // 10 minutes
                    specialEvent.Parameters["boss_count"] = 3;
                    specialEvent.Parameters["boss_health_multiplier"] = 0.7f; // Slightly weaker when multiple
                    break;

                case SpecialEventType.ResourceBoom:
                    specialEvent.Name = "Resource Boom";
                    specialEvent.Description = "Resource nodes yield significantly more materials.";
                    specialEvent.Duration = 900f; // 15 minutes
                    specialEvent.Parameters["resource_multiplier"] = 3.0f;
                    specialEvent.Parameters["rare_resource_chance"] = 0.3f;
                    break;

                case SpecialEventType.TreasureHunt:
                    specialEvent.Name = "Treasure Hunt";
                    specialEvent.Description = "Treasure chests appear throughout the world for a limited time.";
                    specialEvent.Duration = 450f; // 7.5 minutes
                    specialEvent.Parameters["treasure_count"] = 10;
                    specialEvent.Parameters["treasure_quality"] = "legendary";
                    break;

                case SpecialEventType.Plague:
                    specialEvent.Name = "Plague";
                    specialEvent.Description = "A mysterious plague affects survival stats but increases skill gain.";
                    specialEvent.Duration = 720f; // 12 minutes
                    specialEvent.Parameters["stat_decay_multiplier"] = 2.0f;
                    specialEvent.Parameters["skill_xp_multiplier"] = 2.5f;
                    break;

                case SpecialEventType.SuperStorm:
                    specialEvent.Name = "Super Storm";
                    specialEvent.Description = "An intense storm with extreme weather effects.";
                    specialEvent.Duration = 240f; // 4 minutes
                    specialEvent.Parameters["visibility_reduction"] = 0.3f;
                    specialEvent.Parameters["movement_penalty"] = 0.5f;
                    specialEvent.Parameters["lightning_damage"] = 50f;
                    break;

                case SpecialEventType.AncientRuins:
                    specialEvent.Name = "Ancient Ruins Revealed";
                    specialEvent.Description = "Ancient ruins emerge from the ground, containing powerful artifacts.";
                    specialEvent.Duration = 1800f; // 30 minutes
                    specialEvent.Parameters["ruins_count"] = 3;
                    specialEvent.Parameters["artifact_chance"] = 0.6f;
                    break;

                default:
                    return null;
            }

            return specialEvent;
        }

        private void ExecuteEventStart(SpecialEvent specialEvent)
        {
            switch (specialEvent.Type)
            {
                case SpecialEventType.BloodMoon:
                    StartBloodMoon(specialEvent);
                    break;
                case SpecialEventType.MeteorShower:
                    StartMeteorShower(specialEvent);
                    break;
                case SpecialEventType.BossInvasion:
                    StartBossInvasion(specialEvent);
                    break;
                case SpecialEventType.ResourceBoom:
                    StartResourceBoom(specialEvent);
                    break;
                case SpecialEventType.TreasureHunt:
                    StartTreasureHunt(specialEvent);
                    break;
                case SpecialEventType.Plague:
                    StartPlague(specialEvent);
                    break;
                case SpecialEventType.SuperStorm:
                    StartSuperStorm(specialEvent);
                    break;
                case SpecialEventType.AncientRuins:
                    StartAncientRuins(specialEvent);
                    break;
            }
        }

        private void StartBloodMoon(SpecialEvent specialEvent)
        {
            // Modify lighting to red tint
            if (LightingManager.Instance != null)
            {
                LightingManager.Instance.SetAmbientColor(new Color(0.8f, 0.2f, 0.2f));
            }

            // Increase enemy spawn rates
            if (EnemyManager.Instance != null)
            {
                var multiplier = (float)specialEvent.Parameters["enemy_spawn_multiplier"];
                EnemyManager.Instance.SetSpawnRateMultiplier(multiplier);
            }
        }

        private void StartMeteorShower(SpecialEvent specialEvent)
        {
            var spawnRate = (float)specialEvent.Parameters["meteor_spawn_rate"];
            var meteorTimer = new Timer();
            meteorTimer.WaitTime = spawnRate;
            meteorTimer.Timeout += () => SpawnMeteor(specialEvent);
            meteorTimer.Autostart = true;
            AddChild(meteorTimer);

            // Clean up timer when event ends
            GetTree().CreateTimer(specialEvent.Duration).Timeout += () => meteorTimer.QueueFree();
        }

        private void SpawnMeteor(SpecialEvent specialEvent)
        {
            // Spawn meteor at random location
            var worldBounds = WorldManager.Instance?.GetWorldBounds() ?? new Rect2(-1000, -1000, 2000, 2000);
            var meteorPos = new Vector2(
                _random.Next((int)worldBounds.Position.X, (int)worldBounds.End.X),
                _random.Next((int)worldBounds.Position.Y, (int)worldBounds.End.Y)
            );

            // Create meteor impact effect and drop rare materials
            var rareChance = (float)specialEvent.Parameters["rare_material_chance"];
            if (_random.NextDouble() < rareChance)
            {
                LootDropSystem.Instance?.DropItem("meteor_ore", meteorPos, 1.0f);
            }

            GD.Print($"Meteor impact at {meteorPos}");
        }

        private void StartBossInvasion(SpecialEvent specialEvent)
        {
            var bossCount = (int)specialEvent.Parameters["boss_count"];
            var bossTypes = Enum.GetValues<BossType>();

            for (int i = 0; i < bossCount; i++)
            {
                var bossType = bossTypes[_random.Next(bossTypes.Length)];
                var spawnPos = GetRandomSpawnPosition();

                // Spawn boss with reduced health for multiple boss event
                var boss = EnemyManager.Instance?.SpawnBoss(bossType, spawnPos) as BossEnemy;
                if (boss != null)
                {
                    var healthMultiplier = (float)specialEvent.Parameters["boss_health_multiplier"];
                    boss.MaxHealth *= healthMultiplier;
                    boss.CurrentHealth = boss.MaxHealth;
                }
            }
        }

        private void StartResourceBoom(SpecialEvent specialEvent)
        {
            // Notify resource harvesting system about the boom
            var multiplier = (float)specialEvent.Parameters["resource_multiplier"];
            ResourceHarvestingSystem.Instance?.SetHarvestMultiplier(multiplier);
        }

        private void StartTreasureHunt(SpecialEvent specialEvent)
        {
            var treasureCount = (int)specialEvent.Parameters["treasure_count"];

            for (int i = 0; i < treasureCount; i++)
            {
                var treasurePos = GetRandomSpawnPosition();
                SpawnTreasureChest(treasurePos, (string)specialEvent.Parameters["treasure_quality"]);
            }
        }

        private void StartPlague(SpecialEvent specialEvent)
        {
            // Modify survival stats decay rate
            var decayMultiplier = (float)specialEvent.Parameters["stat_decay_multiplier"];
            SurvivalStatsSystem.Instance?.SetDecayMultiplier(decayMultiplier);

            // Increase skill XP gain
            var xpMultiplier = (float)specialEvent.Parameters["skill_xp_multiplier"];
            SkillManager.Instance?.SetXPMultiplier(xpMultiplier);
        }

        private void StartSuperStorm(SpecialEvent specialEvent)
        {
            // Set extreme weather
            WeatherManager.Instance?.SetWeather(WeatherManager.WeatherType.Storm, 2.0f); // Intensity 2.0 for super storm

            // Apply movement penalties and visibility reduction
            var visibilityReduction = (float)specialEvent.Parameters["visibility_reduction"];
            var movementPenalty = (float)specialEvent.Parameters["movement_penalty"];

            // These would be applied through the weather system
            GD.Print($"Super storm: Visibility {visibilityReduction}, Movement penalty {movementPenalty}");
        }

        private void StartAncientRuins(SpecialEvent specialEvent)
        {
            var ruinsCount = (int)specialEvent.Parameters["ruins_count"];

            for (int i = 0; i < ruinsCount; i++)
            {
                var ruinsPos = GetRandomSpawnPosition();
                SpawnAncientRuins(ruinsPos, specialEvent);
            }
        }

        private Vector2 GetRandomSpawnPosition()
        {
            var worldBounds = WorldManager.Instance?.GetWorldBounds() ?? new Rect2(-1000, -1000, 2000, 2000);
            return new Vector2(
                _random.Next((int)worldBounds.Position.X, (int)worldBounds.End.X),
                _random.Next((int)worldBounds.Position.Y, (int)worldBounds.End.Y)
            );
        }

        private void SpawnTreasureChest(Vector2 position, string quality)
        {
            // This would spawn a treasure chest scene at the position
            GD.Print($"Treasure chest ({quality}) spawned at {position}");
        }

        private void SpawnAncientRuins(Vector2 position, SpecialEvent specialEvent)
        {
            // This would spawn ancient ruins POI at the position
            var artifactChance = (float)specialEvent.Parameters["artifact_chance"];
            GD.Print($"Ancient ruins spawned at {position} with {artifactChance * 100}% artifact chance");
        }

        private void EndSpecialEvent(SpecialEvent specialEvent)
        {
            specialEvent.IsActive = false;
            ExecuteEventEnd(specialEvent);

            GD.Print($"Special event ended: {specialEvent.Name}");
            EventBus.Instance?.EmitSignal(EventBus.SignalName.SpecialEventEnded, specialEvent.EventId);

            _activeEvents.Remove(specialEvent);
        }

        private void ExecuteEventEnd(SpecialEvent specialEvent)
        {
            switch (specialEvent.Type)
            {
                case SpecialEventType.BloodMoon:
                    EndBloodMoon();
                    break;
                case SpecialEventType.ResourceBoom:
                    EndResourceBoom();
                    break;
                case SpecialEventType.Plague:
                    EndPlague();
                    break;
                case SpecialEventType.SuperStorm:
                    EndSuperStorm();
                    break;
            }
        }

        private void EndBloodMoon()
        {
            // Restore normal lighting
            LightingManager.Instance?.RestoreNormalLighting();

            // Reset enemy spawn rates
            EnemyManager.Instance?.SetSpawnRateMultiplier(1.0f);
        }

        private void EndResourceBoom()
        {
            // Reset resource harvest multiplier
            ResourceHarvestingSystem.Instance?.SetHarvestMultiplier(1.0f);
        }

        private void EndPlague()
        {
            // Reset survival stats decay rate
            SurvivalStatsSystem.Instance?.SetDecayMultiplier(1.0f);

            // Reset skill XP multiplier
            SkillManager.Instance?.SetXPMultiplier(1.0f);
        }

        private void EndSuperStorm()
        {
            // Return to normal weather
            WeatherManager.Instance?.SetWeather(WeatherManager.WeatherType.Clear, 1.0f);
        }

        public List<SpecialEvent> GetActiveEvents() => new(_activeEvents);

        public void ForceStartEvent(SpecialEventType eventType)
        {
            StartSpecialEvent(eventType);
        }
    }
}
