using Godot;
using System;
using System.Collections.Generic;
using System.Linq;

namespace SurvivalLooterShooter
{
    /// <summary>
    /// Tests for biome-specific enemy spawning system
    /// Validates spawn rates, level scaling, day/night effects, and migration
    /// </summary>
    public partial class BiomeEnemySpawningTests : Node
    {
        private EnemyManager _enemyManager;
        private DayNightCycle _dayNightCycle;
        private Timer _testTimer;
        private int _testPhase = 0;
        private List<string> _testResults = new List<string>();

        public override void _Ready()
        {
            Name = "BiomeEnemySpawningTests";
            Logger.LogInfo("BiomeEnemySpawningTests", "Starting biome-specific enemy spawning tests");

            // Setup test timer
            _testTimer = new Timer();
            _testTimer.WaitTime = 2.0f;
            _testTimer.Timeout += RunNextTest;
            AddChild(_testTimer);

            // Get system references
            _enemyManager = GetNode<EnemyManager>("/root/Main/EnemyManager");
            _dayNightCycle = GetNode<DayNightCycle>("/root/Main/DayNightCycle");

            if (_enemyManager == null)
            {
                Logger.LogError("BiomeEnemySpawningTests", "EnemyManager not found - creating for test");
                _enemyManager = new EnemyManager();
                GetTree().CurrentScene.AddChild(_enemyManager);
            }

            if (_dayNightCycle == null)
            {
                Logger.LogError("BiomeEnemySpawningTests", "DayNightCycle not found - creating for test");
                _dayNightCycle = new DayNightCycle();
                GetTree().CurrentScene.AddChild(_dayNightCycle);
            }

            // Start tests
            _testTimer.Start();
        }

        private void RunNextTest()
        {
            switch (_testPhase)
            {
                case 0:
                    TestBiomeSpawnDataLoading();
                    break;
                case 1:
                    TestDayTimeSpawning();
                    break;
                case 2:
                    TestNightTimeSpawning();
                    break;
                case 3:
                    TestPlayerLevelScaling();
                    break;
                case 4:
                    TestBossSpawning();
                    break;
                case 5:
                    TestEnemyMigration();
                    break;
                case 6:
                    CompleteTests();
                    return;
            }

            _testPhase++;
        }

        /// <summary>
        /// Test 1: Verify biome spawn data is loaded correctly
        /// </summary>
        private void TestBiomeSpawnDataLoading()
        {
            Logger.LogInfo("BiomeEnemySpawningTests", "Test 1: Testing biome spawn data loading");

            try
            {
                // Test that biome spawn tables are loaded
                var biomeSpawnTables = GetPrivateField<Dictionary<string, BiomeSpawnData>>(_enemyManager, "_biomeSpawnTables");
                
                if (biomeSpawnTables != null && biomeSpawnTables.Count > 0)
                {
                    _testResults.Add("✅ Biome spawn data loaded successfully");
                    Logger.LogInfo("BiomeEnemySpawningTests", $"Loaded {biomeSpawnTables.Count} biome spawn configurations");

                    // Check specific biome data
                    if (biomeSpawnTables.ContainsKey("forest"))
                    {
                        var forestData = biomeSpawnTables["forest"];
                        if (forestData.EnemySpawns.Count > 0)
                        {
                            _testResults.Add("✅ Forest biome has enemy spawn data");
                            Logger.LogInfo("BiomeEnemySpawningTests", $"Forest biome has {forestData.EnemySpawns.Count} enemy types");
                        }
                        else
                        {
                            _testResults.Add("❌ Forest biome has no enemy spawn data");
                        }
                    }
                    else
                    {
                        _testResults.Add("❌ Forest biome data not found");
                    }
                }
                else
                {
                    _testResults.Add("❌ No biome spawn data loaded");
                }
            }
            catch (Exception ex)
            {
                _testResults.Add($"❌ Error testing biome data loading: {ex.Message}");
                Logger.LogException("BiomeEnemySpawningTests", ex, "TestBiomeSpawnDataLoading");
            }
        }

        /// <summary>
        /// Test 2: Test day time spawning rates
        /// </summary>
        private void TestDayTimeSpawning()
        {
            Logger.LogInfo("BiomeEnemySpawningTests", "Test 2: Testing day time spawning");

            try
            {
                // Set to day time
                _dayNightCycle?.SetTime(12.0f); // Noon

                // Clear existing enemies
                _enemyManager?.ClearAllEnemies();

                // Simulate player position in forest biome
                var playerPosition = new Vector2(100, 100);
                CallPrivateMethod(_enemyManager, "OnPlayerMoved", playerPosition, Vector2.Zero, false);

                // Force spawn check
                CallPrivateMethod(_enemyManager, "OnSpawnTimerTimeout");

                // Check if enemies were spawned
                var activeEnemies = _enemyManager?.GetActiveEnemies();
                if (activeEnemies != null && activeEnemies.Count > 0)
                {
                    _testResults.Add($"✅ Day time spawning working - {activeEnemies.Count} enemies spawned");
                    Logger.LogInfo("BiomeEnemySpawningTests", $"Day spawning: {activeEnemies.Count} enemies active");
                }
                else
                {
                    _testResults.Add("⚠️ No enemies spawned during day (may be normal due to spawn rates)");
                }
            }
            catch (Exception ex)
            {
                _testResults.Add($"❌ Error testing day time spawning: {ex.Message}");
                Logger.LogException("BiomeEnemySpawningTests", ex, "TestDayTimeSpawning");
            }
        }

        /// <summary>
        /// Test 3: Test night time spawning rates (should be higher)
        /// </summary>
        private void TestNightTimeSpawning()
        {
            Logger.LogInfo("BiomeEnemySpawningTests", "Test 3: Testing night time spawning");

            try
            {
                // Set to night time
                _dayNightCycle?.SetTime(22.0f); // 10 PM

                // Clear existing enemies
                _enemyManager?.ClearAllEnemies();

                // Simulate multiple spawn attempts to test night multiplier
                for (int i = 0; i < 5; i++)
                {
                    CallPrivateMethod(_enemyManager, "OnSpawnTimerTimeout");
                }

                // Check if more enemies were spawned at night
                var activeEnemies = _enemyManager?.GetActiveEnemies();
                if (activeEnemies != null)
                {
                    _testResults.Add($"✅ Night time spawning tested - {activeEnemies.Count} enemies spawned");
                    Logger.LogInfo("BiomeEnemySpawningTests", $"Night spawning: {activeEnemies.Count} enemies active");
                }
                else
                {
                    _testResults.Add("❌ Night time spawning failed");
                }
            }
            catch (Exception ex)
            {
                _testResults.Add($"❌ Error testing night time spawning: {ex.Message}");
                Logger.LogException("BiomeEnemySpawningTests", ex, "TestNightTimeSpawning");
            }
        }

        /// <summary>
        /// Test 4: Test player level scaling
        /// </summary>
        private void TestPlayerLevelScaling()
        {
            Logger.LogInfo("BiomeEnemySpawningTests", "Test 4: Testing player level scaling");

            try
            {
                // Simulate level change
                CallPrivateMethod(_enemyManager, "OnPlayerLevelChanged", 10, 1000.0f);

                // Clear existing enemies
                _enemyManager?.ClearAllEnemies();

                // Try spawning with higher level
                CallPrivateMethod(_enemyManager, "OnSpawnTimerTimeout");

                var activeEnemies = _enemyManager?.GetActiveEnemies();
                if (activeEnemies != null && activeEnemies.Count > 0)
                {
                    var enemy = activeEnemies[0];
                    _testResults.Add($"✅ Level scaling tested - Enemy health: {enemy.MaxHealth}");
                    Logger.LogInfo("BiomeEnemySpawningTests", $"Level 10 enemy stats - Health: {enemy.MaxHealth}, Damage: {enemy.Damage}");
                }
                else
                {
                    _testResults.Add("⚠️ No enemies spawned for level scaling test");
                }
            }
            catch (Exception ex)
            {
                _testResults.Add($"❌ Error testing level scaling: {ex.Message}");
                Logger.LogException("BiomeEnemySpawningTests", ex, "TestPlayerLevelScaling");
            }
        }

        /// <summary>
        /// Test 5: Test boss spawning system
        /// </summary>
        private void TestBossSpawning()
        {
            Logger.LogInfo("BiomeEnemySpawningTests", "Test 5: Testing boss spawning");

            try
            {
                // Set high level to meet boss requirements
                CallPrivateMethod(_enemyManager, "OnPlayerLevelChanged", 15, 5000.0f);

                // Force boss spawn check
                CallPrivateMethod(_enemyManager, "OnBossSpawnTimerTimeout");

                var activeBosses = GetPrivateField<List<Enemy>>(_enemyManager, "_activeBosses");
                if (activeBosses != null)
                {
                    _testResults.Add($"✅ Boss spawning system tested - {activeBosses.Count} bosses active");
                    Logger.LogInfo("BiomeEnemySpawningTests", $"Boss spawning: {activeBosses.Count} bosses active");
                }
                else
                {
                    _testResults.Add("⚠️ Boss spawning system accessible but no bosses spawned (normal due to spawn rates)");
                }
            }
            catch (Exception ex)
            {
                _testResults.Add($"❌ Error testing boss spawning: {ex.Message}");
                Logger.LogException("BiomeEnemySpawningTests", ex, "TestBossSpawning");
            }
        }

        /// <summary>
        /// Test 6: Test enemy migration system
        /// </summary>
        private void TestEnemyMigration()
        {
            Logger.LogInfo("BiomeEnemySpawningTests", "Test 6: Testing enemy migration");

            try
            {
                // Force migration check
                CallPrivateMethod(_enemyManager, "OnMigrationTimerTimeout");

                var migratingEnemies = GetPrivateField<Dictionary<Enemy, MigrationData>>(_enemyManager, "_migratingEnemies");
                if (migratingEnemies != null)
                {
                    _testResults.Add($"✅ Migration system tested - {migratingEnemies.Count} enemies tracked for migration");
                    Logger.LogInfo("BiomeEnemySpawningTests", $"Migration: {migratingEnemies.Count} enemies being tracked");
                }
                else
                {
                    _testResults.Add("❌ Migration system not accessible");
                }
            }
            catch (Exception ex)
            {
                _testResults.Add($"❌ Error testing migration: {ex.Message}");
                Logger.LogException("BiomeEnemySpawningTests", ex, "TestEnemyMigration");
            }
        }

        /// <summary>
        /// Complete all tests and show results
        /// </summary>
        private void CompleteTests()
        {
            Logger.LogInfo("BiomeEnemySpawningTests", "=== BIOME ENEMY SPAWNING TEST RESULTS ===");
            
            foreach (var result in _testResults)
            {
                Logger.LogInfo("BiomeEnemySpawningTests", result);
            }

            int passedTests = _testResults.Count(r => r.StartsWith("✅"));
            int warningTests = _testResults.Count(r => r.StartsWith("⚠️"));
            int failedTests = _testResults.Count(r => r.StartsWith("❌"));

            Logger.LogInfo("BiomeEnemySpawningTests", $"=== SUMMARY: {passedTests} passed, {warningTests} warnings, {failedTests} failed ===");

            // Clean up
            _testTimer?.QueueFree();
            QueueFree();
        }

        /// <summary>
        /// Helper method to get private fields using reflection
        /// </summary>
        private T GetPrivateField<T>(object obj, string fieldName)
        {
            var field = obj.GetType().GetField(fieldName, System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            return field != null ? (T)field.GetValue(obj) : default(T);
        }

        /// <summary>
        /// Helper method to call private methods using reflection
        /// </summary>
        private void CallPrivateMethod(object obj, string methodName, params object[] parameters)
        {
            var method = obj.GetType().GetMethod(methodName, System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            method?.Invoke(obj, parameters);
        }
    }
}