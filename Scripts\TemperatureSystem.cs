using Godot;
using System;
using System.Collections.Generic;

namespace SurvivalLooterShooter
{
    /// <summary>
    /// Manages temperature effects on survival stats and environmental hazards
    /// Integrates with weather system and provides protection mechanics
    /// </summary>
    public partial class TemperatureSystem : Node
    {
        private static TemperatureSystem _instance;
        public static TemperatureSystem Instance => _instance;

        // Temperature thresholds
        private const float FREEZING_TEMP = 0f;
        private const float COLD_TEMP = 10f;
        private const float COMFORTABLE_MIN = 15f;
        private const float COMFORTABLE_MAX = 25f;
        private const float HOT_TEMP = 35f;
        private const float EXTREME_HOT_TEMP = 45f;

        // Current temperature state
        public float CurrentTemperature { get; private set; } = 20f;
        public TemperatureZone CurrentZone { get; private set; } = TemperatureZone.Comfortable;
        
        // Protection values
        private float _clothingProtection = 0f;
        private float _shelterProtection = 0f;
        private bool _isInShelter = false;

        // Temperature effects timer
        private Timer _effectsTimer;
        private const float EFFECTS_UPDATE_INTERVAL = 5f; // Apply effects every 5 seconds

        // Events
        [Signal] public delegate void TemperatureChangedEventHandler(float temperature, TemperatureZone zone);
        [Signal] public delegate void TemperatureHazardEventHandler(string hazardType, float severity);
        [Signal] public delegate void ProtectionChangedEventHandler(float clothingProtection, float shelterProtection);

        public enum TemperatureZone
        {
            Freezing,      // Below 0°C
            Cold,          // 0-10°C
            Cool,          // 10-15°C
            Comfortable,   // 15-25°C
            Warm,          // 25-35°C
            Hot,           // 35-45°C
            ExtremeHot     // Above 45°C
        }

        public override void _Ready()
        {
            // Singleton setup
            if (_instance == null)
            {
                _instance = this;
                AddToGroup("temperature_system");
                Logger.LogInfo("TemperatureSystem", "TemperatureSystem singleton initialized");
            }
            else
            {
                Logger.LogError("TemperatureSystem", "Multiple TemperatureSystem instances detected! Removing duplicate.");
                QueueFree();
                return;
            }

            SetupEffectsTimer();
            ConnectToWeatherSystem();
            
            Logger.LogInfo("TemperatureSystem", "Temperature system initialized");
        }

        /// <summary>
        /// Sets up the timer for applying temperature effects
        /// </summary>
        private void SetupEffectsTimer()
        {
            _effectsTimer = new Timer();
            AddChild(_effectsTimer);
            _effectsTimer.WaitTime = EFFECTS_UPDATE_INTERVAL;
            _effectsTimer.Autostart = true;
            _effectsTimer.Timeout += ApplyTemperatureEffects;
        }

        /// <summary>
        /// Connects to weather system for temperature updates
        /// </summary>
        private void ConnectToWeatherSystem()
        {
            if (WeatherManager.Instance != null)
            {
                WeatherManager.Instance.WeatherChanged += OnWeatherChanged;
                WeatherManager.Instance.SeasonChanged += OnSeasonChanged;
                
                // Get initial temperature
                UpdateTemperature();
            }
        }

        private void OnSeasonChanged(WeatherManager.Season oldSeason, WeatherManager.Season newSeason)
        {
            throw new NotImplementedException();
        }

        private void OnWeatherChanged(WeatherManager.WeatherType oldWeather, WeatherManager.WeatherType newWeather, float intensity)
        {
            throw new NotImplementedException();
        }

        /// <summary>
        /// Updates current temperature based on weather and environmental factors
        /// </summary>
        private void UpdateTemperature()
        {
            if (WeatherManager.Instance == null) return;

            float newTemperature = WeatherManager.Instance.GetCurrentTemperature();
            
            // Apply shelter protection
            if (_isInShelter)
            {
                // Shelter moderates temperature towards comfortable range
                if (newTemperature < COMFORTABLE_MIN)
                {
                    newTemperature = Mathf.Lerp(newTemperature, COMFORTABLE_MIN, _shelterProtection);
                }
                else if (newTemperature > COMFORTABLE_MAX)
                {
                    newTemperature = Mathf.Lerp(newTemperature, COMFORTABLE_MAX, _shelterProtection);
                }
            }

            SetTemperature(newTemperature);
        }

        /// <summary>
        /// Sets the current temperature and updates zone
        /// </summary>
        private void SetTemperature(float temperature)
        {
            float previousTemp = CurrentTemperature;
            CurrentTemperature = temperature;
            
            TemperatureZone previousZone = CurrentZone;
            CurrentZone = GetTemperatureZone(temperature);

            if (Mathf.Abs(previousTemp - CurrentTemperature) > 1f || previousZone != CurrentZone)
            {
                EmitSignal(SignalName.TemperatureChanged, CurrentTemperature, (int)CurrentZone);
                EventBus.Instance?.EmitTemperatureChanged(CurrentTemperature, (int)CurrentZone);
                
                Logger.LogInfo("TemperatureSystem", $"Temperature changed to {CurrentTemperature:F1}°C ({CurrentZone})");
            }
        }

        /// <summary>
        /// Determines temperature zone based on current temperature
        /// </summary>
        private TemperatureZone GetTemperatureZone(float temperature)
        {
            return temperature switch
            {
                < FREEZING_TEMP => TemperatureZone.Freezing,
                < COLD_TEMP => TemperatureZone.Cold,
                < COMFORTABLE_MIN => TemperatureZone.Cool,
                <= COMFORTABLE_MAX => TemperatureZone.Comfortable,
                < HOT_TEMP => TemperatureZone.Warm,
                < EXTREME_HOT_TEMP => TemperatureZone.Hot,
                _ => TemperatureZone.ExtremeHot
            };
        }

        /// <summary>
        /// Applies temperature effects to survival stats
        /// </summary>
        private void ApplyTemperatureEffects()
        {
            if (SurvivalStatsSystem.Instance == null) return;

            float effectiveTemperature = GetEffectiveTemperature();
            TemperatureZone effectiveZone = GetTemperatureZone(effectiveTemperature);

            switch (effectiveZone)
            {
                case TemperatureZone.Freezing:
                    ApplyFreezingEffects();
                    break;
                case TemperatureZone.Cold:
                    ApplyColdEffects();
                    break;
                case TemperatureZone.Hot:
                    ApplyHotEffects();
                    break;
                case TemperatureZone.ExtremeHot:
                    ApplyExtremeHotEffects();
                    break;
                default:
                    // Comfortable temperatures have no negative effects
                    break;
            }
        }

        /// <summary>
        /// Gets effective temperature considering clothing protection
        /// </summary>
        private float GetEffectiveTemperature()
        {
            float effectiveTemp = CurrentTemperature;
            
            // Clothing provides protection against temperature extremes
            if (CurrentTemperature < COMFORTABLE_MIN)
            {
                // Cold protection - clothing warms you up
                float warmthBonus = _clothingProtection * 10f; // Up to 10°C protection
                effectiveTemp = Mathf.Min(effectiveTemp + warmthBonus, COMFORTABLE_MIN);
            }
            else if (CurrentTemperature > COMFORTABLE_MAX)
            {
                // Heat protection - clothing cools you down
                float coolingBonus = _clothingProtection * 8f; // Up to 8°C protection
                effectiveTemp = Mathf.Max(effectiveTemp - coolingBonus, COMFORTABLE_MAX);
            }

            return effectiveTemp;
        }

        /// <summary>
        /// Applies freezing temperature effects
        /// </summary>
        private void ApplyFreezingEffects()
        {
            var statsSystem = SurvivalStatsSystem.Instance;
            
            // Severe health loss from hypothermia
            statsSystem.Health.ModifyValue(-8f);
            
            // Increased hunger from body trying to generate heat
            statsSystem.Hunger.ModifyValue(-5f);
            
            // Faster stamina drain
            statsSystem.Stamina.ModifyValue(-10f);
            
            EmitSignal(SignalName.TemperatureHazard, "hypothermia", 1.0f);
            EventBus.Instance?.EmitTemperatureHazard("hypothermia", 1.0f);
            
            Logger.LogInfo("TemperatureSystem", "Applying hypothermia effects");
        }

        /// <summary>
        /// Applies cold temperature effects
        /// </summary>
        private void ApplyColdEffects()
        {
            var statsSystem = SurvivalStatsSystem.Instance;
            
            // Moderate health loss
            statsSystem.Health.ModifyValue(-3f);
            
            // Increased hunger
            statsSystem.Hunger.ModifyValue(-3f);
            
            EmitSignal(SignalName.TemperatureHazard, "cold_exposure", 0.5f);
            EventBus.Instance?.EmitTemperatureHazard("cold_exposure", 0.5f);
        }

        /// <summary>
        /// Applies hot temperature effects
        /// </summary>
        private void ApplyHotEffects()
        {
            var statsSystem = SurvivalStatsSystem.Instance;
            
            // Increased thirst from dehydration
            statsSystem.Thirst.ModifyValue(-8f);
            
            // Faster stamina drain
            statsSystem.Stamina.ModifyValue(-5f);
            
            EmitSignal(SignalName.TemperatureHazard, "heat_exhaustion", 0.6f);
            EventBus.Instance?.EmitTemperatureHazard("heat_exhaustion", 0.6f);
        }

        /// <summary>
        /// Applies extreme hot temperature effects
        /// </summary>
        private void ApplyExtremeHotEffects()
        {
            var statsSystem = SurvivalStatsSystem.Instance;
            
            // Health loss from heat stroke
            statsSystem.Health.ModifyValue(-6f);
            
            // Severe dehydration
            statsSystem.Thirst.ModifyValue(-12f);
            
            // Severe stamina drain
            statsSystem.Stamina.ModifyValue(-15f);
            
            EmitSignal(SignalName.TemperatureHazard, "heat_stroke", 1.0f);
            EventBus.Instance?.EmitTemperatureHazard("heat_stroke", 1.0f);
            
            Logger.LogInfo("TemperatureSystem", "Applying heat stroke effects");
        }

        #region Event Handlers

        /// <summary>
        /// Handles weather changes that affect temperature
        /// </summary>
        private void OnWeatherChanged(int oldWeather, int newWeather, float intensity)
        {
            UpdateTemperature();
        }

        /// <summary>
        /// Handles seasonal changes that affect base temperature
        /// </summary>
        private void OnSeasonChanged(int oldSeason, int newSeason)
        {
            UpdateTemperature();
        }

        #endregion

        #region Public API

        /// <summary>
        /// Sets clothing protection level (0.0 to 1.0)
        /// </summary>
        public void SetClothingProtection(float protection)
        {
            _clothingProtection = Mathf.Clamp(protection, 0f, 1f);
            EmitSignal(SignalName.ProtectionChanged, _clothingProtection, _shelterProtection);
            EventBus.Instance?.EmitProtectionChanged(_clothingProtection, _shelterProtection);
            
            Logger.LogInfo("TemperatureSystem", $"Clothing protection set to {_clothingProtection:F2}");
        }

        /// <summary>
        /// Sets shelter protection and status
        /// </summary>
        public void SetShelterProtection(bool inShelter, float protection = 0.5f)
        {
            _isInShelter = inShelter;
            _shelterProtection = inShelter ? Mathf.Clamp(protection, 0f, 1f) : 0f;
            
            EmitSignal(SignalName.ProtectionChanged, _clothingProtection, _shelterProtection);
            EventBus.Instance?.EmitProtectionChanged(_clothingProtection, _shelterProtection);
            
            UpdateTemperature(); // Immediately update temperature with shelter effects
            
            Logger.LogInfo("TemperatureSystem", $"Shelter status: {inShelter}, protection: {_shelterProtection:F2}");
        }

        /// <summary>
        /// Gets current temperature protection summary
        /// </summary>
        public TemperatureProtectionData GetProtectionData()
        {
            return new TemperatureProtectionData
            {
                ClothingProtection = _clothingProtection,
                ShelterProtection = _shelterProtection,
                IsInShelter = _isInShelter,
                EffectiveTemperature = GetEffectiveTemperature(),
                TemperatureZone = GetTemperatureZone(GetEffectiveTemperature())
            };
        }

        /// <summary>
        /// Gets temperature zone description
        /// </summary>
        public string GetTemperatureDescription()
        {
            return CurrentZone switch
            {
                TemperatureZone.Freezing => "Freezing - Risk of hypothermia",
                TemperatureZone.Cold => "Cold - Increased hunger and health loss",
                TemperatureZone.Cool => "Cool - Slightly uncomfortable",
                TemperatureZone.Comfortable => "Comfortable - No temperature effects",
                TemperatureZone.Warm => "Warm - Slightly uncomfortable",
                TemperatureZone.Hot => "Hot - Increased thirst and fatigue",
                TemperatureZone.ExtremeHot => "Extreme Heat - Risk of heat stroke",
                _ => "Unknown"
            };
        }

        /// <summary>
        /// Forces a temperature update (useful for testing or immediate updates)
        /// </summary>
        public void ForceTemperatureUpdate()
        {
            UpdateTemperature();
        }

        #endregion

        public override void _ExitTree()
        {
            if (_instance == this)
            {
                _instance = null;
            }
        }
    }

    /// <summary>
    /// Data structure for temperature protection information
    /// </summary>
    public class TemperatureProtectionData
    {
        public float ClothingProtection { get; set; }
        public float ShelterProtection { get; set; }
        public bool IsInShelter { get; set; }
        public float EffectiveTemperature { get; set; }
        public TemperatureSystem.TemperatureZone TemperatureZone { get; set; }
    }
}