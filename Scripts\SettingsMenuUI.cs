using Godot;

public partial class SettingsMenuUI : Control
{
    private OptionButton _qualityOption;
    private CheckBox _vsyncCheck;
    private SpinBox _fpsSpinBox;
    private HSlider _masterVolumeSlider;
    private HSlider _sfxVolumeSlider;
    private HSlider _musicVolumeSlider;
    private Label _masterVolumeValue;
    private Label _sfxVolumeValue;
    private Label _musicVolumeValue;
    private Button _resetButton;
    private Button _applyButton;
    private Button _closeButton;

    public override void _Ready()
    {
        GetNodes();
        SetupUI();
        ConnectSignals();
        LoadCurrentSettings();
    }

    private void GetNodes()
    {
        _qualityOption = GetNode<OptionButton>("CenterContainer/Panel/VBoxContainer/GraphicsSection/QualityContainer/QualityOption");
        _vsyncCheck = GetNode<CheckBox>("CenterContainer/Panel/VBoxContainer/GraphicsSection/VsyncContainer/VsyncCheck");
        _fpsSpinBox = GetNode<SpinBox>("CenterContainer/Panel/VBoxContainer/GraphicsSection/FPSContainer/FPSSpinBox");
        _masterVolumeSlider = GetNode<HSlider>("CenterContainer/Panel/VBoxContainer/AudioSection/MasterVolumeContainer/MasterVolumeSlider");
        _sfxVolumeSlider = GetNode<HSlider>("CenterContainer/Panel/VBoxContainer/AudioSection/SFXVolumeContainer/SFXVolumeSlider");
        _musicVolumeSlider = GetNode<HSlider>("CenterContainer/Panel/VBoxContainer/AudioSection/MusicVolumeContainer/MusicVolumeSlider");
        _masterVolumeValue = GetNode<Label>("CenterContainer/Panel/VBoxContainer/AudioSection/MasterVolumeContainer/MasterVolumeValue");
        _sfxVolumeValue = GetNode<Label>("CenterContainer/Panel/VBoxContainer/AudioSection/SFXVolumeContainer/SFXVolumeValue");
        _musicVolumeValue = GetNode<Label>("CenterContainer/Panel/VBoxContainer/AudioSection/MusicVolumeContainer/MusicVolumeValue");
        _resetButton = GetNode<Button>("CenterContainer/Panel/VBoxContainer/ButtonContainer/ResetButton");
        _applyButton = GetNode<Button>("CenterContainer/Panel/VBoxContainer/ButtonContainer/ApplyButton");
        _closeButton = GetNode<Button>("CenterContainer/Panel/VBoxContainer/ButtonContainer/CloseButton");
    }

    private void SetupUI()
    {
        // Setup quality options
        _qualityOption.AddItem("Low");
        _qualityOption.AddItem("Medium");
        _qualityOption.AddItem("High");
        _qualityOption.AddItem("Ultra");
    }

    private void ConnectSignals()
    {
        _qualityOption.ItemSelected += OnQualityChanged;
        _vsyncCheck.Toggled += OnVsyncToggled;
        _fpsSpinBox.ValueChanged += OnFPSChanged;
        _masterVolumeSlider.ValueChanged += OnMasterVolumeChanged;
        _sfxVolumeSlider.ValueChanged += OnSFXVolumeChanged;
        _musicVolumeSlider.ValueChanged += OnMusicVolumeChanged;
        _resetButton.Pressed += OnResetPressed;
        _applyButton.Pressed += OnApplyPressed;
        _closeButton.Pressed += OnClosePressed;
    }

    private void LoadCurrentSettings()
    {
        if (GraphicsSettings.Instance != null)
        {
            _qualityOption.Selected = (int)GraphicsSettings.Instance.CurrentQuality;
            _vsyncCheck.ButtonPressed = GraphicsSettings.Instance.VsyncEnabled;
            _fpsSpinBox.Value = GraphicsSettings.Instance.TargetFPS;
        }

        if (AudioManager.Instance != null)
        {
            _masterVolumeSlider.Value = AudioManager.Instance.MasterVolume * 100;
            _sfxVolumeSlider.Value = AudioManager.Instance.SFXVolume * 100;
            _musicVolumeSlider.Value = AudioManager.Instance.MusicVolume * 100;
            UpdateVolumeLabels();
        }
    }

    private void OnQualityChanged(long index)
    {
        GraphicsSettings.Instance?.SetQualityLevel((GraphicsSettings.QualityLevel)index);
        LoadCurrentSettings(); // Refresh other settings that may have changed
    }

    private void OnVsyncToggled(bool pressed)
    {
        GraphicsSettings.Instance?.SetVsync(pressed);
    }

    private void OnFPSChanged(double value)
    {
        GraphicsSettings.Instance?.SetTargetFPS((int)value);
    }

    private void OnMasterVolumeChanged(double value)
    {
        AudioManager.Instance?.SetMasterVolume((float)(value / 100.0));
        _masterVolumeValue.Text = $"{(int)value}%";
    }

    private void OnSFXVolumeChanged(double value)
    {
        AudioManager.Instance?.SetSFXVolume((float)(value / 100.0));
        _sfxVolumeValue.Text = $"{(int)value}%";
    }

    private void OnMusicVolumeChanged(double value)
    {
        AudioManager.Instance?.SetMusicVolume((float)(value / 100.0));
        _musicVolumeValue.Text = $"{(int)value}%";
    }

    private void UpdateVolumeLabels()
    {
        _masterVolumeValue.Text = $"{(int)(_masterVolumeSlider.Value)}%";
        _sfxVolumeValue.Text = $"{(int)(_sfxVolumeSlider.Value)}%";
        _musicVolumeValue.Text = $"{(int)(_musicVolumeSlider.Value)}%";
    }

    private void OnResetPressed()
    {
        GraphicsSettings.Instance?.ResetToDefaults();
        AudioManager.Instance?.ResetToDefaults();
        LoadCurrentSettings();
    }

    private void OnApplyPressed()
    {
        // Settings are applied immediately, but we could add a confirmation here
        GD.Print("Settings applied successfully!");
    }

    private void OnClosePressed()
    {
        Visible = false;
    }

    public override void _Input(InputEvent @event)
    {
        if (@event is InputEventKey keyEvent && keyEvent.Pressed && keyEvent.Keycode == Key.Escape)
        {
            OnClosePressed();
        }
    }
}