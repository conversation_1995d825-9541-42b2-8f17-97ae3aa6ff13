using Godot;
using System;

namespace SurvivalLooterShooter
{
    /// <summary>
    /// Provides visual preview for structure placement
    /// </summary>
    public partial class BuildingPreview : Node2D
    {
        private Sprite2D _sprite;
        private StructureBlueprint _blueprint;
        private bool _isValid = true;
        
        // Preview colors
        private readonly Color ValidColor = new Color(0, 1, 0, 0.5f);   // Semi-transparent green
        private readonly Color InvalidColor = new Color(1, 0, 0, 0.5f); // Semi-transparent red

        public override void _Ready()
        {
            // Create sprite for preview
            _sprite = new Sprite2D();
            AddChild(_sprite);
            
            // Set initial properties
            _sprite.Modulate = ValidColor;
            ZIndex = 100; // Ensure preview is drawn on top
        }

        /// <summary>
        /// Sets the blueprint for this preview
        /// </summary>
        public void SetBlueprint(StructureBlueprint blueprint)
        {
            _blueprint = blueprint;
            UpdatePreviewVisual();
        }

        /// <summary>
        /// Sets whether the current placement is valid
        /// </summary>
        public void SetValid(bool valid)
        {
            if (_isValid == valid) return;
            
            _isValid = valid;
            _sprite.Modulate = valid ? ValidColor : InvalidColor;
        }

        /// <summary>
        /// Updates the visual representation of the preview
        /// </summary>
        private void UpdatePreviewVisual()
        {
            if (_blueprint == null || _sprite == null) return;

            // Try to load the structure texture
            string texturePath = $"res://Assets/Structures/{_blueprint.Id}.png";
            
            if (ResourceLoader.Exists(texturePath))
            {
                _sprite.Texture = GD.Load<Texture2D>(texturePath);
            }
            else
            {
                // Create a placeholder colored rectangle
                CreatePlaceholderTexture();
            }
            
            // Set the modulate color based on validity
            _sprite.Modulate = _isValid ? ValidColor : InvalidColor;
        }

        /// <summary>
        /// Creates a placeholder texture when no asset is available
        /// </summary>
        private void CreatePlaceholderTexture()
        {
            if (_blueprint == null) return;

            int width = _blueprint.Size.Width * 32;
            int height = _blueprint.Size.Height * 32;
            
            var image = Image.CreateEmpty(width, height, false, Image.Format.Rgba8);
            
            // Fill with structure type color
            Color baseColor = GetStructureTypeColor();
            image.Fill(baseColor);
            
            // Add border
            DrawBorder(image, Colors.Black, 2);
            
            var texture = ImageTexture.CreateFromImage(image);
            _sprite.Texture = texture;
        }

        /// <summary>
        /// Gets color based on structure type
        /// </summary>
        private Color GetStructureTypeColor()
        {
            if (_blueprint == null) return Colors.White;
            
            return _blueprint.Type switch
            {
                "foundation" => Colors.Gray,
                "defense" => Colors.Brown,
                "crafting_station" => Colors.Blue,
                "storage" => Colors.Green,
                _ => Colors.White
            };
        }

        /// <summary>
        /// Draws a border on the image
        /// </summary>
        private void DrawBorder(Image image, Color borderColor, int borderWidth)
        {
            int width = image.GetWidth();
            int height = image.GetHeight();
            
            // Top and bottom borders
            for (int x = 0; x < width; x++)
            {
                for (int y = 0; y < borderWidth; y++)
                {
                    image.SetPixel(x, y, borderColor);
                    image.SetPixel(x, height - 1 - y, borderColor);
                }
            }
            
            // Left and right borders
            for (int y = 0; y < height; y++)
            {
                for (int x = 0; x < borderWidth; x++)
                {
                    image.SetPixel(x, y, borderColor);
                    image.SetPixel(width - 1 - x, y, borderColor);
                }
            }
        }
    }
}