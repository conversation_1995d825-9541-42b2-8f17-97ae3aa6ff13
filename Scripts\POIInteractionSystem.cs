using Godot;
using System;
using System.Collections.Generic;
using System.Linq;

namespace SurvivalLooterShooter
{
    /// <summary>
    /// POIInteractionSystem handles player interactions with Points of Interest
    /// Manages entering POIs, looting, and interior navigation
    /// </summary>
    public partial class POIInteractionSystem : Node
    {
        private static POIInteractionSystem _instance;
        public static POIInteractionSystem Instance => _instance;

        // Interaction parameters
        [Export] public float InteractionRange { get; set; } = 30f;
        [Export] public float LootInteractionRange { get; set; } = 15f;

        // Current state
        private PointOfInterest _currentPOI;
        private Room _currentRoom;
        private Vector2 _playerPosition;
        private bool _isInsidePOI = false;

        // References
        private POIGenerator _poiGenerator;
        private ExplorationTracker _explorationTracker;

        // Events
        [Signal]
        public delegate void POIEnteredEventHandler(string poiId, string poiName);
        
        [Signal]
        public delegate void POIExitedEventHandler(string poiId);
        
        [Signal]
        public delegate void LootCollectedEventHandler(string itemId, int quantity, Vector2 position);
        
        [Signal]
        public delegate void RoomEnteredEventHandler(string roomId, string roomType);

        public override void _Ready()
        {
            if (_instance == null)
            {
                _instance = this;
                AddToGroup("poi_interaction");
                
                // Get references to other systems
                _poiGenerator = GetNode<POIGenerator>("/root/POIGenerator");
                _explorationTracker = ExplorationTracker.Instance;
                
                GD.Print("POIInteractionSystem singleton initialized");
            }
            else
            {
                GD.PrintErr("Multiple POIInteractionSystem instances detected! Removing duplicate.");
                QueueFree();
            }
        }

        /// <summary>
        /// Updates the interaction system with current player position
        /// </summary>
        public void UpdatePlayerPosition(Vector2 playerPosition)
        {
            _playerPosition = playerPosition;
            
            if (!_isInsidePOI)
            {
                CheckForPOIInteractions();
            }
            else
            {
                CheckForRoomTransitions();
                CheckForExitConditions();
            }
        }

        /// <summary>
        /// Checks for nearby POIs that can be interacted with
        /// </summary>
        private void CheckForPOIInteractions()
        {
            if (_poiGenerator == null) return;

            var nearbyPOIs = _poiGenerator.GetPOIsNear(_playerPosition, InteractionRange);
            
            foreach (var poi in nearbyPOIs)
            {
                if (!poi.IsDiscovered)
                {
                    // Discover the POI
                    _explorationTracker?.UpdateExploration(_playerPosition);
                }
            }
        }

        /// <summary>
        /// Attempts to enter a POI
        /// </summary>
        public bool TryEnterPOI(string poiId)
        {
            var poi = _poiGenerator?.GetPOI(poiId);
            if (poi == null)
            {
                GD.PrintErr($"POI not found: {poiId}");
                return false;
            }

            float distance = _playerPosition.DistanceTo(poi.WorldPosition);
            if (distance > InteractionRange)
            {
                GD.Print($"POI {poiId} is too far away ({distance:F1} > {InteractionRange})");
                return false;
            }

            EnterPOI(poi);
            return true;
        }

        /// <summary>
        /// Enters a POI and sets up interior navigation
        /// </summary>
        private void EnterPOI(PointOfInterest poi)
        {
            _currentPOI = poi;
            _isInsidePOI = true;
            
            // Find the entrance room (first room or main room)
            _currentRoom = FindEntranceRoom(poi.Interior);
            
            EmitSignal(SignalName.POIEntered, poi.Id, poi.Name);
            EventBus.Instance?.EmitSignal(EventBus.SignalName.GameStateChanged, "poi_entered", poi.Id);
            
            if (_currentRoom != null)
            {
                EmitSignal(SignalName.RoomEntered, _currentRoom.Id, _currentRoom.Type.ToString());
            }
            
            GD.Print($"Entered POI: {poi.Name}");
        }

        /// <summary>
        /// Finds the entrance room for a POI interior
        /// </summary>
        private Room FindEntranceRoom(POIInterior interior)
        {
            if (!interior.Rooms.Any()) return null;

            // Look for main rooms first
            var mainRoom = interior.Rooms.FirstOrDefault(r => 
                r.Type == RoomType.MainRoom || 
                r.Type == RoomType.MainHall || 
                r.Type == RoomType.MainChamber);
            
            return mainRoom ?? interior.Rooms.First();
        }

        /// <summary>
        /// Exits the current POI
        /// </summary>
        public void ExitPOI()
        {
            if (!_isInsidePOI || _currentPOI == null) return;

            string poiId = _currentPOI.Id;
            
            _currentPOI = null;
            _currentRoom = null;
            _isInsidePOI = false;
            
            EmitSignal(SignalName.POIExited, poiId);
            EventBus.Instance?.EmitSignal(EventBus.SignalName.GameStateChanged, "poi_exited", poiId);
            
            GD.Print($"Exited POI: {poiId}");
        }

        /// <summary>
        /// Checks for room transitions within the POI
        /// </summary>
        private void CheckForRoomTransitions()
        {
            if (_currentPOI?.Interior?.Rooms == null || _currentRoom == null) return;

            // Check if player is near any room connections
            var connections = _currentPOI.Interior.Connections
                .Where(c => c.FromRoomId == _currentRoom.Id || c.ToRoomId == _currentRoom.Id);

            foreach (var connection in connections)
            {
                // For simplicity, we'll assume room transitions happen automatically
                // In a full implementation, this would check player position against connection positions
            }
        }

        /// <summary>
        /// Checks if player should exit the POI
        /// </summary>
        private void CheckForExitConditions()
        {
            if (_currentPOI == null) return;

            // Check if player moved too far from POI center
            float distance = _playerPosition.DistanceTo(_currentPOI.WorldPosition);
            if (distance > InteractionRange * 1.5f)
            {
                ExitPOI();
            }
        }

        /// <summary>
        /// Attempts to loot an item from the current room
        /// </summary>
        public bool TryLootItem(Vector2 lootPosition)
        {
            if (!_isInsidePOI || _currentRoom == null) return false;

            // Find the closest loot spawn to the specified position
            var closestLoot = _currentRoom.LootSpawns
                .Where(loot => !loot.IsLooted)
                .OrderBy(loot => loot.Position.DistanceTo(lootPosition))
                .FirstOrDefault();

            if (closestLoot == null) return false;

            float distance = closestLoot.Position.DistanceTo(lootPosition);
            if (distance > LootInteractionRange) return false;

            return LootItem(closestLoot);
        }

        /// <summary>
        /// Loots a specific item spawn
        /// </summary>
        private bool LootItem(LootSpawn lootSpawn)
        {
            if (lootSpawn.IsLooted) return false;

            // Check if item can be looted (respawn timer)
            if (!CanLootItem(lootSpawn)) return false;

            // Try to add item to player inventory
            var inventory = GetNode<Inventory>("/root/Inventory");
            if (inventory == null)
            {
                GD.PrintErr("Inventory system not found");
                return false;
            }

            bool success = inventory.AddItem(lootSpawn.ItemId, lootSpawn.Quantity);
            if (success)
            {
                // Mark as looted
                lootSpawn.IsLooted = true;
                lootSpawn.LastLootTime = Time.GetUnixTimeFromSystem();
                
                // Emit loot collected event
                EmitSignal(SignalName.LootCollected, lootSpawn.ItemId, lootSpawn.Quantity, lootSpawn.Position);
                EventBus.Instance?.EmitSignal(EventBus.SignalName.GameStateChanged, "item_collected", lootSpawn.ItemId);
                
                GD.Print($"Looted {lootSpawn.Quantity}x {lootSpawn.ItemId}");
                return true;
            }
            else
            {
                GD.Print("Inventory full - cannot loot item");
                return false;
            }
        }

        /// <summary>
        /// Checks if a loot item can be collected (respawn timer check)
        /// </summary>
        private bool CanLootItem(LootSpawn lootSpawn)
        {
            if (!lootSpawn.IsLooted) return true;

            double currentTime = Time.GetUnixTimeFromSystem();
            return currentTime - lootSpawn.LastLootTime >= lootSpawn.RespawnTime;
        }

        /// <summary>
        /// Gets all available loot in the current room
        /// </summary>
        public List<LootSpawn> GetAvailableLootInCurrentRoom()
        {
            if (!_isInsidePOI || _currentRoom == null) return new List<LootSpawn>();

            return _currentRoom.LootSpawns
                .Where(loot => CanLootItem(loot))
                .ToList();
        }

        /// <summary>
        /// Gets nearby POIs that can be interacted with
        /// </summary>
        public List<PointOfInterest> GetNearbyInteractablePOIs()
        {
            if (_poiGenerator == null) return new List<PointOfInterest>();

            return _poiGenerator.GetPOIsNear(_playerPosition, InteractionRange);
        }

        /// <summary>
        /// Moves to a specific room within the current POI
        /// </summary>
        public bool MoveToRoom(string roomId)
        {
            if (!_isInsidePOI || _currentPOI?.Interior?.Rooms == null) return false;

            var targetRoom = _currentPOI.Interior.Rooms.FirstOrDefault(r => r.Id == roomId);
            if (targetRoom == null) return false;

            // Check if there's a connection from current room to target room
            if (_currentRoom != null && !IsRoomConnected(_currentRoom.Id, roomId)) return false;

            _currentRoom = targetRoom;
            EmitSignal(SignalName.RoomEntered, _currentRoom.Id, _currentRoom.Type.ToString());
            
            GD.Print($"Moved to room: {roomId} ({_currentRoom.Type})");
            return true;
        }

        /// <summary>
        /// Checks if two rooms are connected
        /// </summary>
        private bool IsRoomConnected(string fromRoomId, string toRoomId)
        {
            if (_currentPOI?.Interior?.Connections == null) return false;

            return _currentPOI.Interior.Connections.Any(c => 
                (c.FromRoomId == fromRoomId && c.ToRoomId == toRoomId) ||
                (c.FromRoomId == toRoomId && c.ToRoomId == fromRoomId));
        }

        /// <summary>
        /// Gets connected rooms from the current room
        /// </summary>
        public List<Room> GetConnectedRooms()
        {
            if (!_isInsidePOI || _currentRoom == null || _currentPOI?.Interior == null) 
                return new List<Room>();

            var connectedRoomIds = _currentPOI.Interior.Connections
                .Where(c => c.FromRoomId == _currentRoom.Id || c.ToRoomId == _currentRoom.Id)
                .Select(c => c.FromRoomId == _currentRoom.Id ? c.ToRoomId : c.FromRoomId)
                .ToList();

            return _currentPOI.Interior.Rooms
                .Where(r => connectedRoomIds.Contains(r.Id))
                .ToList();
        }

        /// <summary>
        /// Gets current POI information
        /// </summary>
        public POIInteractionInfo GetCurrentPOIInfo()
        {
            return new POIInteractionInfo
            {
                IsInsidePOI = _isInsidePOI,
                CurrentPOI = _currentPOI,
                CurrentRoom = _currentRoom,
                AvailableLoot = GetAvailableLootInCurrentRoom(),
                ConnectedRooms = GetConnectedRooms(),
                NearbyPOIs = GetNearbyInteractablePOIs()
            };
        }

        /// <summary>
        /// Handles input for POI interactions
        /// </summary>
        public void HandleInteractionInput()
        {
            if (_isInsidePOI)
            {
                // Handle interior interactions (loot, room movement, etc.)
                HandleInteriorInteractions();
            }
            else
            {
                // Handle POI entry
                HandlePOIEntry();
            }
        }

        /// <summary>
        /// Handles interactions while inside a POI
        /// </summary>
        private void HandleInteriorInteractions()
        {
            // Try to loot nearby items
            var availableLoot = GetAvailableLootInCurrentRoom();
            if (availableLoot.Any())
            {
                // Loot the closest item
                var closestLoot = availableLoot
                    .OrderBy(loot => loot.Position.DistanceTo(Vector2.Zero)) // Relative to room center
                    .First();
                
                TryLootItem(closestLoot.Position);
            }
        }

        /// <summary>
        /// Handles POI entry interactions
        /// </summary>
        private void HandlePOIEntry()
        {
            var nearbyPOIs = GetNearbyInteractablePOIs();
            if (nearbyPOIs.Any())
            {
                // Enter the closest POI
                var closestPOI = nearbyPOIs
                    .OrderBy(poi => poi.WorldPosition.DistanceTo(_playerPosition))
                    .First();
                
                TryEnterPOI(closestPOI.Id);
            }
        }

        public override void _ExitTree()
        {
            if (_instance == this)
            {
                _instance = null;
            }
        }
    }

    /// <summary>
    /// Information about current POI interaction state
    /// </summary>
    public class POIInteractionInfo
    {
        public bool IsInsidePOI { get; set; }
        public PointOfInterest CurrentPOI { get; set; }
        public Room CurrentRoom { get; set; }
        public List<LootSpawn> AvailableLoot { get; set; } = new();
        public List<Room> ConnectedRooms { get; set; } = new();
        public List<PointOfInterest> NearbyPOIs { get; set; } = new();
    }
}