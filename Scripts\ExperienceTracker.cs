using Godot;
using System.Collections.Generic;

namespace SurvivalLooterShooter
{
    public partial class ExperienceTracker : Node
{
    public static ExperienceTracker Instance { get; private set; }
    
    // Experience values for different actions
    private readonly Dictionary<string, float> _experienceValues = new Dictionary<string, float>
    {
        // Combat experience
        {"enemy_killed", 25f},
        {"weapon_fired", 1f},
        {"critical_hit", 5f},
        {"reload_weapon", 2f},
        
        // Crafting experience
        {"item_crafted", 10f},
        {"advanced_item_crafted", 20f},
        {"recipe_discovered", 15f},
        
        // Survival experience
        {"item_consumed", 3f},
        {"resource_harvested", 5f},
        {"stat_restored", 2f},
        {"survived_day", 50f},
        
        // Building experience
        {"structure_built", 15f},
        {"structure_upgraded", 25f},
        {"structure_repaired", 8f},
        {"blueprint_unlocked", 30f}
    };
    
    public override void _Ready()
    {
        if (Instance == null)
        {
            Instance = this;
            ConnectToEventBus();
        }
        else
        {
            QueueFree();
        }
    }
    
    private void ConnectToEventBus()
    {
        if (EventBus.Instance != null)
        {
            // Combat events
            EventBus.Instance.WeaponFired += OnWeaponFired;
            
            // Crafting events
            EventBus.Instance.ItemCrafted += OnItemCrafted;
            
            // Survival events
            EventBus.Instance.ConsumableUsed += OnConsumableUsed;
            EventBus.Instance.NewDayStarted += OnNewDayStarted;
            
            // Player action events
            EventBus.Instance.PlayerActionPerformed += OnPlayerActionPerformed;
        }
    }
    
    public void AwardExperience(string actionType, SkillType skillCategory, float multiplier = 1f)
    {
        if (!_experienceValues.ContainsKey(actionType)) return;
        
        float baseExperience = _experienceValues[actionType];
        float finalExperience = baseExperience * multiplier;
        
        // Apply skill bonuses if applicable
        if (SkillManager.Instance != null)
        {
            float experienceBonus = SkillManager.Instance.GetCategoryBonus(skillCategory, "experience_bonus");
            finalExperience *= (1f + experienceBonus);
        }
        
        // Award experience to the skill category
        SkillManager.Instance?.GainExperience(skillCategory, finalExperience);
        
        // Emit event for UI feedback
        EventBus.Instance?.EmitExperienceGained(actionType, skillCategory, finalExperience);
        
        GD.Print($"Awarded {finalExperience} XP to {skillCategory} for {actionType}");
    }
    
    public void AwardExperience(string actionType, string specificSkillId, float multiplier = 1f)
    {
        if (!_experienceValues.ContainsKey(actionType)) return;
        
        float baseExperience = _experienceValues[actionType];
        float finalExperience = baseExperience * multiplier;
        
        // Award experience to specific skill
        SkillManager.Instance?.GainExperience(specificSkillId, finalExperience);
        
        GD.Print($"Awarded {finalExperience} XP to skill {specificSkillId} for {actionType}");
    }
    
    private void OnWeaponFired(string weaponId, string weaponName, float damage, int remainingAmmo)
    {
        AwardExperience("weapon_fired", SkillType.Combat);
        
        // Bonus XP for critical hits (if damage is significantly higher than base)
        var weapon = ItemDatabase.Instance?.GetItem(weaponId);
        if (weapon != null && weapon.Metadata.ContainsKey("damage"))
        {
            float baseDamage = (float)weapon.Metadata["damage"];
            if (damage > baseDamage * 1.5f) // Critical hit threshold
            {
                AwardExperience("critical_hit", SkillType.Combat);
            }
        }
    }
    
    private void OnItemCrafted(string recipeId, string outputItemId, int outputQuantity)
    {
        // Check if it's an advanced recipe
        var recipe = ItemDatabase.Instance?.GetRecipe(recipeId);
        bool isAdvanced = recipe?.Inputs?.Count > 3 || recipe?.CraftingTime > 5f;
        
        string actionType = isAdvanced ? "advanced_item_crafted" : "item_crafted";
        AwardExperience(actionType, SkillType.Crafting, outputQuantity);
    }
    
    private void OnConsumableUsed(string itemId, string effectsJson)
    {
        AwardExperience("item_consumed", SkillType.Survival);
    }
    
    private void OnNewDayStarted(int dayNumber)
    {
        AwardExperience("survived_day", SkillType.Survival);
    }
    
    private void OnPlayerActionPerformed(string actionType, string targetId, float staminaCost)
    {
        switch (actionType)
        {
            case "harvest":
                AwardExperience("resource_harvested", SkillType.Survival);
                break;
            case "build":
                AwardExperience("structure_built", SkillType.Building);
                break;
            case "repair":
                AwardExperience("structure_repaired", SkillType.Building);
                break;
            case "reload":
                AwardExperience("reload_weapon", SkillType.Combat);
                break;
        }
    }
    
    // Method for other systems to award custom experience
    public void AwardCustomExperience(SkillType category, float amount, string reason = "custom")
    {
        SkillManager.Instance?.GainExperience(category, amount);
        EventBus.Instance?.EmitExperienceGained(reason, category, amount);
        GD.Print($"Awarded {amount} custom XP to {category} for {reason}");
    }
    
    public void SetExperienceValue(string actionType, float value)
    {
        _experienceValues[actionType] = value;
    }
    
    public float GetExperienceValue(string actionType)
    {
        return _experienceValues.ContainsKey(actionType) ? _experienceValues[actionType] : 0f;
    }
}
}