using Godot;
using System;
using System.Collections.Generic;

namespace SurvivalLooterShooter
{
    /// <summary>
    /// WorldChunk represents a section of the game world with biomes and resources
    /// Manages chunk-specific content and provides efficient access to chunk data
    /// </summary>
    public partial class WorldChunk : Node2D
    {
        // Chunk properties
        public Vector2I ChunkCoords { get; private set; }
        public int ChunkSize { get; private set; }
        public bool IsGenerated { get; private set; } = false;

        // Chunk data
        private BiomeType[,] _biomeMap;
        private List<ChunkResourceNode> _resources = new List<ChunkResourceNode>();
        private Dictionary<Vector2I, BiomeType> _biomeCache = new Dictionary<Vector2I, BiomeType>();

        // Visual representation
        private Node2D _resourceContainer;
        private Node2D _visualContainer;

        // Chunk bounds
        public Rect2 Bounds => new Rect2(
            ChunkCoords.X * ChunkSize,
            ChunkCoords.Y * ChunkSize,
            ChunkSize,
            ChunkSize
        );

        public WorldChunk(Vector2I chunkCoords, int chunkSize)
        {
            ChunkCoords = chunkCoords;
            ChunkSize = chunkSize;
            
            // Initialize biome map
            _biomeMap = new BiomeType[chunkSize, chunkSize];
            
            // Set world position
            GlobalPosition = new Vector2(chunkCoords.X * chunkSize, chunkCoords.Y * chunkSize);
            
            Name = $"Chunk_{chunkCoords.X}_{chunkCoords.Y}";
        }

        public override void _Ready()
        {
            // Create containers for organization
            _resourceContainer = new Node2D();
            _resourceContainer.Name = "Resources";
            AddChild(_resourceContainer);

            _visualContainer = new Node2D();
            _visualContainer.Name = "Visuals";
            AddChild(_visualContainer);
        }

        /// <summary>
        /// Sets the biome type at a specific local position within the chunk
        /// </summary>
        public void SetBiomeAt(int localX, int localY, BiomeType biome)
        {
            if (IsValidLocalCoordinate(localX, localY))
            {
                _biomeMap[localX, localY] = biome;
                _biomeCache[new Vector2I(localX, localY)] = biome;
            }
        }

        /// <summary>
        /// Gets the biome type at a specific local position within the chunk
        /// </summary>
        public BiomeType GetBiomeAt(int localX, int localY)
        {
            if (!IsValidLocalCoordinate(localX, localY))
                return BiomeType.Plains;

            Vector2I key = new Vector2I(localX, localY);
            if (_biomeCache.TryGetValue(key, out BiomeType cachedBiome))
                return cachedBiome;

            return _biomeMap[localX, localY];
        }

        /// <summary>
        /// Gets the biome type at a world position (if within this chunk)
        /// </summary>
        public BiomeType GetBiomeAtWorldPos(Vector2 worldPos)
        {
            Vector2I localPos = WorldToLocalCoords(worldPos);
            return GetBiomeAt(localPos.X, localPos.Y);
        }

        /// <summary>
        /// Adds a resource node to the chunk
        /// </summary>
        public void AddResource(string itemId, Vector2 worldPosition)
        {
            if (!Bounds.HasPoint(worldPosition))
                return;

            var resourceNode = new ChunkResourceNode(itemId, worldPosition);
            _resources.Add(resourceNode);
            
            // Create visual representation
            CreateResourceVisual(resourceNode);
        }

        /// <summary>
        /// Creates a visual representation of a resource node
        /// </summary>
        private void CreateResourceVisual(ChunkResourceNode resourceNode)
        {
            // Create a simple colored circle for now
            // In a full game, this would load appropriate sprites
            var visual = new Node2D();
            visual.Name = $"Resource_{resourceNode.ItemId}";
            visual.GlobalPosition = resourceNode.WorldPosition;

            // Add a simple colored rectangle as placeholder
            var colorRect = new ColorRect();
            colorRect.Size = new Vector2(8, 8);
            colorRect.Position = new Vector2(-4, -4); // Center the rectangle
            colorRect.Color = GetResourceColor(resourceNode.ItemId);
            
            visual.AddChild(colorRect);
            _resourceContainer.AddChild(visual);
        }

        /// <summary>
        /// Gets a color for resource visualization based on item type
        /// </summary>
        private static Color GetResourceColor(string itemId)
        {
            return itemId switch
            {
                "wood" => Colors.Brown,
                "stone" => Colors.Gray,
                "metal_ore" => Colors.Silver,
                "berries" => Colors.Red,
                "herbs" => Colors.Green,
                "mushrooms" => Colors.Purple,
                "water" => Colors.Blue,
                "sand" => Colors.Yellow,
                "ice" => Colors.Cyan,
                "coal" => Colors.Black,
                "grass" => Colors.LightGreen,
                "cactus" => Colors.DarkGreen,
                "reeds" => Colors.Olive,
                "mud" => Colors.SaddleBrown,
                "seaweed" => Colors.DarkSeaGreen,
                "shells" => Colors.Beige,
                "frozen_berries" => Colors.LightBlue,
                "fur" => Colors.Tan,
                "gems" => Colors.Magenta,
                "pearls" => Colors.White,
                "rare_minerals" => Colors.Gold,
                "poison_berries" => Colors.DarkRed,
                _ => Colors.White
            };
        }

        /// <summary>
        /// Gets all resources in the chunk
        /// </summary>
        public List<ChunkResourceNode> GetResources()
        {
            return new List<ChunkResourceNode>(_resources);
        }

        /// <summary>
        /// Gets resources within a specific radius of a world position
        /// </summary>
        public List<ChunkResourceNode> GetResourcesNear(Vector2 worldPosition, float radius)
        {
            var nearbyResources = new List<ChunkResourceNode>();
            
            foreach (var resource in _resources)
            {
                if (resource.WorldPosition.DistanceTo(worldPosition) <= radius)
                {
                    nearbyResources.Add(resource);
                }
            }
            
            return nearbyResources;
        }

        /// <summary>
        /// Removes a resource from the chunk
        /// </summary>
        public bool RemoveResource(ChunkResourceNode resource)
        {
            if (_resources.Remove(resource))
            {
                // Remove visual representation
                var visual = _resourceContainer.GetNode($"Resource_{resource.ItemId}");
                visual?.QueueFree();
                return true;
            }
            return false;
        }

        /// <summary>
        /// Converts world coordinates to local chunk coordinates
        /// </summary>
        public Vector2I WorldToLocalCoords(Vector2 worldPosition)
        {
            Vector2 localPos = worldPosition - GlobalPosition;
            return new Vector2I(
                Mathf.FloorToInt(localPos.X),
                Mathf.FloorToInt(localPos.Y)
            );
        }

        /// <summary>
        /// Converts local chunk coordinates to world coordinates
        /// </summary>
        public Vector2 LocalToWorldCoords(Vector2I localPosition)
        {
            return GlobalPosition + new Vector2(localPosition.X, localPosition.Y);
        }

        /// <summary>
        /// Checks if local coordinates are valid for this chunk
        /// </summary>
        private bool IsValidLocalCoordinate(int x, int y)
        {
            return x >= 0 && x < ChunkSize && y >= 0 && y < ChunkSize;
        }

        /// <summary>
        /// Sets the chunk as generated
        /// </summary>
        public void SetGenerated(bool generated)
        {
            IsGenerated = generated;
        }

        /// <summary>
        /// Gets the dominant biome type in the chunk
        /// </summary>
        public BiomeType GetDominantBiome()
        {
            var biomeCounts = new Dictionary<BiomeType, int>();
            
            for (int x = 0; x < ChunkSize; x += 4) // Sample every 4th cell for performance
            {
                for (int y = 0; y < ChunkSize; y += 4)
                {
                    BiomeType biome = GetBiomeAt(x, y);
                    biomeCounts[biome] = biomeCounts.GetValueOrDefault(biome, 0) + 1;
                }
            }

            BiomeType dominantBiome = BiomeType.Plains;
            int maxCount = 0;
            
            foreach (var kvp in biomeCounts)
            {
                if (kvp.Value > maxCount)
                {
                    maxCount = kvp.Value;
                    dominantBiome = kvp.Key;
                }
            }

            return dominantBiome;
        }

        /// <summary>
        /// Gets chunk statistics for debugging
        /// </summary>
        public ChunkStats GetStats()
        {
            var biomeCounts = new Dictionary<BiomeType, int>();
            
            for (int x = 0; x < ChunkSize; x++)
            {
                for (int y = 0; y < ChunkSize; y++)
                {
                    BiomeType biome = GetBiomeAt(x, y);
                    biomeCounts[biome] = biomeCounts.GetValueOrDefault(biome, 0) + 1;
                }
            }

            return new ChunkStats
            {
                ChunkCoords = ChunkCoords,
                ResourceCount = _resources.Count,
                BiomeCounts = biomeCounts,
                DominantBiome = GetDominantBiome(),
                IsGenerated = IsGenerated
            };
        }

        /// <summary>
        /// Cleans up chunk resources
        /// </summary>
        public override void _ExitTree()
        {
            _resources.Clear();
            _biomeCache.Clear();
        }
    }

    /// <summary>
    /// Represents a resource node data within a chunk
    /// </summary>
    public class ChunkResourceNode
    {
        public string ItemId { get; set; }
        public Vector2 WorldPosition { get; set; }
        public int Quantity { get; set; } = 1;
        public bool IsHarvested { get; set; } = false;
        public float RespawnTime { get; set; } = 300f; // 5 minutes default
        public float LastHarvestTime { get; set; } = 0f;

        public ChunkResourceNode(string itemId, Vector2 worldPosition, int quantity = 1)
        {
            ItemId = itemId;
            WorldPosition = worldPosition;
            Quantity = quantity;
        }

        /// <summary>
        /// Checks if the resource can be harvested
        /// </summary>
        public bool CanHarvest()
        {
            if (!IsHarvested)
                return true;

            float currentTime = (float)Time.GetUnixTimeFromSystem();
            return currentTime - LastHarvestTime >= RespawnTime;
        }

        /// <summary>
        /// Harvests the resource
        /// </summary>
        public bool Harvest()
        {
            if (!CanHarvest())
                return false;

            IsHarvested = true;
            LastHarvestTime = (float)Time.GetUnixTimeFromSystem();
            return true;
        }
    }

    /// <summary>
    /// Statistics for chunk debugging and monitoring
    /// </summary>
    public class ChunkStats
    {
        public Vector2I ChunkCoords { get; set; }
        public int ResourceCount { get; set; }
        public Dictionary<BiomeType, int> BiomeCounts { get; set; } = new Dictionary<BiomeType, int>();
        public BiomeType DominantBiome { get; set; }
        public bool IsGenerated { get; set; }
    }
}