using Godot;
using System;

namespace SurvivalLooterShooter
{
    /// <summary>
    /// WeaponController handles firing, reloading, and weapon switching
    /// </summary>
    public partial class WeaponController : Node
    {
        private Inventory _inventory;
        private Weapon _currentWeapon;
        private float _lastFireTime = 0f;
        private bool _isReloading = false;
        private float _reloadStartTime = 0f;

        // Events for weapon actions
        [Signal]
        public delegate void WeaponFiredEventHandler(string weaponName, float damage);
        
        [Signal]
        public delegate void WeaponReloadStartedEventHandler(string weaponName, float reloadTime);
        
        [Signal]
        public delegate void WeaponReloadCompletedEventHandler(string weaponName);
        
        [Signal]
        public delegate void WeaponSwitchedEventHandler(string oldWeaponName, string newWeaponName);
        
        [Signal]
        public delegate void AmmoDepletedEventHandler(string weaponName);

        public Weapon CurrentWeapon => _currentWeapon;
        public bool IsReloading => _isReloading;

        /// <summary>
        /// Initializes the weapon controller with an inventory reference
        /// </summary>
        public void Initialize(Inventory inventory)
        {
            _inventory = inventory ?? throw new ArgumentNullException(nameof(inventory));
            
            // Listen for inventory changes to update equipped weapon
            _inventory.ItemEquipped += OnItemEquipped;
            
            // Check if there's already a weapon equipped
            UpdateCurrentWeapon();
            
            GD.Print("WeaponController initialized");
        }

        public override void _Process(double delta)
        {
            // Handle reload timing
            if (_isReloading && _currentWeapon != null)
            {
                float elapsedTime = (float)Time.GetUnixTimeFromSystem() - _reloadStartTime;
                if (elapsedTime >= _currentWeapon.ReloadTime)
                {
                    CompleteReload();
                }
            }
        }

        /// <summary>
        /// Attempts to fire the current weapon
        /// </summary>
        public bool Fire()
        {
            if (_currentWeapon == null)
            {
                GD.PrintErr("No weapon equipped");
                return false;
            }

            if (_isReloading)
            {
                GD.Print("Cannot fire while reloading");
                return false;
            }

            // Check fire rate
            float currentTime = (float)Time.GetUnixTimeFromSystem();
            if (currentTime - _lastFireTime < _currentWeapon.FireRate)
            {
                return false; // Too soon to fire again
            }

            // Check if weapon can fire
            if (!_currentWeapon.CanFire())
            {
                if (_currentWeapon.CurrentAmmo <= 0)
                {
                    EmitSignal(SignalName.AmmoDepleted, _currentWeapon.Name);
                    
                    // Emit event bus event
                    var equippedSlot = _inventory?.GetEquippedWeapon();
                    string weaponId = equippedSlot?.ItemId ?? "";
                    EventBus.Instance?.EmitSignal(EventBus.SignalName.WeaponAmmoDepleted, weaponId, _currentWeapon.Name);
                    
                    GD.Print($"{_currentWeapon.Name} is out of ammo");
                }
                else if (_currentWeapon.IsBroken())
                {
                    GD.Print($"{_currentWeapon.Name} is broken and cannot fire");
                }
                return false;
            }

            // Fire the weapon
            if (_currentWeapon.Fire())
            {
                _lastFireTime = currentTime;
                float baseDamage = _currentWeapon.GetEffectiveDamage();
                
                // Apply skill bonuses to damage
                float finalDamage = ApplySkillBonuses(baseDamage);
                
                EmitSignal(SignalName.WeaponFired, _currentWeapon.Name, finalDamage);
                
                // Emit event bus event
                var equippedSlot = _inventory?.GetEquippedWeapon();
                string weaponId = equippedSlot?.ItemId ?? "";
                EventBus.Instance?.EmitWeaponFired(weaponId, _currentWeapon.Name, finalDamage, _currentWeapon.CurrentAmmo);
                
                GD.Print($"Fired {_currentWeapon.Name} for {finalDamage:F1} damage. Ammo: {_currentWeapon.CurrentAmmo}/{_currentWeapon.MagazineSize}");
                
                // Update weapon metadata in inventory
                UpdateWeaponInInventory();
                
                return true;
            }

            return false;
        }

        /// <summary>
        /// Starts the reload process
        /// </summary>
        public bool StartReload()
        {
            if (_currentWeapon == null)
            {
                GD.PrintErr("No weapon equipped");
                return false;
            }

            if (_isReloading)
            {
                GD.Print("Already reloading");
                return false;
            }

            if (!_currentWeapon.NeedsReload())
            {
                GD.Print("Weapon doesn't need reloading");
                return false;
            }

            // Check if we have compatible ammo
            int availableAmmo = _inventory.GetItemQuantity(_currentWeapon.AmmoType);
            if (availableAmmo <= 0)
            {
                GD.Print($"No {_currentWeapon.AmmoType} available for reload");
                return false;
            }

            // Start reload
            _isReloading = true;
            _reloadStartTime = (float)Time.GetUnixTimeFromSystem();
            
            EmitSignal(SignalName.WeaponReloadStarted, _currentWeapon.Name, _currentWeapon.ReloadTime);
            
            // Emit event bus event
            var equippedSlot = _inventory?.GetEquippedWeapon();
            string weaponId = equippedSlot?.ItemId ?? "";
            EventBus.Instance?.EmitSignal(EventBus.SignalName.WeaponReloadStarted, weaponId, _currentWeapon.Name, _currentWeapon.ReloadTime);
            
            GD.Print($"Started reloading {_currentWeapon.Name} ({_currentWeapon.ReloadTime}s)");
            
            return true;
        }

        /// <summary>
        /// Completes the reload process
        /// </summary>
        private void CompleteReload()
        {
            if (!_isReloading || _currentWeapon == null)
                return;

            // Get available ammo from inventory
            int availableAmmo = _inventory.GetItemQuantity(_currentWeapon.AmmoType);
            if (availableAmmo <= 0)
            {
                GD.Print("No ammo available to complete reload");
                _isReloading = false;
                return;
            }

            // Reload the weapon
            int ammoConsumed = _currentWeapon.Reload(availableAmmo);
            
            // Remove consumed ammo from inventory
            if (ammoConsumed > 0)
            {
                _inventory.RemoveItem(_currentWeapon.AmmoType, ammoConsumed);
            }

            // Update weapon in inventory
            UpdateWeaponInInventory();

            _isReloading = false;
            
            EmitSignal(SignalName.WeaponReloadCompleted, _currentWeapon.Name);
            
            // Emit event bus event
            var equippedSlot = _inventory?.GetEquippedWeapon();
            string weaponId = equippedSlot?.ItemId ?? "";
            EventBus.Instance?.EmitSignal(EventBus.SignalName.WeaponReloadCompleted, weaponId, _currentWeapon.Name, _currentWeapon.CurrentAmmo);
            
            GD.Print($"Reload completed. {_currentWeapon.Name} now has {_currentWeapon.CurrentAmmo}/{_currentWeapon.MagazineSize} ammo");
        }

        /// <summary>
        /// Switches to a different weapon by item ID
        /// </summary>
        public bool SwitchWeapon(string weaponId)
        {
            if (string.IsNullOrEmpty(weaponId))
                return false;

            // Check if we have the weapon in inventory
            if (!_inventory.HasItem(weaponId))
            {
                GD.PrintErr($"Weapon {weaponId} not found in inventory");
                return false;
            }

            // Equip the weapon
            if (_inventory.EquipItem(weaponId, "weapon"))
            {
                UpdateCurrentWeapon();
                return true;
            }

            return false;
        }

        /// <summary>
        /// Updates the current weapon from the equipped weapon slot
        /// </summary>
        private void UpdateCurrentWeapon()
        {
            var equippedWeaponSlot = _inventory?.GetEquippedWeapon();
            Weapon oldWeapon = _currentWeapon;

            if (equippedWeaponSlot != null && !equippedWeaponSlot.IsEmpty)
            {
                var item = ItemDatabase.Instance?.GetItem(equippedWeaponSlot.ItemId);
                if (item != null)
                {
                    // Create weapon from item and apply slot metadata
                    _currentWeapon = Weapon.FromItem(item);
                    if (_currentWeapon != null)
                    {
                        // Apply any metadata from the inventory slot (like durability, current ammo)
                        foreach (var kvp in equippedWeaponSlot.Metadata)
                        {
                            _currentWeapon.SetMetadata(kvp.Key, kvp.Value);
                        }
                        _currentWeapon.InitializeFromMetadata();
                    }
                }
            }
            else
            {
                _currentWeapon = null;
            }

            // Cancel reload if weapon changed
            if (_isReloading && _currentWeapon != oldWeapon)
            {
                _isReloading = false;
            }

            // Emit weapon switched signal
            if (_currentWeapon != oldWeapon)
            {
                string oldWeaponName = oldWeapon?.Name ?? "";
                string newWeaponName = _currentWeapon?.Name ?? "";
                EmitSignal(SignalName.WeaponSwitched, oldWeaponName, newWeaponName);
                
                // Emit event bus event
                var equippedSlot = _inventory?.GetEquippedWeapon();
                string oldWeaponId = ""; // We don't track the old weapon ID easily, so leave empty for now
                string newWeaponId = equippedSlot?.ItemId ?? "";
                EventBus.Instance?.EmitSignal(EventBus.SignalName.WeaponSwitched, oldWeaponId, newWeaponId, oldWeaponName, newWeaponName);
                
                if (_currentWeapon != null)
                {
                    GD.Print($"Switched to {_currentWeapon.Name}");
                }
                else
                {
                    GD.Print("No weapon equipped");
                }
            }
        }

        /// <summary>
        /// Updates the weapon's metadata in the inventory
        /// </summary>
        private void UpdateWeaponInInventory()
        {
            if (_currentWeapon == null || _inventory == null)
                return;

            var equippedSlot = _inventory.GetEquippedWeapon();
            if (equippedSlot != null && !equippedSlot.IsEmpty)
            {
                // Update the slot's metadata with current weapon state
                equippedSlot.Metadata["current_ammo"] = _currentWeapon.CurrentAmmo;
                equippedSlot.Metadata["durability"] = _currentWeapon.Durability;
            }
        }

        /// <summary>
        /// Event handler for when items are equipped
        /// </summary>
        private void OnItemEquipped(string slotType, string itemId)
        {
            if (slotType == "weapon")
            {
                UpdateCurrentWeapon();
            }
        }

        /// <summary>
        /// Gets weapon information for UI display
        /// </summary>
        public WeaponInfo GetWeaponInfo()
        {
            if (_currentWeapon == null)
                return null;

            return new WeaponInfo
            {
                Name = _currentWeapon.Name,
                CurrentAmmo = _currentWeapon.CurrentAmmo,
                MagazineSize = _currentWeapon.MagazineSize,
                Damage = _currentWeapon.GetEffectiveDamage(),
                Condition = _currentWeapon.GetConditionPercentage(),
                IsReloading = _isReloading,
                AmmoType = _currentWeapon.AmmoType
            };
        }

        /// <summary>
        /// Applies skill bonuses to weapon damage
        /// </summary>
        private float ApplySkillBonuses(float baseDamage)
        {
            if (SkillManager.Instance == null) return baseDamage;
            
            float finalDamage = baseDamage;
            
            // Apply weapon proficiency bonus
            float damageBonus = SkillManager.Instance.GetSkillBonus("weapon_proficiency", "damage_multiplier");
            finalDamage *= (1f + damageBonus);
            
            // Check for critical hit
            float critChance = SkillManager.Instance.GetSkillBonus("critical_hit", "crit_chance");
            if (GD.Randf() < critChance)
            {
                float critDamage = SkillManager.Instance.GetSkillBonus("critical_hit", "crit_damage");
                finalDamage *= (1f + critDamage);
                
                // Award critical hit experience
                ExperienceTracker.Instance?.AwardExperience("critical_hit", SkillType.Combat);
            }
            
            return finalDamage;
        }
        
        /// <summary>
        /// Gets the effective reload time with skill bonuses
        /// </summary>
        public float GetEffectiveReloadTime()
        {
            if (_currentWeapon == null) return 0f;
            
            float reloadTime = _currentWeapon.ReloadTime;
            
            // Apply reload speed skill bonus
            if (SkillManager.Instance != null)
            {
                float speedBonus = SkillManager.Instance.GetSkillBonus("reload_speed", "reload_speed");
                reloadTime *= (1f - speedBonus);
            }
            
            return Mathf.Max(0.1f, reloadTime); // Minimum 0.1 second reload time
        }

        public override void _ExitTree()
        {
            // Unsubscribe from events
            if (_inventory != null)
            {
                _inventory.ItemEquipped -= OnItemEquipped;
            }
        }
    }

    /// <summary>
    /// Data structure for weapon information display
    /// </summary>
    public class WeaponInfo
    {
        public string Name { get; set; }
        public int CurrentAmmo { get; set; }
        public int MagazineSize { get; set; }
        public float Damage { get; set; }
        public float Condition { get; set; }
        public bool IsReloading { get; set; }
        public string AmmoType { get; set; }
    }
}