[gd_scene load_steps=3 format=3 uid="uid://bqxvn8ywqxqxq"]

[ext_resource type="Script" uid="uid://mpaeequsuri0" path="res://Scripts/ItemPickup.cs" id="1_0x8y9"]

[sub_resource type="CircleShape2D" id="CircleShape2D_1"]
radius = 50.0

[node name="ItemPickup" type="Area2D" groups=["item_pickups"]]
script = ExtResource("1_0x8y9")

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
shape = SubResource("CircleShape2D_1")

[node name="Sprite2D" type="Sprite2D" parent="."]

[node name="QuantityLabel" type="Label" parent="."]
offset_left = -20.0
offset_top = -40.0
offset_right = 20.0
offset_bottom = -17.0
text = "1"
horizontal_alignment = 1

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
