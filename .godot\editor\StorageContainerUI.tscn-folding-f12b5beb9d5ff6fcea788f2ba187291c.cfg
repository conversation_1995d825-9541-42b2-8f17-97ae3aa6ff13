[folding]

node_unfolds=[Node<PERSON><PERSON>("."), PackedStringArray("Layout"), NodePath("Background"), PackedStringArray("Layout"), NodePath("Background/VBoxContainer"), PackedStringArray("Layout"), <PERSON>dePath("Background/VBoxContainer/HeaderContainer"), PackedStringArray("Layout"), NodePath("Background/VBoxContainer/HeaderContainer/ContainerNameLabel"), PackedStringArray("Layout"), NodePath("Background/VBoxContainer/HeaderContainer/CapacityLabel"), PackedStringArray("Layout"), NodePath("Background/VBoxContainer/ControlsContainer"), PackedStringArray("Layout"), NodePath("Background/VBoxContainer/ControlsContainer/SortButton"), PackedStringArray("Layout"), NodePath("Background/VBoxContainer/ControlsContainer/LockButton"), PackedStringArray("Layout"), Node<PERSON>ath("Background/VBoxContainer/ControlsContainer/SortModeOption"), PackedStringArray("Layout", "popup/item_0", "popup/item_1", "popup/item_2", "popup/item_3", "popup/item_4"), NodePath("Background/VBoxContainer/ControlsContainer/FilterTypeOption"), PackedStringArray("Layout", "popup/item_0", "popup/item_1", "popup/item_2", "popup/item_3", "popup/item_4"), NodePath("Background/VBoxContainer/ControlsContainer/AutoSortCheckbox"), PackedStringArray("Layout"), NodePath("Background/VBoxContainer/ContentContainer"), PackedStringArray("Layout"), NodePath("Background/VBoxContainer/ContentContainer/ContainerSection"), PackedStringArray("Layout"), NodePath("Background/VBoxContainer/ContentContainer/ContainerSection/ContainerLabel"), PackedStringArray("Layout"), NodePath("Background/VBoxContainer/ContentContainer/ContainerSection/ContainerScroll"), PackedStringArray("Layout"), NodePath("Background/VBoxContainer/ContentContainer/ContainerSection/ContainerScroll/ContainerGrid"), PackedStringArray("Layout"), NodePath("Background/VBoxContainer/ContentContainer/InventorySection"), PackedStringArray("Layout"), NodePath("Background/VBoxContainer/ContentContainer/InventorySection/InventoryLabel"), PackedStringArray("Layout"), NodePath("Background/VBoxContainer/ContentContainer/InventorySection/InventoryScroll"), PackedStringArray("Layout"), NodePath("Background/VBoxContainer/ContentContainer/InventorySection/InventoryScroll/InventoryGrid"), PackedStringArray("Layout"), NodePath("Background/CloseButton"), PackedStringArray("Layout"), NodePath("Background/AccessCodePanel"), PackedStringArray("Visibility", "Layout"), NodePath("Background/AccessCodePanel/VBoxContainer"), PackedStringArray("Layout"), NodePath("Background/AccessCodePanel/VBoxContainer/CodeLabel"), PackedStringArray("Layout"), NodePath("Background/AccessCodePanel/VBoxContainer/AccessCodeInput"), PackedStringArray("Layout", "Secret"), NodePath("Background/AccessCodePanel/VBoxContainer/ButtonContainer"), PackedStringArray("Layout"), NodePath("Background/AccessCodePanel/VBoxContainer/ButtonContainer/UnlockButton"), PackedStringArray("Layout"), NodePath("Background/AccessCodePanel/VBoxContainer/ButtonContainer/CancelButton"), PackedStringArray("Layout")]
resource_unfolds=[]
nodes_folded=[]
