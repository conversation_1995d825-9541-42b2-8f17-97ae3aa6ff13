using Godot;
using System;
using System.Collections.Generic;
using System.Linq;

namespace SurvivalLooterShooter
{
    public partial class SkillManager : Node
    {
    public static SkillManager Instance { get; private set; }
    
    private Dictionary<string, Skill> _skills = new Dictionary<string, Skill>();
    private Dictionary<SkillType, List<string>> _skillsByCategory = new Dictionary<SkillType, List<string>>();
    private int _availableSkillPoints = 0;
    
    [Signal]
    public delegate void SkillPointsChangedEventHandler(int newAmount);
    
    [Signal]
    public delegate void SkillUnlockedEventHandler(string skillId);
    
    public override void _Ready()
    {
        if (Instance == null)
        {
            Instance = this;
            GD.Print("SkillManager singleton initialized");
            InitializeSkills();
            ConnectToEventBus();
        }
        else
        {
            GD.PrintErr("Multiple SkillManager instances detected! Removing duplicate.");
            QueueFree();
        }
    }
    
    private void ConnectToEventBus()
    {
        if (EventBus.Instance != null)
        {
            EventBus.Instance.SkillLevelUp += OnSkillLevelUp;
            EventBus.Instance.ExperienceGained += OnExperienceGained;
        }
    }
    
    private void InitializeSkills()
    {
        // Initialize skill categories
        foreach (SkillType category in Enum.GetValues<SkillType>())
        {
            _skillsByCategory[category] = new List<string>();
        }
        
        // Combat Skills
        CreateSkill("weapon_proficiency", "Weapon Proficiency", "Increases damage with all weapons", SkillType.Combat,
            new Dictionary<string, float> { {"damage_multiplier", 0.02f} });
        
        CreateSkill("critical_hit", "Critical Hit", "Increases critical hit chance and damage", SkillType.Combat,
            new Dictionary<string, float> { {"crit_chance", 0.01f}, {"crit_damage", 0.05f} });
        
        CreateSkill("reload_speed", "Fast Reload", "Reduces weapon reload time", SkillType.Combat,
            new Dictionary<string, float> { {"reload_speed", 0.03f} });
        
        CreateSkill("accuracy", "Accuracy", "Improves weapon accuracy and reduces spread", SkillType.Combat,
            new Dictionary<string, float> { {"accuracy_bonus", 0.02f} });
        
        // Crafting Skills
        CreateSkill("crafting_efficiency", "Crafting Efficiency", "Reduces material costs for crafting", SkillType.Crafting,
            new Dictionary<string, float> { {"material_savings", 0.01f} });
        
        CreateSkill("quality_bonus", "Quality Crafting", "Chance to create higher quality items", SkillType.Crafting,
            new Dictionary<string, float> { {"quality_chance", 0.02f} });
        
        CreateSkill("crafting_speed", "Fast Crafting", "Reduces crafting time", SkillType.Crafting,
            new Dictionary<string, float> { {"speed_bonus", 0.05f} });
        
        CreateSkill("advanced_recipes", "Advanced Recipes", "Unlocks complex crafting recipes", SkillType.Crafting,
            new Dictionary<string, float> { {"recipe_unlock", 1f} });
        
        // Survival Skills
        CreateSkill("metabolism", "Efficient Metabolism", "Reduces hunger and thirst decay rate", SkillType.Survival,
            new Dictionary<string, float> { {"decay_reduction", 0.02f} });
        
        CreateSkill("foraging", "Foraging", "Increases resource yield from harvesting", SkillType.Survival,
            new Dictionary<string, float> { {"harvest_bonus", 0.03f} });
        
        CreateSkill("medicine", "Medicine", "Improves healing item effectiveness", SkillType.Survival,
            new Dictionary<string, float> { {"healing_bonus", 0.05f} });
        
        CreateSkill("endurance", "Endurance", "Increases maximum stamina", SkillType.Survival,
            new Dictionary<string, float> { {"max_stamina", 2f} });
        
        // Building Skills
        CreateSkill("construction", "Construction", "Reduces building material costs", SkillType.Building,
            new Dictionary<string, float> { {"material_efficiency", 0.02f} });
        
        CreateSkill("durability", "Structural Integrity", "Increases building durability", SkillType.Building,
            new Dictionary<string, float> { {"durability_bonus", 0.05f} });
        
        CreateSkill("building_speed", "Fast Construction", "Reduces building time", SkillType.Building,
            new Dictionary<string, float> { {"build_speed", 0.04f} });
        
        CreateSkill("advanced_structures", "Advanced Structures", "Unlocks complex building blueprints", SkillType.Building,
            new Dictionary<string, float> { {"structure_unlock", 1f} });
    }
    
    private void CreateSkill(string id, string name, string description, SkillType category, 
        Dictionary<string, float> passiveBonuses, List<string> prerequisites = null)
    {
        var skill = new Skill
        {
            Id = id,
            Name = name,
            Description = description,
            Category = category,
            PassiveBonuses = passiveBonuses,
            Prerequisites = prerequisites ?? new List<string>()
        };
        
        _skills[id] = skill;
        _skillsByCategory[category].Add(id);
    }
    
    public void GainExperience(SkillType category, float amount)
    {
        var categorySkills = _skillsByCategory[category];
        foreach (var skillId in categorySkills)
        {
            var skill = _skills[skillId];
            if (skill.Level > 0) // Only gain XP in skills that have been unlocked
            {
                skill.AddExperience(amount);
            }
        }
    }
    
    public void GainExperience(string skillId, float amount)
    {
        if (_skills.ContainsKey(skillId))
        {
            _skills[skillId].AddExperience(amount);
        }
    }
    
    public bool UnlockSkill(string skillId)
    {
        if (!_skills.ContainsKey(skillId)) return false;
        if (_availableSkillPoints <= 0) return false;
        
        var skill = _skills[skillId];
        if (skill.Level > 0) return false; // Already unlocked
        
        // Check prerequisites
        foreach (var prereq in skill.Prerequisites)
        {
            if (!_skills.ContainsKey(prereq) || _skills[prereq].Level == 0)
            {
                return false;
            }
        }
        
        skill.Level = 1;
        _availableSkillPoints--;
        
        EmitSignal(SignalName.SkillUnlocked, skillId);
        EmitSignal(SignalName.SkillPointsChanged, _availableSkillPoints);
        
        return true;
    }
    
    public void AddSkillPoints(int points)
    {
        _availableSkillPoints += points;
        EmitSignal(SignalName.SkillPointsChanged, _availableSkillPoints);
    }
    
    public int GetAvailableSkillPoints()
    {
        return _availableSkillPoints;
    }
    
    public Skill GetSkill(string skillId)
    {
        return _skills.ContainsKey(skillId) ? _skills[skillId] : null;
    }
    
    public List<Skill> GetSkillsByCategory(SkillType category)
    {
        if (!_skillsByCategory.ContainsKey(category)) return new List<Skill>();
        
        return _skillsByCategory[category]
            .Where(id => _skills.ContainsKey(id))
            .Select(id => _skills[id])
            .ToList();
    }
    
    public float GetSkillBonus(string skillId, string bonusType)
    {
        var skill = GetSkill(skillId);
        return skill?.GetBonusValue(bonusType) ?? 0f;
    }
    
    public float GetCategoryBonus(SkillType category, string bonusType)
    {
        float totalBonus = 0f;
        var categorySkills = GetSkillsByCategory(category);
        
        foreach (var skill in categorySkills)
        {
            totalBonus += skill.GetBonusValue(bonusType);
        }
        
        return totalBonus;
    }
    
    public bool HasActiveAbility(string skillId, string abilityId)
    {
        var skill = GetSkill(skillId);
        return skill?.HasActiveAbility(abilityId) ?? false;
    }
    
    public Dictionary<string, object> GetSaveData()
    {
        var saveData = new Dictionary<string, object>();
        var skillData = new Dictionary<string, Dictionary<string, object>>();
        
        foreach (var kvp in _skills)
        {
            skillData[kvp.Key] = new Dictionary<string, object>
            {
                {"level", kvp.Value.Level},
                {"experience", kvp.Value.Experience}
            };
        }
        
        saveData["skills"] = skillData;
        saveData["available_skill_points"] = _availableSkillPoints;
        
        return saveData;
    }
    
    public void LoadSaveData(Dictionary<string, object> saveData)
    {
        if (saveData.ContainsKey("skills"))
        {
            var skillData = (Dictionary<string, Dictionary<string, object>>)saveData["skills"];
            foreach (var kvp in skillData)
            {
                if (_skills.ContainsKey(kvp.Key))
                {
                    var skill = _skills[kvp.Key];
                    skill.Level = (int)kvp.Value["level"];
                    skill.Experience = (float)kvp.Value["experience"];
                }
            }
        }
        
        if (saveData.ContainsKey("available_skill_points"))
        {
            _availableSkillPoints = (int)saveData["available_skill_points"];
        }
    }
    
    private void OnSkillLevelUp(string skillId, int newLevel)
    {
        // Award skill point every few levels
        if (newLevel % 5 == 0)
        {
            AddSkillPoints(1);
        }
        
        GD.Print($"Skill {skillId} leveled up to {newLevel}");
    }
    
    private void OnExperienceGained(string source, int categoryInt, float amount)
    {
        SkillType category = (SkillType)categoryInt;
        GainExperience(category, amount);
    }
    
    // Endgame content support methods
        public float GetAverageSkillLevel()
        {
            if (_skills.Count == 0) return 0f;
            return (float)_skills.Values.Average(s => s.Level);
        }

        public float GetTotalSkillLevel()
        {
            return _skills.Values.Sum(s => s.Level);
        }

        public Dictionary<string, float> GetAllSkillLevels()
        {
            return _skills.ToDictionary(kvp => kvp.Key, kvp => (float)kvp.Value.Level);
        }

        public void SetSkillLevel(string skillId, float level)
        {
            if (_skills.ContainsKey(skillId))
            {
                _skills[skillId].Level = (int)level;
                GD.Print($"Set skill {skillId} to level {level}");
            }
        }

        public string GetHighestSkill()
        {
            if (_skills.Count == 0) return "none";
            return _skills.OrderByDescending(kvp => kvp.Value.Level).First().Key;
        }

        public void SetXPMultiplier(float multiplier)
        {
            _xpMultiplier = multiplier;
            GD.Print($"XP multiplier set to: {multiplier}");
        }

        private float _xpMultiplier = 1f;
    }
}