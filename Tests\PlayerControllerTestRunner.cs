using Godot;

namespace SurvivalLooterShooter
{
    /// <summary>
    /// Simple test runner to verify PlayerController integration works without errors
    /// </summary>
    public partial class PlayerControllerTestRunner : Node
    {
        public override void _Ready()
        {
            GD.Print("=== PlayerController Integration Test Runner ===");
            
            // Wait a frame to ensure all systems are initialized
            CallDeferred(nameof(RunBasicIntegrationTest));
        }

        private void RunBasicIntegrationTest()
        {
            try
            {
                // Find GameManager
                var gameManager = GetTree().GetFirstNodeInGroup("game_manager") as GameManager;
                if (gameManager == null)
                {
                    GD.PrintErr("❌ GameManager not found in scene");
                    return;
                }

                // Test system access using singleton instances
                var inventory = Inventory.Instance;
                var craftingSystem = CraftingSystem.Instance;
                var weaponController = GetNodeOrNull<WeaponController>("WeaponController"); // WeaponController doesn't use singleton pattern
                var survivalStatsSystem = SurvivalStatsSystem.Instance;
                var saveManager = SaveManager.Instance;
                var playerController = GetNodeOrNull<PlayerController>("PlayerController"); // PlayerController doesn't use singleton pattern

                // Verify all systems are initialized
                bool allSystemsReady = inventory != null && 
                                     craftingSystem != null && 
                                     weaponController != null && 
                                     survivalStatsSystem != null && 
                                     saveManager != null && 
                                     playerController != null;

                if (allSystemsReady)
                {
                    GD.Print("✅ All systems initialized successfully");
                    
                    // Test PlayerController integration
                    if (playerController.IsAlive)
                    {
                        GD.Print("✅ PlayerController is alive and functional");
                    }
                    
                    // Test system coordination
                    if (weaponController.CurrentWeapon != null)
                    {
                        GD.Print("✅ Weapon system integrated correctly");
                    }
                    
                    if (survivalStatsSystem.Health.CurrentValue > 0)
                    {
                        GD.Print("✅ Survival stats system integrated correctly");
                    }
                    
                    GD.Print("🎉 PlayerController integration test PASSED!");
                }
                else
                {
                    GD.PrintErr("❌ Some systems failed to initialize");
                }
            }
            catch (System.Exception ex)
            {
                GD.PrintErr($"❌ Integration test failed: {ex.Message}");
            }
            
            GD.Print("=== End PlayerController Integration Test ===");
        }
    }
}