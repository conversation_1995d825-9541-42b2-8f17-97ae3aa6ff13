using Godot;
using System;
using System.Collections.Generic;
using System.Linq;

namespace SurvivalLooterShooter
{
    /// <summary>
    /// Represents a storage container that can hold items with different capacities and access controls
    /// </summary>
    public partial class StorageContainer : Node
    {
        [Export] public string ContainerId { get; set; }
        [Export] public string ContainerName { get; set; }
        [Export] public int MaxCapacity { get; set; } = 50;
        [Export] public ContainerType Type { get; set; } = ContainerType.Basic;
        [Export] public bool IsLocked { get; set; } = false;
        [Export] public string AccessCode { get; set; } = "";
        
        // Core storage - using item ID as key for quick lookup
        private readonly Dictionary<string, InventorySlot> _items = new();
        
        // Access control
        private readonly HashSet<string> _authorizedPlayers = new();
        private readonly Dictionary<string, ContainerPermission> _playerPermissions = new();
        
        // Sorting and filtering
        private ContainerSortMode _sortMode = ContainerSortMode.None;
        private readonly HashSet<string> _itemFilters = new();
        private bool _autoSort = false;
        
        // Events
        [Signal]
        public delegate void ContainerChangedEventHandler(string itemId, int quantityChange);
        
        [Signal]
        public delegate void ContainerAccessedEventHandler(string playerId, ContainerAccessType accessType);
        
        [Signal]
        public delegate void ContainerLockedEventHandler(bool isLocked);

        public int CurrentCapacity => _items.Values.Sum(slot => slot.Quantity);
        public int AvailableCapacity => MaxCapacity - CurrentCapacity;
        public bool IsFull => CurrentCapacity >= MaxCapacity;
        public bool IsEmpty => _items.Count == 0;
        public Dictionary<string, InventorySlot> Items => new(_items);

        public override void _Ready()
        {
            AddToGroup("storage_containers");
            
            if (string.IsNullOrEmpty(ContainerId))
                ContainerId = Guid.NewGuid().ToString();
                
            Logger.LogInfo("StorageContainer", $"Storage container {ContainerName} initialized with capacity {MaxCapacity}");
        }

        /// <summary>
        /// Initializes the storage container with specified parameters
        /// </summary>
        public void Initialize(string containerId, string name, int capacity, ContainerType type = ContainerType.Basic)
        {
            ContainerId = containerId;
            ContainerName = name;
            MaxCapacity = capacity;
            Type = type;
            
            Logger.LogInfo("StorageContainer", $"Initialized storage container: {name} (ID: {containerId}, Capacity: {capacity})");
        }

        /// <summary>
        /// Attempts to add items to the container
        /// </summary>
        public bool TryAddItem(string itemId, int quantity, Dictionary<string, object> metadata = null)
        {
            if (quantity <= 0) return false;
            
            var item = ItemDatabase.Instance?.GetItem(itemId);
            if (item == null)
            {
                Logger.LogWarning("StorageContainer", $"Item {itemId} not found in database");
                return false;
            }

            // Check if container has space
            if (CurrentCapacity + quantity > MaxCapacity)
            {
                Logger.LogInfo("StorageContainer", $"Container {ContainerName} is full. Cannot add {quantity} {itemId}");
                return false;
            }

            // Check item filters if any are set
            if (_itemFilters.Count > 0 && !_itemFilters.Contains(item.Type))
            {
                Logger.LogInfo("StorageContainer", $"Item type {item.Type} not allowed in filtered container {ContainerName}");
                return false;
            }

            // Add or stack the item
            if (_items.TryGetValue(itemId, out var existingSlot))
            {
                // Stack with existing item
                int maxStack = item.MaxStack;
                int canAdd = Math.Min(quantity, maxStack - existingSlot.Quantity);
                
                if (canAdd > 0)
                {
                    existingSlot.Quantity += canAdd;
                    if (metadata != null)
                    {
                        // Merge metadata if needed
                        foreach (var kvp in metadata)
                        {
                            existingSlot.Metadata[kvp.Key] = kvp.Value;
                        }
                    }
                    
                    EmitSignal(SignalName.ContainerChanged, itemId, canAdd);
                    
                    // Try to add remaining quantity in new slot if needed
                    if (canAdd < quantity)
                    {
                        return TryAddItem(itemId, quantity - canAdd, metadata);
                    }
                    
                    if (_autoSort) SortContainer();
                    return true;
                }
                return false;
            }
            else
            {
                // Create new slot
                var newSlot = new InventorySlot
                {
                    ItemId = itemId,
                    Quantity = Math.Min(quantity, item.MaxStack),
                    Metadata = metadata ?? new Dictionary<string, object>()
                };
                
                _items[itemId] = newSlot;
                EmitSignal(SignalName.ContainerChanged, itemId, newSlot.Quantity);
                
                // Try to add remaining quantity if needed
                if (quantity > item.MaxStack)
                {
                    return TryAddItem(itemId, quantity - item.MaxStack, metadata);
                }
                
                if (_autoSort) SortContainer();
                return true;
            }
        }

        /// <summary>
        /// Attempts to remove items from the container
        /// </summary>
        public bool TryRemoveItem(string itemId, int quantity)
        {
            if (quantity <= 0) return false;
            
            if (!_items.TryGetValue(itemId, out var slot))
                return false;

            if (slot.Quantity < quantity)
                return false;

            slot.Quantity -= quantity;
            
            if (slot.Quantity <= 0)
            {
                _items.Remove(itemId);
            }
            
            EmitSignal(SignalName.ContainerChanged, itemId, -quantity);
            return true;
        }

        /// <summary>
        /// Gets the quantity of a specific item in the container
        /// </summary>
        public int GetItemQuantity(string itemId)
        {
            return _items.TryGetValue(itemId, out var slot) ? slot.Quantity : 0;
        }

        /// <summary>
        /// Checks if the container has at least the specified quantity of an item
        /// </summary>
        public bool HasItem(string itemId, int quantity = 1)
        {
            return GetItemQuantity(itemId) >= quantity;
        }

        /// <summary>
        /// Gets all items in the container, optionally filtered and sorted
        /// </summary>
        public List<InventorySlot> GetItems(string typeFilter = null, ContainerSortMode sortMode = ContainerSortMode.None)
        {
            var items = _items.Values.ToList();
            
            // Apply type filter if specified
            if (!string.IsNullOrEmpty(typeFilter))
            {
                items = items.Where(slot => 
                {
                    var item = ItemDatabase.Instance?.GetItem(slot.ItemId);
                    return item?.Type == typeFilter;
                }).ToList();
            }
            
            // Apply sorting
            return SortItems(items, sortMode != ContainerSortMode.None ? sortMode : _sortMode);
        }

        /// <summary>
        /// Sets access permissions for a player
        /// </summary>
        public void SetPlayerPermission(string playerId, ContainerPermission permission)
        {
            if (permission == ContainerPermission.None)
            {
                _playerPermissions.Remove(playerId);
                _authorizedPlayers.Remove(playerId);
            }
            else
            {
                _playerPermissions[playerId] = permission;
                _authorizedPlayers.Add(playerId);
            }
            
            Logger.LogInfo("StorageContainer", $"Set permission {permission} for player {playerId} on container {ContainerName}");
        }

        /// <summary>
        /// Checks if a player has the specified permission
        /// </summary>
        public bool HasPermission(string playerId, ContainerPermission requiredPermission)
        {
            if (!IsLocked) return true;
            
            if (!_playerPermissions.TryGetValue(playerId, out var permission))
                return false;
                
            return permission >= requiredPermission;
        }

        /// <summary>
        /// Attempts to unlock the container with an access code
        /// </summary>
        public bool TryUnlock(string code)
        {
            if (!IsLocked) return true;
            
            if (string.IsNullOrEmpty(AccessCode) || AccessCode == code)
            {
                IsLocked = false;
                EmitSignal(SignalName.ContainerLocked, false);
                Logger.LogInfo("StorageContainer", $"Container {ContainerName} unlocked");
                return true;
            }
            
            return false;
        }

        /// <summary>
        /// Locks the container with an access code
        /// </summary>
        public void Lock(string accessCode = "")
        {
            IsLocked = true;
            AccessCode = accessCode;
            EmitSignal(SignalName.ContainerLocked, true);
            Logger.LogInfo("StorageContainer", $"Container {ContainerName} locked");
        }

        /// <summary>
        /// Sets item type filters for the container
        /// </summary>
        public void SetItemFilters(params string[] itemTypes)
        {
            _itemFilters.Clear();
            foreach (var type in itemTypes)
            {
                _itemFilters.Add(type);
            }
            
            Logger.LogInfo("StorageContainer", $"Set item filters for container {ContainerName}: {string.Join(", ", itemTypes)}");
        }

        /// <summary>
        /// Enables or disables automatic sorting
        /// </summary>
        public void SetAutoSort(bool enabled, ContainerSortMode sortMode = ContainerSortMode.Name)
        {
            _autoSort = enabled;
            _sortMode = sortMode;
            
            if (enabled) SortContainer();
            
            Logger.LogInfo("StorageContainer", $"Auto-sort {(enabled ? "enabled" : "disabled")} for container {ContainerName}");
        }

        /// <summary>
        /// Manually sorts the container
        /// </summary>
        public void SortContainer(ContainerSortMode sortMode = ContainerSortMode.None)
        {
            var mode = sortMode != ContainerSortMode.None ? sortMode : _sortMode;
            var sortedItems = SortItems(_items.Values.ToList(), mode);
            
            // Rebuild the items dictionary in sorted order
            _items.Clear();
            foreach (var slot in sortedItems)
            {
                _items[slot.ItemId] = slot;
            }
            
            Logger.LogInfo("StorageContainer", $"Sorted container {ContainerName} by {mode}");
        }

        /// <summary>
        /// Transfers items from this container to another
        /// </summary>
        public bool TransferTo(StorageContainer targetContainer, string itemId, int quantity)
        {
            if (!HasItem(itemId, quantity))
                return false;
                
            var slot = _items[itemId];
            var metadata = new Dictionary<string, object>(slot.Metadata);
            
            if (targetContainer.TryAddItem(itemId, quantity, metadata))
            {
                return TryRemoveItem(itemId, quantity);
            }
            
            return false;
        }

        /// <summary>
        /// Gets container statistics
        /// </summary>
        public ContainerStats GetStats()
        {
            return new ContainerStats
            {
                TotalItems = _items.Values.Sum(slot => slot.Quantity),
                UniqueItems = _items.Count,
                Capacity = MaxCapacity,
                UtilizationPercentage = (float)CurrentCapacity / MaxCapacity * 100f,
                IsLocked = IsLocked,
                HasFilters = _itemFilters.Count > 0,
                AutoSortEnabled = _autoSort
            };
        }

        private List<InventorySlot> SortItems(List<InventorySlot> items, ContainerSortMode sortMode)
        {
            return sortMode switch
            {
                ContainerSortMode.Name => items.OrderBy(slot => 
                    ItemDatabase.Instance?.GetItem(slot.ItemId)?.Name ?? slot.ItemId).ToList(),
                ContainerSortMode.Type => items.OrderBy(slot => 
                    ItemDatabase.Instance?.GetItem(slot.ItemId)?.Type ?? "unknown").ToList(),
                ContainerSortMode.Quantity => items.OrderByDescending(slot => slot.Quantity).ToList(),
                ContainerSortMode.Value => items.OrderByDescending(slot => 
                    (ItemDatabase.Instance?.GetItem(slot.ItemId)?.Metadata?.GetValueOrDefault("value", 0) ?? 0)).ToList(),
                _ => items
            };
        }

        /// <summary>
        /// Logs access to the container
        /// </summary>
        public void LogAccess(string playerId, ContainerAccessType accessType)
        {
            EmitSignal(SignalName.ContainerAccessed, playerId, (int)accessType);
            Logger.LogInfo("StorageContainer", $"Player {playerId} {accessType} container {ContainerName}");
        }
    }

    public enum ContainerType
    {
        Basic,
        Reinforced,
        Secure,
        Specialized
    }

    public enum ContainerSortMode
    {
        None,
        Name,
        Type,
        Quantity,
        Value
    }

    public enum ContainerPermission
    {
        None = 0,
        View = 1,
        Deposit = 2,
        Withdraw = 3,
        Full = 4
    }

    public enum ContainerAccessType
    {
        Opened,
        Closed,
        ItemAdded,
        ItemRemoved,
        Locked,
        Unlocked
    }

    public class ContainerStats
    {
        public int TotalItems { get; set; }
        public int UniqueItems { get; set; }
        public int Capacity { get; set; }
        public float UtilizationPercentage { get; set; }
        public bool IsLocked { get; set; }
        public bool HasFilters { get; set; }
        public bool AutoSortEnabled { get; set; }
    }
}