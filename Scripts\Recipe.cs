using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace SurvivalLooterShooter
{
	[Serializable]
	public class RecipeInput
	{
		[JsonPropertyName("id")]
		public string Id { get; set; }

		[JsonPropertyName("amount")]
		public int Amount { get; set; }

		public RecipeInput()
		{
		}

		public RecipeInput(string id, int amount)
		{
			Id = id;
			Amount = amount;
		}
	}

	[Serializable]
	public class RecipeOutput
	{
		[JsonPropertyName("id")]
		public string Id { get; set; }

		[JsonPropertyName("amount")]
		public int Amount { get; set; }

		public RecipeOutput()
		{
		}

		public RecipeOutput(string id, int amount)
		{
			Id = id;
			Amount = amount;
		}
	}

	[Serializable]
	public class Recipe
	{
		[JsonPropertyName("id")]
		public string Id { get; set; }

		[JsonPropertyName("inputs")]
		public List<RecipeInput> Inputs { get; set; } = new List<RecipeInput>();

		[JsonPropertyName("output")]
		public RecipeOutput Output { get; set; }

		[JsonPropertyName("crafting_time")]
		public float CraftingTime { get; set; } = 1.0f;

		[JsonPropertyName("recipe_type")]
		public string RecipeType { get; set; } = "basic";

		public Recipe()
		{
		}

		public Recipe(string id, List<RecipeInput> inputs, RecipeOutput output, float craftingTime = 1.0f)
		{
			Id = id;
			Inputs = inputs ?? new List<RecipeInput>();
			Output = output;
			CraftingTime = craftingTime;
		}

		/// <summary>
		/// Checks if the recipe has valid inputs and output
		/// </summary>
		public virtual bool IsValid()
		{
			return !string.IsNullOrEmpty(Id) && 
				   Inputs != null && 
				   Inputs.Count > 0 && 
				   Output != null && 
				   !string.IsNullOrEmpty(Output.Id) && 
				   Output.Amount > 0;
		}

		/// <summary>
		/// Gets the total amount of a specific input item required
		/// </summary>
		public int GetRequiredAmount(string itemId)
		{
			foreach (var input in Inputs)
			{
				if (input.Id == itemId)
				{
					return input.Amount;
				}
			}
			return 0;
		}

		public override string ToString()
		{
			return $"Recipe {Id}: {Inputs.Count} inputs -> {Output.Id} x{Output.Amount}";
		}
	}
}
