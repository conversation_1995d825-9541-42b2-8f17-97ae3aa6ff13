using Godot;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;

namespace SurvivalLooterShooter
{
    /// <summary>
    /// Manages enemy spawning, lifecycle, and data loading
    /// Handles enemy population control and coordinates with other systems
    /// </summary>
    public partial class EnemyManager : Node
    {
        private static EnemyManager _instance;
        public static EnemyManager Instance => _instance;

        // Enemy data and spawning
        private Dictionary<string, EnemyData> _enemyDatabase = new Dictionary<string, EnemyData>();
        private List<Enemy> _activeEnemies = new List<Enemy>();
        private PackedScene _enemyScene;

        // Spawning configuration
        [Export] public int MaxEnemiesPerBiome { get; set; } = 10;
        [Export] public float SpawnRadius { get; set; } = 500f;
        [Export] public float DespawnRadius { get; set; } = 800f;
        [Export] public float SpawnCheckInterval { get; set; } = 5.0f;

        // Biome-specific spawning
        private Dictionary<string, BiomeSpawnData> _biomeSpawnTables = new Dictionary<string, BiomeSpawnData>();
        private Dictionary<string, List<Enemy>> _biomeEnemies = new Dictionary<string, List<Enemy>>();

        // Player level and progression
        private int _playerLevel = 1;
        private float _playerExperience = 0f;

        // Day/Night cycle integration
        private bool _isNightTime = false;
        private float _nightSpawnMultiplier = 2.0f;
        private float _nightDangerMultiplier = 1.5f;

        // Boss enemy system
        private Dictionary<string, BossSpawnData> _bossSpawnData = new Dictionary<string, BossSpawnData>();
        private List<Enemy> _activeBosses = new List<Enemy>();
        private Timer _bossSpawnTimer;

        // Migration system
        private Timer _migrationTimer;
        private Dictionary<Enemy, MigrationData> _migratingEnemies = new Dictionary<Enemy, MigrationData>();

        // Timers and state
        private Timer _spawnTimer;
        private Timer _cleanupTimer;
        private Vector2 _playerPosition = Vector2.Zero;
        private Random _random = new Random();

        // Events
        [Signal] public delegate void EnemySpawnedEventHandler(Enemy enemy);
        [Signal] public delegate void EnemyDespawnedEventHandler(string enemyId, Vector2 position);
        [Signal] public delegate void EnemyKilledEventHandler(Enemy enemy, float experienceReward);
        [Signal] public delegate void BossSpawnedEventHandler(Enemy boss, string location);
        [Signal] public delegate void BossDefeatedEventHandler(Enemy boss, float bonusExperience);
        [Signal] public delegate void EnemyMigrationStartedEventHandler(Enemy enemy, string fromBiome, string toBiome);

        public override void _Ready()
        {
            // Singleton setup
            if (_instance == null)
            {
                _instance = this;
                AddToGroup("enemy_manager");
                Logger.LogInfo("EnemyManager", "EnemyManager singleton initialized");
            }
            else
            {
                Logger.LogError("EnemyManager", "Multiple EnemyManager instances detected! Removing duplicate.");
                QueueFree();
                return;
            }

            // Load enemy scene
            _enemyScene = GD.Load<PackedScene>("res://Scenes/Enemy.tscn");
            if (_enemyScene == null)
            {
                Logger.LogWarning("EnemyManager", "Enemy scene not found, creating enemies programmatically");
            }

            // Load enemy data
            LoadEnemyData();

            // Load biome spawn data
            LoadBiomeSpawnData();

            // Load boss spawn data
            LoadBossSpawnData();

            // Setup timers
            SetupTimers();

            // Connect to EventBus if available
            if (EventBus.Instance != null)
            {
                EventBus.Instance.PlayerMoved += OnPlayerMoved;
                EventBus.Instance.DayNightChanged += OnDayNightChanged;
                EventBus.Instance.PlayerLevelChanged += OnPlayerLevelChanged;
            }

            Logger.LogInfo("EnemyManager", "EnemyManager initialization completed");
        }

        private void OnDayNightChanged(float currentTime, bool isNight)
        {
            throw new NotImplementedException();
        }

        /// <summary>
        /// Loads enemy data from JSON configuration file
        /// </summary>
        private void LoadEnemyData()
        {
            try
            {
                string filePath = "res://Data/Enemies.json";

                if (!FileAccess.FileExists(filePath))
                {
                    Logger.LogError("EnemyManager", $"Enemy data file not found: {filePath}");
                    CreateDefaultEnemyData();
                    return;
                }

                using var file = FileAccess.Open(filePath, FileAccess.ModeFlags.Read);
                if (file == null)
                {
                    Logger.LogError("EnemyManager", $"Failed to open enemy data file: {filePath}");
                    CreateDefaultEnemyData();
                    return;
                }

                string jsonContent = file.GetAsText();
                var enemyDataList = JsonSerializer.Deserialize<List<EnemyData>>(jsonContent);

                if (enemyDataList == null)
                {
                    Logger.LogError("EnemyManager", "Failed to deserialize enemy data");
                    CreateDefaultEnemyData();
                    return;
                }

                _enemyDatabase.Clear();
                foreach (var enemyData in enemyDataList)
                {
                    if (string.IsNullOrEmpty(enemyData.Id))
                    {
                        Logger.LogWarning("EnemyManager", "Skipping enemy with empty ID");
                        continue;
                    }

                    _enemyDatabase[enemyData.Id] = enemyData;
                    Logger.LogInfo("EnemyManager", $"Loaded enemy: {enemyData.Name} ({enemyData.Id})");
                }

                Logger.LogInfo("EnemyManager", $"Successfully loaded {_enemyDatabase.Count} enemy types");
            }
            catch (Exception ex)
            {
                Logger.LogException("EnemyManager", ex, "LoadEnemyData");
                CreateDefaultEnemyData();
            }
        }

        /// <summary>
        /// Creates default enemy data if loading fails
        /// </summary>
        private void CreateDefaultEnemyData()
        {
            Logger.LogInfo("EnemyManager", "Creating default enemy data");

            var defaultEnemy = new EnemyData
            {
                Id = "default_enemy",
                Name = "Default Enemy",
                Health = 50f,
                MaxHealth = 50f,
                Damage = 10f,
                Speed = 80f,
                DetectionRange = 150f,
                AttackRange = 40f,
                AIType = "aggressive",
                Biomes = new List<string> { "forest", "plains" },
                LootTable = new List<LootDrop>
                {
                    new LootDrop { Item = "raw_meat", Chance = 0.5f, Quantity = new List<int> { 1, 2 } }
                },
                ExperienceReward = 15f,
                SpawnWeight = 1.0f
            };

            _enemyDatabase["default_enemy"] = defaultEnemy;
        }

        /// <summary>
        /// Sets up timers for spawning and cleanup
        /// </summary>
        private void SetupTimers()
        {
            // Spawn timer
            _spawnTimer = new Timer();
            _spawnTimer.Name = "SpawnTimer";
            _spawnTimer.WaitTime = SpawnCheckInterval;
            _spawnTimer.Autostart = true;
            _spawnTimer.Timeout += OnSpawnTimerTimeout;
            AddChild(_spawnTimer);

            // Cleanup timer
            _cleanupTimer = new Timer();
            _cleanupTimer.Name = "CleanupTimer";
            _cleanupTimer.WaitTime = 10.0f; // Clean up every 10 seconds
            _cleanupTimer.Autostart = true;
            _cleanupTimer.Timeout += OnCleanupTimerTimeout;
            AddChild(_cleanupTimer);

            // Boss spawn timer
            _bossSpawnTimer = new Timer();
            _bossSpawnTimer.Name = "BossSpawnTimer";
            _bossSpawnTimer.WaitTime = 300.0f; // Check for boss spawns every 5 minutes
            _bossSpawnTimer.Autostart = true;
            _bossSpawnTimer.Timeout += OnBossSpawnTimerTimeout;
            AddChild(_bossSpawnTimer);

            // Migration timer
            _migrationTimer = new Timer();
            _migrationTimer.Name = "MigrationTimer";
            _migrationTimer.WaitTime = 60.0f; // Check for migrations every minute
            _migrationTimer.Autostart = true;
            _migrationTimer.Timeout += OnMigrationTimerTimeout;
            AddChild(_migrationTimer);
        }

        /// <summary>
        /// Loads biome-specific spawn data from JSON or creates defaults
        /// </summary>
        private void LoadBiomeSpawnData()
        {
            try
            {
                // Load biome data and extract enemy spawn information
                string biomePath = "res://Data/Biomes.json";
                if (FileAccess.FileExists(biomePath))
                {
                    using var file = FileAccess.Open(biomePath, FileAccess.ModeFlags.Read);
                    string jsonContent = file.GetAsText();
                    var jsonData = Json.ParseString(jsonContent).AsGodotDictionary();
                    var biomesArray = jsonData["biomes"].AsGodotArray();

                    foreach (var biomeVariant in biomesArray)
                    {
                        var biomeDict = biomeVariant.AsGodotDictionary();
                        string biomeId = biomeDict["id"].AsString();

                        var biomeSpawnData = new BiomeSpawnData
                        {
                            BiomeId = biomeId,
                            EnemySpawns = new List<BiomeEnemySpawn>()
                        };

                        if (biomeDict.ContainsKey("enemy_spawns"))
                        {
                            var enemySpawns = biomeDict["enemy_spawns"].AsGodotArray();
                            foreach (var enemyVariant in enemySpawns)
                            {
                                var enemyDict = enemyVariant.AsGodotDictionary();
                                biomeSpawnData.EnemySpawns.Add(new BiomeEnemySpawn
                                {
                                    EnemyId = enemyDict["enemy"].AsString(),
                                    BaseSpawnRate = enemyDict["spawn_rate"].AsSingle(),
                                    MaxCount = enemyDict["max_count"].AsInt32(),
                                    MinPlayerLevel = enemyDict.ContainsKey("min_player_level") ? enemyDict["min_player_level"].AsInt32() : 1,
                                    MaxPlayerLevel = enemyDict.ContainsKey("max_player_level") ? enemyDict["max_player_level"].AsInt32() : 100,
                                    NightSpawnMultiplier = enemyDict.ContainsKey("night_multiplier") ? enemyDict["night_multiplier"].AsSingle() : 1.5f,
                                    LevelScaling = enemyDict.ContainsKey("level_scaling") ? enemyDict["level_scaling"].AsSingle() : 0.1f
                                });
                            }
                        }

                        _biomeSpawnTables[biomeId] = biomeSpawnData;
                        _biomeEnemies[biomeId] = new List<Enemy>();
                    }
                }
                else
                {
                    CreateDefaultBiomeSpawnData();
                }

                Logger.LogInfo("EnemyManager", $"Loaded spawn data for {_biomeSpawnTables.Count} biomes");
            }
            catch (Exception ex)
            {
                Logger.LogException("EnemyManager", ex, "LoadBiomeSpawnData");
                CreateDefaultBiomeSpawnData();
            }
        }

        /// <summary>
        /// Creates default biome spawn data if loading fails
        /// </summary>
        private void CreateDefaultBiomeSpawnData()
        {
            var forestSpawn = new BiomeSpawnData
            {
                BiomeId = "forest",
                EnemySpawns = new List<BiomeEnemySpawn>
                {
                    new BiomeEnemySpawn { EnemyId = "forest_wolf", BaseSpawnRate = 0.1f, MaxCount = 3, MinPlayerLevel = 1, MaxPlayerLevel = 10 },
                    new BiomeEnemySpawn { EnemyId = "wild_boar", BaseSpawnRate = 0.05f, MaxCount = 2, MinPlayerLevel = 3, MaxPlayerLevel = 15 }
                }
            };

            _biomeSpawnTables["forest"] = forestSpawn;
            _biomeEnemies["forest"] = new List<Enemy>();
        }

        /// <summary>
        /// Loads boss spawn data from configuration
        /// </summary>
        private void LoadBossSpawnData()
        {
            try
            {
                // Create default boss spawn data
                _bossSpawnData["forest_alpha"] = new BossSpawnData
                {
                    BossId = "forest_alpha_wolf",
                    RequiredBiome = "forest",
                    MinPlayerLevel = 5,
                    SpawnChance = 0.1f,
                    CooldownMinutes = 30,
                    RequiredEnemyKills = 10,
                    LastSpawnTime = DateTime.MinValue
                };

                _bossSpawnData["desert_king"] = new BossSpawnData
                {
                    BossId = "desert_scorpion_king",
                    RequiredBiome = "desert",
                    MinPlayerLevel = 8,
                    SpawnChance = 0.08f,
                    CooldownMinutes = 45,
                    RequiredEnemyKills = 15,
                    LastSpawnTime = DateTime.MinValue
                };

                Logger.LogInfo("EnemyManager", $"Loaded {_bossSpawnData.Count} boss spawn configurations");
            }
            catch (Exception ex)
            {
                Logger.LogException("EnemyManager", ex, "LoadBossSpawnData");
            }
        }

        /// <summary>
        /// Handles player movement updates for spawning calculations
        /// </summary>
        private void OnPlayerMoved(Vector2 position, Vector2 velocity, bool isSprinting)
        {
            _playerPosition = position;
        }

        /// <summary>
        /// Handles day/night cycle changes
        /// </summary>
        private void OnDayNightChanged(bool isNight, float timeOfDay)
        {
            _isNightTime = isNight;
            Logger.LogInfo("EnemyManager", $"Day/Night changed: {(isNight ? "Night" : "Day")} - Spawn rates adjusted");
        }

        /// <summary>
        /// Handles player level changes
        /// </summary>
        private void OnPlayerLevelChanged(int newLevel, float experience)
        {
            _playerLevel = newLevel;
            _playerExperience = experience;
            Logger.LogInfo("EnemyManager", $"Player level changed to {newLevel} - Enemy spawning adjusted");
        }

        /// <summary>
        /// Handles spawn timer timeout - checks if new enemies should be spawned
        /// </summary>
        private void OnSpawnTimerTimeout()
        {
            if (_playerPosition == Vector2.Zero) return; // No player position yet

            // Clean up dead enemies from all biome lists
            CleanupDeadEnemies();

            // Get current biome at player position
            string currentBiome = GetBiomeAtPosition(_playerPosition);
            if (string.IsNullOrEmpty(currentBiome)) return;

            // Try biome-specific spawning
            TrySpawnEnemiesInBiome(currentBiome);
        }

        /// <summary>
        /// Handles cleanup timer timeout - removes distant enemies
        /// </summary>
        private void OnCleanupTimerTimeout()
        {
            if (_playerPosition == Vector2.Zero) return;

            var enemiesToRemove = new List<Enemy>();

            foreach (var enemy in _activeEnemies)
            {
                if (enemy == null || enemy.IsQueuedForDeletion()) continue;

                float distanceToPlayer = enemy.GlobalPosition.DistanceTo(_playerPosition);
                if (distanceToPlayer > DespawnRadius)
                {
                    enemiesToRemove.Add(enemy);
                }
            }

            foreach (var enemy in enemiesToRemove)
            {
                DespawnEnemy(enemy);
            }

            if (enemiesToRemove.Count > 0)
            {
                Logger.LogInfo("EnemyManager", $"Despawned {enemiesToRemove.Count} distant enemies");
            }
        }

        /// <summary>
        /// Handles boss spawn timer timeout - checks for boss spawn opportunities
        /// </summary>
        private void OnBossSpawnTimerTimeout()
        {
            if (_playerPosition == Vector2.Zero) return;

            string currentBiome = GetBiomeAtPosition(_playerPosition);
            if (string.IsNullOrEmpty(currentBiome)) return;

            TrySpawnBossInBiome(currentBiome);
        }

        /// <summary>
        /// Handles migration timer timeout - processes enemy migrations
        /// </summary>
        private void OnMigrationTimerTimeout()
        {
            ProcessEnemyMigrations();
        }

        /// <summary>
        /// Cleans up dead enemies from all tracking lists
        /// </summary>
        private void CleanupDeadEnemies()
        {
            _activeEnemies.RemoveAll(enemy => enemy == null || enemy.IsQueuedForDeletion() || enemy.IsDead);
            _activeBosses.RemoveAll(boss => boss == null || boss.IsQueuedForDeletion() || boss.IsDead);

            foreach (var biomeList in _biomeEnemies.Values)
            {
                biomeList.RemoveAll(enemy => enemy == null || enemy.IsQueuedForDeletion() || enemy.IsDead);
            }
        }

        /// <summary>
        /// Gets the biome at a specific world position
        /// </summary>
        private string GetBiomeAtPosition(Vector2 position)
        {
            // Try to get biome from WorldManager if available
            var worldManager = GetNode<WorldManager>("/root/Main/WorldManager");
            if (worldManager != null)
            {
                var biomeType = worldManager.GetBiomeAt(position);
                return biomeType.ToString().ToLower();
            }

            // Fallback to simple biome determination
            return DetermineBiomeFromPosition(position);
        }

        /// <summary>
        /// Simple fallback biome determination based on position
        /// </summary>
        private string DetermineBiomeFromPosition(Vector2 position)
        {
            // Simple noise-based biome determination as fallback
            float noiseValue = Mathf.Sin(position.X * 0.001f) + Mathf.Cos(position.Y * 0.001f);

            if (noiseValue > 0.5f) return "forest";
            if (noiseValue > 0.0f) return "plains";
            if (noiseValue > -0.5f) return "desert";
            return "mountains";
        }

        /// <summary>
        /// Tries to spawn enemies in a specific biome based on spawn tables
        /// </summary>
        private void TrySpawnEnemiesInBiome(string biomeId)
        {
            if (!_biomeSpawnTables.ContainsKey(biomeId)) return;

            var biomeSpawnData = _biomeSpawnTables[biomeId];
            var biomeEnemies = _biomeEnemies[biomeId];

            foreach (var enemySpawn in biomeSpawnData.EnemySpawns)
            {
                // Check player level requirements
                if (_playerLevel < enemySpawn.MinPlayerLevel || _playerLevel > enemySpawn.MaxPlayerLevel)
                    continue;

                // Count current enemies of this type in the biome
                int currentCount = biomeEnemies.Count(e => e != null && e.EnemyId == enemySpawn.EnemyId);
                if (currentCount >= enemySpawn.MaxCount) continue;

                // Calculate adjusted spawn rate
                float adjustedSpawnRate = CalculateAdjustedSpawnRate(enemySpawn);

                // Roll for spawn
                if (_random.NextDouble() < adjustedSpawnRate)
                {
                    Vector2 spawnPosition = GetRandomSpawnPosition(_playerPosition);
                    var spawnedEnemy = SpawnEnemyInBiome(enemySpawn.EnemyId, spawnPosition, biomeId);

                    if (spawnedEnemy != null)
                    {
                        // Scale enemy stats based on player level
                        ScaleEnemyForPlayerLevel(spawnedEnemy, enemySpawn.LevelScaling);
                    }
                }
            }
        }

        /// <summary>
        /// Calculates adjusted spawn rate based on various factors
        /// </summary>
        private float CalculateAdjustedSpawnRate(BiomeEnemySpawn enemySpawn)
        {
            float spawnRate = enemySpawn.BaseSpawnRate;

            // Apply night time multiplier
            if (_isNightTime)
            {
                spawnRate *= enemySpawn.NightSpawnMultiplier;
            }

            // Apply global night multiplier
            if (_isNightTime)
            {
                spawnRate *= _nightSpawnMultiplier;
            }

            // Apply player level scaling
            float levelFactor = 1.0f + ((_playerLevel - 1) * enemySpawn.LevelScaling);
            spawnRate *= levelFactor;

            return Math.Min(spawnRate, 1.0f); // Cap at 100%
        }

        /// <summary>
        /// Spawns an enemy in a specific biome and tracks it
        /// </summary>
        private Enemy SpawnEnemyInBiome(string enemyId, Vector2 position, string biomeId)
        {
            var enemy = SpawnEnemy(enemyId, position);
            if (enemy != null && _biomeEnemies.ContainsKey(biomeId))
            {
                _biomeEnemies[biomeId].Add(enemy);

                // Set up migration data
                _migratingEnemies[enemy] = new MigrationData
                {
                    CurrentBiome = biomeId,
                    OriginalBiome = biomeId,
                    MigrationCooldown = _random.Next(300, 900) // 5-15 minutes
                };
            }
            return enemy;
        }

        /// <summary>
        /// Scales enemy stats based on player level
        /// </summary>
        private void ScaleEnemyForPlayerLevel(Enemy enemy, float levelScaling)
        {
            if (enemy == null) return;

            float scaleFactor = 1.0f + ((_playerLevel - 1) * levelScaling);

            enemy.MaxHealth *= scaleFactor;
            enemy.CurrentHealth = enemy.MaxHealth;
            enemy.Damage *= scaleFactor;
            enemy.ExperienceReward *= (int)scaleFactor;

            Logger.LogInfo("EnemyManager", $"Scaled {enemy.EnemyName} for player level {_playerLevel} (scale: {scaleFactor:F2})");
        }

        /// <summary>
        /// Tries to spawn a boss in the specified biome
        /// </summary>
        private void TrySpawnBossInBiome(string biomeId)
        {
            foreach (var bossData in _bossSpawnData.Values)
            {
                if (bossData.RequiredBiome != biomeId) continue;
                if (_playerLevel < bossData.MinPlayerLevel) continue;
                if (_activeBosses.Count >= 1) continue; // Only one boss at a time

                // Check cooldown
                var timeSinceLastSpawn = DateTime.Now - bossData.LastSpawnTime;
                if (timeSinceLastSpawn.TotalMinutes < bossData.CooldownMinutes) continue;

                // Check if enough enemies have been killed (simplified check)
                if (_random.NextDouble() < bossData.SpawnChance)
                {
                    Vector2 bossPosition = GetRandomSpawnPosition(_playerPosition, SpawnRadius * 1.5f);
                    var boss = SpawnBoss(bossData.BossId, bossPosition, biomeId);

                    if (boss != null)
                    {
                        bossData.LastSpawnTime = DateTime.Now;
                        EmitSignal(SignalName.BossSpawned, boss, biomeId);
                        Logger.LogInfo("EnemyManager", $"Boss {boss.EnemyName} spawned in {biomeId}!");
                    }
                }
            }
        }

        /// <summary>
        /// Spawns a boss enemy with enhanced stats
        /// </summary>
        private Enemy SpawnBoss(string bossId, Vector2 position, string biomeId)
        {
            var boss = SpawnEnemy(bossId, position);
            if (boss != null)
            {
                // Enhance boss stats
                boss.MaxHealth *= 3.0f;
                boss.CurrentHealth = boss.MaxHealth;
                boss.Damage *= 2.0f;
                boss.ExperienceReward *= (int)5.0f;
                boss.Speed *= 1.2f;

                _activeBosses.Add(boss);

                // Connect to boss death event
                boss.EnemyDied += OnBossDefeated;
            }
            return boss;
        }

        private void OnBossDefeated()
        {
            throw new NotImplementedException();
        }

        /// <summary>
        /// Handles boss defeat events
        /// </summary>
        private void OnBossDefeated(Enemy boss)
        {
            _activeBosses.Remove(boss);
            EmitSignal(SignalName.BossDefeated, boss, boss.ExperienceReward);
            Logger.LogInfo("EnemyManager", $"Boss {boss.EnemyName} defeated! Bonus XP: {boss.ExperienceReward}");
        }

        /// <summary>
        /// Processes enemy migrations between biomes
        /// </summary>
        private void ProcessEnemyMigrations()
        {
            var enemiesToMigrate = new List<Enemy>();

            foreach (var kvp in _migratingEnemies.ToList())
            {
                var enemy = kvp.Key;
                var migrationData = kvp.Value;

                if (enemy == null || enemy.IsQueuedForDeletion() || enemy.IsDead)
                {
                    _migratingEnemies.Remove(enemy);
                    continue;
                }

                migrationData.MigrationCooldown -= 60; // Reduce by 1 minute

                if (migrationData.MigrationCooldown <= 0)
                {
                    // Check if enemy should migrate
                    if (ShouldEnemyMigrate(enemy, migrationData))
                    {
                        enemiesToMigrate.Add(enemy);
                    }
                    else
                    {
                        // Reset cooldown
                        migrationData.MigrationCooldown = _random.Next(300, 900);
                    }
                }
            }

            foreach (var enemy in enemiesToMigrate)
            {
                ProcessEnemyMigration(enemy);
            }
        }

        /// <summary>
        /// Determines if an enemy should migrate to a different biome
        /// </summary>
        private bool ShouldEnemyMigrate(Enemy enemy, MigrationData migrationData)
        {
            // Check distance from player (enemies far from player are more likely to migrate)
            float distanceToPlayer = enemy.GlobalPosition.DistanceTo(_playerPosition);
            if (distanceToPlayer < SpawnRadius * 0.5f) return false; // Too close to player

            // Check if current biome is overcrowded
            string currentBiome = migrationData.CurrentBiome;
            if (_biomeEnemies.ContainsKey(currentBiome))
            {
                int currentBiomeCount = _biomeEnemies[currentBiome].Count;
                if (currentBiomeCount > MaxEnemiesPerBiome * 0.8f) return true; // Overcrowded
            }

            // Random chance for migration
            return _random.NextDouble() < 0.1f; // 10% chance
        }

        /// <summary>
        /// Processes the migration of an enemy to a new biome
        /// </summary>
        private void ProcessEnemyMigration(Enemy enemy)
        {
            if (!_migratingEnemies.ContainsKey(enemy)) return;

            var migrationData = _migratingEnemies[enemy];
            string currentBiome = migrationData.CurrentBiome;

            // Find a suitable target biome
            string targetBiome = FindTargetBiomeForMigration(enemy, currentBiome);
            if (string.IsNullOrEmpty(targetBiome)) return;

            // Remove from current biome
            if (_biomeEnemies.ContainsKey(currentBiome))
            {
                _biomeEnemies[currentBiome].Remove(enemy);
            }

            // Add to target biome
            if (!_biomeEnemies.ContainsKey(targetBiome))
            {
                _biomeEnemies[targetBiome] = new List<Enemy>();
            }
            _biomeEnemies[targetBiome].Add(enemy);

            // Update migration data
            migrationData.CurrentBiome = targetBiome;
            migrationData.MigrationCooldown = _random.Next(600, 1800); // 10-30 minutes

            EmitSignal(SignalName.EnemyMigrationStarted, enemy, currentBiome, targetBiome);
            Logger.LogInfo("EnemyManager", $"{enemy.EnemyName} migrated from {currentBiome} to {targetBiome}");
        }

        /// <summary>
        /// Finds a suitable target biome for enemy migration
        /// </summary>
        private string FindTargetBiomeForMigration(Enemy enemy, string currentBiome)
        {
            var validBiomes = enemy.ValidBiomes.Where(b => b != currentBiome).ToList();
            if (validBiomes.Count == 0) return "";

            // Prefer less crowded biomes
            var sortedBiomes = validBiomes
                .Where(b => _biomeEnemies.ContainsKey(b))
                .OrderBy(b => _biomeEnemies[b].Count)
                .ToList();

            return sortedBiomes.FirstOrDefault() ?? validBiomes[_random.Next(validBiomes.Count)];
        }

        /// <summary>
        /// Attempts to spawn an enemy near the player
        /// </summary>
        private void TrySpawnEnemyNearPlayer()
        {
            // Get a random spawn position around the player
            Vector2 spawnPosition = GetRandomSpawnPosition(_playerPosition);

            // Get appropriate enemy for the current biome (simplified - just pick random for now)
            string enemyId = GetRandomEnemyId();

            if (string.IsNullOrEmpty(enemyId))
            {
                Logger.LogWarning("EnemyManager", "No valid enemy ID found for spawning");
                return;
            }

            SpawnEnemy(enemyId, spawnPosition);
        }

        /// <summary>
        /// Gets a random spawn position around the player within spawn radius
        /// </summary>
        private Vector2 GetRandomSpawnPosition(Vector2 playerPosition, float radius = 0f)
        {
            if (radius <= 0f) radius = SpawnRadius;

            float angle = (float)(_random.NextDouble() * Math.PI * 2);
            float distance = (float)(_random.NextDouble() * (radius - 100) + 100); // Min 100 units away

            Vector2 offset = new Vector2(
                Mathf.Cos(angle) * distance,
                Mathf.Sin(angle) * distance
            );

            return playerPosition + offset;
        }

        /// <summary>
        /// Gets a random enemy ID from the database
        /// </summary>
        private string GetRandomEnemyId()
        {
            if (_enemyDatabase.Count == 0) return "";

            // Use spawn weights to determine probability
            var weightedEnemies = new List<(string id, float weight)>();
            foreach (var kvp in _enemyDatabase)
            {
                weightedEnemies.Add((kvp.Key, kvp.Value.SpawnWeight));
            }

            float totalWeight = weightedEnemies.Sum(e => e.weight);
            float randomValue = (float)(_random.NextDouble() * totalWeight);

            float currentWeight = 0f;
            foreach (var (id, weight) in weightedEnemies)
            {
                currentWeight += weight;
                if (randomValue <= currentWeight)
                {
                    return id;
                }
            }

            // Fallback to first enemy
            return weightedEnemies.First().id;
        }

        /// <summary>
        /// Spawns an enemy of the specified type at the given position
        /// </summary>
        public Enemy SpawnEnemy(string enemyId, Vector2 position)
        {
            if (!_enemyDatabase.ContainsKey(enemyId))
            {
                Logger.LogError("EnemyManager", $"Enemy ID not found in database: {enemyId}");
                return null;
            }

            try
            {
                Enemy enemy;

                // Try to instantiate from scene first, otherwise create programmatically
                if (_enemyScene != null)
                {
                    enemy = _enemyScene.Instantiate<Enemy>();
                }
                else
                {
                    enemy = new Enemy();
                }

                // Initialize with enemy data
                var enemyData = _enemyDatabase[enemyId];
                enemy.Initialize(enemyData);
                enemy.GlobalPosition = position;

                // Connect to enemy events
                enemy.EnemyDied += OnEnemyDied;
                enemy.EnemyTookDamage += OnEnemyTookDamage;
                enemy.EnemyAttacked += OnEnemyAttacked;

                // Add to scene and track
                GetTree().CurrentScene.AddChild(enemy);
                _activeEnemies.Add(enemy);

                // Emit spawn event
                EmitSignal(SignalName.EnemySpawned, enemy);

                Logger.LogInfo("EnemyManager", $"Spawned {enemyData.Name} at {position}");
                return enemy;
            }
            catch (Exception ex)
            {
                Logger.LogException("EnemyManager", ex, $"SpawnEnemy({enemyId}, {position})");
                return null;
            }
        }

        private void OnEnemyAttacked()
        {
            throw new NotImplementedException();
        }

        private void OnEnemyTookDamage(float damage)
        {
            throw new NotImplementedException();
        }

        private void OnEnemyDied()
        {
            throw new NotImplementedException();
        }

        /// <summary>
        /// Despawns an enemy and removes it from tracking
        /// </summary>
        public void DespawnEnemy(Enemy enemy)
        {
            if (enemy == null) return;

            Vector2 position = enemy.GlobalPosition;
            string enemyId = enemy.EnemyId;

            _activeEnemies.Remove(enemy);
            enemy.QueueFree();

            EmitSignal(SignalName.EnemyDespawned, enemyId, position);
            Logger.LogInfo("EnemyManager", $"Despawned enemy {enemyId} at {position}");
        }

        /// <summary>
        /// Handles enemy death events
        /// </summary>
        private void OnEnemyDied(Enemy enemy)
        {
            if (enemy == null) return;

            // Award experience and handle loot
            EmitSignal(SignalName.EnemyKilled, enemy, enemy.ExperienceReward);

            // Handle loot drops
            HandleLootDrops(enemy);

            // Remove from active enemies list
            _activeEnemies.Remove(enemy);

            Logger.LogInfo("EnemyManager", $"Enemy {enemy.EnemyName} died, awarded {enemy.ExperienceReward} XP");
        }

        /// <summary>
        /// Handles enemy damage events
        /// </summary>
        private void OnEnemyTookDamage(Enemy enemy, float damage, float remainingHealth)
        {
            // Could trigger additional effects like blood particles, sound effects, etc.
            Logger.LogDebug("EnemyManager", $"{enemy.EnemyName} took {damage} damage, {remainingHealth} health remaining");
        }

        /// <summary>
        /// Handles enemy attack events
        /// </summary>
        private void OnEnemyAttacked(Enemy enemy, float damage)
        {
            // Could trigger combat effects, player damage, etc.
            Logger.LogDebug("EnemyManager", $"{enemy.EnemyName} attacked for {damage} damage");
        }

        /// <summary>
        /// Handles loot drops when an enemy dies
        /// </summary>
        private void HandleLootDrops(Enemy enemy)
        {
            if (LootDropSystem.Instance != null)
            {
                // Use the new loot drop system for enhanced effects and rare items
                LootDropSystem.Instance.CreateLootDropsFromEnemy(enemy, enemy.GlobalPosition);
            }
            else
            {
                // Fallback to simple loot drops if LootDropSystem is not available
                HandleLootDropsFallback(enemy);
            }
        }

        /// <summary>
        /// Fallback loot drop handling when LootDropSystem is not available
        /// </summary>
        private void HandleLootDropsFallback(Enemy enemy)
        {
            if (string.IsNullOrEmpty(enemy.LootTable)) return;

            // Simple fallback - drop a basic item based on loot table name
            string itemId = enemy.LootTable.Contains("weapon") ? "basic_weapon" : "basic_item";
            int quantity = _random.Next(1, 4);
            CreateItemPickup(itemId, quantity, enemy.GlobalPosition);
        }

        /// <summary>
        /// Creates an item pickup in the world (fallback method)
        /// </summary>
        private void CreateItemPickup(string itemId, int quantity, Vector2 position)
        {
            try
            {
                // Try to load the ItemPickup scene
                var itemPickupScene = GD.Load<PackedScene>("res://Scenes/ItemPickup.tscn");
                if (itemPickupScene != null)
                {
                    var itemPickup = itemPickupScene.Instantiate<ItemPickup>();
                    itemPickup.SetItemData(itemId, quantity);
                    itemPickup.GlobalPosition = position + new Vector2(
                        (float)(_random.NextDouble() - 0.5) * 50,
                        (float)(_random.NextDouble() - 0.5) * 50
                    );
                    GetTree().CurrentScene.AddChild(itemPickup);

                    Logger.LogInfo("EnemyManager", $"Created loot pickup: {quantity}x {itemId} at {position}");
                }
                else
                {
                    Logger.LogWarning("EnemyManager", "ItemPickup scene not found, loot drop skipped");
                }
            }
            catch (Exception ex)
            {
                Logger.LogException("EnemyManager", ex, $"CreateItemPickup({itemId}, {quantity}, {position})");
            }
        }

        /// <summary>
        /// Gets enemy data by ID
        /// </summary>
        public EnemyData GetEnemyData(string enemyId)
        {
            return _enemyDatabase.ContainsKey(enemyId) ? _enemyDatabase[enemyId] : null;
        }

        /// <summary>
        /// Gets all active enemies
        /// </summary>
        public List<Enemy> GetActiveEnemies()
        {
            // Clean up null references
            _activeEnemies.RemoveAll(enemy => enemy == null || enemy.IsQueuedForDeletion());
            return new List<Enemy>(_activeEnemies);
        }

        /// <summary>
        /// Gets enemies within a certain range of a position
        /// </summary>
        public List<Enemy> GetEnemiesInRange(Vector2 position, float range)
        {
            return _activeEnemies
                .Where(enemy => enemy != null && !enemy.IsQueuedForDeletion() && !enemy.IsDead)
                .Where(enemy => enemy.GlobalPosition.DistanceTo(position) <= range)
                .ToList();
        }

        /// <summary>
        /// Gets the closest enemy to a position
        /// </summary>
        public Enemy GetClosestEnemy(Vector2 position)
        {
            Enemy closest = null;
            float closestDistance = float.MaxValue;

            foreach (var enemy in _activeEnemies)
            {
                if (enemy == null || enemy.IsQueuedForDeletion() || enemy.IsDead) continue;

                float distance = enemy.GlobalPosition.DistanceTo(position);
                if (distance < closestDistance)
                {
                    closestDistance = distance;
                    closest = enemy;
                }
            }

            return closest;
        }

        /// <summary>
        /// Clears all active enemies (useful for scene transitions)
        /// </summary>
        public void ClearAllEnemies()
        {
            foreach (var enemy in _activeEnemies)
            {
                if (enemy != null && !enemy.IsQueuedForDeletion())
                {
                    enemy.QueueFree();
                }
            }
            _activeEnemies.Clear();
            Logger.LogInfo("EnemyManager", "Cleared all active enemies");
        }

        public override void _ExitTree()
        {
            if (_instance == this)
            {
                _instance = null;
            }
        }

        public void SetSpawnRateMultiplier(float multiplier)
        {
            _spawnRateMultiplier = multiplier;
            GD.Print($"Enemy spawn rate multipliplier set to: {multiplier}");
        }

        public BossEnemy SpawnBoss(BossType bossType, Vector2 position)
        {
            var bossScene = GD.Load<PackedScene>("res://Scenes/BossEnemy.tscn");
            if (bossScene != null)
            {
                var boss = bossScene.Instantiate<BossEnemy>();
                boss.BossType = bossType;
                boss.GlobalPosition = position;

                GetTree().CurrentScene.AddChild(boss);
                _activeEnemies.Add(boss);

                GD.Print($"Boss spawned: {bossType} at {position}");
                return boss;
            }

            return null;
        }

        public void RegisterCustomEnemy(CustomContent customEnemy)
        {
            // Register custom enemy from mods
            GD.Print($"Registered custom enemy: {customEnemy.Name}");
        }

        public void UnregisterCustomEnemy(string enemyId)
        {
            // Unregister custom enemy
            GD.Print($"Unregistered custom enemy: {enemyId}");
        }

        public void ResetDifficulty()
        {
            _spawnRateMultiplier = 1f;
            _difficultyMultiplier = 1f;
        }

        public void SetDifficultyMultiplier(float multiplier)
        {
            _difficultyMultiplier = multiplier;
            GD.Print($"Enemy difficulty multiplier set to: {multiplier}");
        }

        private float _spawnRateMultiplier = 1f;
        private float _difficultyMultiplier = 1f;
    }

    /// <summary>
    /// Data structure for biome-specific enemy spawning
    /// </summary>
    [System.Serializable]
    public class BiomeSpawnData
    {
        public string BiomeId { get; set; } = "";
        public List<BiomeEnemySpawn> EnemySpawns { get; set; } = new List<BiomeEnemySpawn>();
    }

    /// <summary>
    /// Data structure for individual enemy spawn configuration in a biome
    /// </summary>
    [System.Serializable]
    public class BiomeEnemySpawn
    {
        public string EnemyId { get; set; } = "";
        public float BaseSpawnRate { get; set; } = 0.1f;
        public int MaxCount { get; set; } = 3;
        public int MinPlayerLevel { get; set; } = 1;
        public int MaxPlayerLevel { get; set; } = 100;
        public float NightSpawnMultiplier { get; set; } = 1.5f;
        public float LevelScaling { get; set; } = 0.1f;
    }

    /// <summary>
    /// Data structure for boss enemy spawning
    /// </summary>
    [System.Serializable]
    public class BossSpawnData
    {
        public string BossId { get; set; } = "";
        public string RequiredBiome { get; set; } = "";
        public int MinPlayerLevel { get; set; } = 5;
        public float SpawnChance { get; set; } = 0.1f;
        public int CooldownMinutes { get; set; } = 30;
        public int RequiredEnemyKills { get; set; } = 10;
        public DateTime LastSpawnTime { get; set; } = DateTime.MinValue;
    }

    /// <summary>
    /// Data structure for loot drops
    /// </summary>
    [System.Serializable]
    public class LootDrop
    {
        public string Item { get; set; } = "";
        public float Chance { get; set; } = 0.0f;
        public List<int> Quantity { get; set; } = new List<int>();
    }

    /// <summary>
    /// Data structure for enemy configuration
    /// </summary>
    [System.Serializable]
    public class EnemyData
    {
        public string Id { get; set; } = "";
        public string Name { get; set; } = "";
        public float Health { get; set; } = 50f;
        public float MaxHealth { get; set; } = 50f;
        public float Damage { get; set; } = 10f;
        public float Speed { get; set; } = 80f;
        public float DetectionRange { get; set; } = 150f;
        public float AttackRange { get; set; } = 40f;
        public string AIType { get; set; } = "aggressive";
        public List<string> Biomes { get; set; } = new List<string>();
        public List<LootDrop> LootTable { get; set; } = new List<LootDrop>();
        public float ExperienceReward { get; set; } = 15f;
        public float SpawnWeight { get; set; } = 1.0f;
    }

    /// <summary>
    /// Data structure for tracking enemy migration
    /// </summary>
    [System.Serializable]
    public class MigrationData
    {
        public string CurrentBiome { get; set; } = "";
        public string OriginalBiome { get; set; } = "";
        public int MigrationCooldown { get; set; } = 600; // seconds
    }

}