using Godot;
using System.Collections.Generic;
using SurvivalLooterShooter;

namespace SurvivalLooterShooter.Tests
{
    /// <summary>
    /// Unit tests for ItemDatabase functionality
    /// </summary>
    public partial class ItemDatabaseTests : Node
    {
        private ItemDatabase _itemDatabase;

        public override void _Ready()
        {
            // Create a test instance of ItemDatabase
            _itemDatabase = new ItemDatabase();
            AddChild(_itemDatabase);
            
            // Run all tests
            RunAllTests();
        }

        private void RunAllTests()
        {
            GD.Print("=== Running ItemDatabase Tests ===");
            
            TestItemLoading();
            TestRecipeLoading();
            TestItemLookup();
            TestRecipeLookup();
            TestRecipesByOutput();
            TestItemExistence();
            TestRecipeExistence();
            TestGetAllMethods();
            TestCounts();
            TestNullAndEmptyInputs();
            
            GD.Print("=== ItemDatabase Tests Complete ===");
        }

        private void TestItemLoading()
        {
            GD.Print("Testing item loading...");
            
            // Items should be loaded automatically in _Ready
            Assert(_itemDatabase.ItemCount > 0, "Items should be loaded from JSON");
            
            // Test specific items from our test data
            var bandage = _itemDatabase.GetItem("bandage");
            Assert(bandage != null, "Bandage item should exist");
            Assert(bandage.Name == "Bandage", "Bandage name should be correct");
            Assert(bandage.Type == "consumable", "Bandage type should be consumable");
            Assert(bandage.MaxStack == 10, "Bandage max stack should be 10");
            
            var rifle = _itemDatabase.GetItem("assault_rifle");
            Assert(rifle != null, "Assault rifle should exist");
            Assert(rifle.Name == "Assault Rifle", "Assault rifle name should be correct");
            Assert(rifle.Type == "weapon", "Assault rifle type should be weapon");
            Assert(rifle.MaxStack == 1, "Assault rifle max stack should be 1");
            
            GD.Print("✓ Item loading tests passed");
        }

        private void TestRecipeLoading()
        {
            GD.Print("Testing recipe loading...");
            
            // Recipes should be loaded automatically in _Ready
            Assert(_itemDatabase.RecipeCount > 0, "Recipes should be loaded from JSON");
            
            // Test specific recipe from our test data
            var bandageRecipe = _itemDatabase.GetRecipe("craft_bandage");
            Assert(bandageRecipe != null, "Bandage recipe should exist");
            Assert(bandageRecipe.Inputs.Count == 2, "Bandage recipe should have 2 inputs");
            Assert(bandageRecipe.Output.Id == "bandage", "Bandage recipe output should be bandage");
            Assert(bandageRecipe.Output.Amount == 3, "Bandage recipe should output 3 bandages");
            Assert(bandageRecipe.CraftingTime == 2.0f, "Bandage recipe crafting time should be 2.0");
            
            GD.Print("✓ Recipe loading tests passed");
        }

        private void TestItemLookup()
        {
            GD.Print("Testing item lookup...");
            
            // Test valid item lookup
            var item = _itemDatabase.GetItem("cloth");
            Assert(item != null, "Should find cloth item");
            Assert(item.Id == "cloth", "Item ID should match");
            
            // Test invalid item lookup
            var invalidItem = _itemDatabase.GetItem("nonexistent_item");
            Assert(invalidItem == null, "Should return null for nonexistent item");
            
            GD.Print("✓ Item lookup tests passed");
        }

        private void TestRecipeLookup()
        {
            GD.Print("Testing recipe lookup...");
            
            // Test valid recipe lookup
            var recipe = _itemDatabase.GetRecipe("craft_bandage");
            Assert(recipe != null, "Should find bandage recipe");
            Assert(recipe.Id == "craft_bandage", "Recipe ID should match");
            
            // Test invalid recipe lookup
            var invalidRecipe = _itemDatabase.GetRecipe("nonexistent_recipe");
            Assert(invalidRecipe == null, "Should return null for nonexistent recipe");
            
            GD.Print("✓ Recipe lookup tests passed");
        }

        private void TestRecipesByOutput()
        {
            GD.Print("Testing recipes by output...");
            
            // Test finding recipes by output
            var bandageRecipes = _itemDatabase.GetRecipesByOutput("bandage");
            Assert(bandageRecipes.Count > 0, "Should find recipes that output bandage");
            Assert(bandageRecipes[0].Output.Id == "bandage", "Recipe should output bandage");
            
            // Test nonexistent output
            var nonexistentRecipes = _itemDatabase.GetRecipesByOutput("nonexistent_item");
            Assert(nonexistentRecipes.Count == 0, "Should return empty list for nonexistent output");
            
            GD.Print("✓ Recipes by output tests passed");
        }

        private void TestItemExistence()
        {
            GD.Print("Testing item existence checks...");
            
            // Test existing item
            Assert(_itemDatabase.HasItem("bandage"), "Should return true for existing item");
            
            // Test nonexistent item
            Assert(!_itemDatabase.HasItem("nonexistent_item"), "Should return false for nonexistent item");
            
            // Test null/empty input
            Assert(!_itemDatabase.HasItem(null), "Should return false for null input");
            Assert(!_itemDatabase.HasItem(""), "Should return false for empty input");
            
            GD.Print("✓ Item existence tests passed");
        }

        private void TestRecipeExistence()
        {
            GD.Print("Testing recipe existence checks...");
            
            // Test existing recipe
            Assert(_itemDatabase.HasRecipe("craft_bandage"), "Should return true for existing recipe");
            
            // Test nonexistent recipe
            Assert(!_itemDatabase.HasRecipe("nonexistent_recipe"), "Should return false for nonexistent recipe");
            
            // Test null/empty input
            Assert(!_itemDatabase.HasRecipe(null), "Should return false for null input");
            Assert(!_itemDatabase.HasRecipe(""), "Should return false for empty input");
            
            GD.Print("✓ Recipe existence tests passed");
        }

        private void TestGetAllMethods()
        {
            GD.Print("Testing get all methods...");
            
            var allItems = _itemDatabase.GetAllItems();
            Assert(allItems.Count > 0, "Should return all items");
            Assert(allItems.ContainsKey("bandage"), "Should contain bandage item");
            
            var allRecipes = _itemDatabase.GetAllRecipes();
            Assert(allRecipes.Count > 0, "Should return all recipes");
            Assert(allRecipes.ContainsKey("craft_bandage"), "Should contain bandage recipe");
            
            GD.Print("✓ Get all methods tests passed");
        }

        private void TestCounts()
        {
            GD.Print("Testing count properties...");
            
            Assert(_itemDatabase.ItemCount > 0, "Item count should be greater than 0");
            Assert(_itemDatabase.RecipeCount > 0, "Recipe count should be greater than 0");
            
            // Verify counts match actual data
            var allItems = _itemDatabase.GetAllItems();
            var allRecipes = _itemDatabase.GetAllRecipes();
            
            Assert(_itemDatabase.ItemCount == allItems.Count, "Item count should match dictionary size");
            Assert(_itemDatabase.RecipeCount == allRecipes.Count, "Recipe count should match dictionary size");
            
            GD.Print("✓ Count tests passed");
        }

        private void TestNullAndEmptyInputs()
        {
            GD.Print("Testing null and empty input handling...");
            
            // Test GetItem with null/empty
            Assert(_itemDatabase.GetItem(null) == null, "GetItem should handle null input");
            Assert(_itemDatabase.GetItem("") == null, "GetItem should handle empty input");
            
            // Test GetRecipe with null/empty
            Assert(_itemDatabase.GetRecipe(null) == null, "GetRecipe should handle null input");
            Assert(_itemDatabase.GetRecipe("") == null, "GetRecipe should handle empty input");
            
            // Test GetRecipesByOutput with null/empty
            var nullRecipes = _itemDatabase.GetRecipesByOutput(null);
            Assert(nullRecipes.Count == 0, "GetRecipesByOutput should handle null input");
            
            var emptyRecipes = _itemDatabase.GetRecipesByOutput("");
            Assert(emptyRecipes.Count == 0, "GetRecipesByOutput should handle empty input");
            
            GD.Print("✓ Null and empty input tests passed");
        }

        private void Assert(bool condition, string message)
        {
            if (!condition)
            {
                GD.PrintErr($"ASSERTION FAILED: {message}");
            }
        }
    }
}