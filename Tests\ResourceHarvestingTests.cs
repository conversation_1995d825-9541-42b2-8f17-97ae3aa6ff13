using Godot;
using System;
using System.Collections.Generic;
using System.Linq;

namespace SurvivalLooterShooter
{
    /// <summary>
    /// Comprehensive tests for the resource harvesting system
    /// Tests resource node creation, harvesting mechanics, tool requirements, and regeneration
    /// </summary>
    public partial class ResourceHarvestingTests : Node
    {
        private ResourceHarvestingSystem _resourceSystem;
        private GameManager _gameManager;
        private PlayerController _player;
        private Inventory _inventory;
        
        private int _testsRun = 0;
        private int _testsPassed = 0;
        private List<string> _testResults = new List<string>();

        public override void _Ready()
        {
            GD.Print("=== Resource Harvesting System Tests ===");
            
            // Initialize test environment
            InitializeTestEnvironment();
            
            // Run tests after a short delay to ensure everything is initialized
            CallDeferred(nameof(RunAllTests));
        }

        /// <summary>
        /// Initializes the test environment
        /// </summary>
        private void InitializeTestEnvironment()
        {
            // Create GameManager for testing
            _gameManager = new GameManager();
            AddChild(_gameManager);
        }

        /// <summary>
        /// Runs all resource harvesting tests
        /// </summary>
        private void RunAllTests()
        {
            // Get systems from GameManager
            _resourceSystem = _gameManager.GetResourceHarvestingSystem();
            _player = _gameManager.GetPlayerController();
            _inventory = _gameManager.GetInventory();

            if (_resourceSystem == null || _player == null || _inventory == null)
            {
                GD.PrintErr("Failed to initialize test environment - missing required systems");
                return;
            }

            // Run individual tests
            TestResourceNodeCreation();
            TestResourceNodeProperties();
            TestToolRequirements();
            TestHarvestingMechanics();
            TestResourceDepletion();
            TestResourceRegeneration();
            TestSkillBonuses();
            TestParticleEffects();
            TestEventSystem();
            TestSaveLoadIntegration();

            // Print test summary
            PrintTestSummary();
        }

        /// <summary>
        /// Tests resource node creation and initialization
        /// </summary>
        private void TestResourceNodeCreation()
        {
            RunTest("Resource Node Creation", () =>
            {
                // Create a test resource node
                var resourceNode = new ResourceNode();
                resourceNode.ResourceId = "wood";
                resourceNode.ResourceType = "organic";
                resourceNode.RequiredTool = "axe";
                resourceNode.MinYield = 1;
                resourceNode.MaxYield = 5;
                resourceNode.HarvestTime = 2.0f;
                resourceNode.RegenerationTime = 300f;
                resourceNode.InteractionRange = 50f;
                resourceNode.GlobalPosition = new Vector2(100, 100);

                AddChild(resourceNode);

                // Verify properties
                Assert(resourceNode.ResourceId == "wood", "Resource ID should be set correctly");
                Assert(resourceNode.ResourceType == "organic", "Resource type should be set correctly");
                Assert(resourceNode.RequiredTool == "axe", "Required tool should be set correctly");
                Assert(!resourceNode.IsDepleted, "New resource should not be depleted");
                Assert(!resourceNode.IsBeingHarvested, "New resource should not be being harvested");
                Assert(resourceNode.CurrentYield > 0, "New resource should have yield");

                resourceNode.QueueFree();
                return true;
            });
        }

        /// <summary>
        /// Tests resource node properties and configuration
        /// </summary>
        private void TestResourceNodeProperties()
        {
            RunTest("Resource Node Properties", () =>
            {
                var resourceNode = CreateTestResourceNode("stone", new Vector2(200, 200));
                
                // Test yield properties
                Assert(resourceNode.MinYield <= resourceNode.MaxYield, "Min yield should be <= max yield");
                Assert(resourceNode.CurrentYield <= resourceNode.MaxYield, "Current yield should be <= max yield");
                Assert(resourceNode.CurrentYield >= resourceNode.MinYield, "Current yield should be >= min yield");
                
                // Test time properties
                Assert(resourceNode.HarvestTime > 0, "Harvest time should be positive");
                Assert(resourceNode.RegenerationTime > 0, "Regeneration time should be positive");
                
                // Test range properties
                Assert(resourceNode.InteractionRange > 0, "Interaction range should be positive");
                
                resourceNode.QueueFree();
                return true;
            });
        }

        /// <summary>
        /// Tests tool requirements for harvesting
        /// </summary>
        private void TestToolRequirements()
        {
            RunTest("Tool Requirements", () =>
            {
                // Test resource that requires no tool
                var berryNode = CreateTestResourceNode("berries", new Vector2(300, 300));
                berryNode.RequiredTool = "none";
                
                // Should be able to harvest without tools
                bool canHarvestWithoutTool = berryNode.TryStartHarvesting(_player);
                Assert(canHarvestWithoutTool, "Should be able to harvest berries without tools");
                berryNode.StopHarvesting();
                
                // Test resource that requires a tool
                var woodNode = CreateTestResourceNode("wood", new Vector2(400, 400));
                woodNode.RequiredTool = "axe";
                
                // Clear inventory first
                _inventory.Clear();
                
                // Should not be able to harvest without required tool
                bool canHarvestWithoutRequiredTool = woodNode.TryStartHarvesting(_player);
                Assert(!canHarvestWithoutRequiredTool, "Should not be able to harvest wood without axe");
                
                // Add required tool
                _inventory.AddItem("axe", 1);
                
                // Should now be able to harvest
                bool canHarvestWithTool = woodNode.TryStartHarvesting(_player);
                Assert(canHarvestWithTool, "Should be able to harvest wood with axe");
                
                berryNode.QueueFree();
                woodNode.QueueFree();
                return true;
            });
        }

        /// <summary>
        /// Tests harvesting mechanics and yield calculation
        /// </summary>
        private void TestHarvestingMechanics()
        {
            RunTest("Harvesting Mechanics", () =>
            {
                var resourceNode = CreateTestResourceNode("stone", new Vector2(500, 500));
                resourceNode.RequiredTool = "pickaxe";
                resourceNode.HarvestTime = 0.1f; // Very fast for testing
                
                // Add required tool
                _inventory.AddItem("pickaxe", 1);
                
                int initialYield = resourceNode.CurrentYield;
                int initialStoneCount = _inventory.GetItemQuantity("stone");
                
                // Start harvesting
                bool harvestStarted = resourceNode.TryStartHarvesting(_player);
                Assert(harvestStarted, "Harvesting should start successfully");
                Assert(resourceNode.IsBeingHarvested, "Resource should be marked as being harvested");
                
                // Wait for harvest to complete (simulate time passage)
                // In a real test, we would wait for the actual time, but for unit testing we'll force completion
                // This is a simplified test - in practice you'd need to simulate the time passage
                
                resourceNode.QueueFree();
                return true;
            });
        }

        /// <summary>
        /// Tests resource depletion mechanics
        /// </summary>
        private void TestResourceDepletion()
        {
            RunTest("Resource Depletion", () =>
            {
                var resourceNode = CreateTestResourceNode("berries", new Vector2(600, 600));
                resourceNode.RequiredTool = "none";
                resourceNode.MinYield = 1;
                resourceNode.MaxYield = 1; // Ensure single harvest depletes it
                
                Assert(!resourceNode.IsDepleted, "Resource should not be depleted initially");
                
                // Force depletion for testing
                resourceNode.ForceDeplete();
                
                Assert(resourceNode.IsDepleted, "Resource should be depleted after force depletion");
                Assert(resourceNode.CurrentYield == 0, "Depleted resource should have zero yield");
                
                // Should not be able to harvest depleted resource
                bool canHarvestDepleted = resourceNode.TryStartHarvesting(_player);
                Assert(!canHarvestDepleted, "Should not be able to harvest depleted resource");
                
                resourceNode.QueueFree();
                return true;
            });
        }

        /// <summary>
        /// Tests resource regeneration mechanics
        /// </summary>
        private void TestResourceRegeneration()
        {
            RunTest("Resource Regeneration", () =>
            {
                var resourceNode = CreateTestResourceNode("mushrooms", new Vector2(700, 700));
                resourceNode.RegenerationTime = 0.1f; // Very fast for testing
                
                // Force depletion
                resourceNode.ForceDeplete();
                Assert(resourceNode.IsDepleted, "Resource should be depleted");
                
                // Force regeneration
                resourceNode.ForceRegenerate();
                
                Assert(!resourceNode.IsDepleted, "Resource should not be depleted after regeneration");
                Assert(resourceNode.CurrentYield > 0, "Regenerated resource should have yield");
                
                resourceNode.QueueFree();
                return true;
            });
        }

        /// <summary>
        /// Tests skill-based harvesting bonuses (placeholder for future skill system)
        /// </summary>
        private void TestSkillBonuses()
        {
            RunTest("Skill Bonuses", () =>
            {
                // This is a placeholder test for the skill system integration
                // Currently the skill bonus is just a random value, but in the future
                // it would be based on actual player skills
                
                var resourceNode = CreateTestResourceNode("metal_ore", new Vector2(800, 800));
                
                // Test that skill bonus calculation doesn't crash
                // The actual bonus calculation is done internally during harvesting
                Assert(true, "Skill bonus system should not crash");
                
                resourceNode.QueueFree();
                return true;
            });
        }

        /// <summary>
        /// Tests particle effects and visual feedback
        /// </summary>
        private void TestParticleEffects()
        {
            RunTest("Particle Effects", () =>
            {
                var resourceNode = CreateTestResourceNode("wood", new Vector2(900, 900));
                
                // Verify that particle systems are created
                var particles = resourceNode.GetChildren().OfType<GpuParticles2D>().FirstOrDefault();
                Assert(particles != null, "Resource node should have particle effects");
                
                // Verify that sound system is created
                var sound = resourceNode.GetChildren().OfType<AudioStreamPlayer2D>().FirstOrDefault();
                Assert(sound != null, "Resource node should have sound effects");
                
                resourceNode.QueueFree();
                return true;
            });
        }

        /// <summary>
        /// Tests event system integration
        /// </summary>
        private void TestEventSystem()
        {
            RunTest("Event System Integration", () =>
            {
                bool harvestEventReceived = false;
                bool depletionEventReceived = false;
                bool regenerationEventReceived = false;
                
                var resourceNode = CreateTestResourceNode("herbs", new Vector2(1000, 1000));
                
                // Connect to events
                resourceNode.ResourceHarvested += (resourceId, amount, position) => {
                    harvestEventReceived = true;
                };
                resourceNode.ResourceDepleted += (resourceId, position) => {
                    depletionEventReceived = true;
                };
                resourceNode.ResourceRegenerated += (resourceId, position) => {
                    regenerationEventReceived = true;
                };
                
                // Test depletion event
                resourceNode.ForceDeplete();
                Assert(depletionEventReceived, "Depletion event should be emitted");
                
                // Test regeneration event
                resourceNode.ForceRegenerate();
                Assert(regenerationEventReceived, "Regeneration event should be emitted");
                
                resourceNode.QueueFree();
                return true;
            });
        }

        /// <summary>
        /// Tests save/load integration for resource nodes
        /// </summary>
        private void TestSaveLoadIntegration()
        {
            RunTest("Save/Load Integration", () =>
            {
                var resourceNode = CreateTestResourceNode("stone", new Vector2(1100, 1100));
                resourceNode.RequiredTool = "pickaxe";
                
                // Get save data
                var saveData = resourceNode.GetSaveData();
                
                Assert(saveData != null, "Save data should not be null");
                Assert(saveData.ResourceId == "stone", "Save data should preserve resource ID");
                Assert(saveData.Position == new Vector2(1100, 1100), "Save data should preserve position");
                Assert(saveData.CurrentYield > 0, "Save data should preserve current yield");
                Assert(!saveData.IsDepleted, "Save data should preserve depletion state");
                
                // Test loading save data
                var newResourceNode = CreateTestResourceNode("wood", new Vector2(0, 0));
                newResourceNode.LoadSaveData(saveData);
                
                Assert(newResourceNode.ResourceId == "stone", "Loaded resource should have correct ID");
                Assert(newResourceNode.GlobalPosition == new Vector2(1100, 1100), "Loaded resource should have correct position");
                
                resourceNode.QueueFree();
                newResourceNode.QueueFree();
                return true;
            });
        }

        /// <summary>
        /// Creates a test resource node with default settings
        /// </summary>
        private ResourceNode CreateTestResourceNode(string resourceId, Vector2 position)
        {
            var resourceNode = new ResourceNode();
            resourceNode.ResourceId = resourceId;
            resourceNode.GlobalPosition = position;
            
            // Set default properties
            var item = ItemDatabase.Instance?.GetItem(resourceId);
            if (item != null)
            {
                resourceNode.ResourceType = item.Metadata.ContainsKey("resource_type") ? 
                    item.Metadata["resource_type"].ToString() : "generic";
                resourceNode.RequiredTool = item.Metadata.ContainsKey("tool_required") ? 
                    item.Metadata["tool_required"].ToString() : "none";
            }
            
            resourceNode.MinYield = 1;
            resourceNode.MaxYield = 5;
            resourceNode.HarvestTime = 2.0f;
            resourceNode.RegenerationTime = 300f;
            resourceNode.InteractionRange = 50f;
            
            AddChild(resourceNode);
            return resourceNode;
        }

        /// <summary>
        /// Runs a test with error handling and result tracking
        /// </summary>
        private void RunTest(string testName, Func<bool> testFunction)
        {
            _testsRun++;
            
            try
            {
                bool result = testFunction();
                if (result)
                {
                    _testsPassed++;
                    _testResults.Add($"✅ {testName}: PASSED");
                    GD.Print($"✅ {testName}: PASSED");
                }
                else
                {
                    _testResults.Add($"❌ {testName}: FAILED");
                    GD.PrintErr($"❌ {testName}: FAILED");
                }
            }
            catch (Exception ex)
            {
                _testResults.Add($"💥 {testName}: ERROR - {ex.Message}");
                GD.PrintErr($"💥 {testName}: ERROR - {ex.Message}");
            }
        }

        /// <summary>
        /// Asserts a condition and throws an exception if it fails
        /// </summary>
        private void Assert(bool condition, string message)
        {
            if (!condition)
            {
                throw new Exception($"Assertion failed: {message}");
            }
        }

        /// <summary>
        /// Prints the test summary
        /// </summary>
        private void PrintTestSummary()
        {
            GD.Print("\n=== Resource Harvesting Test Summary ===");
            GD.Print($"Tests Run: {_testsRun}");
            GD.Print($"Tests Passed: {_testsPassed}");
            GD.Print($"Tests Failed: {_testsRun - _testsPassed}");
            GD.Print($"Success Rate: {(_testsPassed * 100.0 / _testsRun):F1}%");
            
            GD.Print("\nDetailed Results:");
            foreach (var result in _testResults)
            {
                GD.Print(result);
            }
            
            if (_testsPassed == _testsRun)
            {
                GD.Print("\n🎉 All resource harvesting tests passed!");
            }
            else
            {
                GD.PrintErr($"\n⚠️ {_testsRun - _testsPassed} tests failed. Check the logs for details.");
            }
        }
    }
}