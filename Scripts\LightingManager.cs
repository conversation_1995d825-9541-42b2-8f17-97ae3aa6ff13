using Godot;
using System;

public partial class LightingManager : Node
{
    public static LightingManager Instance { get; private set; }
    
    private Color _normalAmbientColor = Colors.White;
    private Color _currentAmbientColor = Colors.White;

    public override void _Ready()
    {
        if (Instance == null)
        {
            Instance = this;
        }
        else
        {
            QueueFree();
        }
    }

    public void SetAmbientColor(Color color)
    {
        _currentAmbientColor = color;
        // Apply ambient color to the environment
        GD.Print($"Ambient color set to: {color}");
    }

    public void RestoreNormalLighting()
    {
        _currentAmbientColor = _normalAmbientColor;
        GD.Print("Normal lighting restored");
    }
}