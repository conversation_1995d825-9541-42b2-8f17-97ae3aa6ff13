using Godot;
using System;
using System.Collections.Generic;

namespace SurvivalLooterShooter
{
    /// <summary>
    /// UI for building system - shows available structures and manages building mode
    /// </summary>
    public partial class BuildingUI : Control
    {
        private VBoxContainer _structureList;
        private Label _statusLabel;
        private Button _cancelButton;
        private BuildingManager _buildingManager;
        
        // UI state
        private bool _isVisible = false;

        public override void _Ready()
        {
            // Set up UI structure - fill the screen
            AnchorLeft = 0;
            AnchorTop = 0;
            AnchorRight = 1;
            AnchorBottom = 1;
            
            // Create main container
            var mainContainer = new VBoxContainer();
            AddChild(mainContainer);
            
            // Title
            var titleLabel = new Label();
            titleLabel.Text = "Building Menu";
            titleLabel.AddThemeStyleboxOverride("normal", new StyleBoxFlat());
            mainContainer.AddChild(titleLabel);
            
            // Structure list
            _structureList = new VBoxContainer();
            var scrollContainer = new ScrollContainer();
            scrollContainer.CustomMinimumSize = new Vector2(300, 400);
            scrollContainer.AddChild(_structureList);
            mainContainer.AddChild(scrollContainer);
            
            // Status label
            _statusLabel = new Label();
            _statusLabel.Text = "Select a structure to build";
            mainContainer.AddChild(_statusLabel);
            
            // Cancel button
            _cancelButton = new Button();
            _cancelButton.Text = "Cancel Building";
            _cancelButton.Pressed += OnCancelPressed;
            _cancelButton.Visible = false;
            mainContainer.AddChild(_cancelButton);
            
            // Initially hidden
            Visible = false;
            
            // Get building manager reference
            _buildingManager = BuildingManager.Instance;
            if (_buildingManager != null)
            {
                _buildingManager.BuildingModeChanged += OnBuildingModeChanged;
                PopulateStructureList();
            }
        }

        public override void _Input(InputEvent @event)
        {
            if (@event is InputEventKey keyEvent && keyEvent.Pressed)
            {
                if (keyEvent.Keycode == Key.B)
                {
                    ToggleVisibility();
                }
            }
        }

        /// <summary>
        /// Toggles the building UI visibility
        /// </summary>
        public void ToggleVisibility()
        {
            _isVisible = !_isVisible;
            Visible = _isVisible;
            
            if (_isVisible)
            {
                // Pause game or show cursor
                GetTree().Paused = true;
            }
            else
            {
                // Unpause game
                GetTree().Paused = false;
                
                // Cancel building mode if active
                if (_buildingManager != null && _buildingManager.IsBuildingMode)
                {
                    _buildingManager.SetBuildingMode(false);
                }
            }
        }

        /// <summary>
        /// Populates the structure list with available blueprints
        /// </summary>
        private void PopulateStructureList()
        {
            if (_buildingManager == null) return;
            
            var blueprints = _buildingManager.GetAllBlueprints();
            
            foreach (var kvp in blueprints)
            {
                var blueprint = kvp.Value;
                CreateStructureButton(blueprint);
            }
        }

        /// <summary>
        /// Creates a button for a structure blueprint
        /// </summary>
        private void CreateStructureButton(StructureBlueprint blueprint)
        {
            var container = new HBoxContainer();
            _structureList.AddChild(container);
            
            // Structure button
            var button = new Button();
            button.Text = blueprint.Name;
            button.CustomMinimumSize = new Vector2(200, 40);
            button.Pressed += () => OnStructureSelected(blueprint.Id);
            container.AddChild(button);
            
            // Cost display
            var costLabel = new Label();
            var costText = "Cost: ";
            foreach (var cost in blueprint.BuildCost)
            {
                costText += $"{cost.Item} x{cost.Amount}, ";
            }
            costText = costText.TrimEnd(',', ' ');
            costLabel.Text = costText;
            container.AddChild(costLabel);
        }

        /// <summary>
        /// Handles structure selection
        /// </summary>
        private void OnStructureSelected(string structureId)
        {
            if (_buildingManager == null) return;
            
            // Check if player has required materials
            var blueprint = _buildingManager.GetBlueprint(structureId);
            if (blueprint == null) return;
            
            var inventory = GetNode<Inventory>("/root/GameManager/Inventory");
            if (inventory == null)
            {
                _statusLabel.Text = "Error: Inventory not found";
                return;
            }
            
            // Check materials
            bool hasAllMaterials = true;
            foreach (var cost in blueprint.BuildCost)
            {
                if (!inventory.HasItem(cost.Item, cost.Amount))
                {
                    hasAllMaterials = false;
                    break;
                }
            }
            
            if (!hasAllMaterials)
            {
                _statusLabel.Text = "Insufficient materials!";
                return;
            }
            
            // Enter building mode
            _buildingManager.SetBuildingMode(true, structureId);
            _statusLabel.Text = $"Building {blueprint.Name} - Left click to place, Right click to cancel";
            
            // Hide UI but keep it accessible
            ToggleVisibility();
        }

        /// <summary>
        /// Handles cancel button press
        /// </summary>
        private void OnCancelPressed()
        {
            if (_buildingManager != null)
            {
                _buildingManager.SetBuildingMode(false);
            }
        }

        /// <summary>
        /// Handles building mode changes
        /// </summary>
        private void OnBuildingModeChanged(bool enabled, string structureId)
        {
            _cancelButton.Visible = enabled;
            
            if (!enabled)
            {
                _statusLabel.Text = "Select a structure to build";
            }
        }
    }
}