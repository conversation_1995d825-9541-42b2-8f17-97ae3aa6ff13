using Godot;
using System.Collections.Generic;

namespace SurvivalLooterShooter
{
    /// <summary>
    /// Manages time-based gameplay mechanics and NPC schedules
    /// Handles enemy spawn rate changes, resource regeneration, and other time-dependent systems
    /// </summary>
    public partial class TimeBasedGameplayManager : Node
    {
        private static TimeBasedGameplayManager _instance;
        public static TimeBasedGameplayManager Instance => _instance;

        // Time-based modifiers
        [Export] public float NightEnemySpawnMultiplier { get; set; } = 2.0f;
        [Export] public float NightVisibilityReduction { get; set; } = 0.5f;
        [Export] public float DawnDuskSpawnMultiplier { get; set; } = 1.5f;
        
        // Resource regeneration settings
        [Export] public float ResourceRegenInterval { get; set; } = 300.0f; // 5 minutes
        [Export] public float NightRegenBonus { get; set; } = 1.2f;
        
        // Current time state
        private bool _isNightTime = false;
        private bool _isDawnTime = false;
        private bool _isDuskTime = false;
        private string _currentTimePeriod = "Day";
        
        // Timers
        private Timer _resourceRegenTimer;
        private Timer _enemySpawnTimer;
        
        // System references
        private EnemyManager _enemyManager;
        private ResourceHarvestingSystem _resourceHarvestingSystem;

        public override void _Ready()
        {
            // Singleton setup
            if (_instance == null)
            {
                _instance = this;
                AddToGroup("time_based_gameplay");
                Logger.LogInfo("TimeBasedGameplayManager", "TimeBasedGameplayManager singleton initialized");
            }
            else
            {
                Logger.LogError("TimeBasedGameplayManager", "Multiple TimeBasedGameplayManager instances detected! Removing duplicate.");
                QueueFree();
                return;
            }

            // Setup timers
            SetupTimers();
            
            // Connect to day/night cycle events
            if (DayNightCycle.Instance != null)
            {
                DayNightCycle.Instance.TimeChanged += OnTimeChanged;
                DayNightCycle.Instance.DayStarted += OnDayStarted;
                DayNightCycle.Instance.NightStarted += OnNightStarted;
                DayNightCycle.Instance.Dawn += OnDawn;
                DayNightCycle.Instance.Dusk += OnDusk;
                
                // Set initial state
                _isNightTime = DayNightCycle.Instance.IsNightTime;
                _isDawnTime = DayNightCycle.Instance.IsDawnTime;
                _isDuskTime = DayNightCycle.Instance.IsDuskTime;
                _currentTimePeriod = DayNightCycle.Instance.GetTimePeriod();
            }

            // Get system references
            CallDeferred(nameof(GetSystemReferences));

            Logger.LogInfo("TimeBasedGameplayManager", "Time-based gameplay mechanics initialized");
        }

        /// <summary>
        /// Sets up timers for various time-based mechanics
        /// </summary>
        private void SetupTimers()
        {
            // Resource regeneration timer
            _resourceRegenTimer = new Timer();
            _resourceRegenTimer.WaitTime = ResourceRegenInterval;
            _resourceRegenTimer.Timeout += OnResourceRegenTimer;
            _resourceRegenTimer.Autostart = true;
            AddChild(_resourceRegenTimer);

            // Enemy spawn adjustment timer (checks every 30 seconds)
            _enemySpawnTimer = new Timer();
            _enemySpawnTimer.WaitTime = 30.0f;
            _enemySpawnTimer.Timeout += OnEnemySpawnTimer;
            _enemySpawnTimer.Autostart = true;
            AddChild(_enemySpawnTimer);
        }

        /// <summary>
        /// Gets references to other game systems
        /// </summary>
        private void GetSystemReferences()
        {
            // Get enemy manager
            var gameManager = GetTree().GetFirstNodeInGroup("game_manager") as GameManager;
            if (gameManager != null)
            {
                _enemyManager = gameManager.GetEnemyManager();
                _resourceHarvestingSystem = gameManager.GetResourceHarvestingSystem();
            }
        }

        /// <summary>
        /// Gets the current enemy spawn rate multiplier based on time of day
        /// </summary>
        public float GetEnemySpawnMultiplier()
        {
            if (_isNightTime)
            {
                return NightEnemySpawnMultiplier;
            }
            else if (_isDawnTime || _isDuskTime)
            {
                return DawnDuskSpawnMultiplier;
            }
            else
            {
                return 1.0f; // Normal day spawn rate
            }
        }

        /// <summary>
        /// Gets the current visibility modifier based on time of day
        /// </summary>
        public float GetVisibilityModifier()
        {
            if (_isNightTime)
            {
                return NightVisibilityReduction;
            }
            else if (_isDawnTime || _isDuskTime)
            {
                return 0.8f; // Slightly reduced visibility during twilight
            }
            else
            {
                return 1.0f; // Full visibility during day
            }
        }

        /// <summary>
        /// Gets the current resource regeneration multiplier
        /// </summary>
        public float GetResourceRegenMultiplier()
        {
            if (_isNightTime)
            {
                return NightRegenBonus; // Resources regenerate faster at night
            }
            else
            {
                return 1.0f;
            }
        }

        /// <summary>
        /// Checks if certain activities should be modified based on time
        /// </summary>
        public bool ShouldModifyActivity(string activityType)
        {
            switch (activityType.ToLower())
            {
                case "enemy_spawning":
                    return _isNightTime || _isDawnTime || _isDuskTime;
                case "resource_harvesting":
                    return _isNightTime; // Bonus resources at night
                case "crafting":
                    return false; // Crafting not affected by time
                case "building":
                    return _isNightTime; // Building might be harder at night
                default:
                    return false;
            }
        }

        /// <summary>
        /// Gets time-based activity modifiers
        /// </summary>
        public Dictionary<string, float> GetActivityModifiers()
        {
            var modifiers = new Dictionary<string, float>();
            
            if (_isNightTime)
            {
                modifiers["enemy_spawn_rate"] = NightEnemySpawnMultiplier;
                modifiers["visibility"] = NightVisibilityReduction;
                modifiers["resource_regen"] = NightRegenBonus;
                modifiers["building_speed"] = 0.8f; // Slower building at night
                modifiers["movement_speed"] = 0.9f; // Slightly slower movement at night
            }
            else if (_isDawnTime || _isDuskTime)
            {
                modifiers["enemy_spawn_rate"] = DawnDuskSpawnMultiplier;
                modifiers["visibility"] = 0.8f;
                modifiers["resource_regen"] = 1.1f; // Slight bonus during twilight
            }
            else
            {
                // Day time - normal values
                modifiers["enemy_spawn_rate"] = 1.0f;
                modifiers["visibility"] = 1.0f;
                modifiers["resource_regen"] = 1.0f;
                modifiers["building_speed"] = 1.0f;
                modifiers["movement_speed"] = 1.0f;
            }

            return modifiers;
        }

        #region Timer Event Handlers

        /// <summary>
        /// Handles resource regeneration timer
        /// </summary>
        private void OnResourceRegenTimer()
        {
            if (_resourceHarvestingSystem == null) return;

            float regenMultiplier = GetResourceRegenMultiplier();
            
            // Trigger resource regeneration with time-based bonus
            Logger.LogInfo("TimeBasedGameplayManager", 
                $"Triggering resource regeneration (multiplier: {regenMultiplier:F1})");
            
            // This would integrate with the resource harvesting system
            // to regenerate depleted resource nodes
        }

        /// <summary>
        /// Handles enemy spawn adjustment timer
        /// </summary>
        private void OnEnemySpawnTimer()
        {
            if (_enemyManager == null) return;

            float spawnMultiplier = GetEnemySpawnMultiplier();
            
            // Adjust enemy spawn rates based on time of day
            Logger.LogInfo("TimeBasedGameplayManager", 
                $"Adjusting enemy spawn rates for {_currentTimePeriod} (multiplier: {spawnMultiplier:F1})");
        }

        #endregion

        #region Day/Night Cycle Event Handlers

        private void OnTimeChanged(float currentTime, bool isNight)
        {
            _isNightTime = isNight;
            
            if (DayNightCycle.Instance != null)
            {
                _isDawnTime = DayNightCycle.Instance.IsDawnTime;
                _isDuskTime = DayNightCycle.Instance.IsDuskTime;
                _currentTimePeriod = DayNightCycle.Instance.GetTimePeriod();
            }

            // Emit events for other systems to respond to time changes
            if (EventBus.Instance != null)
            {
                EventBus.Instance.OnDayNightChanged(currentTime, isNight);
            }
        }

        private void OnDayStarted()
        {
            Logger.LogInfo("TimeBasedGameplayManager", "Day started - applying day time modifiers");
            
            // Reset enemy spawn rates to normal
            // Restore full visibility
            // Normal resource regeneration
            
            if (EventBus.Instance != null)
            {
                EventBus.Instance.EmitNotificationRequested("Day has begun", "info", 2.0f);
            }
        }

        private void OnNightStarted()
        {
            Logger.LogInfo("TimeBasedGameplayManager", "Night started - applying night time modifiers");
            
            // Increase enemy spawn rates
            // Reduce visibility
            // Increase resource regeneration
            
            if (EventBus.Instance != null)
            {
                EventBus.Instance.EmitNotificationRequested("Night has fallen - be careful!", "warning", 3.0f);
            }
        }

        private void OnDawn()
        {
            Logger.LogInfo("TimeBasedGameplayManager", "Dawn - applying twilight modifiers");
            
            if (EventBus.Instance != null)
            {
                EventBus.Instance.EmitNotificationRequested("Dawn is breaking", "info", 2.0f);
            }
        }

        private void OnDusk()
        {
            Logger.LogInfo("TimeBasedGameplayManager", "Dusk - applying twilight modifiers");
            
            if (EventBus.Instance != null)
            {
                EventBus.Instance.EmitNotificationRequested("Dusk is falling", "info", 2.0f);
            }
        }

        #endregion

        /// <summary>
        /// Manually triggers a time-based event (useful for testing)
        /// </summary>
        public void TriggerTimeBasedEvent(string eventType)
        {
            switch (eventType.ToLower())
            {
                case "resource_regen":
                    OnResourceRegenTimer();
                    break;
                case "enemy_spawn_adjust":
                    OnEnemySpawnTimer();
                    break;
                default:
                    Logger.LogWarning("TimeBasedGameplayManager", $"Unknown time-based event: {eventType}");
                    break;
            }
        }

        public override void _ExitTree()
        {
            if (_instance == this)
            {
                _instance = null;
            }

            // Disconnect from day/night cycle events
            if (DayNightCycle.Instance != null)
            {
                DayNightCycle.Instance.TimeChanged -= OnTimeChanged;
                DayNightCycle.Instance.DayStarted -= OnDayStarted;
                DayNightCycle.Instance.NightStarted -= OnNightStarted;
                DayNightCycle.Instance.Dawn -= OnDawn;
                DayNightCycle.Instance.Dusk -= OnDusk;
            }
        }
    }
}