using Godot;
using System;
using System.Collections.Generic;
using System.Linq;

namespace SurvivalLooterShooter
{
    /// <summary>
    /// POIGenerator creates and manages Points of Interest (buildings, caves, special locations)
    /// Handles POI placement, interior generation, loot distribution, and reset mechanics
    /// </summary>
    public partial class POIGenerator : Node
    {
        // Generation parameters
        [Export] public float POISpawnChance { get; set; } = 0.15f; // Chance per chunk to spawn a POI
        [Export] public float MinDistanceBetweenPOIs { get; set; } = 200f; // Minimum distance between POIs
        [Export] public int MaxPOIsPerChunk { get; set; } = 2; // Maximum POIs per chunk
        [Export] public float LootRespawnTime { get; set; } = 3600f; // 1 hour in seconds

        // Noise generators for POI placement
        private FastNoiseLite _poiPlacementNoise;
        private FastNoiseLite _poiTypeNoise;
        private FastNoiseLite _lootDensityNoise;

        // POI data and tracking
        private Dictionary<string, POIData> _poiTemplates = new();
        private Dictionary<Vector2I, List<PointOfInterest>> _chunkPOIs = new();
        private Dictionary<string, PointOfInterest> _allPOIs = new(); // Global POI registry by ID
        private Dictionary<string, LootTable> _lootTables = new();

        // World seed for consistent generation
        private int _worldSeed;

        // Events
        [Signal]
        public delegate void POIDiscoveredEventHandler(string poiId, POIType type, Vector2 position);
        
        [Signal]
        public delegate void POILootRespawnedEventHandler(string poiId);

        public override void _Ready()
        {
            LoadPOITemplatesFromJson();
            LoadLootTablesFromJson();
        }

        /// <summary>
        /// Initializes the POI generator with world seed
        /// </summary>
        public void Initialize(int worldSeed)
        {
            _worldSeed = worldSeed;

            // Initialize noise generators
            _poiPlacementNoise = new FastNoiseLite
            {
                Seed = worldSeed + 5000,
                NoiseType = FastNoiseLite.NoiseTypeEnum.Perlin,
                Frequency = 0.003f
            };

            _poiTypeNoise = new FastNoiseLite
            {
                Seed = worldSeed + 6000,
                NoiseType = FastNoiseLite.NoiseTypeEnum.Simplex,
                Frequency = 0.008f
            };

            _lootDensityNoise = new FastNoiseLite
            {
                Seed = worldSeed + 7000,
                NoiseType = FastNoiseLite.NoiseTypeEnum.Cellular,
                Frequency = 0.02f
            };

            GD.Print($"POIGenerator initialized with seed: {worldSeed}");
        }

        /// <summary>
        /// Generates POIs for a world chunk
        /// </summary>
        public void GeneratePOIsForChunk(WorldChunk chunk)
        {
            Vector2 chunkWorldPos = new Vector2(
                chunk.ChunkCoords.X * chunk.ChunkSize,
                chunk.ChunkCoords.Y * chunk.ChunkSize
            );

            var chunkPOIs = new List<PointOfInterest>();
            var random = new Random(_worldSeed + chunk.ChunkCoords.GetHashCode());

            // Determine number of POIs for this chunk
            int poiCount = DeterminePOICount(chunkWorldPos, random);

            for (int i = 0; i < poiCount; i++)
            {
                var poi = GeneratePOI(chunk, chunkWorldPos, random, i);
                if (poi != null && IsValidPOIPlacement(poi, chunkPOIs))
                {
                    chunkPOIs.Add(poi);
                    _allPOIs[poi.Id] = poi;
                    
                    // Create visual representation
                    CreatePOIVisual(poi, chunk);
                    
                    GD.Print($"Generated POI: {poi.Name} at {poi.WorldPosition}");
                }
            }

            _chunkPOIs[chunk.ChunkCoords] = chunkPOIs;
        }

        /// <summary>
        /// Determines the number of POIs to generate in a chunk
        /// </summary>
        private int DeterminePOICount(Vector2 chunkWorldPos, Random random)
        {
            float placementNoise = _poiPlacementNoise.GetNoise2D(chunkWorldPos.X, chunkWorldPos.Y);
            
            // Higher noise values increase POI spawn chance
            if (placementNoise > 0.3f)
            {
                return random.NextDouble() < POISpawnChance ? random.Next(1, MaxPOIsPerChunk + 1) : 0;
            }
            else if (placementNoise > 0.0f)
            {
                return random.NextDouble() < POISpawnChance * 0.5f ? 1 : 0;
            }
            
            return 0;
        }

        /// <summary>
        /// Generates a single POI
        /// </summary>
        private PointOfInterest GeneratePOI(WorldChunk chunk, Vector2 chunkWorldPos, Random random, int index)
        {
            // Determine POI position within chunk
            Vector2 localPos = new Vector2(
                (float)(random.NextDouble() * chunk.ChunkSize * 0.8f + chunk.ChunkSize * 0.1f),
                (float)(random.NextDouble() * chunk.ChunkSize * 0.8f + chunk.ChunkSize * 0.1f)
            );
            Vector2 worldPos = chunkWorldPos + localPos;

            // Determine POI type based on biome and noise
            BiomeType biome = chunk.GetBiomeAtWorldPos(worldPos);
            POIType poiType = DeterminePOIType(worldPos, biome, random);

            // Get POI template
            string templateId = GetPOITemplateId(poiType, biome);
            if (!_poiTemplates.TryGetValue(templateId, out POIData template))
            {
                GD.PrintErr($"No template found for POI type {poiType} in biome {biome}");
                return null;
            }

            // Create POI instance
            string poiId = $"poi_{chunk.ChunkCoords.X}_{chunk.ChunkCoords.Y}_{index}";
            var poi = new PointOfInterest
            {
                Id = poiId,
                Name = template.Name,
                Type = poiType,
                WorldPosition = worldPos,
                ChunkCoords = chunk.ChunkCoords,
                Template = template,
                IsDiscovered = false,
                LastResetTime = Time.GetUnixTimeFromSystem(),
                Interior = GenerateInterior(template, random)
            };

            // Generate loot for the POI
            GenerateLootForPOI(poi, random);

            return poi;
        }

        /// <summary>
        /// Determines POI type based on location and biome
        /// </summary>
        private POIType DeterminePOIType(Vector2 worldPos, BiomeType biome, Random random)
        {
            float typeNoise = _poiTypeNoise.GetNoise2D(worldPos.X, worldPos.Y);
            
            // Biome-specific POI preferences
            var biomePreferences = biome switch
            {
                BiomeType.Forest => new[] { POIType.AbandonedCabin, POIType.Cave, POIType.Ruins },
                BiomeType.Desert => new[] { POIType.Oasis, POIType.AncientTemple, POIType.Cave },
                BiomeType.Mountains => new[] { POIType.Cave, POIType.MiningOutpost, POIType.Ruins },
                BiomeType.Swamp => new[] { POIType.AbandonedShack, POIType.Cave, POIType.Ruins },
                BiomeType.Tundra => new[] { POIType.IceCave, POIType.AbandonedOutpost, POIType.Ruins },
                BiomeType.Plains => new[] { POIType.Farmstead, POIType.Ruins, POIType.Cave },
                BiomeType.Ocean => new[] { POIType.Shipwreck, POIType.UndergroundCave },
                _ => new[] { POIType.Ruins, POIType.Cave }
            };

            // Use noise to select from biome preferences
            int index = Mathf.FloorToInt((typeNoise + 1.0f) * 0.5f * biomePreferences.Length);
            index = Mathf.Clamp(index, 0, biomePreferences.Length - 1);
            
            return biomePreferences[index];
        }

        /// <summary>
        /// Gets the template ID for a POI type and biome combination
        /// </summary>
        private string GetPOITemplateId(POIType poiType, BiomeType biome)
        {
            return $"{poiType.ToString().ToLower()}_{biome.ToString().ToLower()}";
        }

        /// <summary>
        /// Validates POI placement to ensure proper spacing
        /// </summary>
        private bool IsValidPOIPlacement(PointOfInterest newPOI, List<PointOfInterest> existingPOIs)
        {
            foreach (var existingPOI in existingPOIs)
            {
                if (newPOI.WorldPosition.DistanceTo(existingPOI.WorldPosition) < MinDistanceBetweenPOIs)
                {
                    return false;
                }
            }

            // Check against POIs in neighboring chunks
            foreach (var chunkPOIs in _chunkPOIs.Values)
            {
                foreach (var poi in chunkPOIs)
                {
                    if (newPOI.WorldPosition.DistanceTo(poi.WorldPosition) < MinDistanceBetweenPOIs)
                    {
                        return false;
                    }
                }
            }

            return true;
        }

        /// <summary>
        /// Generates interior layout for a POI
        /// </summary>
        private POIInterior GenerateInterior(POIData template, Random random)
        {
            var interior = new POIInterior
            {
                Rooms = new List<Room>(),
                Connections = new List<RoomConnection>(),
                Size = template.InteriorSize
            };

            // Generate rooms based on template
            int roomCount = random.Next(template.MinRooms, template.MaxRooms + 1);
            
            for (int i = 0; i < roomCount; i++)
            {
                var room = new Room
                {
                    Id = $"room_{i}",
                    Type = DetermineRoomType(template.Type, random),
                    Position = GenerateRoomPosition(interior, random),
                    Size = GenerateRoomSize(template.Type, random),
                    LootSpawns = new List<LootSpawn>()
                };

                interior.Rooms.Add(room);
            }

            // Generate connections between rooms
            GenerateRoomConnections(interior, random);

            return interior;
        }

        /// <summary>
        /// Determines room type based on POI type
        /// </summary>
        private RoomType DetermineRoomType(POIType poiType, Random random)
        {
            var roomTypes = poiType switch
            {
                POIType.AbandonedCabin => new[] { RoomType.LivingRoom, RoomType.Kitchen, RoomType.Bedroom, RoomType.Storage },
                POIType.Cave => new[] { RoomType.MainChamber, RoomType.Tunnel, RoomType.TreasureRoom },
                POIType.Ruins => new[] { RoomType.MainHall, RoomType.Chamber, RoomType.Vault },
                POIType.AbandonedShack => new[] { RoomType.MainRoom, RoomType.Storage },
                POIType.MiningOutpost => new[] { RoomType.Office, RoomType.Storage, RoomType.Workshop },
                POIType.AncientTemple => new[] { RoomType.Sanctum, RoomType.Chamber, RoomType.Vault },
                _ => new[] { RoomType.MainRoom, RoomType.Storage }
            };

            return roomTypes[random.Next(roomTypes.Length)];
        }

        /// <summary>
        /// Generates room position within interior bounds
        /// </summary>
        private Vector2 GenerateRoomPosition(POIInterior interior, Random random)
        {
            return new Vector2(
                (float)(random.NextDouble() * interior.Size.X * 0.8f),
                (float)(random.NextDouble() * interior.Size.Y * 0.8f)
            );
        }

        /// <summary>
        /// Generates room size based on POI type
        /// </summary>
        private Vector2 GenerateRoomSize(POIType poiType, Random random)
        {
            var sizeRange = poiType switch
            {
                POIType.Cave => new Vector2(8, 16),
                POIType.AbandonedCabin => new Vector2(6, 12),
                POIType.Ruins => new Vector2(10, 20),
                POIType.AncientTemple => new Vector2(12, 24),
                _ => new Vector2(6, 12)
            };

            return new Vector2(
                (float)(random.NextDouble() * (sizeRange.Y - sizeRange.X) + sizeRange.X),
                (float)(random.NextDouble() * (sizeRange.Y - sizeRange.X) + sizeRange.X)
            );
        }

        /// <summary>
        /// Generates connections between rooms
        /// </summary>
        private void GenerateRoomConnections(POIInterior interior, Random random)
        {
            if (interior.Rooms.Count < 2) return;

            // Connect each room to at least one other room
            for (int i = 1; i < interior.Rooms.Count; i++)
            {
                int connectTo = random.Next(i);
                interior.Connections.Add(new RoomConnection
                {
                    FromRoomId = interior.Rooms[i].Id,
                    ToRoomId = interior.Rooms[connectTo].Id,
                    Type = ConnectionType.Door
                });
            }

            // Add some additional connections for complexity
            int extraConnections = random.Next(0, interior.Rooms.Count / 2);
            for (int i = 0; i < extraConnections; i++)
            {
                int room1 = random.Next(interior.Rooms.Count);
                int room2 = random.Next(interior.Rooms.Count);
                
                if (room1 != room2)
                {
                    interior.Connections.Add(new RoomConnection
                    {
                        FromRoomId = interior.Rooms[room1].Id,
                        ToRoomId = interior.Rooms[room2].Id,
                        Type = random.NextDouble() < 0.7 ? ConnectionType.Door : ConnectionType.Passage
                    });
                }
            }
        }

        /// <summary>
        /// Generates loot for a POI based on its template and rarity tiers
        /// </summary>
        private void GenerateLootForPOI(PointOfInterest poi, Random random)
        {
            if (!_lootTables.TryGetValue(poi.Template.LootTableId, out LootTable lootTable))
            {
                GD.PrintErr($"No loot table found for POI template: {poi.Template.LootTableId}");
                return;
            }

            // Generate loot for each room
            foreach (var room in poi.Interior.Rooms)
            {
                int lootSpawnCount = DetermineLootSpawnCount(room.Type, random);
                
                for (int i = 0; i < lootSpawnCount; i++)
                {
                    var lootSpawn = GenerateLootSpawn(lootTable, room, random);
                    if (lootSpawn != null)
                    {
                        room.LootSpawns.Add(lootSpawn);
                    }
                }
            }
        }

        /// <summary>
        /// Determines how many loot spawns a room should have
        /// </summary>
        private int DetermineLootSpawnCount(RoomType roomType, Random random)
        {
            return roomType switch
            {
                RoomType.TreasureRoom => random.Next(3, 6),
                RoomType.Vault => random.Next(2, 5),
                RoomType.Storage => random.Next(2, 4),
                RoomType.Workshop => random.Next(1, 4),
                RoomType.MainChamber => random.Next(1, 3),
                RoomType.MainHall => random.Next(1, 3),
                _ => random.Next(0, 2)
            };
        }

        /// <summary>
        /// Generates a single loot spawn
        /// </summary>
        private LootSpawn GenerateLootSpawn(LootTable lootTable, Room room, Random random)
        {
            // Select loot tier based on room type and random chance
            LootTier tier = DetermineLootTier(room.Type, random);
            
            // Get items for this tier
            var tierItems = lootTable.Items.Where(item => item.Tier == tier).ToList();
            if (!tierItems.Any())
            {
                return null;
            }

            // Select item based on weight
            var selectedItem = SelectWeightedItem(tierItems, random);
            if (selectedItem == null)
            {
                return null;
            }

            return new LootSpawn
            {
                ItemId = selectedItem.ItemId,
                Quantity = random.Next(selectedItem.MinQuantity, selectedItem.MaxQuantity + 1),
                Position = new Vector2(
                    (float)(random.NextDouble() * room.Size.X),
                    (float)(random.NextDouble() * room.Size.Y)
                ),
                IsLooted = false,
                RespawnTime = selectedItem.RespawnTime,
                LastLootTime = 0
            };
        }

        /// <summary>
        /// Determines loot tier based on room type and random chance
        /// </summary>
        private LootTier DetermineLootTier(RoomType roomType, Random random)
        {
            double roll = random.NextDouble();
            
            return roomType switch
            {
                RoomType.TreasureRoom => roll < 0.3 ? LootTier.Legendary : roll < 0.6 ? LootTier.Rare : LootTier.Uncommon,
                RoomType.Vault => roll < 0.1 ? LootTier.Legendary : roll < 0.4 ? LootTier.Rare : LootTier.Uncommon,
                RoomType.Storage => roll < 0.05 ? LootTier.Rare : roll < 0.3 ? LootTier.Uncommon : LootTier.Common,
                RoomType.Workshop => roll < 0.1 ? LootTier.Rare : roll < 0.4 ? LootTier.Uncommon : LootTier.Common,
                _ => roll < 0.02 ? LootTier.Rare : roll < 0.15 ? LootTier.Uncommon : LootTier.Common
            };
        }

        /// <summary>
        /// Selects an item from a weighted list
        /// </summary>
        private LootTableItem SelectWeightedItem(List<LootTableItem> items, Random random)
        {
            float totalWeight = items.Sum(item => item.Weight);
            float randomValue = (float)(random.NextDouble() * totalWeight);
            
            float currentWeight = 0;
            foreach (var item in items)
            {
                currentWeight += item.Weight;
                if (randomValue <= currentWeight)
                {
                    return item;
                }
            }
            
            return items.LastOrDefault();
        }

        /// <summary>
        /// Creates visual representation of a POI
        /// </summary>
        private void CreatePOIVisual(PointOfInterest poi, WorldChunk chunk)
        {
            var visual = new Node2D
            {
                Name = $"POI_{poi.Id}",
                GlobalPosition = poi.WorldPosition
            };

            // Create visual indicator based on POI type
            var sprite = new ColorRect
            {
                Size = new Vector2(16, 16),
                Position = new Vector2(-8, -8),
                Color = GetPOIColor(poi.Type)
            };

            visual.AddChild(sprite);
            
            // Add to chunk's visual container
            var visualContainer = chunk.GetNode("Visuals");
            visualContainer?.AddChild(visual);
        }

        /// <summary>
        /// Gets color for POI visualization
        /// </summary>
        private static Color GetPOIColor(POIType poiType)
        {
            return poiType switch
            {
                POIType.AbandonedCabin => Colors.Brown,
                POIType.Cave => Colors.DarkGray,
                POIType.Ruins => Colors.Gray,
                POIType.AbandonedShack => Colors.SaddleBrown,
                POIType.MiningOutpost => Colors.Orange,
                POIType.AncientTemple => Colors.Gold,
                POIType.Oasis => Colors.Cyan,
                POIType.IceCave => Colors.LightBlue,
                POIType.Shipwreck => Colors.DarkBlue,
                POIType.Farmstead => Colors.Yellow,
                POIType.AbandonedOutpost => Colors.DarkRed,
                POIType.UndergroundCave => Colors.Black,
                _ => Colors.Purple
            };
        }

        /// <summary>
        /// Gets POIs in a specific chunk
        /// </summary>
        public List<PointOfInterest> GetPOIsInChunk(Vector2I chunkCoords)
        {
            return _chunkPOIs.TryGetValue(chunkCoords, out var pois) ? new List<PointOfInterest>(pois) : new List<PointOfInterest>();
        }

        /// <summary>
        /// Gets POIs within a radius of a world position
        /// </summary>
        public List<PointOfInterest> GetPOIsNear(Vector2 worldPosition, float radius)
        {
            var nearbyPOIs = new List<PointOfInterest>();
            
            foreach (var poi in _allPOIs.Values)
            {
                if (poi.WorldPosition.DistanceTo(worldPosition) <= radius)
                {
                    nearbyPOIs.Add(poi);
                }
            }
            
            return nearbyPOIs;
        }

        /// <summary>
        /// Gets a POI by its ID
        /// </summary>
        public PointOfInterest GetPOI(string poiId)
        {
            return _allPOIs.TryGetValue(poiId, out var poi) ? poi : null;
        }

        /// <summary>
        /// Marks a POI as discovered
        /// </summary>
        public void DiscoverPOI(string poiId, Vector2 playerPosition)
        {
            if (_allPOIs.TryGetValue(poiId, out var poi) && !poi.IsDiscovered)
            {
                poi.IsDiscovered = true;
                poi.DiscoveryTime = Time.GetUnixTimeFromSystem();
                
                EmitSignal(SignalName.POIDiscovered, poiId, (int)poi.Type, poi.WorldPosition);
                EventBus.Instance?.EmitSignal(EventBus.SignalName.GameStateChanged, "poi_discovered", poiId);
                
                GD.Print($"POI discovered: {poi.Name} at {poi.WorldPosition}");
            }
        }

        /// <summary>
        /// Resets a POI's loot and state
        /// </summary>
        public void ResetPOI(string poiId)
        {
            if (_allPOIs.TryGetValue(poiId, out var poi))
            {
                // Reset all loot spawns
                foreach (var room in poi.Interior.Rooms)
                {
                    foreach (var lootSpawn in room.LootSpawns)
                    {
                        lootSpawn.IsLooted = false;
                        lootSpawn.LastLootTime = 0;
                    }
                }

                poi.LastResetTime = Time.GetUnixTimeFromSystem();
                
                EmitSignal(SignalName.POILootRespawned, poiId);
                GD.Print($"POI reset: {poi.Name}");
            }
        }

        /// <summary>
        /// Updates POI states (loot respawning, etc.)
        /// </summary>
        public void UpdatePOIs(double deltaTime)
        {
            double currentTime = Time.GetUnixTimeFromSystem();
            
            foreach (var poi in _allPOIs.Values)
            {
                // Check for loot respawning
                bool anyLootRespawned = false;
                
                foreach (var room in poi.Interior.Rooms)
                {
                    foreach (var lootSpawn in room.LootSpawns)
                    {
                        if (lootSpawn.IsLooted && 
                            currentTime - lootSpawn.LastLootTime >= lootSpawn.RespawnTime)
                        {
                            lootSpawn.IsLooted = false;
                            anyLootRespawned = true;
                        }
                    }
                }

                if (anyLootRespawned)
                {
                    EmitSignal(SignalName.POILootRespawned, poi.Id);
                }
            }
        }

        /// <summary>
        /// Loads POI templates from JSON
        /// </summary>
        private void LoadPOITemplatesFromJson()
        {
            try
            {
                string jsonPath = "res://Data/POITemplates.json";
                if (!FileAccess.FileExists(jsonPath))
                {
                    GD.PrintErr("POITemplates.json not found, creating fallback data");
                    InitializeFallbackPOITemplates();
                    return;
                }

                using var file = FileAccess.Open(jsonPath, FileAccess.ModeFlags.Read);
                string jsonContent = file.GetAsText();
                
                var jsonData = Json.ParseString(jsonContent).AsGodotDictionary();
                var templatesArray = jsonData["poi_templates"].AsGodotArray();

                foreach (var templateVariant in templatesArray)
                {
                    var templateDict = templateVariant.AsGodotDictionary();
                    var poiData = ParsePOITemplateFromJson(templateDict);
                    _poiTemplates[poiData.Id] = poiData;
                }

                GD.Print($"Loaded {_poiTemplates.Count} POI templates from JSON");
            }
            catch (Exception ex)
            {
                GD.PrintErr($"Error loading POI templates: {ex.Message}");
                InitializeFallbackPOITemplates();
            }
        }

        /// <summary>
        /// Loads loot tables from JSON
        /// </summary>
        private void LoadLootTablesFromJson()
        {
            try
            {
                string jsonPath = "res://Data/LootTables.json";
                if (!FileAccess.FileExists(jsonPath))
                {
                    GD.PrintErr("LootTables.json not found, creating fallback data");
                    InitializeFallbackLootTables();
                    return;
                }

                using var file = FileAccess.Open(jsonPath, FileAccess.ModeFlags.Read);
                string jsonContent = file.GetAsText();
                
                var jsonData = Json.ParseString(jsonContent).AsGodotDictionary();
                var tablesArray = jsonData["loot_tables"].AsGodotArray();

                foreach (var tableVariant in tablesArray)
                {
                    var tableDict = tableVariant.AsGodotDictionary();
                    var lootTable = ParseLootTableFromJson(tableDict);
                    _lootTables[lootTable.Id] = lootTable;
                }

                GD.Print($"Loaded {_lootTables.Count} loot tables from JSON");
            }
            catch (Exception ex)
            {
                GD.PrintErr($"Error loading loot tables: {ex.Message}");
                InitializeFallbackLootTables();
            }
        }

        /// <summary>
        /// Parses POI template from JSON
        /// </summary>
        private static POIData ParsePOITemplateFromJson(Godot.Collections.Dictionary templateDict)
        {
            var interiorSize = templateDict["interior_size"].AsGodotArray();
            
            return new POIData
            {
                Id = templateDict["id"].AsString(),
                Name = templateDict["name"].AsString(),
                Type = Enum.Parse<POIType>(templateDict["type"].AsString(), true),
                InteriorSize = new Vector2(interiorSize[0].AsSingle(), interiorSize[1].AsSingle()),
                MinRooms = templateDict["min_rooms"].AsInt32(),
                MaxRooms = templateDict["max_rooms"].AsInt32(),
                LootTableId = templateDict["loot_table_id"].AsString(),
                ResetTime = templateDict["reset_time"].AsSingle()
            };
        }

        /// <summary>
        /// Parses loot table from JSON
        /// </summary>
        private static LootTable ParseLootTableFromJson(Godot.Collections.Dictionary tableDict)
        {
            var lootTable = new LootTable
            {
                Id = tableDict["id"].AsString(),
                Name = tableDict["name"].AsString(),
                Items = new List<LootTableItem>()
            };

            var itemsArray = tableDict["items"].AsGodotArray();
            foreach (var itemVariant in itemsArray)
            {
                var itemDict = itemVariant.AsGodotDictionary();
                var quantityRange = itemDict["quantity_range"].AsGodotArray();
                
                lootTable.Items.Add(new LootTableItem
                {
                    ItemId = itemDict["item_id"].AsString(),
                    Weight = itemDict["weight"].AsSingle(),
                    Tier = Enum.Parse<LootTier>(itemDict["tier"].AsString(), true),
                    MinQuantity = quantityRange[0].AsInt32(),
                    MaxQuantity = quantityRange[1].AsInt32(),
                    RespawnTime = itemDict["respawn_time"].AsSingle()
                });
            }

            return lootTable;
        }

        /// <summary>
        /// Initializes fallback POI templates
        /// </summary>
        private void InitializeFallbackPOITemplates()
        {
            _poiTemplates["abandonedcabin_forest"] = new POIData
            {
                Id = "abandonedcabin_forest",
                Name = "Abandoned Forest Cabin",
                Type = POIType.AbandonedCabin,
                InteriorSize = new Vector2(20, 15),
                MinRooms = 2,
                MaxRooms = 4,
                LootTableId = "cabin_loot",
                ResetTime = 3600f
            };

            _poiTemplates["cave_mountains"] = new POIData
            {
                Id = "cave_mountains",
                Name = "Mountain Cave",
                Type = POIType.Cave,
                InteriorSize = new Vector2(30, 25),
                MinRooms = 3,
                MaxRooms = 6,
                LootTableId = "cave_loot",
                ResetTime = 7200f
            };

            GD.Print("Fallback POI templates initialized");
        }

        /// <summary>
        /// Initializes fallback loot tables
        /// </summary>
        private void InitializeFallbackLootTables()
        {
            _lootTables["cabin_loot"] = new LootTable
            {
                Id = "cabin_loot",
                Name = "Cabin Loot",
                Items = new List<LootTableItem>
                {
                    new() { ItemId = "wood", Weight = 10f, Tier = LootTier.Common, MinQuantity = 5, MaxQuantity = 15, RespawnTime = 1800f },
                    new() { ItemId = "cloth", Weight = 8f, Tier = LootTier.Common, MinQuantity = 2, MaxQuantity = 8, RespawnTime = 1800f },
                    new() { ItemId = "bandage", Weight = 5f, Tier = LootTier.Uncommon, MinQuantity = 1, MaxQuantity = 3, RespawnTime = 3600f }
                }
            };

            _lootTables["cave_loot"] = new LootTable
            {
                Id = "cave_loot",
                Name = "Cave Loot",
                Items = new List<LootTableItem>
                {
                    new() { ItemId = "stone", Weight = 15f, Tier = LootTier.Common, MinQuantity = 10, MaxQuantity = 25, RespawnTime = 1800f },
                    new() { ItemId = "metal_ore", Weight = 8f, Tier = LootTier.Uncommon, MinQuantity = 3, MaxQuantity = 8, RespawnTime = 3600f },
                    new() { ItemId = "gems", Weight = 2f, Tier = LootTier.Rare, MinQuantity = 1, MaxQuantity = 2, RespawnTime = 7200f }
                }
            };

            GD.Print("Fallback loot tables initialized");
        }

        /// <summary>
        /// Gets save data for POI system
        /// </summary>
        public POISaveData GetSaveData()
        {
            var saveData = new POISaveData
            {
                DiscoveredPOIs = new List<string>(),
                POIStates = new Dictionary<string, POIState>()
            };

            foreach (var poi in _allPOIs.Values)
            {
                if (poi.IsDiscovered)
                {
                    saveData.DiscoveredPOIs.Add(poi.Id);
                }

                saveData.POIStates[poi.Id] = new POIState
                {
                    LastResetTime = poi.LastResetTime,
                    LootStates = poi.Interior.Rooms.SelectMany(room => room.LootSpawns)
                        .Select(loot => new LootState
                        {
                            IsLooted = loot.IsLooted,
                            LastLootTime = loot.LastLootTime
                        }).ToList()
                };
            }

            return saveData;
        }

        /// <summary>
        /// Loads save data for POI system
        /// </summary>
        public void LoadSaveData(POISaveData saveData)
        {
            foreach (var poiId in saveData.DiscoveredPOIs)
            {
                if (_allPOIs.TryGetValue(poiId, out var poi))
                {
                    poi.IsDiscovered = true;
                }
            }

            foreach (var kvp in saveData.POIStates)
            {
                if (_allPOIs.TryGetValue(kvp.Key, out var poi))
                {
                    poi.LastResetTime = kvp.Value.LastResetTime;
                    
                    int lootIndex = 0;
                    foreach (var room in poi.Interior.Rooms)
                    {
                        foreach (var lootSpawn in room.LootSpawns)
                        {
                            if (lootIndex < kvp.Value.LootStates.Count)
                            {
                                var lootState = kvp.Value.LootStates[lootIndex];
                                lootSpawn.IsLooted = lootState.IsLooted;
                                lootSpawn.LastLootTime = lootState.LastLootTime;
                                lootIndex++;
                            }
                        }
                    }
                }
            }
        }
    }
}