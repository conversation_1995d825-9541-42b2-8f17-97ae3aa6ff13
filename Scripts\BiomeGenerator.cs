using Godot;
using System;
using System.Collections.Generic;
using System.Linq;

namespace SurvivalLooterShooter
{
    /// <summary>
    /// BiomeGenerator creates different biome types using noise-based terrain generation
    /// Handles biome placement, transitions, and biome-specific content generation with JSON-based configuration
    /// </summary>
    public partial class BiomeGenerator : Node
    {
        // Noise generators for different aspects
        private FastNoiseLite _biomeNoise;
        private FastNoiseLite _elevationNoise;
        private FastNoiseLite _temperatureNoise;
        private FastNoiseLite _humidityNoise;
        private FastNoiseLite _transitionNoise;

        // Generation parameters
        private int _worldSeed;
        private Dictionary<string, BiomeData> _biomeData = new Dictionary<string, BiomeData>();
        private Dictionary<BiomeType, string> _biomeTypeToId = new Dictionary<BiomeType, string>();

        // Biome transition parameters
        private const float TRANSITION_ZONE_SIZE = 0.15f; // Size of transition zones between biomes
        private const float RESOURCE_CLUSTER_SPREAD = 12.0f; // Maximum spread for resource clusters

        public override void _Ready()
        {
            LoadBiomeDataFromJson();
        }

        /// <summary>
        /// Initializes the biome generator with noise generators
        /// </summary>
        public void Initialize(int seed, FastNoiseLite biomeNoise, FastNoiseLite elevationNoise)
        {
            _worldSeed = seed;
            _biomeNoise = biomeNoise;
            _elevationNoise = elevationNoise;

            // Create additional noise generators
            _temperatureNoise = new FastNoiseLite();
            _temperatureNoise.Seed = seed + 2000;
            _temperatureNoise.NoiseType = FastNoiseLite.NoiseTypeEnum.Simplex;
            _temperatureNoise.Frequency = 0.005f;

            _humidityNoise = new FastNoiseLite();
            _humidityNoise.Seed = seed + 3000;
            _humidityNoise.NoiseType = FastNoiseLite.NoiseTypeEnum.Perlin;
            _humidityNoise.Frequency = 0.008f;

            _transitionNoise = new FastNoiseLite();
            _transitionNoise.Seed = seed + 4000;
            _transitionNoise.NoiseType = FastNoiseLite.NoiseTypeEnum.Cellular;
            _transitionNoise.Frequency = 0.02f;

            GD.Print("BiomeGenerator initialized with seed: " + seed);
        }

        /// <summary>
        /// Updates the seed for world regeneration
        /// </summary>
        public void UpdateSeed(int newSeed)
        {
            _worldSeed = newSeed;
            _biomeNoise.Seed = newSeed;
            _elevationNoise.Seed = newSeed + 1000;
            _temperatureNoise.Seed = newSeed + 2000;
            _humidityNoise.Seed = newSeed + 3000;
            _transitionNoise.Seed = newSeed + 4000;
        }

        /// <summary>
        /// Generates content for a world chunk
        /// </summary>
        public void GenerateChunk(WorldChunk chunk)
        {
            Vector2 chunkWorldPos = new Vector2(
                chunk.ChunkCoords.X * chunk.ChunkSize,
                chunk.ChunkCoords.Y * chunk.ChunkSize
            );

            // Generate biome map for the chunk with transitions
            GenerateBiomeMapWithTransitions(chunk, chunkWorldPos);
            
            // Generate resources based on biomes with clustering
            GenerateResourcesWithClustering(chunk, chunkWorldPos);
            
            // Add visual biome indicators
            GenerateBiomeVisualEffects(chunk, chunkWorldPos);
            
            // Set chunk as generated
            chunk.SetGenerated(true);
        }

        /// <summary>
        /// Generates the biome map for a chunk with smooth transitions
        /// </summary>
        private void GenerateBiomeMapWithTransitions(WorldChunk chunk, Vector2 chunkWorldPos)
        {
            for (int x = 0; x < chunk.ChunkSize; x++)
            {
                for (int y = 0; y < chunk.ChunkSize; y++)
                {
                    Vector2 worldPos = chunkWorldPos + new Vector2(x, y);
                    BiomeType biome = DetermineBiomeWithTransitions(worldPos);
                    chunk.SetBiomeAt(x, y, biome);
                }
            }
        }

        /// <summary>
        /// Determines the biome type at a specific world position with transition zones
        /// </summary>
        private BiomeType DetermineBiomeWithTransitions(Vector2 worldPosition)
        {
            float elevation = _elevationNoise.GetNoise2D(worldPosition.X, worldPosition.Y);
            float temperature = _temperatureNoise.GetNoise2D(worldPosition.X, worldPosition.Y);
            float humidity = _humidityNoise.GetNoise2D(worldPosition.X, worldPosition.Y);
            float transitionNoise = _transitionNoise.GetNoise2D(worldPosition.X, worldPosition.Y);

            // Get primary biome based on environmental factors
            BiomeType primaryBiome = GetPrimaryBiome(elevation, temperature, humidity);
            
            // Check for transition zones
            var nearbyBiomes = GetNearbyBiomes(worldPosition, primaryBiome);
            
            if (nearbyBiomes.Count > 1)
            {
                // We're in a transition zone, use noise to blend between biomes
                float blendFactor = (transitionNoise + 1.0f) * 0.5f; // Normalize to 0-1
                
                if (blendFactor < 0.3f)
                    return nearbyBiomes[0];
                else if (blendFactor > 0.7f && nearbyBiomes.Count > 1)
                    return nearbyBiomes[1];
            }
            
            return primaryBiome;
        }

        /// <summary>
        /// Gets the primary biome based on environmental factors
        /// </summary>
        private BiomeType GetPrimaryBiome(float elevation, float temperature, float humidity)
        {
            // Ocean areas (very low elevation)
            if (elevation < -0.4f)
                return BiomeType.Ocean;

            // Mountain areas (high elevation)
            if (elevation > 0.3f)
            {
                if (temperature < -0.3f)
                    return BiomeType.Tundra;
                else
                    return BiomeType.Mountains;
            }

            // Land biomes based on temperature and humidity
            if (temperature > 0.0f)
            {
                // Warm areas
                if (humidity > 0.0f)
                {
                    if (humidity > 0.3f && elevation < -0.1f)
                        return BiomeType.Swamp;
                    else
                        return BiomeType.Forest;
                }
                else
                {
                    return BiomeType.Desert;
                }
            }
            else
            {
                // Cold areas
                if (humidity > 0.0f)
                    return BiomeType.Forest;
                else
                    return BiomeType.Tundra;
            }
        }

        /// <summary>
        /// Gets nearby biomes for transition zone calculation
        /// </summary>
        private List<BiomeType> GetNearbyBiomes(Vector2 worldPosition, BiomeType primaryBiome)
        {
            var biomes = new List<BiomeType> { primaryBiome };
            
            // Sample nearby positions to find adjacent biomes
            Vector2[] sampleOffsets = {
                new Vector2(TRANSITION_ZONE_SIZE * 50, 0),
                new Vector2(-TRANSITION_ZONE_SIZE * 50, 0),
                new Vector2(0, TRANSITION_ZONE_SIZE * 50),
                new Vector2(0, -TRANSITION_ZONE_SIZE * 50)
            };

            foreach (var offset in sampleOffsets)
            {
                Vector2 samplePos = worldPosition + offset;
                float elevation = _elevationNoise.GetNoise2D(samplePos.X, samplePos.Y);
                float temperature = _temperatureNoise.GetNoise2D(samplePos.X, samplePos.Y);
                float humidity = _humidityNoise.GetNoise2D(samplePos.X, samplePos.Y);
                
                BiomeType nearbyBiome = GetPrimaryBiome(elevation, temperature, humidity);
                if (nearbyBiome != primaryBiome && !biomes.Contains(nearbyBiome))
                {
                    biomes.Add(nearbyBiome);
                    if (biomes.Count >= 2) break; // Limit to 2 biomes for blending
                }
            }
            
            return biomes;
        }

        /// <summary>
        /// Gets the biome type at a specific world position
        /// </summary>
        public BiomeType GetBiomeAt(Vector2 worldPosition)
        {
            return DetermineBiomeWithTransitions(worldPosition);
        }

        /// <summary>
        /// Generates resources within a chunk based on biome types with advanced clustering
        /// </summary>
        private void GenerateResourcesWithClustering(WorldChunk chunk, Vector2 chunkWorldPos)
        {
            var random = new Random(_worldSeed + chunk.ChunkCoords.GetHashCode());

            for (int x = 0; x < chunk.ChunkSize; x += 8) // Sample every 8 units for performance
            {
                for (int y = 0; y < chunk.ChunkSize; y += 8)
                {
                    Vector2 worldPos = chunkWorldPos + new Vector2(x, y);
                    BiomeType biome = chunk.GetBiomeAt(x, y);
                    string biomeId = GetBiomeId(biome);

                    if (_biomeData.TryGetValue(biomeId, out BiomeData biomeInfo))
                    {
                        GenerateResourceClustersForBiome(chunk, worldPos, biomeInfo, random);
                    }
                }
            }
        }

        /// <summary>
        /// Generates resource clusters for a specific biome at a location
        /// </summary>
        private void GenerateResourceClustersForBiome(WorldChunk chunk, Vector2 worldPos, BiomeData biomeData, Random random)
        {
            float elevation = _elevationNoise.GetNoise2D(worldPos.X, worldPos.Y);
            
            foreach (var resourceSpawn in biomeData.ResourceSpawns)
            {
                // Check if elevation is within spawn range for this resource
                if (elevation < resourceSpawn.SpawnHeight[0] || elevation > resourceSpawn.SpawnHeight[1])
                    continue;

                if (random.NextDouble() < resourceSpawn.Density)
                {
                    // Create resource cluster with improved distribution
                    int clusterSize = random.Next(resourceSpawn.ClusterSize[0], resourceSpawn.ClusterSize[1] + 1);
                    Vector2 clusterCenter = worldPos + new Vector2(
                        (float)(random.NextDouble() - 0.5) * RESOURCE_CLUSTER_SPREAD,
                        (float)(random.NextDouble() - 0.5) * RESOURCE_CLUSTER_SPREAD
                    );
                    
                    for (int i = 0; i < clusterSize; i++)
                    {
                        // Use Gaussian distribution for more natural clustering
                        float angle = (float)(random.NextDouble() * Math.PI * 2);
                        float distance = (float)(random.NextGaussian() * 3.0 + 1.0); // Gaussian with mean=1, std=3
                        distance = Math.Max(0.5f, Math.Min(distance, 8.0f)); // Clamp distance
                        
                        Vector2 resourcePos = clusterCenter + new Vector2(
                            Mathf.Cos(angle) * distance,
                            Mathf.Sin(angle) * distance
                        );

                        // Ensure resource is within chunk bounds
                        if (chunk.Bounds.HasPoint(resourcePos))
                        {
                            chunk.AddResource(resourceSpawn.Item, resourcePos);
                        }
                    }
                }
            }
        }

        /// <summary>
        /// Generates visual effects and environmental indicators for biomes
        /// </summary>
        private void GenerateBiomeVisualEffects(WorldChunk chunk, Vector2 chunkWorldPos)
        {
            // Sample biome at chunk center for dominant visual effects
            Vector2 centerPos = chunkWorldPos + new Vector2(chunk.ChunkSize * 0.5f, chunk.ChunkSize * 0.5f);
            BiomeType dominantBiome = chunk.GetDominantBiome();
            string biomeId = GetBiomeId(dominantBiome);
            
            if (_biomeData.TryGetValue(biomeId, out BiomeData biomeData))
            {
                // Create environmental effects based on biome properties
                CreateBiomeEnvironmentalEffects(chunk, biomeData);
            }
        }

        /// <summary>
        /// Creates environmental effects for a biome
        /// </summary>
        private void CreateBiomeEnvironmentalEffects(WorldChunk chunk, BiomeData biomeData)
        {
            // This would create particle effects, ambient sounds, etc.
            // For now, we'll just log the biome's visual properties
            GD.Print($"Chunk {chunk.ChunkCoords} has biome effects: {biomeData.VisualProperties.ParticleEffect}");
            
            // In a full implementation, this would:
            // - Create particle systems for environmental effects
            // - Set ambient lighting based on biome colors
            // - Add weather-specific visual elements
            // - Create biome-specific ambient sounds
        }

        /// <summary>
        /// Loads biome data from JSON configuration file
        /// </summary>
        private void LoadBiomeDataFromJson()
        {
            try
            {
                string jsonPath = "res://Data/Biomes.json";
                if (!FileAccess.FileExists(jsonPath))
                {
                    GD.PrintErr("Biomes.json not found at: " + jsonPath);
                    InitializeFallbackBiomeData();
                    return;
                }

                using var file = FileAccess.Open(jsonPath, FileAccess.ModeFlags.Read);
                string jsonContent = file.GetAsText();
                
                var jsonData = Json.ParseString(jsonContent).AsGodotDictionary();
                var biomesArray = jsonData["biomes"].AsGodotArray();

                foreach (var biomeVariant in biomesArray)
                {
                    var biomeDict = biomeVariant.AsGodotDictionary();
                    var biomeData = ParseBiomeFromJson(biomeDict);
                    
                    _biomeData[biomeData.Id] = biomeData;
                    
                    // Map BiomeType enum to string ID
                    if (Enum.TryParse<BiomeType>(biomeData.Id, true, out BiomeType biomeType))
                    {
                        _biomeTypeToId[biomeType] = biomeData.Id;
                    }
                }

                GD.Print($"Loaded {_biomeData.Count} biome types from JSON");
            }
            catch (Exception ex)
            {
                GD.PrintErr($"Error loading biome data: {ex.Message}");
                InitializeFallbackBiomeData();
            }
        }

        /// <summary>
        /// Parses a single biome from JSON dictionary
        /// </summary>
        private BiomeData ParseBiomeFromJson(Godot.Collections.Dictionary biomeDict)
        {
            var biomeData = new BiomeData
            {
                Id = biomeDict["id"].AsString(),
                Name = biomeDict["name"].AsString(),
                Humidity = biomeDict["humidity"].AsSingle()
            };

            // Parse temperature range
            var tempRange = biomeDict["temperature_range"].AsGodotArray();
            biomeData.TemperatureRange = new Vector2(tempRange[0].AsSingle(), tempRange[1].AsSingle());

            // Parse elevation range
            var elevRange = biomeDict["elevation_range"].AsGodotArray();
            biomeData.ElevationRange = new Vector2(elevRange[0].AsSingle(), elevRange[1].AsSingle());

            // Parse resource spawns
            var resourceSpawns = biomeDict["resource_spawns"].AsGodotArray();
            foreach (var resourceVariant in resourceSpawns)
            {
                var resourceDict = resourceVariant.AsGodotDictionary();
                var clusterSize = resourceDict["cluster_size"].AsGodotArray();
                var spawnHeight = resourceDict["spawn_height"].AsGodotArray();
                
                biomeData.ResourceSpawns.Add(new ResourceSpawn
                {
                    Item = resourceDict["item"].AsString(),
                    Density = resourceDict["density"].AsSingle(),
                    ClusterSize = new int[] { clusterSize[0].AsInt32(), clusterSize[1].AsInt32() },
                    SpawnHeight = new float[] { spawnHeight[0].AsSingle(), spawnHeight[1].AsSingle() }
                });
            }

            // Parse enemy spawns
            var enemySpawns = biomeDict["enemy_spawns"].AsGodotArray();
            foreach (var enemyVariant in enemySpawns)
            {
                var enemyDict = enemyVariant.AsGodotDictionary();
                biomeData.EnemySpawns.Add(new EnemySpawn
                {
                    Enemy = enemyDict["enemy"].AsString(),
                    SpawnRate = enemyDict["spawn_rate"].AsSingle(),
                    MaxCount = enemyDict["max_count"].AsInt32()
                });
            }

            // Parse weather modifiers
            var weatherDict = biomeDict["weather_modifiers"].AsGodotDictionary();
            biomeData.WeatherModifiers = new WeatherModifiers
            {
                RainFrequency = weatherDict["rain_frequency"].AsSingle(),
                TemperatureVariance = weatherDict["temperature_variance"].AsSingle(),
                WindStrength = weatherDict["wind_strength"].AsSingle()
            };

            // Parse visual properties
            var visualDict = biomeDict["visual_properties"].AsGodotDictionary();
            biomeData.VisualProperties = new VisualProperties
            {
                GroundColor = visualDict["ground_color"].AsString(),
                AccentColor = visualDict["accent_color"].AsString(),
                ParticleEffect = visualDict["particle_effect"].AsString()
            };

            return biomeData;
        }

        /// <summary>
        /// Initializes fallback biome data if JSON loading fails
        /// </summary>
        private void InitializeFallbackBiomeData()
        {
            GD.Print("Initializing fallback biome data");
            
            _biomeData["plains"] = new BiomeData
            {
                Id = "plains",
                Name = "Plains",
                TemperatureRange = new Vector2(15, 25),
                Humidity = 0.5f,
                ElevationRange = new Vector2(-0.2f, 0.2f),
                ResourceSpawns = new List<ResourceSpawn>
                {
                    new ResourceSpawn { Item = "grass", Density = 0.8f, ClusterSize = new int[] { 2, 5 }, SpawnHeight = new float[] { 0.0f, 0.1f } },
                    new ResourceSpawn { Item = "stone", Density = 0.2f, ClusterSize = new int[] { 1, 3 }, SpawnHeight = new float[] { 0.0f, 0.3f } }
                }
            };

            _biomeTypeToId[BiomeType.Plains] = "plains";
            
            GD.Print("Fallback biome data initialized");
        }

        /// <summary>
        /// Gets biome data for a specific biome type
        /// </summary>
        public BiomeData GetBiomeData(BiomeType biomeType)
        {
            string biomeId = GetBiomeId(biomeType);
            _biomeData.TryGetValue(biomeId, out BiomeData data);
            return data;
        }

        /// <summary>
        /// Gets biome data by string ID
        /// </summary>
        public BiomeData GetBiomeData(string biomeId)
        {
            _biomeData.TryGetValue(biomeId, out BiomeData data);
            return data;
        }

        /// <summary>
        /// Gets the string ID for a biome type
        /// </summary>
        private string GetBiomeId(BiomeType biomeType)
        {
            return _biomeTypeToId.TryGetValue(biomeType, out string id) ? id : biomeType.ToString().ToLower();
        }

        /// <summary>
        /// Gets all available biome types
        /// </summary>
        public BiomeType[] GetAllBiomeTypes()
        {
            return (BiomeType[])Enum.GetValues(typeof(BiomeType));
        }

        /// <summary>
        /// Gets biome transition factor between two positions
        /// </summary>
        public float GetBiomeTransitionFactor(Vector2 position1, Vector2 position2)
        {
            BiomeType biome1 = DetermineBiomeWithTransitions(position1);
            BiomeType biome2 = DetermineBiomeWithTransitions(position2);
            
            if (biome1 == biome2)
                return 1.0f;
                
            float distance = position1.DistanceTo(position2);
            float transitionDistance = TRANSITION_ZONE_SIZE * 100.0f; // Scale transition zone
            
            return Mathf.Clamp(1.0f - (distance / transitionDistance), 0.0f, 1.0f);
        }

        /// <summary>
        /// Gets weather modifiers for a biome at a specific position
        /// </summary>
        public WeatherModifiers GetWeatherModifiers(Vector2 worldPosition)
        {
            BiomeType biome = DetermineBiomeWithTransitions(worldPosition);
            string biomeId = GetBiomeId(biome);
            
            if (_biomeData.TryGetValue(biomeId, out BiomeData biomeData))
            {
                return biomeData.WeatherModifiers;
            }
            
            return new WeatherModifiers(); // Default weather
        }
    }

    /// <summary>
    /// Enhanced data structure defining biome characteristics and resource spawns
    /// </summary>
    [System.Serializable]
    public class BiomeData
    {
        public string Id { get; set; }
        public string Name { get; set; }
        public Vector2 TemperatureRange { get; set; }
        public float Humidity { get; set; }
        public Vector2 ElevationRange { get; set; }
        public List<ResourceSpawn> ResourceSpawns { get; set; } = new List<ResourceSpawn>();
        public List<EnemySpawn> EnemySpawns { get; set; } = new List<EnemySpawn>();
        public WeatherModifiers WeatherModifiers { get; set; } = new WeatherModifiers();
        public VisualProperties VisualProperties { get; set; } = new VisualProperties();
    }

    /// <summary>
    /// Enhanced resource spawn definition with clustering and height constraints
    /// </summary>
    [System.Serializable]
    public class ResourceSpawn
    {
        public string Item { get; set; }
        public float Density { get; set; } // Probability of spawning (0.0 to 1.0)
        public int[] ClusterSize { get; set; } = new int[] { 1, 1 }; // [min, max]
        public float[] SpawnHeight { get; set; } = new float[] { 0.0f, 1.0f }; // [min, max] elevation range
    }

    /// <summary>
    /// Defines enemy spawn parameters for biomes
    /// </summary>
    [System.Serializable]
    public class EnemySpawn
    {
        public string Enemy { get; set; }
        public float SpawnRate { get; set; }
        public int MaxCount { get; set; }
    }

    /// <summary>
    /// Weather modifiers specific to each biome
    /// </summary>
    [System.Serializable]
    public class WeatherModifiers
    {
        public float RainFrequency { get; set; } = 0.2f;
        public float TemperatureVariance { get; set; } = 5.0f;
        public float WindStrength { get; set; } = 0.3f;
    }

    /// <summary>
    /// Visual properties for biome rendering and effects
    /// </summary>
    [System.Serializable]
    public class VisualProperties
    {
        public string GroundColor { get; set; } = "#7CB342";
        public string AccentColor { get; set; } = "#8BC34A";
        public string ParticleEffect { get; set; } = "none";
    }
}
   
 /// <summary>
    /// Extension methods for Random class
    /// </summary>
    public static class RandomExtensions
    {
        /// <summary>
        /// Generates a random number from a Gaussian (normal) distribution
        /// </summary>
        public static double NextGaussian(this Random random, double mean = 0.0, double stdDev = 1.0)
        {
            // Box-Muller transform
            double u1 = 1.0 - random.NextDouble(); // Uniform(0,1] random doubles
            double u2 = 1.0 - random.NextDouble();
            double randStdNormal = Math.Sqrt(-2.0 * Math.Log(u1)) * Math.Sin(2.0 * Math.PI * u2);
            return mean + stdDev * randStdNormal;
        }
    }