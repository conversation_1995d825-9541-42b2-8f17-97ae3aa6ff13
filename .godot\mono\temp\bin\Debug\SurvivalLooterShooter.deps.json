{"runtimeTarget": {"name": ".NETCoreApp,Version=v8.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v8.0": {"SurvivalLooterShooter/1.0.0": {"dependencies": {"Godot.SourceGenerators": "4.4.1", "GodotSharp": "4.4.1", "GodotSharpEditor": "4.4.1"}, "runtime": {"SurvivalLooterShooter.dll": {}}}, "Godot.SourceGenerators/4.4.1": {}, "GodotSharp/4.4.1": {"runtime": {"lib/net8.0/GodotSharp.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "GodotSharpEditor/4.4.1": {"dependencies": {"GodotSharp": "4.4.1"}, "runtime": {"lib/net8.0/GodotSharpEditor.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}}}, "libraries": {"SurvivalLooterShooter/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Godot.SourceGenerators/4.4.1": {"type": "package", "serviceable": true, "sha512": "sha512-V/cuX41BxippWGD79zrP2bhqfXkuyiy9OFuCkTu3flo7I6STSJca637TL2phe7rzROIFre0vQR1+PAMdsjO3zg==", "path": "godot.sourcegenerators/4.4.1", "hashPath": "godot.sourcegenerators.4.4.1.nupkg.sha512"}, "GodotSharp/4.4.1": {"type": "package", "serviceable": true, "sha512": "sha512-ghnQEo5LikQPfbCYcVxje8epffNCiyNG4zvGWUDRZRC1O+653+yqG3wdxk3+5RZsA3jaRuGKRavsGcnhLKe12g==", "path": "godotsharp/4.4.1", "hashPath": "godotsharp.4.4.1.nupkg.sha512"}, "GodotSharpEditor/4.4.1": {"type": "package", "serviceable": true, "sha512": "sha512-cc3nA24r/sjW8KxrNqfSQk4CQMQYcg/kYk5935R88IhGBdRuTH4miaBXPlzR/td5GR8i1oGtCMWyW8axpQ0DxA==", "path": "godotsharpeditor/4.4.1", "hashPath": "godotsharpeditor.4.4.1.nupkg.sha512"}}}