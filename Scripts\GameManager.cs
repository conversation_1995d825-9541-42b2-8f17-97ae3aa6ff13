using Godot;
using <PERSON><PERSON><PERSON>erShooter;
using System;

public partial class GameManager : Node
{
    public static GameManager Instance { get; private set; }

    private float _currentPlayTime = 0f;
    private PlayerController _playerController;

    public override void _Ready()
    {
        if (Instance == null)
        {
            Instance = this;
            InitializeGame();
        }
        else
        {
            QueueFree();
        }
    }

    private void InitializeGame()
    {
        // Find the player in the scene
        _playerController = GetTree().CurrentScene.GetNode<PlayerController>("Player");

        if (_playerController != null)
        {
            GD.Print("GameManager: Player found in scene successfully");
        }
        else
        {
            GD.PrintErr("GameManager: Failed to find Player in scene");
        }
    }

    public override void _Process(double delta)
    {
        _currentPlayTime += (float)delta;
    }

    public float GetCurrentPlayTime()
    {
        return _currentPlayTime;
    }

    internal Inventory GetInventory()
    {
        return Inventory.Instance;
    }

    internal ResourceHarvestingSystem GetResourceHarvestingSystem()
    {
        return ResourceHarvestingSystem.Instance;
    }

    internal PlayerController GetPlayerController()
    {
        return _playerController;
    }

    internal EnemyManager GetEnemyManager()
    {
        return EnemyManager.Instance;
    }
}