using Godot;
using <PERSON><PERSON><PERSON>erShooter;
using System;

public partial class GameManager : Node
{
    public static GameManager Instance { get; private set; }

    private float _currentPlayTime = 0f;
    private PlayerController _playerController;
    private PackedScene _playerScene;

    public override void _Ready()
    {
        if (Instance == null)
        {
            Instance = this;
            InitializeGame();
        }
        else
        {
            QueueFree();
        }
    }

    private void InitializeGame()
    {
        // Load player scene
        _playerScene = GD.Load<PackedScene>("res://Scenes/Player.tscn");

        if (_playerScene != null)
        {
            // Instantiate player
            _playerController = _playerScene.Instantiate<PlayerController>();

            // Add player to the scene
            GetTree().CurrentScene.AddChild(_playerController);

            // Set initial player position (center of screen)
            _playerController.GlobalPosition = new Vector2(960, 540);

            GD.Print("GameManager: Player instantiated successfully");
        }
        else
        {
            GD.PrintErr("GameManager: Failed to load Player scene");
        }
    }

    public override void _Process(double delta)
    {
        _currentPlayTime += (float)delta;
    }

    public float GetCurrentPlayTime()
    {
        return _currentPlayTime;
    }

    internal Inventory GetInventory()
    {
        return Inventory.Instance;
    }

    internal ResourceHarvestingSystem GetResourceHarvestingSystem()
    {
        return ResourceHarvestingSystem.Instance;
    }

    internal PlayerController GetPlayerController()
    {
        return _playerController;
    }

    internal EnemyManager GetEnemyManager()
    {
        return EnemyManager.Instance;
    }
}