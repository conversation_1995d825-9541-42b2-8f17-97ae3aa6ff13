using Godot;
using Survival<PERSON>ooterShooter;
using System;

public partial class GameManager : Node
{
    public static GameManager Instance { get; private set; }
    
    private float _currentPlayTime = 0f;

    public override void _Ready()
    {
        if (Instance == null)
        {
            Instance = this;
        }
        else
        {
            QueueFree();
        }
    }

    public override void _Process(double delta)
    {
        _currentPlayTime += (float)delta;
    }

    public float GetCurrentPlayTime()
    {
        return _currentPlayTime;
    }

    internal Inventory GetInventory()
    {
        throw new NotImplementedException();
    }

    internal ResourceHarvestingSystem GetResourceHarvestingSystem()
    {
        throw new NotImplementedException();
    }

    internal PlayerController GetPlayerController()
    {
        throw new NotImplementedException();
    }

    internal EnemyManager GetEnemyManager()
    {
        throw new NotImplementedException();
    }
}