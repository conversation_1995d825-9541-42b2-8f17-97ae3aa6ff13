using Godot;
using System;
using System.Collections.Generic;

namespace SurvivalLooterShooter
{
    public enum BossType
    {
        <PERSON><PERSON><PERSON><PERSON>,
        <PERSON><PERSON><PERSON><PERSON>,
        <PERSON><PERSON><PERSON><PERSON><PERSON>,
        <PERSON><PERSON><PERSON><PERSON><PERSON>,
        AncientGuardian
    }

    public partial class BossEnemy : Enemy
    {
        [Export] public BossType BossType { get; set; }
        [Export] public float SpawnRadius { get; set; } = 500f;
        [Export] public int MinPlayerLevel { get; set; } = 10;
        public List<string> SpecialAbilities { get; set; } = new();
        [Export] public float RageThreshold { get; set; } = 0.3f; // Health percentage to enter rage mode
        [Export] public bool IsRaging { get; set; } = false;
        [Export] public float RageMultiplier { get; set; } = 1.5f;

        private Timer _abilityTimer;
        private Random _random = new();
        private bool _hasSpawnedMinions = false;

        public override void _Ready()
        {
            base._Ready();
            SetupBossStats();
            SetupAbilityTimer();

            // Boss enemies are significantly stronger
            MaxHealth *= 5;
            CurrentHealth = MaxHealth;
            Damage *= 2;
            ExperienceReward *= 10;
        }

        private void SetupBossStats()
        {
            switch (BossType)
            {
                case BossType.AlphaWolf:
                    EnemyType = "alpha_wolf";
                    Speed = 150f;
                    DetectionRange = 300f;
                    AttackRange = 80f;
                    SpecialAbilities = new() { "howl", "pack_summon", "leap_attack" };
                    break;

                case BossType.GiantSpider:
                    EnemyType = "giant_spider";
                    Speed = 100f;
                    DetectionRange = 250f;
                    AttackRange = 120f;
                    SpecialAbilities = new() { "web_trap", "poison_spit", "spawn_eggs" };
                    break;

                case BossType.BanditLeader:
                    EnemyType = "bandit_leader";
                    Speed = 120f;
                    DetectionRange = 400f;
                    AttackRange = 200f;
                    SpecialAbilities = new() { "rally_cry", "explosive_shot", "smoke_bomb" };
                    break;

                case BossType.MutantBeast:
                    EnemyType = "mutant_beast";
                    Speed = 80f;
                    DetectionRange = 200f;
                    AttackRange = 100f;
                    SpecialAbilities = new() { "acid_spray", "regeneration", "berserker_rage" };
                    break;

                case BossType.AncientGuardian:
                    EnemyType = "ancient_guardian";
                    Speed = 60f;
                    DetectionRange = 350f;
                    AttackRange = 150f;
                    SpecialAbilities = new() { "stone_throw", "earthquake", "shield_bash" };
                    break;
            }
        }

        private void SetupAbilityTimer()
        {
            _abilityTimer = new Timer();
            _abilityTimer.WaitTime = 5f; // Use special ability every 5 seconds
            _abilityTimer.Timeout += UseSpecialAbility;
            _abilityTimer.Autostart = true;
            AddChild(_abilityTimer);
        }

        public override void TakeDamage(float damage)
        {
            base.TakeDamage(damage);

            // Check for rage mode
            if (!IsRaging && GetHealthPercentage() <= RageThreshold)
            {
                EnterRageMode();
            }

            // Spawn minions at half health
            if (!_hasSpawnedMinions && GetHealthPercentage() <= 0.5f)
            {
                SpawnMinions();
                _hasSpawnedMinions = true;
            }
        }

        private void EnterRageMode()
        {
            IsRaging = true;
            Damage *= RageMultiplier;
            Speed *= RageMultiplier;
            _abilityTimer.WaitTime /= RageMultiplier; // Use abilities more frequently

            GD.Print($"{EnemyType} has entered rage mode!");
            EventBus.Instance?.EmitSignal(EventBus.SignalName.BossEnterRage, this);
        }

        private void UseSpecialAbility()
        {
            if (SpecialAbilities.Count == 0 || CurrentState != AIState.Attacking) return;

            var ability = SpecialAbilities[_random.Next(SpecialAbilities.Count)];
            ExecuteSpecialAbility(ability);
        }

        private void ExecuteSpecialAbility(string ability)
        {
            switch (ability)
            {
                case "howl":
                    ExecuteHowl();
                    break;
                case "pack_summon":
                    ExecutePackSummon();
                    break;
                case "leap_attack":
                    ExecuteLeapAttack();
                    break;
                case "web_trap":
                    ExecuteWebTrap();
                    break;
                case "poison_spit":
                    ExecutePoisonSpit();
                    break;
                case "spawn_eggs":
                    ExecuteSpawnEggs();
                    break;
                case "rally_cry":
                    ExecuteRallyCry();
                    break;
                case "explosive_shot":
                    ExecuteExplosiveShot();
                    break;
                case "smoke_bomb":
                    ExecuteSmokeBomb();
                    break;
                case "acid_spray":
                    ExecuteAcidSpray();
                    break;
                case "regeneration":
                    ExecuteRegeneration();
                    break;
                case "berserker_rage":
                    ExecuteBerserkerRage();
                    break;
                case "stone_throw":
                    ExecuteStoneThrow();
                    break;
                case "earthquake":
                    ExecuteEarthquake();
                    break;
                case "shield_bash":
                    ExecuteShieldBash();
                    break;
            }

            GD.Print($"{EnemyType} used {ability}!");
        }

        // Special ability implementations
        private void ExecuteHowl()
        {
            // Buff nearby enemies and intimidate player
            var nearbyEnemies = GetNearbyEnemies(200f);
            foreach (var enemy in nearbyEnemies)
            {
                enemy.Damage *= 1.2f;
                enemy.Speed *= 1.2f;
            }
        }

        private void ExecutePackSummon()
        {
            // Spawn wolf minions
            for (int i = 0; i < 3; i++)
            {
                var spawnPos = GlobalPosition + new Vector2(_random.Next(-100, 100), _random.Next(-100, 100));
                EnemyManager.Instance?.SpawnEnemy("forest_wolf", spawnPos);
            }
        }

        private void ExecuteLeapAttack()
        {
            // Leap towards player and deal extra damage
            if (Target != null)
            {
                var direction = (((Node2D)Target).GlobalPosition - GlobalPosition).Normalized();
                GlobalPosition += direction * 200f;
                // Deal double damage on next attack
                Damage *= 2f;
            }
        }

        private void ExecuteWebTrap()
        {
            // Create web trap at player location
            if (Target != null)
            {
                // This would create a web trap area effect
                GD.Print("Web trap created at player location");
            }
        }

        private void ExecutePoisonSpit()
        {
            // Ranged poison attack
            if (Target != null)
            {
                var direction = (((Node2D)Target).GlobalPosition - GlobalPosition).Normalized();
                // Create poison projectile
                GD.Print("Poison spit launched");
            }
        }

        private void ExecuteSpawnEggs()
        {
            // Spawn spider eggs that hatch into small spiders
            for (int i = 0; i < 2; i++)
            {
                var spawnPos = GlobalPosition + new Vector2(_random.Next(-150, 150), _random.Next(-150, 150));
                // Spawn spider eggs (would be implemented as timed spawners)
                GD.Print($"Spider egg spawned at {spawnPos}");
            }
        }

        private void ExecuteRallyCry()
        {
            // Boost all nearby bandits
            var nearbyEnemies = GetNearbyEnemies(300f);
            foreach (var enemy in nearbyEnemies)
            {
                if (enemy.EnemyType.Contains("bandit"))
                {
                    enemy.MaxHealth *= 1.5f;
                    enemy.CurrentHealth = enemy.MaxHealth;
                }
            }
        }

        private void ExecuteExplosiveShot()
        {
            // Fire explosive projectile
            if (Target != null)
            {
                var direction = (((Node2D)Target).GlobalPosition - GlobalPosition).Normalized();
                // Create explosive projectile with area damage
                GD.Print("Explosive shot fired");
            }
        }

        private void ExecuteSmokeBomb()
        {
            // Create smoke cloud that reduces visibility
            GD.Print("Smoke bomb deployed - visibility reduced");
        }

        private void ExecuteAcidSpray()
        {
            // Area acid attack
            var nearbyTargets = GetNearbyPlayers(150f);
            foreach (var target in nearbyTargets)
            {
                // Apply acid damage over time
                GD.Print("Acid spray applied to nearby targets");
            }
        }

        private void ExecuteRegeneration()
        {
            // Heal over time
            var healAmount = MaxHealth * 0.1f;
            CurrentHealth = Math.Min(CurrentHealth + healAmount, MaxHealth);
            GD.Print($"Boss regenerated {healAmount} health");
        }

        private void ExecuteBerserkerRage()
        {
            // Temporary massive damage boost
            Damage *= 3f;

            // Reset after 10 seconds
            GetTree().CreateTimer(10f).Timeout += () => Damage /= 3f;
        }

        private void ExecuteStoneThrow()
        {
            // Throw large stone projectile
            if (Target != null)
            {
                var direction = (((Node2D)Target).GlobalPosition - GlobalPosition).Normalized();
                // Create stone projectile with knockback
                GD.Print("Large stone thrown");
            }
        }

        private void ExecuteEarthquake()
        {
            // Area ground slam
            var nearbyTargets = GetNearbyPlayers(200f);
            foreach (var target in nearbyTargets)
            {
                // Apply damage and stun
                GD.Print("Earthquake damages nearby targets");
            }
        }

        private void ExecuteShieldBash()
        {
            // Charge attack with knockback
            if (Target != null)
            {
                var direction = (((Node2D)Target).GlobalPosition - GlobalPosition).Normalized();
                GlobalPosition += direction * 100f;
                // Apply knockback and stun to target
                GD.Print("Shield bash executed");
            }
        }

        private void SpawnMinions()
        {
            var minionType = BossType switch
            {
                BossType.AlphaWolf => "forest_wolf",
                BossType.GiantSpider => "small_spider",
                BossType.BanditLeader => "bandit",
                BossType.MutantBeast => "mutant_spawn",
                BossType.AncientGuardian => "stone_golem",
                _ => "forest_wolf"
            };

            for (int i = 0; i < 4; i++)
            {
                var spawnPos = GlobalPosition + new Vector2(_random.Next(-200, 200), _random.Next(-200, 200));
                EnemyManager.Instance?.SpawnEnemy(minionType, spawnPos);
            }

            GD.Print($"{EnemyType} spawned minions!");
        }

        private List<Enemy> GetNearbyEnemies(float radius)
        {
            var enemies = new List<Enemy>();
            var bodies = GetTree().GetNodesInGroup("enemies");

            foreach (Node body in bodies)
            {
                if (body is Enemy enemy && enemy != this)
                {
                    if (GlobalPosition.DistanceTo(enemy.GlobalPosition) <= radius)
                    {
                        enemies.Add(enemy);
                    }
                }
            }

            return enemies;
        }

        private List<Node> GetNearbyPlayers(float radius)
        {
            var players = new List<Node>();
            var bodies = GetTree().GetNodesInGroup("players");

            foreach (Node body in bodies)
            {
                if (GlobalPosition.DistanceTo(((Node2D)body).GlobalPosition) <= radius)
                {
                    players.Add(body);
                }
            }

            return players;
        }

        public override void Die()
        {
            // Boss death triggers special events
            EventBus.Instance?.EmitSignal(EventBus.SignalName.BossDefeated, this);

            // Drop rare loot
            DropBossLoot();

            base.Die();
        }

        private void DropBossLoot()
        {
            var rareLoot = BossType switch
            {
                BossType.AlphaWolf => new[] { "alpha_pelt", "wolf_fang", "pack_leader_essence" },
                BossType.GiantSpider => new[] { "spider_silk", "venom_sac", "chitin_armor" },
                BossType.BanditLeader => new[] { "leader_weapon", "tactical_gear", "bandit_treasure" },
                BossType.MutantBeast => new[] { "mutant_core", "toxic_gland", "beast_hide" },
                BossType.AncientGuardian => new[] { "ancient_stone", "guardian_core", "relic_fragment" },
                _ => new[] { "rare_material" }
            };

            foreach (var lootItem in rareLoot)
            {
                LootDropSystem.Instance?.DropItem(lootItem, GlobalPosition, 1.0f); // 100% drop chance for boss loot
            }
        }
    }
}