using Godot;
using System;
using System.Collections.Generic;

namespace SurvivalLooterShooter
{
    /// <summary>
    /// Manages dynamic weather patterns and environmental effects
    /// Provides weather transitions, seasonal changes, and gameplay modifiers
    /// </summary>
    public partial class WeatherManager : Node
    {
        private static WeatherManager _instance;
        public static WeatherManager Instance => _instance;

        // Weather types
        public enum WeatherType
        {
            Clear,
            Cloudy,
            LightRain,
            HeavyRain,
            Thunderstorm,
            Snow,
            Blizzard,
            Fog,
            Sandstorm,
            Hail,
            Storm
        }

        // Season types
        public enum Season
        {
            Spring,
            Summer,
            Autumn,
            Winter
        }

        // Current weather state
        [Export] public WeatherType CurrentWeather { get; private set; } = WeatherType.Clear;
        [Export] public float WeatherIntensity { get; private set; } = 0.0f;
        [Export] public Season CurrentSeason { get; private set; } = Season.Spring;
        
        // Weather transition
        private WeatherType _targetWeather = WeatherType.Clear;
        private float _transitionProgress = 1.0f;
        private float _transitionDuration = 30.0f; // 30 seconds for weather transitions
        
        // Weather timing
        [Export] public float MinWeatherDuration { get; set; } = 300.0f; // 5 minutes minimum
        [Export] public float MaxWeatherDuration { get; set; } = 1200.0f; // 20 minutes maximum
        private float _currentWeatherTimer = 0.0f;
        private float _currentWeatherDuration = 600.0f; // 10 minutes default
        
        // Season timing
        [Export] public float SeasonLengthDays { get; set; } = 7.0f; // 7 in-game days per season
        private float _seasonTimer = 0.0f;
        
        // Weather prediction
        private Queue<WeatherType> _weatherForecast = new Queue<WeatherType>();
        private const int ForecastDays = 3;
        
        // Weather effects
        private Dictionary<WeatherType, WeatherEffectData> _weatherEffects;
        
        // Biome influence
        private string _currentBiome = "plains";
        private Dictionary<string, BiomeWeatherData> _biomeWeatherData;
        
        // Events
        [Signal] public delegate void WeatherChangedEventHandler(WeatherType oldWeather, WeatherType newWeather, float intensity);
        [Signal] public delegate void SeasonChangedEventHandler(Season oldSeason, Season newSeason);
        [Signal] public delegate void WeatherTransitionStartedEventHandler(WeatherType fromWeather, WeatherType toWeather, float duration);
        [Signal] public delegate void WeatherTransitionCompletedEventHandler(WeatherType weather, float intensity);
        [Signal] public delegate void WeatherForecastUpdatedEventHandler();

        public override void _Ready()
        {
            // Singleton setup
            if (_instance == null)
            {
                _instance = this;
                AddToGroup("weather_manager");
                Logger.LogInfo("WeatherManager", "WeatherManager singleton initialized");
            }
            else
            {
                Logger.LogError("WeatherManager", "Multiple WeatherManager instances detected! Removing duplicate.");
                QueueFree();
                return;
            }

            InitializeWeatherEffects();
            InitializeBiomeWeatherData();
            GenerateInitialForecast();
            
            // Connect to day/night cycle for seasonal progression
            if (DayNightCycle.Instance != null)
            {
                DayNightCycle.Instance.DayStarted += OnNewDay;
            }

            // Set initial weather duration
            _currentWeatherDuration = (float)GD.RandRange(MinWeatherDuration, MaxWeatherDuration);
            
            Logger.LogInfo("WeatherManager", $"Weather system initialized - Current: {CurrentWeather}, Season: {CurrentSeason}");
        }

        public override void _Process(double delta)
        {
            float deltaTime = (float)delta;
            UpdateWeatherTransition(deltaTime);
            UpdateWeatherTimer(deltaTime);
            UpdateSeasonTimer(deltaTime);
        }

        /// <summary>
        /// Initializes weather effect data for each weather type
        /// </summary>
        private void InitializeWeatherEffects()
        {
            _weatherEffects = new Dictionary<WeatherType, WeatherEffectData>
            {
                [WeatherType.Clear] = new WeatherEffectData
                {
                    VisibilityModifier = 1.0f,
                    MovementSpeedModifier = 1.0f,
                    ThirstDecayModifier = 1.2f,
                    HungerDecayModifier = 1.0f,
                    StaminaRegenModifier = 1.1f,
                    CraftingSpeedModifier = 1.0f,
                    FireCraftingAllowed = true,
                    Description = "Clear skies with good visibility"
                },
                [WeatherType.Cloudy] = new WeatherEffectData
                {
                    VisibilityModifier = 0.9f,
                    MovementSpeedModifier = 1.0f,
                    ThirstDecayModifier = 1.0f,
                    HungerDecayModifier = 1.0f,
                    StaminaRegenModifier = 1.0f,
                    CraftingSpeedModifier = 1.0f,
                    FireCraftingAllowed = true,
                    Description = "Overcast skies with reduced visibility"
                },
                [WeatherType.LightRain] = new WeatherEffectData
                {
                    VisibilityModifier = 0.8f,
                    MovementSpeedModifier = 0.95f,
                    ThirstDecayModifier = 0.8f,
                    HungerDecayModifier = 1.0f,
                    StaminaRegenModifier = 0.9f,
                    CraftingSpeedModifier = 0.9f,
                    FireCraftingAllowed = false,
                    Description = "Light rain reducing fire-based activities"
                },
                [WeatherType.HeavyRain] = new WeatherEffectData
                {
                    VisibilityModifier = 0.6f,
                    MovementSpeedModifier = 0.85f,
                    ThirstDecayModifier = 0.5f,
                    HungerDecayModifier = 1.1f,
                    StaminaRegenModifier = 0.8f,
                    CraftingSpeedModifier = 0.7f,
                    FireCraftingAllowed = false,
                    Description = "Heavy rain severely limiting outdoor activities"
                },
                [WeatherType.Thunderstorm] = new WeatherEffectData
                {
                    VisibilityModifier = 0.4f,
                    MovementSpeedModifier = 0.7f,
                    ThirstDecayModifier = 0.3f,
                    HungerDecayModifier = 1.2f,
                    StaminaRegenModifier = 0.6f,
                    CraftingSpeedModifier = 0.5f,
                    FireCraftingAllowed = false,
                    Description = "Dangerous thunderstorm with lightning"
                },
                [WeatherType.Snow] = new WeatherEffectData
                {
                    VisibilityModifier = 0.7f,
                    MovementSpeedModifier = 0.8f,
                    ThirstDecayModifier = 1.0f,
                    HungerDecayModifier = 1.3f,
                    StaminaRegenModifier = 0.7f,
                    CraftingSpeedModifier = 0.8f,
                    FireCraftingAllowed = true,
                    Description = "Snow reducing movement and increasing hunger"
                },
                [WeatherType.Blizzard] = new WeatherEffectData
                {
                    VisibilityModifier = 0.3f,
                    MovementSpeedModifier = 0.5f,
                    ThirstDecayModifier = 1.0f,
                    HungerDecayModifier = 1.5f,
                    StaminaRegenModifier = 0.5f,
                    CraftingSpeedModifier = 0.4f,
                    FireCraftingAllowed = true,
                    Description = "Severe blizzard with extreme cold"
                },
                [WeatherType.Fog] = new WeatherEffectData
                {
                    VisibilityModifier = 0.3f,
                    MovementSpeedModifier = 0.9f,
                    ThirstDecayModifier = 0.9f,
                    HungerDecayModifier = 1.0f,
                    StaminaRegenModifier = 0.95f,
                    CraftingSpeedModifier = 0.95f,
                    FireCraftingAllowed = true,
                    Description = "Dense fog severely limiting visibility"
                },
                [WeatherType.Sandstorm] = new WeatherEffectData
                {
                    VisibilityModifier = 0.2f,
                    MovementSpeedModifier = 0.6f,
                    ThirstDecayModifier = 1.8f,
                    HungerDecayModifier = 1.1f,
                    StaminaRegenModifier = 0.6f,
                    CraftingSpeedModifier = 0.3f,
                    FireCraftingAllowed = false,
                    Description = "Sandstorm with extreme thirst and limited visibility"
                },
                [WeatherType.Hail] = new WeatherEffectData
                {
                    VisibilityModifier = 0.5f,
                    MovementSpeedModifier = 0.7f,
                    ThirstDecayModifier = 0.8f,
                    HungerDecayModifier = 1.2f,
                    StaminaRegenModifier = 0.6f,
                    CraftingSpeedModifier = 0.6f,
                    FireCraftingAllowed = false,
                    Description = "Hailstorm causing damage and limiting activities"
                }
            };
        }

        /// <summary>
        /// Initializes biome-specific weather patterns
        /// </summary>
        private void InitializeBiomeWeatherData()
        {
            _biomeWeatherData = new Dictionary<string, BiomeWeatherData>
            {
                ["plains"] = new BiomeWeatherData
                {
                    WeatherProbabilities = new Dictionary<WeatherType, float>
                    {
                        [WeatherType.Clear] = 0.4f,
                        [WeatherType.Cloudy] = 0.25f,
                        [WeatherType.LightRain] = 0.2f,
                        [WeatherType.HeavyRain] = 0.1f,
                        [WeatherType.Thunderstorm] = 0.05f
                    },
                    TemperatureModifier = 0.0f,
                    HumidityModifier = 0.0f
                },
                ["forest"] = new BiomeWeatherData
                {
                    WeatherProbabilities = new Dictionary<WeatherType, float>
                    {
                        [WeatherType.Clear] = 0.3f,
                        [WeatherType.Cloudy] = 0.3f,
                        [WeatherType.LightRain] = 0.25f,
                        [WeatherType.HeavyRain] = 0.1f,
                        [WeatherType.Fog] = 0.05f
                    },
                    TemperatureModifier = -2.0f,
                    HumidityModifier = 0.2f
                },
                ["desert"] = new BiomeWeatherData
                {
                    WeatherProbabilities = new Dictionary<WeatherType, float>
                    {
                        [WeatherType.Clear] = 0.6f,
                        [WeatherType.Cloudy] = 0.2f,
                        [WeatherType.Sandstorm] = 0.15f,
                        [WeatherType.LightRain] = 0.05f
                    },
                    TemperatureModifier = 8.0f,
                    HumidityModifier = -0.4f
                },
                ["mountains"] = new BiomeWeatherData
                {
                    WeatherProbabilities = new Dictionary<WeatherType, float>
                    {
                        [WeatherType.Clear] = 0.25f,
                        [WeatherType.Cloudy] = 0.3f,
                        [WeatherType.Snow] = 0.2f,
                        [WeatherType.Blizzard] = 0.1f,
                        [WeatherType.Fog] = 0.15f
                    },
                    TemperatureModifier = -8.0f,
                    HumidityModifier = -0.1f
                },
                ["swamp"] = new BiomeWeatherData
                {
                    WeatherProbabilities = new Dictionary<WeatherType, float>
                    {
                        [WeatherType.Cloudy] = 0.3f,
                        [WeatherType.LightRain] = 0.3f,
                        [WeatherType.HeavyRain] = 0.2f,
                        [WeatherType.Fog] = 0.15f,
                        [WeatherType.Clear] = 0.05f
                    },
                    TemperatureModifier = 2.0f,
                    HumidityModifier = 0.3f
                },
                ["tundra"] = new BiomeWeatherData
                {
                    WeatherProbabilities = new Dictionary<WeatherType, float>
                    {
                        [WeatherType.Snow] = 0.4f,
                        [WeatherType.Blizzard] = 0.25f,
                        [WeatherType.Cloudy] = 0.2f,
                        [WeatherType.Clear] = 0.1f,
                        [WeatherType.Fog] = 0.05f
                    },
                    TemperatureModifier = -15.0f,
                    HumidityModifier = -0.2f
                },
                ["ocean"] = new BiomeWeatherData
                {
                    WeatherProbabilities = new Dictionary<WeatherType, float>
                    {
                        [WeatherType.Clear] = 0.3f,
                        [WeatherType.Cloudy] = 0.25f,
                        [WeatherType.LightRain] = 0.2f,
                        [WeatherType.HeavyRain] = 0.15f,
                        [WeatherType.Thunderstorm] = 0.1f
                    },
                    TemperatureModifier = -3.0f,
                    HumidityModifier = 0.4f
                }
            };
        }

        /// <summary>
        /// Updates weather transition progress
        /// </summary>
        private void UpdateWeatherTransition(float delta)
        {
            if (_transitionProgress < 1.0f)
            {
                _transitionProgress += delta / _transitionDuration;
                
                if (_transitionProgress >= 1.0f)
                {
                    _transitionProgress = 1.0f;
                    CurrentWeather = _targetWeather;
                    WeatherIntensity = CalculateWeatherIntensity();
                    
                    EmitSignal(SignalName.WeatherTransitionCompleted, (int)CurrentWeather, WeatherIntensity);
                    Logger.LogInfo("WeatherManager", $"Weather transition completed: {CurrentWeather} (Intensity: {WeatherIntensity:F2})");
                }
            }
        }

        /// <summary>
        /// Updates the weather change timer
        /// </summary>
        private void UpdateWeatherTimer(float delta)
        {
            _currentWeatherTimer += delta;
            
            if (_currentWeatherTimer >= _currentWeatherDuration)
            {
                ChangeWeatherRandomly();
                _currentWeatherTimer = 0.0f;
                _currentWeatherDuration = (float)GD.RandRange(MinWeatherDuration, MaxWeatherDuration);
            }
        }

        /// <summary>
        /// Updates the seasonal timer
        /// </summary>
        private void UpdateSeasonTimer(float delta)
        {
            if (DayNightCycle.Instance != null)
            {
                // Season changes based on day/night cycle
                float dayProgress = DayNightCycle.Instance.GetDayProgress();
                _seasonTimer += delta * DayNightCycle.Instance.GetDayProgress() / (24.0f * 3600.0f); // Convert to days
                
                if (_seasonTimer >= SeasonLengthDays)
                {
                    ChangeSeason();
                    _seasonTimer = 0.0f;
                }
            }
        }

        /// <summary>
        /// Changes weather randomly based on biome and season
        /// </summary>
        private void ChangeWeatherRandomly()
        {
            if (!_biomeWeatherData.ContainsKey(_currentBiome))
            {
                Logger.LogWarning("WeatherManager", $"Unknown biome: {_currentBiome}, using plains weather");
                _currentBiome = "plains";
            }

            var biomeData = _biomeWeatherData[_currentBiome];
            var seasonalProbabilities = ApplySeasonalModifiers(biomeData.WeatherProbabilities);
            
            WeatherType newWeather = SelectWeatherFromProbabilities(seasonalProbabilities);
            SetWeather(newWeather);
        }

        /// <summary>
        /// Applies seasonal modifiers to weather probabilities
        /// </summary>
        private Dictionary<WeatherType, float> ApplySeasonalModifiers(Dictionary<WeatherType, float> baseProbabilities)
        {
            var modifiedProbabilities = new Dictionary<WeatherType, float>(baseProbabilities);
            
            switch (CurrentSeason)
            {
                case Season.Spring:
                    // More rain in spring
                    if (modifiedProbabilities.ContainsKey(WeatherType.LightRain))
                        modifiedProbabilities[WeatherType.LightRain] *= 1.3f;
                    if (modifiedProbabilities.ContainsKey(WeatherType.HeavyRain))
                        modifiedProbabilities[WeatherType.HeavyRain] *= 1.2f;
                    break;
                    
                case Season.Summer:
                    // More clear weather in summer
                    if (modifiedProbabilities.ContainsKey(WeatherType.Clear))
                        modifiedProbabilities[WeatherType.Clear] *= 1.4f;
                    if (modifiedProbabilities.ContainsKey(WeatherType.Thunderstorm))
                        modifiedProbabilities[WeatherType.Thunderstorm] *= 1.3f;
                    break;
                    
                case Season.Autumn:
                    // More cloudy and foggy weather
                    if (modifiedProbabilities.ContainsKey(WeatherType.Cloudy))
                        modifiedProbabilities[WeatherType.Cloudy] *= 1.3f;
                    if (modifiedProbabilities.ContainsKey(WeatherType.Fog))
                        modifiedProbabilities[WeatherType.Fog] *= 1.5f;
                    break;
                    
                case Season.Winter:
                    // More snow and cold weather
                    if (modifiedProbabilities.ContainsKey(WeatherType.Snow))
                        modifiedProbabilities[WeatherType.Snow] *= 1.5f;
                    if (modifiedProbabilities.ContainsKey(WeatherType.Blizzard))
                        modifiedProbabilities[WeatherType.Blizzard] *= 1.3f;
                    // Reduce rain probability in winter
                    if (modifiedProbabilities.ContainsKey(WeatherType.LightRain))
                        modifiedProbabilities[WeatherType.LightRain] *= 0.5f;
                    if (modifiedProbabilities.ContainsKey(WeatherType.HeavyRain))
                        modifiedProbabilities[WeatherType.HeavyRain] *= 0.3f;
                    break;
            }
            
            return modifiedProbabilities;
        }

        /// <summary>
        /// Selects weather type based on probability weights
        /// </summary>
        private WeatherType SelectWeatherFromProbabilities(Dictionary<WeatherType, float> probabilities)
        {
            float totalWeight = 0.0f;
            foreach (var prob in probabilities.Values)
            {
                totalWeight += prob;
            }
            
            float randomValue = GD.Randf() * totalWeight;
            float currentWeight = 0.0f;
            
            foreach (var kvp in probabilities)
            {
                currentWeight += kvp.Value;
                if (randomValue <= currentWeight)
                {
                    return kvp.Key;
                }
            }
            
            // Fallback to clear weather
            return WeatherType.Clear;
        }

        /// <summary>
        /// Calculates weather intensity based on current conditions
        /// </summary>
        private float CalculateWeatherIntensity()
        {
            return CurrentWeather switch
            {
                WeatherType.Clear => (float)GD.RandRange(0.8f, 1.0f),
                WeatherType.Cloudy => (float)GD.RandRange(0.3f, 0.7f),
                WeatherType.LightRain => (float)GD.RandRange(0.2f, 0.5f),
                WeatherType.HeavyRain => (float)GD.RandRange(0.6f, 0.9f),
                WeatherType.Thunderstorm => (float)GD.RandRange(0.8f, 1.0f),
                WeatherType.Snow => (float)GD.RandRange(0.3f, 0.8f),
                WeatherType.Blizzard => (float)GD.RandRange(0.7f, 1.0f),
                WeatherType.Fog => (float)GD.RandRange(0.4f, 0.9f),
                WeatherType.Sandstorm => (float)GD.RandRange(0.6f, 1.0f),
                WeatherType.Hail => (float)GD.RandRange(0.5f, 0.8f),
                _ => 0.5f
            };
        }

        /// <summary>
        /// Changes to the next season
        /// </summary>
        private void ChangeSeason()
        {
            Season oldSeason = CurrentSeason;
            CurrentSeason = (Season)(((int)CurrentSeason + 1) % 4);
            
            EmitSignal(SignalName.SeasonChanged, (int)oldSeason, (int)CurrentSeason);
            
            // Emit EventBus event
            EventBus.Instance?.EmitSeasonChanged((int)oldSeason, (int)CurrentSeason);
            
            Logger.LogInfo("WeatherManager", $"Season changed from {oldSeason} to {CurrentSeason}");
            
            // Update weather forecast for new season
            GenerateWeatherForecast();
        }

        /// <summary>
        /// Generates initial weather forecast
        /// </summary>
        private void GenerateInitialForecast()
        {
            GenerateWeatherForecast();
        }

        /// <summary>
        /// Generates weather forecast for the next few days
        /// </summary>
        private void GenerateWeatherForecast()
        {
            _weatherForecast.Clear();
            
            if (!_biomeWeatherData.ContainsKey(_currentBiome))
            {
                _currentBiome = "plains";
            }
            
            var biomeData = _biomeWeatherData[_currentBiome];
            var seasonalProbabilities = ApplySeasonalModifiers(biomeData.WeatherProbabilities);
            
            for (int i = 0; i < ForecastDays; i++)
            {
                WeatherType forecastWeather = SelectWeatherFromProbabilities(seasonalProbabilities);
                _weatherForecast.Enqueue(forecastWeather);
            }
            
            EmitSignal(SignalName.WeatherForecastUpdated);
            EventBus.Instance?.EmitWeatherForecastUpdated();
        }

        /// <summary>
        /// Called when a new day starts
        /// </summary>
        private void OnNewDay()
        {
            // Update forecast - remove today's weather and add a new day
            if (_weatherForecast.Count > 0)
            {
                _weatherForecast.Dequeue();
                
                if (!_biomeWeatherData.ContainsKey(_currentBiome))
                {
                    _currentBiome = "plains";
                }
                
                var biomeData = _biomeWeatherData[_currentBiome];
                var seasonalProbabilities = ApplySeasonalModifiers(biomeData.WeatherProbabilities);
                WeatherType newForecastWeather = SelectWeatherFromProbabilities(seasonalProbabilities);
                _weatherForecast.Enqueue(newForecastWeather);
                
                EmitSignal(SignalName.WeatherForecastUpdated);
                EventBus.Instance?.EmitWeatherForecastUpdated();
            }
        }

        #region Public API

        /// <summary>
        /// Sets the weather to a specific type
        /// </summary>
        public void SetWeather(WeatherType weatherType, float transitionDuration = -1.0f)
        {
            if (weatherType == CurrentWeather && _transitionProgress >= 1.0f)
                return;
                
            WeatherType oldWeather = CurrentWeather;
            _targetWeather = weatherType;
            _transitionDuration = transitionDuration > 0 ? transitionDuration : 30.0f;
            _transitionProgress = 0.0f;
            
            EmitSignal(SignalName.WeatherTransitionStarted, (int)oldWeather, (int)_targetWeather, _transitionDuration);
            EmitSignal(SignalName.WeatherChanged, (int)oldWeather, (int)_targetWeather, WeatherIntensity);
            
            // Emit EventBus events
            EventBus.Instance?.EmitWeatherChanged((int)oldWeather, (int)_targetWeather, WeatherIntensity);
            
            Logger.LogInfo("WeatherManager", $"Weather transition started: {oldWeather} -> {_targetWeather}");
        }

        /// <summary>
        /// Sets the current biome for weather calculations
        /// </summary>
        public void SetCurrentBiome(string biomeId)
        {
            if (_currentBiome != biomeId)
            {
                _currentBiome = biomeId;
                Logger.LogInfo("WeatherManager", $"Biome changed to: {biomeId}");
                
                // Regenerate forecast for new biome
                GenerateWeatherForecast();
            }
        }

        /// <summary>
        /// Gets the current weather effects
        /// </summary>
        public WeatherEffectData GetCurrentWeatherEffects()
        {
            if (_weatherEffects.ContainsKey(CurrentWeather))
            {
                var effects = _weatherEffects[CurrentWeather];
                
                // Apply intensity scaling
                return new WeatherEffectData
                {
                    VisibilityModifier = Mathf.Lerp(1.0f, effects.VisibilityModifier, WeatherIntensity),
                    MovementSpeedModifier = Mathf.Lerp(1.0f, effects.MovementSpeedModifier, WeatherIntensity),
                    ThirstDecayModifier = Mathf.Lerp(1.0f, effects.ThirstDecayModifier, WeatherIntensity),
                    HungerDecayModifier = Mathf.Lerp(1.0f, effects.HungerDecayModifier, WeatherIntensity),
                    StaminaRegenModifier = Mathf.Lerp(1.0f, effects.StaminaRegenModifier, WeatherIntensity),
                    CraftingSpeedModifier = Mathf.Lerp(1.0f, effects.CraftingSpeedModifier, WeatherIntensity),
                    FireCraftingAllowed = effects.FireCraftingAllowed,
                    Description = effects.Description
                };
            }
            
            return new WeatherEffectData(); // Default effects
        }

        /// <summary>
        /// Gets the weather forecast
        /// </summary>
        public WeatherType[] GetWeatherForecast()
        {
            return _weatherForecast.ToArray();
        }

        /// <summary>
        /// Gets the current temperature based on biome, season, and weather
        /// </summary>
        public float GetCurrentTemperature()
        {
            float baseTemp = 20.0f; // Default temperature
            
            // Apply biome modifier
            if (_biomeWeatherData.ContainsKey(_currentBiome))
            {
                baseTemp += _biomeWeatherData[_currentBiome].TemperatureModifier;
            }
            
            // Apply seasonal modifier
            float seasonalModifier = CurrentSeason switch
            {
                Season.Spring => 0.0f,
                Season.Summer => 8.0f,
                Season.Autumn => -3.0f,
                Season.Winter => -12.0f,
                _ => 0.0f
            };
            
            baseTemp += seasonalModifier;
            
            // Apply weather modifier
            float weatherModifier = CurrentWeather switch
            {
                WeatherType.Snow => -8.0f,
                WeatherType.Blizzard => -15.0f,
                WeatherType.LightRain => -2.0f,
                WeatherType.HeavyRain => -4.0f,
                WeatherType.Thunderstorm => -5.0f,
                WeatherType.Clear => 2.0f,
                _ => 0.0f
            };
            
            baseTemp += weatherModifier * WeatherIntensity;
            
            return baseTemp;
        }

        /// <summary>
        /// Checks if fire-based crafting is allowed in current weather
        /// </summary>
        public bool IsFireCraftingAllowed()
        {
            var effects = GetCurrentWeatherEffects();
            return effects.FireCraftingAllowed;
        }

        /// <summary>
        /// Gets a formatted weather description
        /// </summary>
        public string GetWeatherDescription()
        {
            var effects = GetCurrentWeatherEffects();
            string intensityDesc = WeatherIntensity switch
            {
                < 0.3f => "Light",
                < 0.7f => "Moderate",
                _ => "Heavy"
            };
            
            return $"{intensityDesc} {CurrentWeather}: {effects.Description}";
        }

        /// <summary>
        /// Forces an immediate weather change (for debugging/testing)
        /// </summary>
        public void ForceWeatherChange(WeatherType weatherType)
        {
            SetWeather(weatherType, 1.0f); // Quick transition
        }

        #endregion

        // Endgame content support methods
        public void SetWeatherForced(WeatherType weatherType, float intensity)
        {
            CurrentWeather = weatherType;
            WeatherIntensity = intensity;
            GD.Print($"Weather set to: {weatherType} with intensity {intensity}");
        }

        public override void _ExitTree()
        {
            if (_instance == this)
            {
                _instance = null;
            }
        }

        internal void SetWeather(object storm, float v)
        {
            throw new NotImplementedException();
        }
    }

    /// <summary>
    /// Data structure for weather effects on gameplay
    /// </summary>
    public class WeatherEffectData
    {
        public float VisibilityModifier { get; set; } = 1.0f;
        public float MovementSpeedModifier { get; set; } = 1.0f;
        public float ThirstDecayModifier { get; set; } = 1.0f;
        public float HungerDecayModifier { get; set; } = 1.0f;
        public float StaminaRegenModifier { get; set; } = 1.0f;
        public float CraftingSpeedModifier { get; set; } = 1.0f;
        public bool FireCraftingAllowed { get; set; } = true;
        public string Description { get; set; } = "";
    }

    /// <summary>
    /// Data structure for biome-specific weather patterns
    /// </summary>
    public class BiomeWeatherData
    {
        public Dictionary<WeatherManager.WeatherType, float> WeatherProbabilities { get; set; } = new();
        public float TemperatureModifier { get; set; } = 0.0f;
        public float HumidityModifier { get; set; } = 0.0f;

    }

}