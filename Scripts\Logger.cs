using Godot;
using System;
using System.Collections.Generic;
using System.IO;

namespace SurvivalLooterShooter
{
    /// <summary>
    /// Centralized logging system for debugging and error tracking
    /// </summary>
    public static class Logger
    {
        public enum LogLevel
        {
            Debug = 0,
            Info = 1,
            Warning = 2,
            Error = 3,
            Critical = 4
        }

        private static LogLevel _currentLogLevel = LogLevel.Info;
        private static bool _enableFileLogging = true;
        private static string _logFilePath = "user://logs/game.log";
        private static readonly object _logLock = new object();
        private static readonly Queue<string> _logBuffer = new Queue<string>();
        private const int MAX_LOG_BUFFER_SIZE = 1000;

        static Logger()
        {
            InitializeLogging();
        }

        /// <summary>
        /// Initializes the logging system
        /// </summary>
        private static void InitializeLogging()
        {
            try
            {
                // Create logs directory if it doesn't exist
                string logDir = Path.GetDirectoryName(_logFilePath);
                if (!DirAccess.DirExistsAbsolute(logDir))
                {
                    DirAccess.MakeDirRecursiveAbsolute(logDir);
                }

                // Clear old log file on startup
                if (Godot.FileAccess.FileExists(_logFilePath))
                {
                    var file = Godot.FileAccess.Open(_logFilePath, Godot.FileAccess.ModeFlags.Write);
                    file?.Close();
                }

                LogInfo("Logger", "Logging system initialized");
            }
            catch (Exception ex)
            {
                GD.PrintErr($"Failed to initialize logging system: {ex.Message}");
                _enableFileLogging = false;
            }
        }

        /// <summary>
        /// Sets the minimum log level
        /// </summary>
        public static void SetLogLevel(LogLevel level)
        {
            _currentLogLevel = level;
            LogInfo("Logger", $"Log level set to {level}");
        }

        /// <summary>
        /// Enables or disables file logging
        /// </summary>
        public static void SetFileLogging(bool enabled)
        {
            _enableFileLogging = enabled;
            LogInfo("Logger", $"File logging {(enabled ? "enabled" : "disabled")}");
        }

        /// <summary>
        /// Logs a debug message
        /// </summary>
        public static void LogDebug(string system, string message)
        {
            Log(LogLevel.Debug, system, message);
        }

        /// <summary>
        /// Logs an info message
        /// </summary>
        public static void LogInfo(string system, string message)
        {
            Log(LogLevel.Info, system, message);
        }

        /// <summary>
        /// Logs a warning message
        /// </summary>
        public static void LogWarning(string system, string message)
        {
            Log(LogLevel.Warning, system, message);
        }

        /// <summary>
        /// Logs an error message
        /// </summary>
        public static void LogError(string system, string message)
        {
            Log(LogLevel.Error, system, message);
        }

        /// <summary>
        /// Logs a critical error message
        /// </summary>
        public static void LogCritical(string system, string message)
        {
            Log(LogLevel.Critical, system, message);
        }

        /// <summary>
        /// Logs an exception with full details
        /// </summary>
        public static void LogException(string system, Exception exception, string context = "")
        {
            string message = $"Exception in {context}: {exception.Message}";
            if (!string.IsNullOrEmpty(exception.StackTrace))
            {
                message += $"\nStack trace: {exception.StackTrace}";
            }
            
            if (exception.InnerException != null)
            {
                message += $"\nInner exception: {exception.InnerException.Message}";
            }

            Log(LogLevel.Error, system, message);
        }

        /// <summary>
        /// Core logging method
        /// </summary>
        private static void Log(LogLevel level, string system, string message)
        {
            if (level < _currentLogLevel)
                return;

            lock (_logLock)
            {
                try
                {
                    string timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fff");
                    string levelStr = level.ToString().ToUpper().PadRight(8);
                    string systemStr = system.PadRight(15);
                    string logEntry = $"[{timestamp}] [{levelStr}] [{systemStr}] {message}";

                    // Output to Godot console
                    switch (level)
                    {
                        case LogLevel.Debug:
                        case LogLevel.Info:
                            GD.Print(logEntry);
                            break;
                        case LogLevel.Warning:
                            GD.Print($"⚠️ {logEntry}");
                            break;
                        case LogLevel.Error:
                        case LogLevel.Critical:
                            GD.PrintErr($"❌ {logEntry}");
                            break;
                    }

                    // Add to buffer for file logging
                    if (_enableFileLogging)
                    {
                        _logBuffer.Enqueue(logEntry);
                        
                        // Maintain buffer size
                        while (_logBuffer.Count > MAX_LOG_BUFFER_SIZE)
                        {
                            _logBuffer.Dequeue();
                        }

                        // Write to file immediately for errors and critical messages
                        if (level >= LogLevel.Error)
                        {
                            FlushLogsToFile();
                        }
                    }
                }
                catch (Exception ex)
                {
                    GD.PrintErr($"Logging system error: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// Flushes buffered logs to file
        /// </summary>
        public static void FlushLogsToFile()
        {
            if (!_enableFileLogging || _logBuffer.Count == 0)
                return;

            lock (_logLock)
            {
                try
                {
                    var file = Godot.FileAccess.Open(_logFilePath, Godot.FileAccess.ModeFlags.WriteRead);
                    if (file == null)
                        return;

                    // Move to end of file
                    file.SeekEnd();

                    // Write all buffered entries
                    while (_logBuffer.Count > 0)
                    {
                        string entry = _logBuffer.Dequeue();
                        file.StoreLine(entry);
                    }

                    file.Close();
                }
                catch (Exception ex)
                {
                    GD.PrintErr($"Failed to write logs to file: {ex.Message}");
                    _enableFileLogging = false;
                }
            }
        }

        /// <summary>
        /// Gets recent log entries for debugging
        /// </summary>
        public static List<string> GetRecentLogs(int count = 50)
        {
            lock (_logLock)
            {
                var logs = new List<string>();
                var bufferArray = _logBuffer.ToArray();
                
                int startIndex = Math.Max(0, bufferArray.Length - count);
                for (int i = startIndex; i < bufferArray.Length; i++)
                {
                    logs.Add(bufferArray[i]);
                }
                
                return logs;
            }
        }

        /// <summary>
        /// Clears the log buffer
        /// </summary>
        public static void ClearLogs()
        {
            lock (_logLock)
            {
                _logBuffer.Clear();
                LogInfo("Logger", "Log buffer cleared");
            }
        }
    }
}