[gd_scene load_steps=5 format=3 uid="uid://cs7dg6i4034ka"]

[ext_resource type="PackedScene" uid="uid://bqxvn8ywqxqxq" path="res://Scenes/ItemPickup.tscn" id="1_demo"]
[ext_resource type="Script" path="res://Scripts/PlayerController.cs" id="2_demo"]

[sub_resource type="RectangleShape2D" id="RectangleShape2D_1"]
size = Vector2(32, 32)

[sub_resource type="RectangleShape2D" id="RectangleShape2D_2"]
size = Vector2(64, 64)

[node name="ItemPickupDemo" type="Node2D"]

[node name="Player" type="CharacterBody2D" parent="."]
position = Vector2(400, 300)
script = ExtResource("2_demo")

[node name="CollisionShape2D" type="CollisionShape2D" parent="Player"]
shape = SubResource("RectangleShape2D_1")

[node name="Sprite2D" type="Sprite2D" parent="Player"]
modulate = Color(0, 0, 1, 1)

[node name="Inventory" type="Node" parent="Player"]
script = ExtResource("res://Scripts/Inventory.cs")

[node name="ItemPickup1" parent="." instance=ExtResource("1_demo")]
position = Vector2(200, 200)
ItemId = "canned_food"
Quantity = 3

[node name="ItemPickup2" parent="." instance=ExtResource("1_demo")]
position = Vector2(600, 200)
ItemId = "energy_drink"
Quantity = 1

[node name="ItemPickup3" parent="." instance=ExtResource("1_demo")]
position = Vector2(400, 500)
ItemId = "bandage"
Quantity = 5
AutoPickup = true

[node name="Instructions" type="Label" parent="."]
offset_left = 50.0
offset_top = 50.0
offset_right = 800.0
offset_bottom = 150.0
text = "Item Pickup Demo
Use WASD to move the blue square (player)
Press E near items to pick them up
The bottom item has auto-pickup enabled
Press I to open inventory (if implemented)"