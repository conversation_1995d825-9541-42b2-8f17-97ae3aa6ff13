using Godot;
using System;
using System.Collections.Generic;

namespace SurvivalLooterShooter
{
    /// <summary>
    /// Unit tests for world generation system components
    /// Tests WorldManager, BiomeGenerator, ChunkLoader, and WorldChunk functionality
    /// </summary>
    public partial class WorldGenerationTests : Node
    {
        private int _testsPassed = 0;
        private int _testsFailed = 0;
        private List<string> _testResults = new List<string>();

        public override void _Ready()
        {
            GD.Print("=== World Generation System Tests ===");
            
            // Wait a frame to ensure systems are ready
            CallDeferred(nameof(RunAllTests));
        }

        private void RunAllTests()
        {
            TestWorldManagerInitialization();
            TestBiomeGeneratorInitialization();
            TestChunkLoaderInitialization();
            TestWorldChunkCreation();
            TestBiomeGeneration();
            TestResourceGeneration();
            TestChunkCoordinateConversion();
            TestChunkLoadingUnloading();
            TestWorldSaveData();
            
            PrintTestResults();
        }

        /// <summary>
        /// Tests WorldManager initialization and basic functionality
        /// </summary>
        private void TestWorldManagerInitialization()
        {
            string testName = "WorldManager Initialization";
            
            try
            {
                var worldManager = new WorldManager();
                AddChild(worldManager);
                
                // Test default values
                Assert(worldManager.ChunkSize > 0, "ChunkSize should be positive");
                Assert(worldManager.RenderDistance > 0, "RenderDistance should be positive");
                Assert(worldManager.WorldSeed != 0, "WorldSeed should be set");
                
                // Test world generation
                worldManager.GenerateWorld(12345);
                Assert(worldManager.IsWorldGenerated, "World should be marked as generated");
                Assert(worldManager.WorldSeed == 12345, "WorldSeed should be updated");
                
                worldManager.QueueFree();
                PassTest(testName);
            }
            catch (Exception ex)
            {
                FailTest(testName, ex.Message);
            }
        }

        /// <summary>
        /// Tests BiomeGenerator initialization and biome determination
        /// </summary>
        private void TestBiomeGeneratorInitialization()
        {
            string testName = "BiomeGenerator Initialization";
            
            try
            {
                var biomeGenerator = new BiomeGenerator();
                AddChild(biomeGenerator);
                
                // Create noise generators for testing
                var biomeNoise = new FastNoiseLite();
                biomeNoise.Seed = 12345;
                var elevationNoise = new FastNoiseLite();
                elevationNoise.Seed = 12346;
                
                biomeGenerator.Initialize(12345, biomeNoise, elevationNoise);
                
                // Test biome determination at different positions
                BiomeType biome1 = biomeGenerator.GetBiomeAt(Vector2.Zero);
                BiomeType biome2 = biomeGenerator.GetBiomeAt(new Vector2(100, 100));
                
                Assert(Enum.IsDefined(typeof(BiomeType), biome1), "Should return valid biome type");
                Assert(Enum.IsDefined(typeof(BiomeType), biome2), "Should return valid biome type");
                
                // Test biome data retrieval
                var biomeData = biomeGenerator.GetBiomeData(BiomeType.Forest);
                Assert(biomeData != null, "Should return biome data for Forest");
                Assert(biomeData.ResourceSpawns.Count > 0, "Forest should have resource spawns");
                
                biomeGenerator.QueueFree();
                PassTest(testName);
            }
            catch (Exception ex)
            {
                FailTest(testName, ex.Message);
            }
        }

        /// <summary>
        /// Tests ChunkLoader initialization and queue management
        /// </summary>
        private void TestChunkLoaderInitialization()
        {
            string testName = "ChunkLoader Initialization";
            
            try
            {
                var chunkLoader = new ChunkLoader();
                AddChild(chunkLoader);
                
                chunkLoader.Initialize(64, 3);
                
                Assert(chunkLoader.ChunkSize == 64, "ChunkSize should be set correctly");
                Assert(chunkLoader.RenderDistance == 3, "RenderDistance should be set correctly");
                
                // Test queue operations
                Assert(chunkLoader.GetLoadQueueSize() == 0, "Load queue should start empty");
                Assert(chunkLoader.GetUnloadQueueSize() == 0, "Unload queue should start empty");
                
                // Test force loading
                chunkLoader.ForceLoadChunk(new Vector2I(0, 0));
                Assert(chunkLoader.GetLoadQueueSize() > 0, "Load queue should have items after force load");
                
                // Test clearing queues
                chunkLoader.ClearQueues();
                Assert(chunkLoader.GetLoadQueueSize() == 0, "Load queue should be empty after clear");
                
                chunkLoader.QueueFree();
                PassTest(testName);
            }
            catch (Exception ex)
            {
                FailTest(testName, ex.Message);
            }
        }

        /// <summary>
        /// Tests WorldChunk creation and basic functionality
        /// </summary>
        private void TestWorldChunkCreation()
        {
            string testName = "WorldChunk Creation";
            
            try
            {
                var chunk = new WorldChunk(new Vector2I(0, 0), 64);
                AddChild(chunk);
                
                Assert(chunk.ChunkCoords == new Vector2I(0, 0), "Chunk coordinates should be set correctly");
                Assert(chunk.ChunkSize == 64, "Chunk size should be set correctly");
                Assert(!chunk.IsGenerated, "Chunk should not be generated initially");
                
                // Test biome setting and getting
                chunk.SetBiomeAt(0, 0, BiomeType.Forest);
                Assert(chunk.GetBiomeAt(0, 0) == BiomeType.Forest, "Should set and get biome correctly");
                
                // Test resource addition
                chunk.AddResource("wood", new Vector2(10, 10));
                var resources = chunk.GetResources();
                Assert(resources.Count == 1, "Should have one resource after addition");
                Assert(resources[0].ItemId == "wood", "Resource should have correct item ID");
                
                // Test coordinate conversion
                Vector2I localCoords = chunk.WorldToLocalCoords(new Vector2(10, 10));
                Vector2 worldCoords = chunk.LocalToWorldCoords(localCoords);
                Assert(worldCoords.DistanceTo(new Vector2(10, 10)) < 1.0f, "Coordinate conversion should be accurate");
                
                chunk.QueueFree();
                PassTest(testName);
            }
            catch (Exception ex)
            {
                FailTest(testName, ex.Message);
            }
        }

        /// <summary>
        /// Tests biome generation consistency and variety
        /// </summary>
        private void TestBiomeGeneration()
        {
            string testName = "Biome Generation";
            
            try
            {
                var biomeGenerator = new BiomeGenerator();
                AddChild(biomeGenerator);
                
                var biomeNoise = new FastNoiseLite();
                biomeNoise.Seed = 12345;
                var elevationNoise = new FastNoiseLite();
                elevationNoise.Seed = 12346;
                
                biomeGenerator.Initialize(12345, biomeNoise, elevationNoise);
                
                // Test biome consistency (same position should return same biome)
                BiomeType biome1 = biomeGenerator.GetBiomeAt(new Vector2(50, 50));
                BiomeType biome2 = biomeGenerator.GetBiomeAt(new Vector2(50, 50));
                Assert(biome1 == biome2, "Same position should return same biome");
                
                // Test biome variety (different positions should potentially have different biomes)
                var biomeSet = new HashSet<BiomeType>();
                for (int i = 0; i < 100; i++)
                {
                    Vector2 pos = new Vector2(i * 10, i * 10);
                    BiomeType biome = biomeGenerator.GetBiomeAt(pos);
                    biomeSet.Add(biome);
                }
                
                Assert(biomeSet.Count > 1, "Should generate multiple biome types across different positions");
                
                biomeGenerator.QueueFree();
                PassTest(testName);
            }
            catch (Exception ex)
            {
                FailTest(testName, ex.Message);
            }
        }

        /// <summary>
        /// Tests resource generation within chunks
        /// </summary>
        private void TestResourceGeneration()
        {
            string testName = "Resource Generation";
            
            try
            {
                var biomeGenerator = new BiomeGenerator();
                AddChild(biomeGenerator);
                
                var biomeNoise = new FastNoiseLite();
                biomeNoise.Seed = 12345;
                var elevationNoise = new FastNoiseLite();
                elevationNoise.Seed = 12346;
                
                biomeGenerator.Initialize(12345, biomeNoise, elevationNoise);
                
                var chunk = new WorldChunk(new Vector2I(0, 0), 64);
                AddChild(chunk);
                
                // Generate chunk content
                biomeGenerator.GenerateChunk(chunk);
                
                Assert(chunk.IsGenerated, "Chunk should be marked as generated");
                
                var resources = chunk.GetResources();
                // Resources should be generated (though exact count depends on biome and randomness)
                GD.Print($"Generated {resources.Count} resources in chunk");
                
                // Test resource properties
                if (resources.Count > 0)
                {
                    var resource = resources[0];
                    Assert(!string.IsNullOrEmpty(resource.ItemId), "Resource should have valid item ID");
                    Assert(resource.Quantity > 0, "Resource should have positive quantity");
                    Assert(resource.CanHarvest(), "New resource should be harvestable");
                }
                
                chunk.QueueFree();
                biomeGenerator.QueueFree();
                PassTest(testName);
            }
            catch (Exception ex)
            {
                FailTest(testName, ex.Message);
            }
        }

        /// <summary>
        /// Tests coordinate conversion functions
        /// </summary>
        private void TestChunkCoordinateConversion()
        {
            string testName = "Chunk Coordinate Conversion";
            
            try
            {
                var worldManager = new WorldManager();
                AddChild(worldManager);
                
                // Test world to chunk conversion
                Vector2I chunkCoords1 = worldManager.WorldToChunkCoords(new Vector2(0, 0));
                Vector2I chunkCoords2 = worldManager.WorldToChunkCoords(new Vector2(64, 64));
                Vector2I chunkCoords3 = worldManager.WorldToChunkCoords(new Vector2(-32, -32));
                
                Assert(chunkCoords1 == new Vector2I(0, 0), "Origin should be in chunk (0,0)");
                Assert(chunkCoords2 == new Vector2I(1, 1), "Position (64,64) should be in chunk (1,1)");
                Assert(chunkCoords3 == new Vector2I(-1, -1), "Negative position should work correctly");
                
                // Test chunk to world conversion
                Vector2 worldPos1 = worldManager.ChunkToWorldCoords(new Vector2I(0, 0));
                Vector2 worldPos2 = worldManager.ChunkToWorldCoords(new Vector2I(1, 1));
                
                Assert(worldPos1.X >= 0 && worldPos1.X < 64, "Chunk (0,0) should map to valid world position");
                Assert(worldPos2.X >= 64 && worldPos2.X < 128, "Chunk (1,1) should map to valid world position");
                
                worldManager.QueueFree();
                PassTest(testName);
            }
            catch (Exception ex)
            {
                FailTest(testName, ex.Message);
            }
        }

        /// <summary>
        /// Tests chunk loading and unloading mechanics
        /// </summary>
        private void TestChunkLoadingUnloading()
        {
            string testName = "Chunk Loading/Unloading";
            
            try
            {
                var worldManager = new WorldManager();
                AddChild(worldManager);
                
                worldManager.GenerateWorld(12345);
                
                // Test initial chunk loading
                int initialChunkCount = worldManager.LoadedChunks.Count;
                Assert(initialChunkCount > 0, "Should have loaded initial chunks");
                
                // Test chunk loading around different position
                worldManager.UpdateWorldLoading(new Vector2(200, 200));
                
                // Allow time for chunk processing
                // Note: In a real test, we'd use async/await, but for simplicity we'll skip the wait
                
                // Should have loaded new chunks
                Assert(worldManager.LoadedChunks.Count >= initialChunkCount, "Should maintain or increase chunk count");
                
                // Test chunk existence
                Vector2I testChunkCoords = worldManager.WorldToChunkCoords(new Vector2(200, 200));
                Assert(worldManager.IsChunkLoaded(testChunkCoords), "Chunk at player position should be loaded");
                
                worldManager.QueueFree();
                PassTest(testName);
            }
            catch (Exception ex)
            {
                FailTest(testName, ex.Message);
            }
        }

        /// <summary>
        /// Tests world save data serialization
        /// </summary>
        private void TestWorldSaveData()
        {
            string testName = "World Save Data";
            
            try
            {
                var worldManager = new WorldManager();
                AddChild(worldManager);
                
                worldManager.GenerateWorld(54321);
                
                // Get save data
                var saveData = worldManager.GetWorldSaveData();
                Assert(saveData != null, "Should create save data");
                Assert(saveData.Seed == 54321, "Save data should have correct seed");
                Assert(saveData.GeneratedChunks != null, "Save data should have chunk list");
                
                // Test loading save data
                var newWorldManager = new WorldManager();
                AddChild(newWorldManager);
                
                newWorldManager.LoadWorldSaveData(saveData);
                Assert(newWorldManager.WorldSeed == 54321, "Should load correct seed");
                
                worldManager.QueueFree();
                newWorldManager.QueueFree();
                PassTest(testName);
            }
            catch (Exception ex)
            {
                FailTest(testName, ex.Message);
            }
        }

        /// <summary>
        /// Prints test results summary
        /// </summary>
        private void PrintTestResults()
        {
            GD.Print("\n=== World Generation System Test Results ===");
            GD.Print($"Tests Passed: {_testsPassed}");
            GD.Print($"Tests Failed: {_testsFailed}");
            GD.Print($"Total Tests: {_testsPassed + _testsFailed}");
            
            if (_testResults.Count > 0)
            {
                GD.Print("\nDetailed Results:");
                foreach (string result in _testResults)
                {
                    GD.Print(result);
                }
            }
            
            if (_testsFailed == 0)
            {
                GD.Print("🎉 All world generation tests passed!");
            }
            else
            {
                GD.PrintErr($"❌ {_testsFailed} test(s) failed");
            }
            
            GD.Print("=== End World Generation System Tests ===\n");
        }

        private void Assert(bool condition, string message)
        {
            if (!condition)
            {
                throw new Exception($"Assertion failed: {message}");
            }
        }

        private void PassTest(string testName)
        {
            _testsPassed++;
            _testResults.Add($"✅ {testName}: PASSED");
        }

        private void FailTest(string testName, string error)
        {
            _testsFailed++;
            _testResults.Add($"❌ {testName}: FAILED - {error}");
        }
    }
}