using Godot;
using System;
using System.Collections.Generic;
using System.Linq;

namespace SurvivalLooterShooter
{
    /// <summary>
    /// Manages player's item collection with stacking logic and equipment slots
    /// </summary>
    public partial class Inventory : Node
    {
        // Core inventory storage - using item ID as key for quick lookup
        private Dictionary<string, InventorySlot> _items = new Dictionary<string, InventorySlot>();
        
        // Equipment slots for special items
        private Dictionary<string, InventorySlot> _equipmentSlots = new Dictionary<string, InventorySlot>();

        // Events for inventory changes
        [Signal]
        public delegate void InventoryChangedEventHandler(string itemId, int quantityChange);
        
        [Signal]
        public delegate void ItemEquippedEventHandler(string slotType, string itemId);

        public Inventory()
        {
            InitializeEquipmentSlots();
        }

        public override void _Ready()
        {
            // Add to inventory group for easy access
            AddToGroup("inventory");
        }

        private void InitializeEquipmentSlots()
        {
            // Initialize equipment slots
            _equipmentSlots["weapon"] = new InventorySlot();
            _equipmentSlots["armor"] = new InventorySlot();
            _equipmentSlots["tool"] = new InventorySlot();
        }

        /// <summary>
        /// Adds items to the inventory with stacking logic and max stack validation
        /// </summary>
        /// <param name="itemId">The ID of the item to add</param>
        /// <param name="quantity">The quantity to add</param>
        /// <param name="metadata">Optional metadata for the item</param>
        /// <returns>True if all items were added successfully, false otherwise</returns>
        public bool AddItem(string itemId, int quantity, Dictionary<string, object> metadata = null)
        {
            try
            {
                // Input validation
                if (string.IsNullOrEmpty(itemId))
                {
                    Logger.LogError("Inventory", "AddItem called with null or empty itemId");
                    return false;
                }

                if (quantity <= 0)
                {
                    Logger.LogError("Inventory", $"AddItem called with invalid quantity: {quantity}");
                    return false;
                }

                // Get item definition with error handling
                var item = ItemDatabase.Instance?.GetItem(itemId);
                if (item == null)
                {
                    Logger.LogError("Inventory", $"Item not found in database: {itemId}");
                    return false;
                }

                // Validate metadata if provided
                if (metadata != null)
                {
                    foreach (var kvp in metadata)
                    {
                        if (kvp.Value == null)
                        {
                            Logger.LogWarning("Inventory", $"Null metadata value for key '{kvp.Key}' in item '{itemId}'");
                        }
                    }
                }

                // Check if we can add all the items before making any changes
                if (!CanAddItem(itemId, quantity, metadata))
                {
                    Logger.LogWarning("Inventory", $"Cannot add {quantity} x {itemId} - insufficient space or invalid item");
                    return false;
                }

                int remainingQuantity = quantity;

                // Try to stack with existing items first
                if (_items.ContainsKey(itemId))
                {
                    var existingSlot = _items[itemId];
                    if (existingSlot.CanStackWith(itemId, metadata))
                    {
                        int availableSpace = existingSlot.GetRemainingStackSpace();
                        int amountToAdd = Math.Min(remainingQuantity, availableSpace);
                        
                        existingSlot.Quantity += amountToAdd;
                        remainingQuantity -= amountToAdd;

                        // Merge metadata if needed
                        if (metadata != null)
                        {
                            foreach (var kvp in metadata)
                            {
                                if (!existingSlot.Metadata.ContainsKey(kvp.Key))
                                {
                                    existingSlot.Metadata[kvp.Key] = kvp.Value;
                                }
                            }
                        }
                    }
                }

                // If there are still items to add, create new slots
                while (remainingQuantity > 0)
                {
                    int stackSize = Math.Min(remainingQuantity, item.MaxStack);
                    var newSlot = new InventorySlot(itemId, stackSize, metadata);
                    
                    // For stackable items, we might need to find a unique key
                    string slotKey = itemId;
                    int slotIndex = 1;
                    while (_items.ContainsKey(slotKey) && !_items[slotKey].IsEmpty)
                    {
                        slotKey = $"{itemId}_{slotIndex}";
                        slotIndex++;
                        
                        // Prevent infinite loop
                        if (slotIndex > 1000)
                        {
                            Logger.LogError("Inventory", $"Failed to find available slot for item {itemId} after 1000 attempts");
                            return false;
                        }
                    }

                    _items[slotKey] = newSlot;
                    remainingQuantity -= stackSize;
                }

                // Emit inventory changed signal
                EmitSignal(SignalName.InventoryChanged, itemId, quantity);
                
                // Emit event bus event with error handling
                try
                {
                    EventBus.Instance?.EmitItemAdded(itemId, quantity);
                }
                catch (Exception ex)
                {
                    Logger.LogException("Inventory", ex, "EmitItemAdded event");
                }
                
                Logger.LogDebug("Inventory", $"Added {quantity} x {item.Name} to inventory");
                return true;
            }
            catch (Exception ex)
            {
                Logger.LogException("Inventory", ex, "AddItem");
                return false;
            }
        }

        /// <summary>
        /// Removes items from the inventory with stack management
        /// </summary>
        /// <param name="itemId">The ID of the item to remove</param>
        /// <param name="quantity">The quantity to remove</param>
        /// <returns>True if the requested quantity was removed, false otherwise</returns>
        public bool RemoveItem(string itemId, int quantity)
        {
            try
            {
                // Input validation
                if (string.IsNullOrEmpty(itemId))
                {
                    Logger.LogError("Inventory", "RemoveItem called with null or empty itemId");
                    return false;
                }

                if (quantity <= 0)
                {
                    Logger.LogError("Inventory", $"RemoveItem called with invalid quantity: {quantity}");
                    return false;
                }

                // Check if we have enough items to remove
                int availableQuantity = GetItemQuantity(itemId);
                if (availableQuantity < quantity)
                {
                    Logger.LogWarning("Inventory", $"Not enough {itemId} to remove. Available: {availableQuantity}, Requested: {quantity}");
                    return false;
                }

                int remainingToRemove = quantity;

                // Find all slots containing this item and remove from them
                var slotsToRemove = new List<string>();
                
                foreach (var kvp in _items.ToList()) // ToList to avoid modification during iteration
                {
                    if (kvp.Value.ItemId == itemId && remainingToRemove > 0)
                    {
                        var slot = kvp.Value;
                        int amountToRemove = Math.Min(remainingToRemove, slot.Quantity);
                        
                        slot.Quantity -= amountToRemove;
                        remainingToRemove -= amountToRemove;

                        // Mark empty slots for removal
                        if (slot.IsEmpty)
                        {
                            slotsToRemove.Add(kvp.Key);
                        }
                    }
                }

                // Remove empty slots
                foreach (var slotKey in slotsToRemove)
                {
                    _items.Remove(slotKey);
                }

                // Emit inventory changed signal
                EmitSignal(SignalName.InventoryChanged, itemId, -quantity);
                
                // Emit event bus event with error handling
                try
                {
                    EventBus.Instance?.EmitItemRemoved(itemId, quantity);
                }
                catch (Exception ex)
                {
                    Logger.LogException("Inventory", ex, "EmitItemRemoved event");
                }
                
                var item = ItemDatabase.Instance?.GetItem(itemId);
                Logger.LogDebug("Inventory", $"Removed {quantity} x {item?.Name ?? itemId} from inventory");
                return true;
            }
            catch (Exception ex)
            {
                Logger.LogException("Inventory", ex, "RemoveItem");
                return false;
            }
        }

        /// <summary>
        /// Checks if items can be added to the inventory
        /// </summary>
        /// <param name="itemId">The ID of the item to check</param>
        /// <param name="quantity">The quantity to check</param>
        /// <param name="metadata">Optional metadata for the item</param>
        /// <returns>True if the items can be added, false otherwise</returns>
        public bool CanAddItem(string itemId, int quantity, Dictionary<string, object> metadata = null)
        {
            if (string.IsNullOrEmpty(itemId) || quantity <= 0)
                return false;

            var item = ItemDatabase.Instance?.GetItem(itemId);
            if (item == null)
                return false;

            // For non-stackable items, we need enough empty slots
            if (item.MaxStack <= 1)
            {
                // Count how many of this item we can still add (assuming reasonable inventory limit)
                int existingCount = GetItemQuantity(itemId);
                int maxAllowed = 100; // Reasonable limit for non-stackable items
                return existingCount + quantity <= maxAllowed;
            }

            // For stackable items, check if we can fit them in existing or new stacks
            int remainingQuantity = quantity;

            // Check existing stacks
            if (_items.ContainsKey(itemId))
            {
                var existingSlot = _items[itemId];
                if (existingSlot.CanStackWith(itemId, metadata))
                {
                    remainingQuantity -= existingSlot.GetRemainingStackSpace();
                }
            }

            // If we still have items to place, we can create new stacks
            // For simplicity, assume we can always create new stacks (inventory expansion logic can be added later)
            return remainingQuantity <= 0 || remainingQuantity <= item.MaxStack * 100; // Reasonable limit
        }

        /// <summary>
        /// Gets the total quantity of a specific item in the inventory
        /// </summary>
        /// <param name="itemId">The ID of the item to count</param>
        /// <returns>The total quantity of the item</returns>
        public int GetItemQuantity(string itemId)
        {
            if (string.IsNullOrEmpty(itemId))
                return 0;

            int totalQuantity = 0;
            foreach (var slot in _items.Values)
            {
                if (slot.ItemId == itemId)
                {
                    totalQuantity += slot.Quantity;
                }
            }

            return totalQuantity;
        }

        /// <summary>
        /// Gets all items in the inventory
        /// </summary>
        /// <returns>Dictionary of all inventory slots</returns>
        public Dictionary<string, InventorySlot> GetAllItems()
        {
            var result = new Dictionary<string, InventorySlot>();
            foreach (var kvp in _items)
            {
                if (!kvp.Value.IsEmpty)
                {
                    result[kvp.Key] = kvp.Value.Clone();
                }
            }
            return result;
        }

        /// <summary>
        /// Checks if the inventory contains a specific item
        /// </summary>
        /// <param name="itemId">The ID of the item to check</param>
        /// <param name="quantity">The minimum quantity required (default: 1)</param>
        /// <returns>True if the inventory contains the item in the specified quantity</returns>
        public bool HasItem(string itemId, int quantity = 1)
        {
            return GetItemQuantity(itemId) >= quantity;
        }

        /// <summary>
        /// Equips an item to the specified equipment slot
        /// </summary>
        /// <param name="itemId">The ID of the item to equip</param>
        /// <param name="slotType">The equipment slot type (weapon, armor, tool)</param>
        /// <returns>True if the item was equipped successfully</returns>
        public bool EquipItem(string itemId, string slotType = null)
        {
            if (string.IsNullOrEmpty(itemId))
                return false;

            // Check if we have the item in inventory
            if (!HasItem(itemId))
            {
                GD.PrintErr($"Cannot equip {itemId}: item not in inventory");
                return false;
            }

            var item = ItemDatabase.Instance?.GetItem(itemId);
            if (item == null)
                return false;

            // Determine slot type if not specified
            if (string.IsNullOrEmpty(slotType))
            {
                slotType = item.Type switch
                {
                    "weapon" => "weapon",
                    "armor" => "armor",
                    "tool" => "tool",
                    _ => "weapon" // Default to weapon slot
                };
            }

            if (!_equipmentSlots.ContainsKey(slotType))
            {
                GD.PrintErr($"Invalid equipment slot type: {slotType}");
                return false;
            }

            // Unequip current item if any
            var currentEquipped = _equipmentSlots[slotType];
            if (!currentEquipped.IsEmpty)
            {
                // Add the currently equipped item back to inventory
                AddItem(currentEquipped.ItemId, currentEquipped.Quantity, currentEquipped.Metadata);
            }

            // Remove one instance of the item from inventory
            if (!RemoveItem(itemId, 1))
            {
                GD.PrintErr($"Failed to remove {itemId} from inventory for equipping");
                return false;
            }

            // Equip the new item
            _equipmentSlots[slotType] = new InventorySlot(itemId, 1);

            // Emit equipped signal
            EmitSignal(SignalName.ItemEquipped, slotType, itemId);
            
            // Emit event bus event
            string previousItemId = currentEquipped.IsEmpty ? "" : currentEquipped.ItemId;
            EventBus.Instance?.EmitSignal(EventBus.SignalName.ItemEquipped, slotType, itemId, previousItemId);
            
            GD.Print($"Equipped {item.Name} to {slotType} slot");
            return true;
        }

        /// <summary>
        /// Gets the currently equipped item in the specified slot
        /// </summary>
        /// <param name="slotType">The equipment slot type</param>
        /// <returns>The equipped item slot, or null if empty</returns>
        public InventorySlot GetEquippedItem(string slotType)
        {
            if (string.IsNullOrEmpty(slotType) || !_equipmentSlots.ContainsKey(slotType))
                return null;

            var slot = _equipmentSlots[slotType];
            return slot.IsEmpty ? null : slot.Clone();
        }

        /// <summary>
        /// Gets the currently equipped weapon
        /// </summary>
        /// <returns>The equipped weapon slot, or null if no weapon equipped</returns>
        public InventorySlot GetEquippedWeapon()
        {
            return GetEquippedItem("weapon");
        }

        /// <summary>
        /// Clears all items from the inventory
        /// </summary>
        public void Clear()
        {
            _items.Clear();
            InitializeEquipmentSlots();
            GD.Print("Inventory cleared");
        }

        /// <summary>
        /// Gets the total number of unique items in the inventory
        /// </summary>
        public int GetUniqueItemCount()
        {
            var uniqueItems = new HashSet<string>();
            foreach (var slot in _items.Values)
            {
                if (!slot.IsEmpty)
                {
                    uniqueItems.Add(slot.ItemId);
                }
            }
            return uniqueItems.Count;
        }

        /// <summary>
        /// Gets the total number of item stacks in the inventory
        /// </summary>
        public int GetSlotCount()
        {
            return _items.Count(kvp => !kvp.Value.IsEmpty);
        }

        public override string ToString()
        {
            return $"Inventory: {GetSlotCount()} slots, {GetUniqueItemCount()} unique items";
        }

        // Endgame content support methods
        public void ClearInventory()
        {
            _items.Clear();
            GD.Print("Inventory cleared");
        }

        public void ExpandInventorySlots(int additionalSlots)
        {
            _maxSlots += additionalSlots;
            GD.Print($"Inventory expanded by {additionalSlots} slots");
        }

        private int _maxSlots = 50;

        public static Inventory Instance { get; internal set; }
    }
}