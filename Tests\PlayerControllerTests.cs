using Godot;
using System;
using System.Collections.Generic;

namespace SurvivalLooterShooter
{
    /// <summary>
    /// Integration tests for PlayerController functionality
    /// Tests complete player workflow including movement, combat, inventory, and survival stats
    /// </summary>
    public partial class PlayerControllerTests : Node
    {
        private PlayerController _playerController;
        private Inventory _inventory;
        private CraftingSystem _craftingSystem;
        private WeaponController _weaponController;
        private SurvivalStatsSystem _survivalStatsSystem;
        private SaveManager _saveManager;
        
        // Mock UI components for testing (not used in core tests)
        private MockInventoryUI _mockInventoryUI;
        private MockCraftingUI _mockCraftingUI;
        private MockSurvivalHUD _mockSurvivalHUD;
        
        private int _testsPassed = 0;
        private int _testsFailed = 0;
        private List<string> _testResults = new List<string>();

        public override void _Ready()
        {
            GD.Print("=== PlayerController Integration Tests ===");
            
            SetupTestEnvironment();
            RunAllTests();
            PrintTestResults();
        }

        /// <summary>
        /// Sets up the test environment with all required systems
        /// </summary>
        private void SetupTestEnvironment()
        {
            // Wait for ItemDatabase to be ready
            if (ItemDatabase.Instance == null)
            {
                CallDeferred(nameof(SetupTestEnvironmentDeferred));
                return;
            }
            
            SetupTestEnvironmentDeferred();
        }

        private void SetupTestEnvironmentDeferred()
        {
            // Create core systems
            _inventory = new Inventory();
            AddChild(_inventory);
            
            _craftingSystem = new CraftingSystem();
            AddChild(_craftingSystem);
            _craftingSystem.SetInventory(_inventory);
            
            _weaponController = new WeaponController();
            AddChild(_weaponController);
            _weaponController.Initialize(_inventory);
            
            _survivalStatsSystem = new SurvivalStatsSystem();
            AddChild(_survivalStatsSystem);
            
            _saveManager = new SaveManager();
            AddChild(_saveManager);
            
            // Create mock UI components
            CreateMockUIComponents();
            
            // Create PlayerController
            _playerController = new PlayerController();
            AddChild(_playerController);
            
            // Initialize PlayerController with core systems (UI components optional for testing)
            _playerController.Initialize(
                _inventory,
                _craftingSystem,
                _weaponController,
                _survivalStatsSystem,
                _saveManager
            );
            
            // Add test items to inventory
            AddTestItems();
            
            GD.Print("Test environment setup complete");
        }

        /// <summary>
        /// Creates mock UI components for testing
        /// </summary>
        private void CreateMockUIComponents()
        {
            // For testing purposes, we'll create minimal mock UI components
            // These are not passed to PlayerController since UI is optional for core testing
            _mockInventoryUI = new MockInventoryUI();
            AddChild(_mockInventoryUI);
            
            _mockCraftingUI = new MockCraftingUI();
            AddChild(_mockCraftingUI);
            
            _mockSurvivalHUD = new MockSurvivalHUD();
            AddChild(_mockSurvivalHUD);
        }

        /// <summary>
        /// Adds test items to the inventory
        /// </summary>
        private void AddTestItems()
        {
            _inventory.AddItem("assault_rifle", 1, new Dictionary<string, object> 
            { 
                ["durability"] = 100f, 
                ["current_ammo"] = 30 
            });
            _inventory.AddItem("rifle_ammo", 120);
            _inventory.AddItem("bandage", 5);
            _inventory.AddItem("canned_food", 3);
            _inventory.AddItem("energy_drink", 2);
            
            // Equip the weapon
            _inventory.EquipItem("assault_rifle", "weapon");
        }

        /// <summary>
        /// Runs all integration tests
        /// </summary>
        private void RunAllTests()
        {
            TestPlayerInitialization();
            TestPlayerMovement();
            TestCombatIntegration();
            TestInventoryIntegration();
            TestSurvivalStatsIntegration();
            TestConsumableUsage();
            TestSaveLoadIntegration();
            TestPlayerDeathAndRespawn();
            TestStaminaManagement();
            TestActionCooldowns();
        }

        /// <summary>
        /// Tests player controller initialization
        /// </summary>
        private void TestPlayerInitialization()
        {
            string testName = "Player Initialization";
            
            try
            {
                Assert(_playerController != null, "PlayerController should be created");
                Assert(_playerController.IsAlive, "Player should be alive initially");
                Assert(_playerController.GetPlayerPosition() != Vector2.Zero, "Player should have a valid position");
                
                PassTest(testName);
            }
            catch (Exception ex)
            {
                FailTest(testName, ex.Message);
            }
        }

        /// <summary>
        /// Tests player movement functionality
        /// </summary>
        private void TestPlayerMovement()
        {
            string testName = "Player Movement";
            
            try
            {
                Vector2 initialPosition = _playerController.GetPlayerPosition();
                
                // Simulate movement input (this would normally come from input events)
                // For testing, we'll check that the movement system is properly set up
                Assert(_playerController.MoveSpeed > 0, "Player should have movement speed");
                Assert(_playerController.SprintMultiplier > 1, "Sprint multiplier should be greater than 1");
                
                PassTest(testName);
            }
            catch (Exception ex)
            {
                FailTest(testName, ex.Message);
            }
        }

        /// <summary>
        /// Tests combat system integration
        /// </summary>
        private void TestCombatIntegration()
        {
            string testName = "Combat Integration";
            
            try
            {
                // Check that weapon is equipped
                var equippedWeapon = _inventory.GetEquippedWeapon();
                Assert(equippedWeapon != null, "Weapon should be equipped");
                Assert(equippedWeapon.ItemId == "assault_rifle", "Assault rifle should be equipped");
                
                // Check weapon controller integration
                Assert(_weaponController.CurrentWeapon != null, "WeaponController should have current weapon");
                Assert(_weaponController.CurrentWeapon.CurrentAmmo > 0, "Weapon should have ammo");
                
                PassTest(testName);
            }
            catch (Exception ex)
            {
                FailTest(testName, ex.Message);
            }
        }

        /// <summary>
        /// Tests inventory system integration
        /// </summary>
        private void TestInventoryIntegration()
        {
            string testName = "Inventory Integration";
            
            try
            {
                // Check inventory has test items
                Assert(_inventory.HasItem("rifle_ammo"), "Inventory should have rifle ammo");
                Assert(_inventory.HasItem("bandage"), "Inventory should have bandages");
                Assert(_inventory.GetItemQuantity("rifle_ammo") > 0, "Should have rifle ammo quantity");
                
                // Test adding and removing items
                int initialBandages = _inventory.GetItemQuantity("bandage");
                _inventory.AddItem("bandage", 2);
                Assert(_inventory.GetItemQuantity("bandage") == initialBandages + 2, "Should add bandages correctly");
                
                _inventory.RemoveItem("bandage", 1);
                Assert(_inventory.GetItemQuantity("bandage") == initialBandages + 1, "Should remove bandages correctly");
                
                PassTest(testName);
            }
            catch (Exception ex)
            {
                FailTest(testName, ex.Message);
            }
        }

        /// <summary>
        /// Tests survival stats system integration
        /// </summary>
        private void TestSurvivalStatsIntegration()
        {
            string testName = "Survival Stats Integration";
            
            try
            {
                // Check that survival stats are initialized
                Assert(_survivalStatsSystem.Health != null, "Health stat should exist");
                Assert(_survivalStatsSystem.Hunger != null, "Hunger stat should exist");
                Assert(_survivalStatsSystem.Thirst != null, "Thirst stat should exist");
                Assert(_survivalStatsSystem.Stamina != null, "Stamina stat should exist");
                
                // Check initial values
                Assert(_survivalStatsSystem.Health.CurrentValue > 0, "Health should be positive");
                Assert(_survivalStatsSystem.Stamina.CurrentValue > 0, "Stamina should be positive");
                
                PassTest(testName);
            }
            catch (Exception ex)
            {
                FailTest(testName, ex.Message);
            }
        }

        /// <summary>
        /// Tests consumable item usage
        /// </summary>
        private void TestConsumableUsage()
        {
            string testName = "Consumable Usage";
            
            try
            {
                // Get initial health value
                float initialHealth = _survivalStatsSystem.Health.CurrentValue;
                
                // Reduce health slightly to test healing
                _survivalStatsSystem.Health.ModifyValue(-10f);
                float reducedHealth = _survivalStatsSystem.Health.CurrentValue;
                
                // Check that we have bandages
                Assert(_inventory.HasItem("bandage"), "Should have bandages for testing");
                
                // The consumable usage would be tested through the PlayerController's UseConsumable method
                // For this test, we'll verify the systems are properly connected
                Assert(_survivalStatsSystem.ConsumeItem("bandage"), "Should be able to consume bandage");
                
                PassTest(testName);
            }
            catch (Exception ex)
            {
                FailTest(testName, ex.Message);
            }
        }

        /// <summary>
        /// Tests save/load system integration
        /// </summary>
        private void TestSaveLoadIntegration()
        {
            string testName = "Save/Load Integration";
            
            try
            {
                // Initialize SaveManager with all systems
                _saveManager.Initialize(null, _inventory, _survivalStatsSystem, _weaponController, _playerController);
                
                // Get initial player position
                Vector2 initialPosition = _playerController.GetPlayerPosition();
                
                // Modify player position
                Vector2 testPosition = new Vector2(100, 200);
                _playerController.SetPlayerPosition(testPosition);
                
                // Test that movement data can be collected
                var movementData = _playerController.GetMovementData();
                Assert(movementData != null, "Should be able to get movement data");
                Assert(movementData.Position == testPosition, "Movement data should have correct position");
                
                // Test that movement data can be loaded
                var newMovementData = new PlayerMovementData
                {
                    Position = new Vector2(300, 400),
                    Velocity = Vector2.Zero,
                    IsSprinting = false,
                    IsAlive = true
                };
                
                _playerController.LoadMovementData(newMovementData);
                Assert(_playerController.GetPlayerPosition() == newMovementData.Position, "Should load position correctly");
                
                PassTest(testName);
            }
            catch (Exception ex)
            {
                FailTest(testName, ex.Message);
            }
        }

        /// <summary>
        /// Tests player death and respawn mechanics
        /// </summary>
        private void TestPlayerDeathAndRespawn()
        {
            string testName = "Player Death and Respawn";
            
            try
            {
                // Ensure player is alive initially
                Assert(_playerController.IsAlive, "Player should be alive initially");
                
                // Simulate death by setting health to zero
                _survivalStatsSystem.Health.SetValue(0f);
                
                // Wait a frame for death processing
                CallDeferred(nameof(CheckDeathState));
                
                PassTest(testName);
            }
            catch (Exception ex)
            {
                FailTest(testName, ex.Message);
            }
        }

        private void CheckDeathState()
        {
            // After death, player should be marked as not alive
            // Note: The actual death mechanics depend on the SurvivalStatsSystem implementation
            GD.Print($"Player alive status after health depletion: {_playerController.IsAlive}");
        }

        /// <summary>
        /// Tests stamina management during actions
        /// </summary>
        private void TestStaminaManagement()
        {
            string testName = "Stamina Management";
            
            try
            {
                // Get initial stamina
                float initialStamina = _survivalStatsSystem.Stamina.CurrentValue;
                
                // Check that stamina costs are configured
                Assert(_playerController.ActionStaminaCost > 0, "Action stamina cost should be positive");
                Assert(_playerController.StaminaCostPerSecond > 0, "Stamina cost per second should be positive");
                
                // Test that actions would consume stamina (integration test)
                // The actual stamina consumption happens during action execution
                
                PassTest(testName);
            }
            catch (Exception ex)
            {
                FailTest(testName, ex.Message);
            }
        }

        /// <summary>
        /// Tests action cooldown system
        /// </summary>
        private void TestActionCooldowns()
        {
            string testName = "Action Cooldowns";
            
            try
            {
                // Test that the player controller has action validation
                // This is tested indirectly through the CanPerformAction method
                
                // Check that player can perform actions when alive and has stamina
                Assert(_playerController.IsAlive, "Player should be alive for actions");
                Assert(_survivalStatsSystem.Stamina.CurrentValue >= _playerController.ActionStaminaCost, 
                       "Player should have enough stamina for actions");
                
                PassTest(testName);
            }
            catch (Exception ex)
            {
                FailTest(testName, ex.Message);
            }
        }

        /// <summary>
        /// Prints the test results summary
        /// </summary>
        private void PrintTestResults()
        {
            GD.Print("\n=== PlayerController Integration Test Results ===");
            GD.Print($"Tests Passed: {_testsPassed}");
            GD.Print($"Tests Failed: {_testsFailed}");
            GD.Print($"Total Tests: {_testsPassed + _testsFailed}");
            
            if (_testResults.Count > 0)
            {
                GD.Print("\nDetailed Results:");
                foreach (string result in _testResults)
                {
                    GD.Print(result);
                }
            }
            
            if (_testsFailed == 0)
            {
                GD.Print("🎉 All PlayerController integration tests passed!");
            }
            else
            {
                GD.PrintErr($"❌ {_testsFailed} test(s) failed");
            }
            
            GD.Print("=== End PlayerController Integration Tests ===\n");
        }

        private void Assert(bool condition, string message)
        {
            if (!condition)
            {
                throw new Exception($"Assertion failed: {message}");
            }
        }

        private void PassTest(string testName)
        {
            _testsPassed++;
            _testResults.Add($"✅ {testName}: PASSED");
        }

        private void FailTest(string testName, string error)
        {
            _testsFailed++;
            _testResults.Add($"❌ {testName}: FAILED - {error}");
        }
    }

    /// <summary>
    /// Mock InventoryUI for testing - inherits from Control to match InventoryUI base class
    /// </summary>
    public partial class MockInventoryUI : Control
    {
        public new bool IsVisible { get; private set; } = false;
        
        public void Initialize(Inventory inventory)
        {
            // Mock initialization
        }
        
        public void ToggleInventory()
        {
            IsVisible = !IsVisible;
            GD.Print($"Mock InventoryUI toggled: {IsVisible}");
        }
        
        public void HideInventory()
        {
            IsVisible = false;
            GD.Print("Mock InventoryUI hidden");
        }
        
        public void UpdateInventoryDisplay()
        {
            GD.Print("Mock InventoryUI display updated");
        }
    }

    /// <summary>
    /// Mock CraftingUI for testing - inherits from Control to match CraftingUI base class
    /// </summary>
    public partial class MockCraftingUI : Control
    {
        public new bool IsVisible { get; private set; } = false;
        
        public void Initialize(CraftingSystem craftingSystem, Inventory inventory, InventoryUI inventoryUI)
        {
            // Mock initialization
        }
        
        public void ToggleCrafting()
        {
            IsVisible = !IsVisible;
            GD.Print($"Mock CraftingUI toggled: {IsVisible}");
        }
        
        public void HideCrafting()
        {
            IsVisible = false;
            GD.Print("Mock CraftingUI hidden");
        }
    }

    /// <summary>
    /// Mock SurvivalHUD for testing - inherits from Control to match SurvivalHUD base class
    /// </summary>
    public partial class MockSurvivalHUD : Control
    {
        public void Initialize(SurvivalStatsSystem survivalStatsSystem, WeaponController weaponController)
        {
            // Mock initialization
        }
        
        public void UpdateDisplay()
        {
            GD.Print("Mock SurvivalHUD display updated");
        }
    }
}