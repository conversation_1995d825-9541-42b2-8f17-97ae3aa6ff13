using Godot;
using System;
using System.Collections.Generic;
using System.Linq;

namespace SurvivalLooterShooter
{
    /// <summary>
    /// ResourceHarvestingSystem manages all resource nodes in the world
    /// Handles resource spawning, harvesting mechanics, skill bonuses, and resource management
    /// </summary>
    public partial class ResourceHarvestingSystem : Node
    {
        private static ResourceHarvestingSystem _instance;
        public static ResourceHarvestingSystem Instance => _instance;

        // Resource management
        private Dictionary<Vector2, List<ResourceNode>> _chunkResources = new Dictionary<Vector2, List<ResourceNode>>();
        private Dictionary<string, ResourceNodeTemplate> _resourceTemplates = new Dictionary<string, ResourceNodeTemplate>();
        private List<ResourceNode> _activeResourceNodes = new List<ResourceNode>();
        
        // Configuration
        [Export] public int ChunkSize { get; set; } = 512;
        [Export] public float ResourceSpawnRadius { get; set; } = 1000f;
        [Export] public int MaxResourcesPerChunk { get; set; } = 50;
        [Export] public float RegenerationCheckInterval { get; set; } = 30f;
        
        // Timers
        private float _regenerationTimer = 0f;
        private float _spawnCheckTimer = 0f;
        private const float SpawnCheckInterval = 5f;
        
        // Events
        [Signal]
        public delegate void ResourceNodeSpawnedEventHandler(string resourceId, Vector2 position);
        
        [Signal]
        public delegate void ResourceNodeDepletedEventHandler(string resourceId, Vector2 position);
        
        [Signal]
        public delegate void ResourceHarvestedEventHandler(string resourceId, int amount, Vector2 position, string playerId);

        public override void _Ready()
        {
            if (_instance == null)
            {
                _instance = this;
                InitializeResourceSystem();
                GD.Print("ResourceHarvestingSystem singleton initialized");
            }
            else
            {
                GD.PrintErr("Multiple ResourceHarvestingSystem instances detected! Removing duplicate.");
                QueueFree();
            }
        }

        /// <summary>
        /// Initializes the resource harvesting system
        /// </summary>
        private void InitializeResourceSystem()
        {
            // Load resource templates from item database
            LoadResourceTemplates();
            
            // Connect to world events
            ConnectToWorldEvents();
            
            // Add to resource system group
            AddToGroup("resource_system");
        }

        /// <summary>
        /// Loads resource templates from the item database
        /// </summary>
        private void LoadResourceTemplates()
        {
            var itemDatabase = ItemDatabase.Instance;
            if (itemDatabase == null)
            {
                GD.PrintErr("ItemDatabase not available for resource template loading");
                return;
            }

            // Get all resource items from the database
            var allItems = itemDatabase.GetAllItems();
            foreach (var item in allItems.Values)
            {
                if (item.Type == "resource")
                {
                    var template = CreateResourceTemplate(item);
                    _resourceTemplates[item.Id] = template;
                }
            }

            GD.Print($"Loaded {_resourceTemplates.Count} resource templates");
        }

        /// <summary>
        /// Creates a resource node template from an item definition
        /// </summary>
        private ResourceNodeTemplate CreateResourceTemplate(Item item)
        {
            var template = new ResourceNodeTemplate
            {
                ResourceId = item.Id,
                ResourceType = item.Metadata.ContainsKey("resource_type") ? 
                    item.Metadata["resource_type"].ToString() : "generic",
                RequiredTool = item.Metadata.ContainsKey("tool_required") ? 
                    item.Metadata["tool_required"].ToString() : "none",
                MinYield = 1,
                MaxYield = 5,
                HarvestTime = 2.0f,
                RegenerationTime = 300f, // 5 minutes default
                InteractionRange = 50f
            };

            // Customize based on resource type
            switch (template.ResourceType)
            {
                case "organic":
                    template.HarvestTime = 1.5f;
                    template.RegenerationTime = 180f; // 3 minutes
                    template.MaxYield = 3;
                    break;
                case "mineral":
                    template.HarvestTime = 3.0f;
                    template.RegenerationTime = 600f; // 10 minutes
                    template.MaxYield = 8;
                    break;
            }

            return template;
        }

        /// <summary>
        /// Connects to world manager events for resource spawning
        /// </summary>
        private void ConnectToWorldEvents()
        {
            // Connect to world manager if available
            var worldManager = WorldManager.Instance;
            if (worldManager != null)
            {
                // In a full implementation, we would connect to chunk loading events
                GD.Print("Connected to WorldManager for resource spawning");
            }
        }

        public override void _Process(double delta)
        {
            float deltaTime = (float)delta;
            
            UpdateRegenerationTimer(deltaTime);
            UpdateSpawnChecks(deltaTime);
            UpdateActiveResourceNodes(deltaTime);
        }

        /// <summary>
        /// Updates the regeneration timer for depleted resources
        /// </summary>
        private void UpdateRegenerationTimer(float delta)
        {
            _regenerationTimer += delta;
            
            if (_regenerationTimer >= RegenerationCheckInterval)
            {
                CheckResourceRegeneration();
                _regenerationTimer = 0f;
            }
        }

        /// <summary>
        /// Updates spawn checks for new resource nodes
        /// </summary>
        private void UpdateSpawnChecks(float delta)
        {
            _spawnCheckTimer += delta;
            
            if (_spawnCheckTimer >= SpawnCheckInterval)
            {
                CheckResourceSpawning();
                _spawnCheckTimer = 0f;
            }
        }

        /// <summary>
        /// Updates all active resource nodes
        /// </summary>
        private void UpdateActiveResourceNodes(float delta)
        {
            // Clean up destroyed nodes
            _activeResourceNodes.RemoveAll(node => !IsInstanceValid(node));
        }

        /// <summary>
        /// Checks for resource regeneration in all chunks
        /// </summary>
        private void CheckResourceRegeneration()
        {
            foreach (var chunkResources in _chunkResources.Values)
            {
                foreach (var resourceNode in chunkResources.ToList())
                {
                    if (IsInstanceValid(resourceNode) && resourceNode.IsDepleted)
                    {
                        // Resource regeneration is handled by the ResourceNode itself
                        // This method could be used for additional logic if needed
                    }
                }
            }
        }

        /// <summary>
        /// Checks if new resource nodes should be spawned near the player
        /// </summary>
        private void CheckResourceSpawning()
        {
            var playerController = GetTree().GetFirstNodeInGroup("player") as PlayerController;
            if (playerController == null) return;

            Vector2 playerPosition = playerController.GlobalPosition;
            Vector2 playerChunk = GetChunkCoordinates(playerPosition);

            // Check surrounding chunks for resource spawning
            for (int x = -1; x <= 1; x++)
            {
                for (int y = -1; y <= 1; y++)
                {
                    Vector2 chunkCoords = playerChunk + new Vector2(x, y);
                    EnsureChunkHasResources(chunkCoords);
                }
            }
        }

        /// <summary>
        /// Ensures a chunk has the appropriate number of resource nodes
        /// </summary>
        private void EnsureChunkHasResources(Vector2 chunkCoords)
        {
            if (!_chunkResources.ContainsKey(chunkCoords))
            {
                _chunkResources[chunkCoords] = new List<ResourceNode>();
            }

            var chunkResources = _chunkResources[chunkCoords];
            
            // Remove invalid nodes
            chunkResources.RemoveAll(node => !IsInstanceValid(node));

            // Check if we need to spawn more resources
            if (chunkResources.Count < MaxResourcesPerChunk)
            {
                SpawnResourcesInChunk(chunkCoords, MaxResourcesPerChunk - chunkResources.Count);
            }
        }

        /// <summary>
        /// Spawns resource nodes in a specific chunk
        /// </summary>
        private void SpawnResourcesInChunk(Vector2 chunkCoords, int count)
        {
            Vector2 chunkWorldPos = chunkCoords * ChunkSize;
            var worldManager = WorldManager.Instance;

            for (int i = 0; i < count; i++)
            {
                // Generate random position within chunk
                Vector2 localPos = new Vector2(
                    GD.RandRange(0, ChunkSize),
                    GD.RandRange(0, ChunkSize)
                );
                Vector2 worldPos = chunkWorldPos + localPos;

                // Get biome at this position
                BiomeType biome = worldManager?.GetBiomeAt(worldPos) ?? BiomeType.Plains;
                
                // Select appropriate resource for this biome
                string resourceId = SelectResourceForBiome(biome, worldPos);
                if (!string.IsNullOrEmpty(resourceId))
                {
                    SpawnResourceNode(resourceId, worldPos, chunkCoords);
                }
            }
        }

        /// <summary>
        /// Selects an appropriate resource type for the given biome
        /// </summary>
        private string SelectResourceForBiome(BiomeType biome, Vector2 position)
        {
            // Get biome data to determine resource spawns
            var biomeData = GetBiomeResourceSpawns(biome);
            if (biomeData.Count == 0) return null;

            // Weighted random selection based on density
            float totalWeight = biomeData.Sum(spawn => spawn.Density);
            float randomValue = GD.Randf() * totalWeight;
            float currentWeight = 0f;

            foreach (var spawn in biomeData)
            {
                currentWeight += spawn.Density;
                if (randomValue <= currentWeight)
                {
                    return spawn.ItemId;
                }
            }

            return biomeData.FirstOrDefault()?.ItemId;
        }

        /// <summary>
        /// Gets resource spawn data for a specific biome
        /// </summary>
        private List<BiomeResourceSpawn> GetBiomeResourceSpawns(BiomeType biome)
        {
            // This would normally come from biome data
            // For now, we'll use hardcoded values based on the biome
            return biome switch
            {
                BiomeType.Forest => new List<BiomeResourceSpawn>
                {
                    new BiomeResourceSpawn { ItemId = "wood", Density = 0.6f },
                    new BiomeResourceSpawn { ItemId = "berries", Density = 0.3f },
                    new BiomeResourceSpawn { ItemId = "mushrooms", Density = 0.2f }
                },
                BiomeType.Mountains => new List<BiomeResourceSpawn>
                {
                    new BiomeResourceSpawn { ItemId = "stone", Density = 0.7f },
                    new BiomeResourceSpawn { ItemId = "metal_ore", Density = 0.3f }
                },
                BiomeType.Plains => new List<BiomeResourceSpawn>
                {
                    new BiomeResourceSpawn { ItemId = "berries", Density = 0.2f },
                    new BiomeResourceSpawn { ItemId = "stone", Density = 0.3f }
                },
                _ => new List<BiomeResourceSpawn>
                {
                    new BiomeResourceSpawn { ItemId = "stone", Density = 0.5f }
                }
            };
        }

        /// <summary>
        /// Spawns a resource node at the specified position
        /// </summary>
        private void SpawnResourceNode(string resourceId, Vector2 worldPos, Vector2 chunkCoords)
        {
            if (!_resourceTemplates.ContainsKey(resourceId))
            {
                GD.PrintErr($"No template found for resource: {resourceId}");
                return;
            }

            var template = _resourceTemplates[resourceId];
            
            // Create resource node
            var resourceNode = new ResourceNode();
            resourceNode.ResourceId = resourceId;
            resourceNode.ResourceType = template.ResourceType;
            resourceNode.RequiredTool = template.RequiredTool;
            resourceNode.MinYield = template.MinYield;
            resourceNode.MaxYield = template.MaxYield;
            resourceNode.HarvestTime = template.HarvestTime;
            resourceNode.RegenerationTime = template.RegenerationTime;
            resourceNode.InteractionRange = template.InteractionRange;
            resourceNode.GlobalPosition = worldPos;

            // Add to scene
            GetTree().CurrentScene.AddChild(resourceNode);
            
            // Track the resource node
            _chunkResources[chunkCoords].Add(resourceNode);
            _activeResourceNodes.Add(resourceNode);

            // Connect to resource events
            resourceNode.ResourceHarvested += OnResourceHarvested;
            resourceNode.ResourceDepleted += OnResourceDepleted;
            resourceNode.ResourceRegenerated += OnResourceRegenerated;

            // Emit spawn event
            EmitSignal(SignalName.ResourceNodeSpawned, resourceId, worldPos);
            EventBus.Instance?.EmitSignal(EventBus.SignalName.GameStateChanged, "resource_spawned", resourceId);

            GD.Print($"Spawned {resourceId} resource node at {worldPos}");
        }

        /// <summary>
        /// Gets chunk coordinates for a world position
        /// </summary>
        private Vector2 GetChunkCoordinates(Vector2 worldPos)
        {
            return new Vector2(
                Mathf.FloorToInt(worldPos.X / ChunkSize),
                Mathf.FloorToInt(worldPos.Y / ChunkSize)
            );
        }

        /// <summary>
        /// Gets all resource nodes within a radius of a position
        /// </summary>
        public List<ResourceNode> GetResourceNodesInRadius(Vector2 position, float radius)
        {
            var nearbyNodes = new List<ResourceNode>();
            
            foreach (var resourceNode in _activeResourceNodes)
            {
                if (IsInstanceValid(resourceNode))
                {
                    float distance = position.DistanceTo(resourceNode.GlobalPosition);
                    if (distance <= radius)
                    {
                        nearbyNodes.Add(resourceNode);
                    }
                }
            }
            
            return nearbyNodes;
        }

        /// <summary>
        /// Gets the closest harvestable resource node to a position
        /// </summary>
        public ResourceNode GetClosestHarvestableResource(Vector2 position, float maxDistance = 100f)
        {
            ResourceNode closest = null;
            float closestDistance = float.MaxValue;

            foreach (var resourceNode in _activeResourceNodes)
            {
                if (IsInstanceValid(resourceNode) && !resourceNode.IsDepleted)
                {
                    float distance = position.DistanceTo(resourceNode.GlobalPosition);
                    if (distance <= maxDistance && distance < closestDistance)
                    {
                        closest = resourceNode;
                        closestDistance = distance;
                    }
                }
            }

            return closest;
        }

        /// <summary>
        /// Attempts to harvest a resource at the specified position
        /// </summary>
        public bool TryHarvestResourceAt(Vector2 position, PlayerController player, float interactionRange = 50f)
        {
            var nearbyResources = GetResourceNodesInRadius(position, interactionRange);
            var harvestableResource = nearbyResources.FirstOrDefault(r => !r.IsDepleted && !r.IsBeingHarvested);

            if (harvestableResource != null)
            {
                return harvestableResource.TryStartHarvesting(player);
            }

            return false;
        }

        #region Event Handlers

        /// <summary>
        /// Handles resource harvested events
        /// </summary>
        private void OnResourceHarvested(string resourceId, int amount, Vector2 position)
        {
            EmitSignal(SignalName.ResourceHarvested, resourceId, amount, position, "player");
            EventBus.Instance?.EmitSignal(EventBus.SignalName.PlayerActionPerformed, "resource_harvested", resourceId, 0f);
            
            GD.Print($"Resource harvested: {amount}x {resourceId} at {position}");
        }

        /// <summary>
        /// Handles resource depleted events
        /// </summary>
        private void OnResourceDepleted(string resourceId, Vector2 position)
        {
            EmitSignal(SignalName.ResourceNodeDepleted, resourceId, position);
            EventBus.Instance?.EmitSignal(EventBus.SignalName.GameStateChanged, "resource_depleted", resourceId);
            
            GD.Print($"Resource depleted: {resourceId} at {position}");
        }

        /// <summary>
        /// Handles resource regenerated events
        /// </summary>
        private void OnResourceRegenerated(string resourceId, Vector2 position)
        {
            EventBus.Instance?.EmitSignal(EventBus.SignalName.GameStateChanged, "resource_regenerated", resourceId);
            
            GD.Print($"Resource regenerated: {resourceId} at {position}");
        }

        #endregion

        #region Public Interface

        /// <summary>
        /// Forces regeneration of all depleted resources (for testing/admin)
        /// </summary>
        public void ForceRegenerateAllResources()
        {
            foreach (var resourceNode in _activeResourceNodes)
            {
                if (IsInstanceValid(resourceNode) && resourceNode.IsDepleted)
                {
                    resourceNode.ForceRegenerate();
                }
            }
            
            GD.Print("Forced regeneration of all depleted resources");
        }

        /// <summary>
        /// Clears all resource nodes from the world
        /// </summary>
        public void ClearAllResources()
        {
            foreach (var resourceNode in _activeResourceNodes.ToList())
            {
                if (IsInstanceValid(resourceNode))
                {
                    resourceNode.QueueFree();
                }
            }
            
            _activeResourceNodes.Clear();
            _chunkResources.Clear();
            
            GD.Print("Cleared all resource nodes");
        }

        /// <summary>
        /// Gets statistics about the resource system
        /// </summary>
        public ResourceSystemStats GetStats()
        {
            int totalNodes = _activeResourceNodes.Count(n => IsInstanceValid(n));
            int depletedNodes = _activeResourceNodes.Count(n => IsInstanceValid(n) && n.IsDepleted);
            int activeChunks = _chunkResources.Count(kvp => kvp.Value.Any(n => IsInstanceValid(n)));

            return new ResourceSystemStats
            {
                TotalResourceNodes = totalNodes,
                DepletedResourceNodes = depletedNodes,
                ActiveResourceNodes = totalNodes - depletedNodes,
                ActiveChunks = activeChunks,
                ResourceTypes = _resourceTemplates.Count
            };
        }

        #endregion

        public override void _ExitTree()
        {
            if (_instance == this)
            {
                _instance = null;
            }
        }

        internal void SetHarvestMultiplier(float multiplier)
        {
            throw new NotImplementedException();
        }
    }

    /// <summary>
    /// Template for creating resource nodes
    /// </summary>
    public class ResourceNodeTemplate
    {
        public string ResourceId { get; set; }
        public string ResourceType { get; set; }
        public string RequiredTool { get; set; }
        public int MinYield { get; set; }
        public int MaxYield { get; set; }
        public float HarvestTime { get; set; }
        public float RegenerationTime { get; set; }
        public float InteractionRange { get; set; }
    }

    /// <summary>
    /// Biome resource spawn configuration
    /// </summary>
    public class BiomeResourceSpawn
    {
        public string ItemId { get; set; }
        public float Density { get; set; }
    }

    /// <summary>
    /// Statistics about the resource system
    /// </summary>
    public class ResourceSystemStats
    {
        public int TotalResourceNodes { get; set; }
        public int DepletedResourceNodes { get; set; }
        public int ActiveResourceNodes { get; set; }
        public int ActiveChunks { get; set; }
        public int ResourceTypes { get; set; }
        // Endgame content support methods
        public void SetHarvestMultiplier(float multiplier)
        {
            _harvestMultiplier = multiplier;
            GD.Print($"Resource harvest multiplier set to: {multiplier}");
        }

        private float _harvestMultiplier = 1f;
    }

    /// <summary>
}