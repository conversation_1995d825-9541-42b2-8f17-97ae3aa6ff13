using Godot;
using System;
using System.Collections.Generic;
using System.Text.Json;

namespace SurvivalLooterShooter
{
    /// <summary>
    /// Test runner for data validation and error handling systems
    /// </summary>
    public partial class DataValidationTests : Node
    {
        private int _testsRun = 0;
        private int _testsPassed = 0;
        private int _testsFailed = 0;

        public override void _Ready()
        {
            Logger.LogInfo("DataValidationTests", "Starting data validation tests");
            
            // Run all validation tests
            TestItemValidation();
            TestRecipeValidation();
            TestSaveDataValidation();
            TestJsonValidation();
            TestFallbackData();
            TestLoggingSystem();
            
            // Print results
            Logger.LogInfo("DataValidationTests", $"Tests completed - Run: {_testsRun}, Passed: {_testsPassed}, Failed: {_testsFailed}");
            
            if (_testsFailed > 0)
            {
                Logger.LogError("DataValidationTests", $"{_testsFailed} tests failed!");
            }
            else
            {
                Logger.LogInfo("DataValidationTests", "All tests passed!");
            }
        }

        private void TestItemValidation()
        {
            Logger.LogInfo("DataValidationTests", "Testing item validation");

            // Test valid item
            var validItem = new Item("test_item", "Test Item", "consumable", 10);
            validItem.SetMetadata("health_restore", 25f);
            
            bool isValid = DataValidator.ValidateItem(validItem, out List<string> errors);
            AssertTrue("Valid item should pass validation", isValid);
            AssertTrue("Valid item should have no errors", errors.Count == 0);

            // Test item with missing name
            var itemMissingName = new Item("test_item_2", "", "consumable", 5);
            isValid = DataValidator.ValidateItem(itemMissingName, out errors);
            AssertTrue("Item with missing name should be corrected", !string.IsNullOrEmpty(itemMissingName.Name));

            // Test item with invalid max stack
            var itemInvalidStack = new Item("test_item_3", "Test Item 3", "material", -5);
            isValid = DataValidator.ValidateItem(itemInvalidStack, out errors);
            AssertTrue("Item with invalid max_stack should be corrected", itemInvalidStack.MaxStack > 0);

            // Test null item
            isValid = DataValidator.ValidateItem(null, out errors);
            AssertFalse("Null item should fail validation", isValid);
            AssertTrue("Null item should have errors", errors.Count > 0);

            // Test weapon validation
            var weapon = new Item("test_weapon", "Test Weapon", "weapon", 1);
            isValid = DataValidator.ValidateItem(weapon, out errors);
            AssertTrue("Weapon should have default damage metadata", weapon.Metadata.ContainsKey("damage"));
            AssertTrue("Weapon should have default ammo_type metadata", weapon.Metadata.ContainsKey("ammo_type"));
        }

        private void TestRecipeValidation()
        {
            Logger.LogInfo("DataValidationTests", "Testing recipe validation");

            // Test valid recipe
            var validRecipe = new Recipe("test_recipe", 
                new List<RecipeInput> { new RecipeInput("input_item", 2) },
                new RecipeOutput("output_item", 1),
                3.0f);
            
            bool isValid = DataValidator.ValidateRecipe(validRecipe, out List<string> errors);
            AssertTrue("Valid recipe should pass validation", isValid);

            // Test recipe with no inputs
            var recipeNoInputs = new Recipe("test_recipe_2", 
                new List<RecipeInput>(),
                new RecipeOutput("output_item", 1),
                1.0f);
            
            isValid = DataValidator.ValidateRecipe(recipeNoInputs, out errors);
            AssertFalse("Recipe with no inputs should fail validation", isValid);

            // Test recipe with invalid crafting time
            var recipeInvalidTime = new Recipe("test_recipe_3",
                new List<RecipeInput> { new RecipeInput("input_item", 1) },
                new RecipeOutput("output_item", 1),
                -1.0f);
            
            isValid = DataValidator.ValidateRecipe(recipeInvalidTime, out errors);
            AssertTrue("Recipe with invalid crafting time should be corrected", recipeInvalidTime.CraftingTime > 0);

            // Test null recipe
            isValid = DataValidator.ValidateRecipe(null, out errors);
            AssertFalse("Null recipe should fail validation", isValid);
        }

        private void TestSaveDataValidation()
        {
            Logger.LogInfo("DataValidationTests", "Testing save data validation");

            // Test valid save data
            var validSaveData = new GameSaveData
            {
                LastSaveTime = DateTime.UtcNow,
                SaveVersion = "1.0.0",
                PlayerPosition = Vector2.Zero,
                PlayerVelocity = Vector2.Zero
            };
            
            bool isValid = DataValidator.ValidateSaveData(validSaveData, out List<string> errors);
            AssertTrue("Valid save data should pass validation", isValid);

            // Test save data with null collections
            var saveDataNullCollections = new GameSaveData();
            saveDataNullCollections.InventoryItems = null;
            saveDataNullCollections.EquipmentSlots = null;
            saveDataNullCollections.SurvivalStats = null;
            
            isValid = DataValidator.ValidateSaveData(saveDataNullCollections, out errors);
            AssertTrue("Save data with null collections should be corrected", 
                saveDataNullCollections.InventoryItems != null &&
                saveDataNullCollections.EquipmentSlots != null &&
                saveDataNullCollections.SurvivalStats != null);

            // Test null save data
            isValid = DataValidator.ValidateSaveData(null, out errors);
            AssertFalse("Null save data should fail validation", isValid);
        }

        private void TestJsonValidation()
        {
            Logger.LogInfo("DataValidationTests", "Testing JSON validation");

            // Test valid JSON
            string validJson = "{\"test\": \"value\"}";
            bool isValid = DataValidator.ValidateJsonContent(validJson, out string error);
            AssertTrue("Valid JSON should pass validation", isValid);
            AssertTrue("Valid JSON should have no error", string.IsNullOrEmpty(error));

            // Test invalid JSON
            string invalidJson = "{\"test\": \"value\"";
            isValid = DataValidator.ValidateJsonContent(invalidJson, out error);
            AssertFalse("Invalid JSON should fail validation", isValid);
            AssertFalse("Invalid JSON should have error message", string.IsNullOrEmpty(error));

            // Test empty JSON
            isValid = DataValidator.ValidateJsonContent("", out error);
            AssertFalse("Empty JSON should fail validation", isValid);

            // Test null JSON
            isValid = DataValidator.ValidateJsonContent(null, out error);
            AssertFalse("Null JSON should fail validation", isValid);
        }

        private void TestFallbackData()
        {
            Logger.LogInfo("DataValidationTests", "Testing fallback data creation");

            // Test fallback items
            var fallbackItems = DataValidator.CreateFallbackItems();
            AssertTrue("Fallback items should not be empty", fallbackItems.Count > 0);
            
            foreach (var item in fallbackItems)
            {
                bool isValid = DataValidator.ValidateItem(item, out List<string> errors);
                AssertTrue($"Fallback item '{item.Id}' should be valid", isValid);
            }

            // Test fallback recipes
            var fallbackRecipes = DataValidator.CreateFallbackRecipes();
            AssertTrue("Fallback recipes should not be empty", fallbackRecipes.Count > 0);
            
            foreach (var recipe in fallbackRecipes)
            {
                bool isValid = DataValidator.ValidateRecipe(recipe, out List<string> errors);
                AssertTrue($"Fallback recipe '{recipe.Id}' should be valid", isValid);
            }
        }

        private void TestLoggingSystem()
        {
            Logger.LogInfo("DataValidationTests", "Testing logging system");

            // Test different log levels
            Logger.LogDebug("TestSystem", "Debug message");
            Logger.LogInfo("TestSystem", "Info message");
            Logger.LogWarning("TestSystem", "Warning message");
            Logger.LogError("TestSystem", "Error message");
            Logger.LogCritical("TestSystem", "Critical message");

            // Test exception logging
            try
            {
                throw new InvalidOperationException("Test exception");
            }
            catch (Exception ex)
            {
                Logger.LogException("TestSystem", ex, "Testing exception logging");
            }

            // Test log buffer
            var recentLogs = Logger.GetRecentLogs(5);
            AssertTrue("Should have recent logs", recentLogs.Count > 0);

            Logger.LogInfo("DataValidationTests", "Logging system test completed");
        }

        // Test assertion helpers
        private void AssertTrue(string message, bool condition)
        {
            _testsRun++;
            if (condition)
            {
                _testsPassed++;
                Logger.LogDebug("DataValidationTests", $"✓ {message}");
            }
            else
            {
                _testsFailed++;
                Logger.LogError("DataValidationTests", $"✗ {message}");
            }
        }

        private void AssertFalse(string message, bool condition)
        {
            AssertTrue(message, !condition);
        }
    }
}