using Godot;
using System;

namespace SurvivalLooterShooter
{
    public partial class GamePolishManager : Node
    {
        private static GamePolishManager _instance;
        public static GamePolishManager Instance => _instance;

        // UI References
        private Control _settingsMenu;
        private Control _achievementMenu;
        private Control _statisticsMenu;
        private Control _pauseMenu;

        // State
        private bool _gameInitialized = false;
        private bool _firstTimePlayer = true;

        public override void _Ready()
        {
            _instance = this;
            InitializePolishSystems();
            SetupUI();
            CheckFirstTimePlayer();
        }

        private void InitializePolishSystems()
        {
            // Ensure all polish systems are ready
            if (PerformanceProfiler.Instance == null)
                GD.PrintErr("PerformanceProfiler not initialized");

            if (GraphicsSettings.Instance == null)
                GD.PrintErr("GraphicsSettings not initialized");

            if (AudioManager.Instance == null)
                GD.PrintErr("AudioManager not initialized");

            if (AchievementSystem.Instance == null)
                GD.PrintErr("AchievementSystem not initialized");

            if (PlayerStatistics.Instance == null)
                GD.PrintErr("PlayerStatistics not initialized");

            if (TutorialSystem.Instance == null)
                GD.PrintErr("TutorialSystem not initialized");

            GD.Print("Game polish systems initialized successfully");
        }

        private void SetupUI()
        {
            // Load settings menu scene
            var settingsScene = GD.Load<PackedScene>("res://Scenes/SettingsMenu.tscn");
            if (settingsScene != null)
            {
                _settingsMenu = settingsScene.Instantiate<Control>();
                _settingsMenu.Visible = false;
                AddChild(_settingsMenu);
            }

            // Create achievement menu
            CreateAchievementMenu();

            // Create statistics menu
            CreateStatisticsMenu();

            // Create pause menu
            CreatePauseMenu();
        }

        private void CreateAchievementMenu()
        {
            _achievementMenu = new Control();
            _achievementMenu.SetAnchorsAndOffsetsPreset((Control.LayoutPreset)15);
            _achievementMenu.Visible = false;
            AddChild(_achievementMenu);

            // Background
            var background = new ColorRect();
            background.SetAnchorsAndOffsetsPreset((Control.LayoutPreset)15);
            background.Color = new Color(0, 0, 0, 0.8f);
            _achievementMenu.AddChild(background);

            // Panel
            var panel = new Panel();
            panel.SetAnchorsAndOffsetsPreset((Control.LayoutPreset)4);
            panel.Size = new Vector2(1000, 600);
            panel.Position = new Vector2(-500, -300);
            _achievementMenu.AddChild(panel);

            // Title
            var title = new Label();
            title.Text = "ACHIEVEMENTS";
            title.SetAnchorsAndOffsetsPreset((Control.LayoutPreset)0);
            title.Size = new Vector2(0, 50);
            title.HorizontalAlignment = HorizontalAlignment.Center;
            title.VerticalAlignment = VerticalAlignment.Center;
            panel.AddChild(title);

            // Achievement list
            var scrollContainer = new ScrollContainer();
            scrollContainer.SetAnchorsAndOffsetsPreset((Control.LayoutPreset)15);
            scrollContainer.OffsetTop = 60;
            scrollContainer.OffsetBottom = -50;
            scrollContainer.OffsetLeft = 20;
            scrollContainer.OffsetRight = -20;
            panel.AddChild(scrollContainer);

            var achievementList = new VBoxContainer();
            achievementList.Name = "AchievementList";
            scrollContainer.AddChild(achievementList);

            // Close button
            var closeButton = new Button();
            closeButton.Text = "Close";
            closeButton.SetAnchorsAndOffsetsPreset((Control.LayoutPreset)3);
            closeButton.Size = new Vector2(100, 40);
            closeButton.Position = new Vector2(-120, -50);
            closeButton.Pressed += () => _achievementMenu.Visible = false;
            panel.AddChild(closeButton);

            // Populate achievements
            PopulateAchievements();
        }

        private void PopulateAchievements()
        {
            if (AchievementSystem.Instance == null) return;

            var achievementList = _achievementMenu.GetNode<VBoxContainer>("Panel/ScrollContainer/AchievementList");

            // Clear existing children
            foreach (Node child in achievementList.GetChildren())
            {
                child.QueueFree();
            }

            // Add unlocked achievements
            var unlockedAchievements = AchievementSystem.Instance.GetUnlockedAchievements();
            foreach (var achievement in unlockedAchievements)
            {
                var achievementItem = CreateAchievementItem(achievement, true);
                achievementList.AddChild(achievementItem);
            }

            // Add locked achievements
            var lockedAchievements = AchievementSystem.Instance.GetLockedAchievements();
            foreach (var achievement in lockedAchievements)
            {
                var achievementItem = CreateAchievementItem(achievement, false);
                achievementList.AddChild(achievementItem);
            }
        }

        private Control CreateAchievementItem(AchievementSystem.Achievement achievement, bool unlocked)
        {
            var container = new HBoxContainer();
            container.CustomMinimumSize = new Vector2(0, 60);

            // Icon placeholder
            var icon = new ColorRect();
            icon.Size = new Vector2(50, 50);
            icon.Color = unlocked ? new Color(1.0f, 0.84f, 0.0f) : new Color(0.5f, 0.5f, 0.5f); // Gold : Gray
            container.AddChild(icon);

            // Text container
            var textContainer = new VBoxContainer();
            textContainer.SizeFlagsHorizontal = Control.SizeFlags.ExpandFill;
            container.AddChild(textContainer);

            // Achievement name
            var nameLabel = new Label();
            nameLabel.Text = achievement.Name;
            nameLabel.AddThemeColorOverride("font_color", unlocked ? new Color(1.0f, 1.0f, 1.0f) : new Color(0.5f, 0.5f, 0.5f)); // White : Gray
            textContainer.AddChild(nameLabel);

            // Achievement description
            var descLabel = new Label();
            descLabel.Text = achievement.Description;
            descLabel.AddThemeColorOverride("font_color", unlocked ? new Color(0.75f, 0.75f, 0.75f) : new Color(0.25f, 0.25f, 0.25f)); // LightGray : DarkGray
            textContainer.AddChild(descLabel);

            // Points
            var pointsLabel = new Label();
            pointsLabel.Text = $"{achievement.Points} pts";
            pointsLabel.HorizontalAlignment = HorizontalAlignment.Right;
            pointsLabel.SizeFlagsHorizontal = Control.SizeFlags.ShrinkEnd;
            container.AddChild(pointsLabel);

            return container;
        }

        private void CreateStatisticsMenu()
        {
            _statisticsMenu = new Control();
            _statisticsMenu.SetAnchorsAndOffsetsPreset((Control.LayoutPreset)15);
            _statisticsMenu.Visible = false;
            AddChild(_statisticsMenu);

            // Background
            var background = new ColorRect();
            background.SetAnchorsAndOffsetsPreset((Control.LayoutPreset)15);
            background.Color = new Color(0, 0, 0, 0.8f);
            _statisticsMenu.AddChild(background);

            // Panel
            var panel = new Panel();
            panel.SetAnchorsAndOffsetsPreset((Control.LayoutPreset)4);
            panel.Size = new Vector2(1000, 600);
            panel.Position = new Vector2(-500, -300);
            _statisticsMenu.AddChild(panel);

            // Title
            var title = new Label();
            title.Text = "PLAYER STATISTICS";
            title.SetAnchorsAndOffsetsPreset((Control.LayoutPreset)0);
            title.Size = new Vector2(0, 50);
            title.HorizontalAlignment = HorizontalAlignment.Center;
            title.VerticalAlignment = VerticalAlignment.Center;
            panel.AddChild(title);

            // Tab container for different stat categories
            var tabContainer = new TabContainer();
            tabContainer.SetAnchorsAndOffsetsPreset((Control.LayoutPreset)15); // FullRect
            tabContainer.OffsetTop = 60;
            tabContainer.OffsetBottom = -50;
            tabContainer.OffsetLeft = 20;
            tabContainer.OffsetRight = -20;
            panel.AddChild(tabContainer);

            // Create tabs for each stat category
            CreateStatTab(tabContainer, "Combat", PlayerStatistics.StatCategory.Combat);
            CreateStatTab(tabContainer, "Survival", PlayerStatistics.StatCategory.Survival);
            CreateStatTab(tabContainer, "Crafting", PlayerStatistics.StatCategory.Crafting);
            CreateStatTab(tabContainer, "Building", PlayerStatistics.StatCategory.Building);
            CreateStatTab(tabContainer, "Exploration", PlayerStatistics.StatCategory.Exploration);
            CreateStatTab(tabContainer, "Crafting", PlayerStatistics.StatCategory.Crafting);
            CreateStatTab(tabContainer, "Combat", PlayerStatistics.StatCategory.Combat);

            // Close button
            var closeButton = new Button();
            closeButton.Text = "Close";
            closeButton.SetAnchorsAndOffsetsPreset((Control.LayoutPreset)3);
            closeButton.Size = new Vector2(100, 40);
            closeButton.Position = new Vector2(-120, -50);
            closeButton.Pressed += () => _statisticsMenu.Visible = false;
            panel.AddChild(closeButton);
        }

        private void CreateStatTab(TabContainer parent, string tabName, PlayerStatistics.StatCategory category)
        {
            var scrollContainer = new ScrollContainer();
            scrollContainer.Name = tabName;
            parent.AddChild(scrollContainer);

            var statList = new VBoxContainer();
            scrollContainer.AddChild(statList);

            if (PlayerStatistics.Instance != null)
            {
                var stats = PlayerStatistics.Instance.GetStatsByCategory(category);
                foreach (var kvp in stats)
                {
                    var statItem = new HBoxContainer();

                    var nameLabel = new Label();
                    nameLabel.Text = kvp.Key;
                    nameLabel.SizeFlagsHorizontal = Control.SizeFlags.ExpandFill;
                    statItem.AddChild(nameLabel);

                    var valueLabel = new Label();
                    valueLabel.Text = kvp.Value.ToString("F1");
                    valueLabel.HorizontalAlignment = HorizontalAlignment.Right;
                    statItem.AddChild(valueLabel);

                    statList.AddChild(statItem);
                }
            }
        }

        private void CreatePauseMenu()
        {
            _pauseMenu = new Control();
            _pauseMenu.SetAnchorsAndOffsetsPreset((Control.LayoutPreset)15);
            _pauseMenu.Visible = false;
            AddChild(_pauseMenu);

            // Background
            var background = new ColorRect();
            background.SetAnchorsAndOffsetsPreset((Control.LayoutPreset)15);
            background.Color = new Color(0, 0, 0, 0.8f);
            _pauseMenu.AddChild(background);

            // Panel
            var panel = new Panel();
            panel.SetAnchorsAndOffsetsPreset((Control.LayoutPreset)8);
            panel.Size = new Vector2(400, 500);
            panel.Position = new Vector2(-200, -250);
            _pauseMenu.AddChild(panel);

            // Title
            var title = new Label();
            title.Text = "GAME PAUSED";
            title.SetAnchorsAndOffsetsPreset((Control.LayoutPreset)0);
            title.Size = new Vector2(0, 50);
            title.HorizontalAlignment = HorizontalAlignment.Center;
            title.VerticalAlignment = VerticalAlignment.Center;
            panel.AddChild(title);

            // Button container
            var buttonContainer = new VBoxContainer();
            buttonContainer.SetAnchorsAndOffsetsPreset((Control.LayoutPreset)8);
            buttonContainer.Size = new Vector2(200, 300);
            buttonContainer.Position = new Vector2(-100, -150);
            panel.AddChild(buttonContainer);

            // Resume button
            var resumeButton = new Button();
            resumeButton.Text = "Resume";
            resumeButton.Pressed += ResumeGame;
            buttonContainer.AddChild(resumeButton);

            // Settings button
            var settingsButton = new Button();
            settingsButton.Text = "Settings";
            settingsButton.Pressed += ShowSettings;
            buttonContainer.AddChild(settingsButton);

            // Achievements button
            var achievementsButton = new Button();
            achievementsButton.Text = "Achievements";
            achievementsButton.Pressed += ShowAchievements;
            buttonContainer.AddChild(achievementsButton);

            // Statistics button
            var statisticsButton = new Button();
            statisticsButton.Text = "Statistics";
            statisticsButton.Pressed += ShowStatistics;
            buttonContainer.AddChild(statisticsButton);

            // Tutorial button
            var tutorialButton = new Button();
            tutorialButton.Text = "Restart Tutorial";
            tutorialButton.Pressed += RestartTutorial;
            buttonContainer.AddChild(tutorialButton);

            // Quit button
            var quitButton = new Button();
            quitButton.Text = "Quit to Menu";
            quitButton.Pressed += QuitToMenu;
            buttonContainer.AddChild(quitButton);
        }

        private void CheckFirstTimePlayer()
        {
            _firstTimePlayer = !TutorialSystem.Instance?.IsTutorialCompleted() ?? true;

            if (_firstTimePlayer)
            {
                // Show welcome message and start tutorial
                CallDeferred(nameof(ShowWelcomeAndStartTutorial));
            }
            else
            {
                // Play background music for returning players
                AudioManager.Instance?.PlayMusic("main_theme");
            }
        }

        private void ShowWelcomeAndStartTutorial()
        {
            var welcomeDialog = new AcceptDialog();
            welcomeDialog.Title = "Welcome to Survival Looter Shooter!";
            welcomeDialog.DialogText = "Welcome! This appears to be your first time playing.\nWould you like to start the tutorial to learn the basics?";

            // Add custom buttons
            welcomeDialog.AddButton("Start Tutorial", false, "tutorial");
            welcomeDialog.AddButton("Skip Tutorial", false, "skip");

            welcomeDialog.CustomAction += (StringName action) =>
            {
                if (action == "tutorial")
                {
                    TutorialSystem.Instance?.StartTutorial();
                }
                else if (action == "skip")
                {
                    AudioManager.Instance?.PlayMusic("main_theme");
                }
            };

            welcomeDialog.PopupCentered();
            GetTree().CurrentScene.AddChild(welcomeDialog);
        }

        public override void _Input(InputEvent @event)
        {
            if (@event is InputEventKey keyEvent && keyEvent.Pressed)
            {
                switch (keyEvent.Keycode)
                {
                    case Key.Escape:
                        TogglePauseMenu();
                        break;
                    case Key.F1:
                        ShowSettings();
                        break;
                    case Key.F2:
                        ShowAchievements();
                        break;
                    case Key.F3:
                        // Performance profiler toggle (handled by PerformanceProfiler)
                        break;
                    case Key.F5:
                        ShowStatistics();
                        break;
                }
            }
        }

        private void TogglePauseMenu()
        {
            if (_pauseMenu.Visible)
            {
                ResumeGame();
            }
            else
            {
                PauseGame();
            }
        }

        private void PauseGame()
        {
            GetTree().Paused = true;
            _pauseMenu.Visible = true;
            AudioManager.Instance?.PlaySFX("ui_open");
        }

        private void ResumeGame()
        {
            GetTree().Paused = false;
            _pauseMenu.Visible = false;
            AudioManager.Instance?.PlaySFX("ui_close");
        }

        public void ShowSettings()
        {
            _settingsMenu?.Show();
            AudioManager.Instance?.PlaySFX("ui_open");
        }

        public void ShowAchievements()
        {
            PopulateAchievements(); // Refresh achievements
            _achievementMenu.Visible = true;
            AudioManager.Instance?.PlaySFX("ui_open");
        }

        public void ShowStatistics()
        {
            _statisticsMenu.Visible = true;
            AudioManager.Instance?.PlaySFX("ui_open");
        }

        private void RestartTutorial()
        {
            ResumeGame();
            TutorialSystem.Instance?.RestartTutorial();
        }

        private void QuitToMenu()
        {
            // Save game state before quitting
            SaveManager.Instance?.SaveGame();

            // Return to main menu
            GetTree().Paused = false;
            GetTree().ChangeSceneToFile("res://Scenes/MainMenu.tscn");
        }

        // Public methods for other systems to use
        public void ShowAchievementNotification(string achievementName, string description, int points)
        {
            // Create floating notification
            var notification = new Control();
            notification.SetAnchorsAndOffsetsPreset((Control.LayoutPreset)2);
            notification.Position = new Vector2(-320, 20);
            notification.Size = new Vector2(300, 80);
            AddChild(notification);

            var panel = new Panel();
            panel.SetAnchorsAndOffsetsPreset((Control.LayoutPreset)15);
            notification.AddChild(panel);

            var label = new Label();
            label.Text = $"Achievement Unlocked!\n{achievementName}\n+{points} points";
            label.SetAnchorsAndOffsetsPreset((Control.LayoutPreset)15);
            label.HorizontalAlignment = HorizontalAlignment.Center;
            label.VerticalAlignment = VerticalAlignment.Center;
            panel.AddChild(label);

            // Animate notification
            var tween = CreateTween();
            tween.TweenProperty(notification, "position:x", -20, 0.5f);
            tween.TweenInterval(3.0f);
            tween.TweenProperty(notification, "position:x", -320, 0.5f);
            tween.TweenCallback(Callable.From(() => notification.QueueFree()));

            AudioManager.Instance?.PlaySFX("achievement_unlock");
        }

        public void InitializeGameSystems()
        {
            if (_gameInitialized) return;

            // Initialize all game systems with polish features
            PerformanceProfiler.Instance?.StartProfile("GameInitialization");

            // Start background music
            AudioManager.Instance?.PlayMusic("main_theme", true, 2.0f);

            // Start ambient sounds based on current biome
            AudioManager.Instance?.PlayAmbient("forest_ambient", true, 0.7f);

            PerformanceProfiler.Instance?.EndProfile("GameInitialization");

            _gameInitialized = true;
            GD.Print("Game systems initialized with polish features");
        }
    }
}
