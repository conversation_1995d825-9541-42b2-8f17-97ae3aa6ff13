using Godot;
using System.Collections.Generic;

namespace SurvivalLooterShooter
{
    public partial class SkillSystemTests : Node
{
    private SkillManager _skillManager;
    private ExperienceTracker _experienceTracker;
    private bool _testsCompleted = false;
    
    public override void _Ready()
    {
        GD.Print("=== Starting Skill System Tests ===");
        
        // Initialize systems
        _skillManager = new SkillManager();
        AddChild(_skillManager);
        
        _experienceTracker = new ExperienceTracker();
        AddChild(_experienceTracker);
        
        // Wait a frame for initialization
        CallDeferred(nameof(RunTests));
    }
    
    private void RunTests()
    {
        TestSkillInitialization();
        TestExperienceGain();
        TestSkillLevelUp();
        TestSkillUnlocking();
        TestSkillBonuses();
        TestSaveLoad();
        
        _testsCompleted = true;
        GD.Print("=== Skill System Tests Completed ===");
    }
    
    private void TestSkillInitialization()
    {
        GD.Print("Testing skill initialization...");
        
        // Test that skills are properly initialized
        var combatSkills = _skillManager.GetSkillsByCategory(SkillType.Combat);
        Assert(combatSkills.Count > 0, "Combat skills should be initialized");
        
        var craftingSkills = _skillManager.GetSkillsByCategory(SkillType.Crafting);
        Assert(craftingSkills.Count > 0, "Crafting skills should be initialized");
        
        var survivalSkills = _skillManager.GetSkillsByCategory(SkillType.Survival);
        Assert(survivalSkills.Count > 0, "Survival skills should be initialized");
        
        var buildingSkills = _skillManager.GetSkillsByCategory(SkillType.Building);
        Assert(buildingSkills.Count > 0, "Building skills should be initialized");
        
        // Test specific skill retrieval
        var weaponProficiency = _skillManager.GetSkill("weapon_proficiency");
        Assert(weaponProficiency != null, "Weapon proficiency skill should exist");
        Assert(weaponProficiency.Level == 0, "Skills should start at level 0");
        Assert(weaponProficiency.Experience == 0f, "Skills should start with 0 experience");
        
        GD.Print("✓ Skill initialization tests passed");
    }
    
    private void TestExperienceGain()
    {
        GD.Print("Testing experience gain...");
        
        // Test direct experience gain
        var weaponSkill = _skillManager.GetSkill("weapon_proficiency");
        float initialXP = weaponSkill.Experience;
        
        _skillManager.GainExperience("weapon_proficiency", 50f);
        
        Assert(weaponSkill.Experience > initialXP, "Experience should increase after gaining XP");
        Assert(weaponSkill.Experience == initialXP + 50f, "Experience should increase by exact amount");
        
        // Test category experience gain
        var craftingSkill = _skillManager.GetSkill("crafting_efficiency");
        float initialCraftingXP = craftingSkill.Experience;
        
        _skillManager.GainExperience(SkillType.Crafting, 25f);
        
        // Note: Category XP only goes to unlocked skills, so this won't increase until skill is unlocked
        Assert(craftingSkill.Experience == initialCraftingXP, "Locked skills shouldn't gain category XP");
        
        GD.Print("✓ Experience gain tests passed");
    }
    
    private void TestSkillLevelUp()
    {
        GD.Print("Testing skill level up...");
        
        var weaponSkill = _skillManager.GetSkill("weapon_proficiency");
        int initialLevel = weaponSkill.Level;
        
        // Add enough XP to level up (first level requires 100 XP)
        float requiredXP = weaponSkill.GetTotalExperienceRequired(1);
        _skillManager.GainExperience("weapon_proficiency", requiredXP);
        
        Assert(weaponSkill.Level > initialLevel, "Skill should level up with sufficient XP");
        
        GD.Print("✓ Skill level up tests passed");
    }
    
    private void TestSkillUnlocking()
    {
        GD.Print("Testing skill unlocking...");
        
        // Give some skill points
        _skillManager.AddSkillPoints(5);
        
        int initialPoints = _skillManager.GetAvailableSkillPoints();
        Assert(initialPoints == 5, "Should have 5 skill points");
        
        // Unlock a skill
        bool unlocked = _skillManager.UnlockSkill("weapon_proficiency");
        Assert(unlocked, "Should be able to unlock weapon proficiency");
        
        int remainingPoints = _skillManager.GetAvailableSkillPoints();
        Assert(remainingPoints == initialPoints - 1, "Should consume 1 skill point");
        
        var skill = _skillManager.GetSkill("weapon_proficiency");
        Assert(skill.Level >= 1, "Unlocked skill should be at least level 1");
        
        // Test unlocking with insufficient points
        _skillManager.AddSkillPoints(-10); // Remove all points
        bool cannotUnlock = _skillManager.UnlockSkill("critical_hit");
        Assert(!cannotUnlock, "Should not be able to unlock without skill points");
        
        GD.Print("✓ Skill unlocking tests passed");
    }
    
    private void TestSkillBonuses()
    {
        GD.Print("Testing skill bonuses...");
        
        // Unlock and level up a skill
        _skillManager.AddSkillPoints(1);
        _skillManager.UnlockSkill("weapon_proficiency");
        
        var skill = _skillManager.GetSkill("weapon_proficiency");
        
        // Test bonus calculation
        float damageBonus = skill.GetBonusValue("damage_multiplier");
        Assert(damageBonus > 0f, "Unlocked skill should provide bonus");
        
        // Test skill manager bonus retrieval
        float managerBonus = _skillManager.GetSkillBonus("weapon_proficiency", "damage_multiplier");
        Assert(managerBonus == damageBonus, "Manager should return same bonus as skill");
        
        // Test category bonus
        float categoryBonus = _skillManager.GetCategoryBonus(SkillType.Combat, "damage_multiplier");
        Assert(categoryBonus >= damageBonus, "Category bonus should include skill bonus");
        
        GD.Print("✓ Skill bonus tests passed");
    }
    
    private void TestSaveLoad()
    {
        GD.Print("Testing save/load functionality...");
        
        // Set up some skill data
        _skillManager.AddSkillPoints(3);
        _skillManager.UnlockSkill("crafting_efficiency");
        _skillManager.GainExperience("crafting_efficiency", 150f);
        
        // Get save data
        var saveData = _skillManager.GetSaveData();
        Assert(saveData != null, "Should be able to get save data");
        Assert(saveData.ContainsKey("skills"), "Save data should contain skills");
        Assert(saveData.ContainsKey("available_skill_points"), "Save data should contain skill points");
        
        // Create new skill manager and load data
        var newSkillManager = new SkillManager();
        AddChild(newSkillManager);
        
        newSkillManager.LoadSaveData(saveData);
        
        // Verify loaded data
        var loadedSkill = newSkillManager.GetSkill("crafting_efficiency");
        var originalSkill = _skillManager.GetSkill("crafting_efficiency");
        
        Assert(loadedSkill.Level == originalSkill.Level, "Loaded skill level should match original");
        Assert(loadedSkill.Experience == originalSkill.Experience, "Loaded skill XP should match original");
        
        int loadedPoints = newSkillManager.GetAvailableSkillPoints();
        int originalPoints = _skillManager.GetAvailableSkillPoints();
        Assert(loadedPoints == originalPoints, "Loaded skill points should match original");
        
        newSkillManager.QueueFree();
        
        GD.Print("✓ Save/load tests passed");
    }
    
    private void Assert(bool condition, string message)
    {
        if (!condition)
        {
            GD.PrintErr($"ASSERTION FAILED: {message}");
        }
    }
    
    public override void _Input(InputEvent @event)
    {
        if (@event.IsActionPressed("ui_accept") && _testsCompleted)
        {
            GetTree().Quit();
        }
    }
}
}