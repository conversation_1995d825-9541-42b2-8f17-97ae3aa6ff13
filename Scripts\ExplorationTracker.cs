using Godot;
using System;
using System.Collections.Generic;
using System.Linq;

namespace SurvivalLooterShooter
{
    /// <summary>
    /// ExplorationTracker manages map revelation, area discovery, and exploration progress
    /// Tracks visited locations, discovered POIs, and provides exploration statistics
    /// </summary>
    public partial class ExplorationTracker : Node
    {
        private static ExplorationTracker _instance;
        public static ExplorationTracker Instance => _instance;

        // Exploration parameters
        [Export] public float RevealRadius { get; set; } = 50f; // Radius around player that gets revealed
        [Export] public float POIDiscoveryRadius { get; set; } = 25f; // Distance to discover POIs
        [Export] public int ExplorationGridSize { get; set; } = 16; // Size of exploration grid cells

        // Exploration data
        private HashSet<Vector2I> _exploredCells = new();
        private HashSet<Vector2I> _visitedChunks = new();
        private Dictionary<string, POIDiscoveryData> _discoveredPOIs = new();
        private Dictionary<BiomeType, float> _biomeExplorationProgress = new();
        
        // Player tracking
        private Vector2 _lastPlayerPosition = Vector2.Zero;
        private float _totalDistanceTraveled = 0f;
        private double _totalExplorationTime = 0;
        private double _sessionStartTime = 0;

        // Map revelation data
        private Dictionary<Vector2I, MapCell> _mapCells = new();
        private Dictionary<Vector2I, float> _fogOfWar = new(); // 0.0 = fully fogged, 1.0 = fully revealed

        // Events
        [Signal]
        public delegate void AreaExploredEventHandler(Vector2I cellCoords, BiomeType biome);
        
        [Signal]
        public delegate void BiomeDiscoveredEventHandler(BiomeType biome, Vector2 position);
        
        [Signal]
        public delegate void ExplorationMilestoneEventHandler(string milestone, float progress);

        public override void _Ready()
        {
            if (_instance == null)
            {
                _instance = this;
                AddToGroup("exploration_tracker");
                _sessionStartTime = Time.GetUnixTimeFromSystem();
                InitializeBiomeProgress();
                GD.Print("ExplorationTracker singleton initialized");
            }
            else
            {
                GD.PrintErr("Multiple ExplorationTracker instances detected! Removing duplicate.");
                QueueFree();
            }
        }

        /// <summary>
        /// Initializes biome exploration progress tracking
        /// </summary>
        private void InitializeBiomeProgress()
        {
            foreach (BiomeType biome in Enum.GetValues<BiomeType>())
            {
                _biomeExplorationProgress[biome] = 0f;
            }
        }

        /// <summary>
        /// Updates exploration based on player position
        /// </summary>
        public void UpdateExploration(Vector2 playerPosition)
        {
            // Update distance traveled
            if (_lastPlayerPosition != Vector2.Zero)
            {
                _totalDistanceTraveled += playerPosition.DistanceTo(_lastPlayerPosition);
            }
            _lastPlayerPosition = playerPosition;

            // Update exploration time
            _totalExplorationTime = Time.GetUnixTimeFromSystem() - _sessionStartTime;

            // Reveal map around player
            RevealMapAroundPosition(playerPosition);

            // Check for POI discoveries
            CheckForPOIDiscoveries(playerPosition);

            // Update chunk visitation
            Vector2I currentChunk = WorldManager.Instance?.WorldToChunkCoords(playerPosition) ?? Vector2I.Zero;
            if (!_visitedChunks.Contains(currentChunk))
            {
                _visitedChunks.Add(currentChunk);
                OnChunkFirstVisited(currentChunk, playerPosition);
            }
        }

        /// <summary>
        /// Reveals map cells around a position
        /// </summary>
        private void RevealMapAroundPosition(Vector2 position)
        {
            Vector2I centerCell = WorldToExplorationCell(position);
            int revealCells = Mathf.CeilToInt(RevealRadius / ExplorationGridSize);

            for (int x = -revealCells; x <= revealCells; x++)
            {
                for (int y = -revealCells; y <= revealCells; y++)
                {
                    Vector2I cellCoords = centerCell + new Vector2I(x, y);
                    Vector2 cellWorldPos = ExplorationCellToWorld(cellCoords);
                    
                    float distance = position.DistanceTo(cellWorldPos);
                    if (distance <= RevealRadius)
                    {
                        RevealMapCell(cellCoords, cellWorldPos, distance);
                    }
                }
            }
        }

        /// <summary>
        /// Reveals a specific map cell
        /// </summary>
        private void RevealMapCell(Vector2I cellCoords, Vector2 cellWorldPos, float distanceFromPlayer)
        {
            bool wasNewlyExplored = !_exploredCells.Contains(cellCoords);
            
            if (wasNewlyExplored)
            {
                _exploredCells.Add(cellCoords);
                
                // Get biome at this location
                BiomeType biome = WorldManager.Instance?.GetBiomeAt(cellWorldPos) ?? BiomeType.Plains;
                
                // Create map cell data
                var mapCell = new MapCell
                {
                    Coordinates = cellCoords,
                    WorldPosition = cellWorldPos,
                    Biome = biome,
                    DiscoveryTime = Time.GetUnixTimeFromSystem(),
                    IsFullyRevealed = distanceFromPlayer <= RevealRadius * 0.5f
                };
                
                _mapCells[cellCoords] = mapCell;
                
                // Update biome exploration progress
                UpdateBiomeExplorationProgress(biome);
                
                // Emit exploration event
                EmitSignal(SignalName.AreaExplored, cellCoords, (int)biome);
                EventBus.Instance?.EmitSignal(EventBus.SignalName.GameStateChanged, "area_explored", cellCoords.ToString());
            }

            // Update fog of war
            float revealStrength = 1.0f - Mathf.Clamp(distanceFromPlayer / RevealRadius, 0f, 1f);
            _fogOfWar[cellCoords] = Mathf.Max(_fogOfWar.GetValueOrDefault(cellCoords, 0f), revealStrength);
        }

        /// <summary>
        /// Updates biome exploration progress and checks for milestones
        /// </summary>
        private void UpdateBiomeExplorationProgress(BiomeType biome)
        {
            _biomeExplorationProgress[biome] += 1f;
            
            // Check for biome discovery milestone (first time exploring this biome)
            if (_biomeExplorationProgress[biome] == 1f)
            {
                EmitSignal(SignalName.BiomeDiscovered, (int)biome, _lastPlayerPosition);
                EmitSignal(SignalName.ExplorationMilestone, $"biome_discovered_{biome}", 1f);
                GD.Print($"New biome discovered: {biome}");
            }

            // Check for exploration milestones
            CheckExplorationMilestones();
        }

        /// <summary>
        /// Checks for various exploration milestones
        /// </summary>
        private void CheckExplorationMilestones()
        {
            int totalExploredCells = _exploredCells.Count;
            float totalDistance = _totalDistanceTraveled;
            int discoveredPOIs = _discoveredPOIs.Count;
            int visitedChunks = _visitedChunks.Count;

            // Distance milestones
            CheckDistanceMilestone(1000f, "distance_1km");
            CheckDistanceMilestone(5000f, "distance_5km");
            CheckDistanceMilestone(10000f, "distance_10km");

            // Exploration area milestones
            CheckExplorationMilestone(100, "explored_100_areas");
            CheckExplorationMilestone(500, "explored_500_areas");
            CheckExplorationMilestone(1000, "explored_1000_areas");

            // POI discovery milestones
            CheckPOIMilestone(5, "discovered_5_pois");
            CheckPOIMilestone(10, "discovered_10_pois");
            CheckPOIMilestone(25, "discovered_25_pois");

            // Chunk exploration milestones
            CheckChunkMilestone(10, "visited_10_chunks");
            CheckChunkMilestone(50, "visited_50_chunks");
            CheckChunkMilestone(100, "visited_100_chunks");
        }

        /// <summary>
        /// Checks and emits distance milestone if reached
        /// </summary>
        private void CheckDistanceMilestone(float threshold, string milestoneId)
        {
            if (_totalDistanceTraveled >= threshold && !HasMilestone(milestoneId))
            {
                EmitSignal(SignalName.ExplorationMilestone, milestoneId, _totalDistanceTraveled / threshold);
                SetMilestone(milestoneId);
            }
        }

        /// <summary>
        /// Checks and emits exploration milestone if reached
        /// </summary>
        private void CheckExplorationMilestone(int threshold, string milestoneId)
        {
            if (_exploredCells.Count >= threshold && !HasMilestone(milestoneId))
            {
                EmitSignal(SignalName.ExplorationMilestone, milestoneId, (float)_exploredCells.Count / threshold);
                SetMilestone(milestoneId);
            }
        }

        /// <summary>
        /// Checks and emits POI milestone if reached
        /// </summary>
        private void CheckPOIMilestone(int threshold, string milestoneId)
        {
            if (_discoveredPOIs.Count >= threshold && !HasMilestone(milestoneId))
            {
                EmitSignal(SignalName.ExplorationMilestone, milestoneId, (float)_discoveredPOIs.Count / threshold);
                SetMilestone(milestoneId);
            }
        }

        /// <summary>
        /// Checks and emits chunk milestone if reached
        /// </summary>
        private void CheckChunkMilestone(int threshold, string milestoneId)
        {
            if (_visitedChunks.Count >= threshold && !HasMilestone(milestoneId))
            {
                EmitSignal(SignalName.ExplorationMilestone, milestoneId, (float)_visitedChunks.Count / threshold);
                SetMilestone(milestoneId);
            }
        }

        /// <summary>
        /// Checks for POI discoveries near player position
        /// </summary>
        private void CheckForPOIDiscoveries(Vector2 playerPosition)
        {
            if (WorldManager.Instance == null) return;

            // Get current chunk and check for POIs
            Vector2I currentChunk = WorldManager.Instance.WorldToChunkCoords(playerPosition);
            
            // Check POIs in current and adjacent chunks
            for (int x = -1; x <= 1; x++)
            {
                for (int y = -1; y <= 1; y++)
                {
                    Vector2I chunkCoords = currentChunk + new Vector2I(x, y);
                    CheckPOIsInChunk(chunkCoords, playerPosition);
                }
            }
        }

        /// <summary>
        /// Checks for POI discoveries in a specific chunk
        /// </summary>
        private void CheckPOIsInChunk(Vector2I chunkCoords, Vector2 playerPosition)
        {
            // This would integrate with POIGenerator to get POIs in chunk
            // For now, we'll simulate this check
            var poiGenerator = GetNode<POIGenerator>("/root/POIGenerator");
            if (poiGenerator == null) return;

            var poisInChunk = poiGenerator.GetPOIsInChunk(chunkCoords);
            
            foreach (var poi in poisInChunk)
            {
                if (!poi.IsDiscovered && 
                    playerPosition.DistanceTo(poi.WorldPosition) <= POIDiscoveryRadius)
                {
                    DiscoverPOI(poi);
                }
            }
        }

        /// <summary>
        /// Marks a POI as discovered
        /// </summary>
        private void DiscoverPOI(PointOfInterest poi)
        {
            if (_discoveredPOIs.ContainsKey(poi.Id)) return;

            var discoveryData = new POIDiscoveryData
            {
                POI = poi,
                DiscoveryTime = Time.GetUnixTimeFromSystem(),
                DiscoveryPosition = _lastPlayerPosition
            };

            _discoveredPOIs[poi.Id] = discoveryData;
            
            // Mark POI as discovered in the POI system
            var poiGenerator = GetNode<POIGenerator>("/root/POIGenerator");
            poiGenerator?.DiscoverPOI(poi.Id, _lastPlayerPosition);

            GD.Print($"POI discovered: {poi.Name} at {poi.WorldPosition}");
        }

        /// <summary>
        /// Called when a chunk is visited for the first time
        /// </summary>
        private void OnChunkFirstVisited(Vector2I chunkCoords, Vector2 playerPosition)
        {
            BiomeType dominantBiome = WorldManager.Instance?.GetBiomeAt(playerPosition) ?? BiomeType.Plains;
            GD.Print($"First visit to chunk {chunkCoords} in {dominantBiome} biome");
            
            EventBus.Instance?.EmitSignal(EventBus.SignalName.GameStateChanged, "chunk_first_visit", chunkCoords.ToString());
        }

        /// <summary>
        /// Converts world position to exploration cell coordinates
        /// </summary>
        private Vector2I WorldToExplorationCell(Vector2 worldPosition)
        {
            return new Vector2I(
                Mathf.FloorToInt(worldPosition.X / ExplorationGridSize),
                Mathf.FloorToInt(worldPosition.Y / ExplorationGridSize)
            );
        }

        /// <summary>
        /// Converts exploration cell coordinates to world position (cell center)
        /// </summary>
        private Vector2 ExplorationCellToWorld(Vector2I cellCoords)
        {
            return new Vector2(
                cellCoords.X * ExplorationGridSize + ExplorationGridSize * 0.5f,
                cellCoords.Y * ExplorationGridSize + ExplorationGridSize * 0.5f
            );
        }

        /// <summary>
        /// Gets exploration progress for a specific biome (0.0 to 1.0)
        /// </summary>
        public float GetBiomeExplorationProgress(BiomeType biome)
        {
            float explored = _biomeExplorationProgress.GetValueOrDefault(biome, 0f);
            // Normalize based on expected exploration (this could be configurable)
            float maxExpected = 100f; // Expected cells to explore for "complete" biome exploration
            return Mathf.Clamp(explored / maxExpected, 0f, 1f);
        }

        /// <summary>
        /// Gets overall exploration progress (0.0 to 1.0)
        /// </summary>
        public float GetOverallExplorationProgress()
        {
            float totalProgress = _biomeExplorationProgress.Values.Sum();
            float maxExpected = Enum.GetValues<BiomeType>().Length * 100f;
            return Mathf.Clamp(totalProgress / maxExpected, 0f, 1f);
        }

        /// <summary>
        /// Gets exploration statistics
        /// </summary>
        public ExplorationStats GetExplorationStats()
        {
            return new ExplorationStats
            {
                TotalAreasExplored = _exploredCells.Count,
                TotalDistanceTraveled = _totalDistanceTraveled,
                TotalExplorationTime = _totalExplorationTime,
                ChunksVisited = _visitedChunks.Count,
                POIsDiscovered = _discoveredPOIs.Count,
                BiomesDiscovered = _biomeExplorationProgress.Count(kvp => kvp.Value > 0),
                OverallProgress = GetOverallExplorationProgress()
            };
        }

        /// <summary>
        /// Gets discovered POIs
        /// </summary>
        public List<POIDiscoveryData> GetDiscoveredPOIs()
        {
            return new List<POIDiscoveryData>(_discoveredPOIs.Values);
        }

        /// <summary>
        /// Gets explored map cells
        /// </summary>
        public List<MapCell> GetExploredCells()
        {
            return new List<MapCell>(_mapCells.Values);
        }

        /// <summary>
        /// Gets fog of war value for a position (0.0 = fully fogged, 1.0 = fully revealed)
        /// </summary>
        public float GetFogOfWarAt(Vector2 worldPosition)
        {
            Vector2I cellCoords = WorldToExplorationCell(worldPosition);
            return _fogOfWar.GetValueOrDefault(cellCoords, 0f);
        }

        /// <summary>
        /// Checks if a position has been explored
        /// </summary>
        public bool IsPositionExplored(Vector2 worldPosition)
        {
            Vector2I cellCoords = WorldToExplorationCell(worldPosition);
            return _exploredCells.Contains(cellCoords);
        }

        /// <summary>
        /// Gets save data for exploration system
        /// </summary>
        public ExplorationSaveData GetSaveData()
        {
            return new ExplorationSaveData
            {
                ExploredCells = new List<Vector2I>(_exploredCells),
                VisitedChunks = new List<Vector2I>(_visitedChunks),
                DiscoveredPOIs = _discoveredPOIs.Keys.ToList(),
                BiomeExplorationProgress = new Dictionary<BiomeType, float>(_biomeExplorationProgress),
                TotalDistanceTraveled = _totalDistanceTraveled,
                TotalExplorationTime = _totalExplorationTime,
                FogOfWar = new Dictionary<Vector2I, float>(_fogOfWar),
                CompletedMilestones = new List<string>(_completedMilestones)
            };
        }

        /// <summary>
        /// Loads save data for exploration system
        /// </summary>
        public void LoadSaveData(ExplorationSaveData saveData)
        {
            _exploredCells = new HashSet<Vector2I>(saveData.ExploredCells);
            _visitedChunks = new HashSet<Vector2I>(saveData.VisitedChunks);
            _biomeExplorationProgress = new Dictionary<BiomeType, float>(saveData.BiomeExplorationProgress);
            _totalDistanceTraveled = saveData.TotalDistanceTraveled;
            _totalExplorationTime = saveData.TotalExplorationTime;
            _fogOfWar = new Dictionary<Vector2I, float>(saveData.FogOfWar);
            _completedMilestones = new HashSet<string>(saveData.CompletedMilestones);

            // Rebuild map cells from explored cells
            foreach (var cellCoords in _exploredCells)
            {
                Vector2 worldPos = ExplorationCellToWorld(cellCoords);
                BiomeType biome = WorldManager.Instance?.GetBiomeAt(worldPos) ?? BiomeType.Plains;
                
                _mapCells[cellCoords] = new MapCell
                {
                    Coordinates = cellCoords,
                    WorldPosition = worldPos,
                    Biome = biome,
                    DiscoveryTime = 0, // We don't save discovery time, so use 0
                    IsFullyRevealed = _fogOfWar.GetValueOrDefault(cellCoords, 0f) > 0.8f
                };
            }
        }

        // Milestone tracking
        private HashSet<string> _completedMilestones = new();

        private bool HasMilestone(string milestoneId)
        {
            return _completedMilestones.Contains(milestoneId);
        }

        private void SetMilestone(string milestoneId)
        {
            _completedMilestones.Add(milestoneId);
        }

        public override void _ExitTree()
        {
            if (_instance == this)
            {
                _instance = null;
            }
        }
    }

    /// <summary>
    /// Data structure for POI discovery information
    /// </summary>
    [Serializable]
    public class POIDiscoveryData
    {
        public PointOfInterest POI { get; set; }
        public double DiscoveryTime { get; set; }
        public Vector2 DiscoveryPosition { get; set; }
    }

    /// <summary>
    /// Data structure for map cell information
    /// </summary>
    [Serializable]
    public class MapCell
    {
        public Vector2I Coordinates { get; set; }
        public Vector2 WorldPosition { get; set; }
        public BiomeType Biome { get; set; }
        public double DiscoveryTime { get; set; }
        public bool IsFullyRevealed { get; set; }
    }

    /// <summary>
    /// Exploration statistics
    /// </summary>
    [Serializable]
    public class ExplorationStats
    {
        public int TotalAreasExplored { get; set; }
        public float TotalDistanceTraveled { get; set; }
        public double TotalExplorationTime { get; set; }
        public int ChunksVisited { get; set; }
        public int POIsDiscovered { get; set; }
        public int BiomesDiscovered { get; set; }
        public float OverallProgress { get; set; }
    }

    /// <summary>
    /// Save data for exploration system
    /// </summary>
    [Serializable]
    public class ExplorationSaveData
    {
        public List<Vector2I> ExploredCells { get; set; } = new();
        public List<Vector2I> VisitedChunks { get; set; } = new();
        public List<string> DiscoveredPOIs { get; set; } = new();
        public Dictionary<BiomeType, float> BiomeExplorationProgress { get; set; } = new();
        public float TotalDistanceTraveled { get; set; }
        public double TotalExplorationTime { get; set; }
        public Dictionary<Vector2I, float> FogOfWar { get; set; } = new();
        public List<string> CompletedMilestones { get; set; } = new();
    }
}