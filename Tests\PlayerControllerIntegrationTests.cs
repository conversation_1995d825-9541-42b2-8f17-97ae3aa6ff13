using Godot;
using System;
using System.Collections.Generic;

namespace SurvivalLooterShooter
{
    /// <summary>
    /// Comprehensive integration tests for PlayerController complete workflow
    /// Tests all aspects of player controller integration with game systems
    /// Requirements: 6.6, 3.1, 4.1
    /// </summary>
    public partial class PlayerControllerIntegrationTests : Node
    {
        private PlayerController _playerController;
        private Inventory _inventory;
        private CraftingSystem _craftingSystem;
        private WeaponController _weaponController;
        private SurvivalStatsSystem _survivalStatsSystem;
        private SaveManager _saveManager;
        
        private int _testsPassed = 0;
        private int _testsFailed = 0;
        private List<string> _testResults = new List<string>();

        public override void _Ready()
        {
            GD.Print("=== PlayerController Complete Workflow Integration Tests ===");
            
            // Wait for ItemDatabase to be ready
            if (ItemDatabase.Instance == null)
            {
                CallDeferred(nameof(SetupAndRunTests));
            }
            else
            {
                SetupAndRunTests();
            }
        }

        private void SetupAndRunTests()
        {
            SetupTestEnvironment();
            RunCompleteWorkflowTests();
            PrintTestResults();
        }

        /// <summary>
        /// Sets up the complete test environment with all systems
        /// </summary>
        private void SetupTestEnvironment()
        {
            // Create all core systems
            _inventory = new Inventory();
            AddChild(_inventory);
            
            _craftingSystem = new CraftingSystem();
            AddChild(_craftingSystem);
            _craftingSystem.SetInventory(_inventory);
            
            _weaponController = new WeaponController();
            AddChild(_weaponController);
            _weaponController.Initialize(_inventory);
            
            _survivalStatsSystem = new SurvivalStatsSystem();
            AddChild(_survivalStatsSystem);
            
            _saveManager = new SaveManager();
            AddChild(_saveManager);
            
            // Create PlayerController
            _playerController = new PlayerController();
            AddChild(_playerController);
            
            // Initialize PlayerController with all systems
            _playerController.Initialize(
                _inventory,
                _craftingSystem,
                _weaponController,
                _survivalStatsSystem,
                _saveManager
            );
            
            // Initialize SaveManager with PlayerController
            _saveManager.Initialize(null, _inventory, _survivalStatsSystem, _weaponController, _playerController);
            
            // Add comprehensive test items
            SetupTestItems();
            
            GD.Print("Complete test environment setup finished");
        }

        /// <summary>
        /// Sets up comprehensive test items for workflow testing
        /// </summary>
        private void SetupTestItems()
        {
            // Add weapons and ammo
            _inventory.AddItem("assault_rifle", 1, new Dictionary<string, object> 
            { 
                ["durability"] = 100f, 
                ["current_ammo"] = 30 
            });
            _inventory.AddItem("rifle_ammo", 150);
            
            // Add crafting materials
            _inventory.AddItem("cloth", 20);
            _inventory.AddItem("alcohol", 10);
            _inventory.AddItem("metal_scrap", 15);
            _inventory.AddItem("gunpowder", 8);
            
            // Add consumables
            _inventory.AddItem("bandage", 8);
            _inventory.AddItem("canned_food", 5);
            _inventory.AddItem("energy_drink", 4);
            _inventory.AddItem("herbal_tea", 6);
            _inventory.AddItem("mre", 2);
            
            // Equip weapon
            _inventory.EquipItem("assault_rifle", "weapon");
        }

        /// <summary>
        /// Runs comprehensive workflow integration tests
        /// </summary>
        private void RunCompleteWorkflowTests()
        {
            TestSystemCoordination();
            TestInputHandlingIntegration();
            TestCombatWorkflow();
            TestInventoryManagementWorkflow();
            TestCraftingWorkflow();
            TestSurvivalStatsWorkflow();
            TestSaveLoadWorkflow();
            TestPlayerDeathRecoveryWorkflow();
            TestStaminaIntegrationWorkflow();
            TestCompleteGameplayLoop();
        }

        /// <summary>
        /// Tests that PlayerController properly coordinates all systems
        /// Requirement 6.6: PlayerController coordinates all systems
        /// </summary>
        private void TestSystemCoordination()
        {
            string testName = "System Coordination";
            
            try
            {
                // Verify all systems are properly initialized
                Assert(_playerController != null, "PlayerController should be initialized");
                Assert(_inventory != null, "Inventory system should be accessible");
                Assert(_craftingSystem != null, "Crafting system should be accessible");
                Assert(_weaponController != null, "Weapon controller should be accessible");
                Assert(_survivalStatsSystem != null, "Survival stats system should be accessible");
                Assert(_saveManager != null, "Save manager should be accessible");
                
                // Verify PlayerController is alive and functional
                Assert(_playerController.IsAlive, "Player should be alive initially");
                Assert(_playerController.GetPlayerPosition() != Vector2.Zero, "Player should have valid position");
                
                // Verify system interconnections
                Assert(_weaponController.CurrentWeapon != null, "Weapon should be equipped through inventory");
                Assert(_survivalStatsSystem.Health.CurrentValue > 0, "Health should be initialized");
                Assert(_survivalStatsSystem.Stamina.CurrentValue > 0, "Stamina should be initialized");
                
                PassTest(testName);
            }
            catch (Exception ex)
            {
                FailTest(testName, ex.Message);
            }
        }

        /// <summary>
        /// Tests input handling integration across all systems
        /// Requirement 6.6: Input handling for inventory, crafting, combat, and interaction
        /// </summary>
        private void TestInputHandlingIntegration()
        {
            string testName = "Input Handling Integration";
            
            try
            {
                // Test that PlayerController has proper input configuration
                Assert(_playerController.MoveSpeed > 0, "Movement speed should be configured");
                Assert(_playerController.SprintMultiplier > 1, "Sprint multiplier should be configured");
                Assert(_playerController.ActionStaminaCost > 0, "Action stamina cost should be configured");
                Assert(_playerController.StaminaCostPerSecond > 0, "Sprint stamina cost should be configured");
                
                // Test action validation system
                bool canPerformAction = _playerController.IsAlive && 
                                       _survivalStatsSystem.Stamina.CurrentValue >= _playerController.ActionStaminaCost;
                Assert(canPerformAction, "Player should be able to perform actions when alive with stamina");
                
                PassTest(testName);
            }
            catch (Exception ex)
            {
                FailTest(testName, ex.Message);
            }
        }

        /// <summary>
        /// Tests complete combat workflow integration
        /// Requirement 3.1: Combat system integration
        /// </summary>
        private void TestCombatWorkflow()
        {
            string testName = "Combat Workflow Integration";
            
            try
            {
                // Verify weapon is equipped and ready
                var equippedWeapon = _inventory.GetEquippedWeapon();
                Assert(equippedWeapon != null, "Weapon should be equipped");
                Assert(equippedWeapon.ItemId == "assault_rifle", "Correct weapon should be equipped");
                
                // Verify weapon controller integration
                Assert(_weaponController.CurrentWeapon != null, "WeaponController should have current weapon");
                Assert(_weaponController.CurrentWeapon.CurrentAmmo > 0, "Weapon should have ammunition");
                
                // Test firing workflow
                int initialAmmo = _weaponController.CurrentWeapon.CurrentAmmo;
                bool canFire = _weaponController.CurrentWeapon.CanFire();
                Assert(canFire, "Should be able to fire weapon with ammo");
                
                // Test reload workflow
                bool needsReload = _weaponController.CurrentWeapon.NeedsReload();
                Assert(_inventory.HasItem("rifle_ammo"), "Should have ammo for reloading");
                
                // Test ammo management integration
                int totalAmmo = _inventory.GetItemQuantity("rifle_ammo");
                Assert(totalAmmo > 0, "Should have reserve ammunition");
                
                PassTest(testName);
            }
            catch (Exception ex)
            {
                FailTest(testName, ex.Message);
            }
        }

        /// <summary>
        /// Tests inventory management workflow
        /// Requirement 6.6: Inventory system integration
        /// </summary>
        private void TestInventoryManagementWorkflow()
        {
            string testName = "Inventory Management Workflow";
            
            try
            {
                // Test item addition and removal
                int initialBandages = _inventory.GetItemQuantity("bandage");
                Assert(initialBandages > 0, "Should have initial bandages");
                
                // Test item stacking
                _inventory.AddItem("bandage", 2);
                int afterAddition = _inventory.GetItemQuantity("bandage");
                Assert(afterAddition == initialBandages + 2, "Items should stack correctly");
                
                // Test item removal
                bool removed = _inventory.RemoveItem("bandage", 1);
                Assert(removed, "Should be able to remove items");
                Assert(_inventory.GetItemQuantity("bandage") == afterAddition - 1, "Item count should decrease");
                
                // Test equipment management
                Assert(_inventory.GetEquippedWeapon() != null, "Should have equipped weapon");
                
                // Test inventory capacity and validation
                Assert(_inventory.HasItem("cloth"), "Should have crafting materials");
                Assert(_inventory.HasItem("rifle_ammo"), "Should have ammunition");
                
                PassTest(testName);
            }
            catch (Exception ex)
            {
                FailTest(testName, ex.Message);
            }
        }

        /// <summary>
        /// Tests crafting workflow integration
        /// Requirement 6.6: Crafting system integration
        /// </summary>
        private void TestCraftingWorkflow()
        {
            string testName = "Crafting Workflow Integration";
            
            try
            {
                // Get a recipe to test with
                var bandageRecipe = ItemDatabase.Instance?.GetRecipe("craft_bandage");
                Assert(bandageRecipe != null, "Should have bandage recipe available");
                
                // Test recipe validation
                bool canCraft = _craftingSystem.CanCraft(bandageRecipe);
                Assert(canCraft, "Should be able to craft bandage with available materials");
                
                // Test material checking
                var missingMaterials = _craftingSystem.GetMissingMaterials(bandageRecipe);
                Assert(missingMaterials.Count == 0, "Should have all required materials");
                
                // Test crafting execution
                int initialBandages = _inventory.GetItemQuantity("bandage");
                int initialCloth = _inventory.GetItemQuantity("cloth");
                int initialAlcohol = _inventory.GetItemQuantity("alcohol");
                
                bool crafted = _craftingSystem.CraftItem(bandageRecipe);
                Assert(crafted, "Crafting should succeed");
                
                // Verify material consumption and output
                Assert(_inventory.GetItemQuantity("bandage") > initialBandages, "Should have more bandages after crafting");
                Assert(_inventory.GetItemQuantity("cloth") < initialCloth, "Should have consumed cloth");
                Assert(_inventory.GetItemQuantity("alcohol") < initialAlcohol, "Should have consumed alcohol");
                
                PassTest(testName);
            }
            catch (Exception ex)
            {
                FailTest(testName, ex.Message);
            }
        }

        /// <summary>
        /// Tests survival stats workflow integration
        /// Requirement 4.1: Survival stats integration with player actions
        /// </summary>
        private void TestSurvivalStatsWorkflow()
        {
            string testName = "Survival Stats Workflow Integration";
            
            try
            {
                // Verify all survival stats are initialized
                Assert(_survivalStatsSystem.Health != null, "Health stat should exist");
                Assert(_survivalStatsSystem.Hunger != null, "Hunger stat should exist");
                Assert(_survivalStatsSystem.Thirst != null, "Thirst stat should exist");
                Assert(_survivalStatsSystem.Stamina != null, "Stamina stat should exist");
                
                // Test initial stat values
                Assert(_survivalStatsSystem.Health.CurrentValue > 0, "Health should be positive");
                Assert(_survivalStatsSystem.Hunger.CurrentValue > 0, "Hunger should be positive");
                Assert(_survivalStatsSystem.Thirst.CurrentValue > 0, "Thirst should be positive");
                Assert(_survivalStatsSystem.Stamina.CurrentValue > 0, "Stamina should be positive");
                
                // Test consumable integration
                float initialHealth = _survivalStatsSystem.Health.CurrentValue;
                
                // Reduce health to test healing
                _survivalStatsSystem.Health.ModifyValue(-20f);
                float reducedHealth = _survivalStatsSystem.Health.CurrentValue;
                Assert(reducedHealth < initialHealth, "Health should be reduced");
                
                // Test consumable usage
                bool consumed = _survivalStatsSystem.ConsumeItem("bandage");
                Assert(consumed, "Should be able to consume bandage");
                
                // Test stat restoration (bandage should restore health)
                float afterHealing = _survivalStatsSystem.Health.CurrentValue;
                Assert(afterHealing > reducedHealth, "Health should be restored after using bandage");
                
                // Test different consumable types
                float initialHunger = _survivalStatsSystem.Hunger.CurrentValue;
                _survivalStatsSystem.Hunger.ModifyValue(-15f);
                
                consumed = _survivalStatsSystem.ConsumeItem("canned_food");
                Assert(consumed, "Should be able to consume food");
                Assert(_survivalStatsSystem.Hunger.CurrentValue > initialHunger - 15f, "Food should restore hunger");
                
                PassTest(testName);
            }
            catch (Exception ex)
            {
                FailTest(testName, ex.Message);
            }
        }

        /// <summary>
        /// Tests save/load workflow integration
        /// Requirement 6.6: Save/load system integration
        /// </summary>
        private void TestSaveLoadWorkflow()
        {
            string testName = "Save/Load Workflow Integration";
            
            try
            {
                // Test player movement data collection
                Vector2 testPosition = new Vector2(500, 300);
                _playerController.SetPlayerPosition(testPosition);
                
                var movementData = _playerController.GetMovementData();
                Assert(movementData != null, "Should be able to get movement data");
                Assert(movementData.Position == testPosition, "Movement data should have correct position");
                Assert(movementData.IsAlive == _playerController.IsAlive, "Movement data should reflect alive status");
                
                // Test movement data loading
                var newMovementData = new PlayerMovementData
                {
                    Position = new Vector2(800, 600),
                    Velocity = Vector2.Zero,
                    IsSprinting = false,
                    IsAlive = true
                };
                
                _playerController.LoadMovementData(newMovementData);
                Assert(_playerController.GetPlayerPosition() == newMovementData.Position, "Should load position correctly");
                Assert(_playerController.IsAlive == newMovementData.IsAlive, "Should load alive status correctly");
                
                // Test save manager integration
                Assert(_saveManager != null, "Save manager should be available");
                
                PassTest(testName);
            }
            catch (Exception ex)
            {
                FailTest(testName, ex.Message);
            }
        }

        /// <summary>
        /// Tests player death and recovery workflow
        /// Requirement 4.1: Death and respawn mechanics
        /// </summary>
        private void TestPlayerDeathRecoveryWorkflow()
        {
            string testName = "Player Death Recovery Workflow";
            
            try
            {
                // Ensure player starts alive
                Assert(_playerController.IsAlive, "Player should start alive");
                
                // Test death scenario
                _survivalStatsSystem.Health.SetValue(0f);
                
                // The death handling is asynchronous, so we'll test the setup
                Assert(_survivalStatsSystem.Health.CurrentValue == 0f, "Health should be zero");
                
                // Test that player controller can handle death state changes
                // The actual death/respawn mechanics are handled by SurvivalStatsSystem
                
                // Reset health for other tests
                _survivalStatsSystem.Health.SetValue(_survivalStatsSystem.Health.MaxValue);
                
                PassTest(testName);
            }
            catch (Exception ex)
            {
                FailTest(testName, ex.Message);
            }
        }

        /// <summary>
        /// Tests stamina integration with player actions
        /// Requirement 4.1: Stamina integration with actions
        /// </summary>
        private void TestStaminaIntegrationWorkflow()
        {
            string testName = "Stamina Integration Workflow";
            
            try
            {
                // Test stamina configuration
                Assert(_playerController.ActionStaminaCost > 0, "Actions should have stamina cost");
                Assert(_playerController.StaminaCostPerSecond > 0, "Sprinting should have stamina cost");
                
                // Test stamina validation for actions
                float initialStamina = _survivalStatsSystem.Stamina.CurrentValue;
                Assert(initialStamina >= _playerController.ActionStaminaCost, "Should have enough stamina for actions");
                
                // Test stamina depletion scenario
                _survivalStatsSystem.Stamina.SetValue(_playerController.ActionStaminaCost - 1f);
                float lowStamina = _survivalStatsSystem.Stamina.CurrentValue;
                Assert(lowStamina < _playerController.ActionStaminaCost, "Stamina should be below action cost");
                
                // Reset stamina for other tests
                _survivalStatsSystem.Stamina.SetValue(_survivalStatsSystem.Stamina.MaxValue);
                
                PassTest(testName);
            }
            catch (Exception ex)
            {
                FailTest(testName, ex.Message);
            }
        }

        /// <summary>
        /// Tests complete gameplay loop integration
        /// Comprehensive test of all systems working together
        /// </summary>
        private void TestCompleteGameplayLoop()
        {
            string testName = "Complete Gameplay Loop Integration";
            
            try
            {
                // Simulate a complete gameplay scenario
                
                // 1. Player starts with equipment and resources
                Assert(_inventory.GetEquippedWeapon() != null, "Player should have equipped weapon");
                Assert(_inventory.HasItem("rifle_ammo"), "Player should have ammunition");
                Assert(_inventory.HasItem("bandage"), "Player should have healing items");
                Assert(_inventory.HasItem("canned_food"), "Player should have food");
                
                // 2. Player can perform combat actions
                Assert(_weaponController.CurrentWeapon.CanFire(), "Player should be able to fire weapon");
                Assert(_inventory.HasItem(_weaponController.CurrentWeapon.AmmoType), "Player should have ammo for weapon");
                
                // 3. Player can manage survival needs
                Assert(_survivalStatsSystem.ConsumeItem("canned_food"), "Player should be able to eat food");
                Assert(_survivalStatsSystem.ConsumeItem("bandage"), "Player should be able to use medical items");
                
                // 4. Player can craft items
                var recipe = ItemDatabase.Instance?.GetRecipe("craft_bandage");
                if (recipe != null)
                {
                    Assert(_craftingSystem.CanCraft(recipe), "Player should be able to craft items");
                }
                
                // 5. Player state can be saved and loaded
                var movementData = _playerController.GetMovementData();
                Assert(movementData != null, "Player state should be serializable");
                
                // 6. All systems respond to player actions
                Assert(_playerController.IsAlive, "Player should remain alive during normal gameplay");
                Assert(_survivalStatsSystem.Health.CurrentValue > 0, "Player should maintain health");
                Assert(_survivalStatsSystem.Stamina.CurrentValue > 0, "Player should maintain stamina");
                
                PassTest(testName);
            }
            catch (Exception ex)
            {
                FailTest(testName, ex.Message);
            }
        }

        /// <summary>
        /// Prints comprehensive test results
        /// </summary>
        private void PrintTestResults()
        {
            GD.Print("\n=== PlayerController Complete Workflow Integration Test Results ===");
            GD.Print($"Tests Passed: {_testsPassed}");
            GD.Print($"Tests Failed: {_testsFailed}");
            GD.Print($"Total Tests: {_testsPassed + _testsFailed}");
            
            if (_testResults.Count > 0)
            {
                GD.Print("\nDetailed Results:");
                foreach (string result in _testResults)
                {
                    GD.Print(result);
                }
            }
            
            if (_testsFailed == 0)
            {
                GD.Print("🎉 All PlayerController complete workflow integration tests passed!");
                GD.Print("✅ PlayerController successfully coordinates all game systems");
                GD.Print("✅ Input handling integration working correctly");
                GD.Print("✅ Combat workflow fully integrated");
                GD.Print("✅ Survival stats integration functioning properly");
                GD.Print("✅ Complete gameplay loop validated");
            }
            else
            {
                GD.PrintErr($"❌ {_testsFailed} integration test(s) failed");
            }
            
            GD.Print("=== End PlayerController Complete Workflow Integration Tests ===\n");
        }

        private void Assert(bool condition, string message)
        {
            if (!condition)
            {
                throw new Exception($"Assertion failed: {message}");
            }
        }

        private void PassTest(string testName)
        {
            _testsPassed++;
            _testResults.Add($"✅ {testName}: PASSED");
        }

        private void FailTest(string testName, string error)
        {
            _testsFailed++;
            _testResults.Add($"❌ {testName}: FAILED - {error}");
        }
    }
}