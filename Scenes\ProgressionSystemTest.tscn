[gd_scene load_steps=2 format=3 uid="uid://b5q2mpdsxjefk"]

[ext_resource type="Script" path="res://Scripts/ProgressionSystemTestRunner.cs" id="1_abc123"]

[node name="ProgressionSystemTest" type="Node"]
script = ExtResource("1_abc123")

[node name="EventBus" type="Node" parent="."]
script = ExtResource("res://Scripts/EventBus.cs")

[node name="ItemDatabase" type="Node" parent="."]
script = ExtResource("res://Scripts/ItemDatabase.cs")

[node name="Label" type="Label" parent="."]
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -200.0
offset_top = -100.0
offset_right = 200.0
offset_bottom = 100.0
text = "Progression System Test

Press keys to test:
1 - Weapon Proficiency Level Up
2 - Crafting Efficiency Level Up  
3 - Activate Berserker Mode
4 - Player Death (Penalties)
5 - Gain Experience

Check console for results"
horizontal_alignment = 1
vertical_alignment = 1