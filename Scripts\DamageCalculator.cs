using System;
using System.Collections.Generic;

namespace SurvivalLooterShooter
{
    /// <summary>
    /// Handles damage calculations for weapon-target interactions
    /// </summary>
    public static class DamageCalculator
    {
        /// <summary>
        /// Calculates damage dealt by a weapon to a target
        /// </summary>
        /// <param name="weapon">The weapon being used</param>
        /// <param name="targetType">Type of target (e.g., "enemy", "player", "structure")</param>
        /// <param name="distance">Distance to target</param>
        /// <param name="modifiers">Additional damage modifiers</param>
        /// <returns>Final damage amount</returns>
        public static float CalculateDamage(Weapon weapon, string targetType = "enemy", float distance = 0f, Dictionary<string, float> modifiers = null)
        {
            if (weapon == null)
                return 0f;

            float baseDamage = weapon.GetEffectiveDamage();
            
            // Apply distance falloff
            float distanceModifier = CalculateDistanceFalloff(weapon, distance);
            
            // Apply target type modifier
            float targetModifier = GetTargetTypeModifier(weapon, targetType);
            
            // Apply additional modifiers
            float additionalModifier = 1f;
            if (modifiers != null)
            {
                foreach (var modifier in modifiers.Values)
                {
                    additionalModifier *= modifier;
                }
            }

            float finalDamage = baseDamage * distanceModifier * targetModifier * additionalModifier;
            
            // Ensure minimum damage of 1
            return Math.Max(1f, finalDamage);
        }

        /// <summary>
        /// Calculates damage falloff based on distance
        /// </summary>
        private static float CalculateDistanceFalloff(Weapon weapon, float distance)
        {
            if (distance <= 0f || weapon.Range <= 0f)
                return 1f; // No falloff if distance is 0 or weapon has no range limit

            // Calculate falloff percentage based on weapon range
            float falloffStart = weapon.Range * 0.3f; // Damage starts falling off at 30% of max range
            
            if (distance <= falloffStart)
                return 1f; // Full damage within optimal range
            
            if (distance >= weapon.Range)
                return 0.1f; // Minimum 10% damage at max range and beyond
            
            // Linear falloff between optimal range and max range
            float falloffDistance = distance - falloffStart;
            float falloffRange = weapon.Range - falloffStart;
            float falloffPercentage = falloffDistance / falloffRange;
            
            return Math.Max(0.1f, 1f - (falloffPercentage * 0.9f));
        }

        /// <summary>
        /// Gets damage modifier based on target type
        /// </summary>
        private static float GetTargetTypeModifier(Weapon weapon, string targetType)
        {
            // Different weapon types might be more effective against certain targets
            return weapon.WeaponType switch
            {
                "automatic" => targetType switch
                {
                    "enemy" => 1f,
                    "structure" => 0.7f,
                    "armor" => 0.8f,
                    _ => 1f
                },
                "single" => targetType switch
                {
                    "enemy" => 1.2f,
                    "structure" => 0.9f,
                    "armor" => 1.1f,
                    _ => 1f
                },
                "shotgun" => targetType switch
                {
                    "enemy" => 1.3f,
                    "structure" => 0.6f,
                    "armor" => 0.7f,
                    _ => 1f
                },
                _ => 1f
            };
        }

        /// <summary>
        /// Calculates critical hit damage
        /// </summary>
        public static float CalculateCriticalDamage(float baseDamage, float criticalMultiplier = 2f)
        {
            return baseDamage * criticalMultiplier;
        }

        /// <summary>
        /// Determines if an attack is a critical hit based on chance
        /// </summary>
        public static bool IsCriticalHit(float criticalChance = 0.05f)
        {
            var random = new Random();
            return random.NextDouble() < criticalChance;
        }

        /// <summary>
        /// Calculates damage with potential critical hit
        /// </summary>
        public static DamageResult CalculateDamageWithCritical(Weapon weapon, string targetType = "enemy", float distance = 0f, Dictionary<string, float> modifiers = null, float criticalChance = 0.05f, float criticalMultiplier = 2f)
        {
            float baseDamage = CalculateDamage(weapon, targetType, distance, modifiers);
            bool isCritical = IsCriticalHit(criticalChance);
            
            float finalDamage = isCritical ? CalculateCriticalDamage(baseDamage, criticalMultiplier) : baseDamage;
            
            return new DamageResult
            {
                Damage = finalDamage,
                IsCritical = isCritical,
                BaseDamage = baseDamage
            };
        }

        /// <summary>
        /// Calculates armor penetration
        /// </summary>
        public static float CalculateArmorPenetration(float damage, float armorValue, float penetration = 0f)
        {
            float effectiveArmor = Math.Max(0f, armorValue - penetration);
            float damageReduction = effectiveArmor / (effectiveArmor + 100f); // Diminishing returns formula
            
            return damage * (1f - damageReduction);
        }
    }

    /// <summary>
    /// Result of a damage calculation
    /// </summary>
    public class DamageResult
    {
        public float Damage { get; set; }
        public float BaseDamage { get; set; }
        public bool IsCritical { get; set; }
        
        public override string ToString()
        {
            return $"Damage: {Damage:F1}{(IsCritical ? " (CRITICAL)" : "")}";
        }
    }
}