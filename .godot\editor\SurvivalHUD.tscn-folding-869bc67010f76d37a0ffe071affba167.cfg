[folding]

node_unfolds=[Node<PERSON><PERSON>("."), PackedStringArray("Layout", "Mouse"), NodePath("StatsContainer"), PackedStringArray("Layout"), NodePath("StatsContainer/StatsBackground"), PackedStringArray("Layout", "Theme Overrides"), NodePath("StatsContainer/StatsBackground/StatsVBox"), PackedStringArray("Layout"), NodePath("StatsContainer/StatsBackground/StatsVBox/HealthContainer"), PackedStringArray("Layout"), NodePath("StatsContainer/StatsBackground/StatsVBox/HealthContainer/HealthLabel"), PackedStringArray("Layout"), NodePath("StatsContainer/StatsBackground/StatsVBox/HealthContainer/HealthBar"), PackedStringArray("Layout"), NodePath("StatsContainer/StatsBackground/StatsVBox/HealthContainer/HealthValue"), PackedStringArray("Layout"), NodePath("StatsContainer/StatsBackground/StatsVBox/HungerContainer"), PackedStringArray("Layout"), NodePath("StatsContainer/StatsBackground/StatsVBox/HungerContainer/HungerLabel"), PackedStringArray("Layout"), NodePath("StatsContainer/StatsBackground/StatsVBox/HungerContainer/HungerBar"), PackedStringArray("Layout"), NodePath("StatsContainer/StatsBackground/StatsVBox/HungerContainer/HungerValue"), PackedStringArray("Layout"), NodePath("StatsContainer/StatsBackground/StatsVBox/ThirstContainer"), PackedStringArray("Layout"), NodePath("StatsContainer/StatsBackground/StatsVBox/ThirstContainer/ThirstLabel"), PackedStringArray("Layout"), NodePath("StatsContainer/StatsBackground/StatsVBox/ThirstContainer/ThirstBar"), PackedStringArray("Layout"), NodePath("StatsContainer/StatsBackground/StatsVBox/ThirstContainer/ThirstValue"), PackedStringArray("Layout"), NodePath("StatsContainer/StatsBackground/StatsVBox/StaminaContainer"), PackedStringArray("Layout"), NodePath("StatsContainer/StatsBackground/StatsVBox/StaminaContainer/StaminaLabel"), PackedStringArray("Layout"), NodePath("StatsContainer/StatsBackground/StatsVBox/StaminaContainer/StaminaBar"), PackedStringArray("Layout"), NodePath("StatsContainer/StatsBackground/StatsVBox/StaminaContainer/StaminaValue"), PackedStringArray("Layout"), NodePath("WeaponContainer"), PackedStringArray("Layout"), NodePath("WeaponContainer/WeaponBackground"), PackedStringArray("Layout", "Theme Overrides"), NodePath("WeaponContainer/WeaponBackground/WeaponVBox"), PackedStringArray("Layout"), NodePath("WeaponContainer/WeaponBackground/WeaponVBox/WeaponNameLabel"), PackedStringArray("Layout"), NodePath("WeaponContainer/WeaponBackground/WeaponVBox/AmmoContainer"), PackedStringArray("Layout"), NodePath("WeaponContainer/WeaponBackground/WeaponVBox/AmmoContainer/AmmoLabel"), PackedStringArray("Layout"), NodePath("WeaponContainer/WeaponBackground/WeaponVBox/AmmoContainer/AmmoValue"), PackedStringArray("Layout"), NodePath("WeaponContainer/WeaponBackground/WeaponVBox/ConditionContainer"), PackedStringArray("Layout"), NodePath("WeaponContainer/WeaponBackground/WeaponVBox/ConditionContainer/ConditionLabel"), PackedStringArray("Layout"), NodePath("WeaponContainer/WeaponBackground/WeaponVBox/ConditionContainer/ConditionBar"), PackedStringArray("Layout"), NodePath("WeaponContainer/WeaponBackground/WeaponVBox/ReloadingLabel"), PackedStringArray("Visibility", "Layout")]
resource_unfolds=["res://Scenes/SurvivalHUD.tscn::StyleBoxFlat_Background", PackedStringArray("Resource", "Corner Radius")]
nodes_folded=[]
