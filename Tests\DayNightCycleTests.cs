using Godot;

namespace SurvivalLooterShooter
{
    /// <summary>
    /// Test runner for day/night cycle system functionality
    /// </summary>
    public partial class DayNightCycleTests : Node
    {
        private bool _testsCompleted = false;

        public override void _Ready()
        {
            // Wait a frame for systems to initialize
            CallDeferred(nameof(RunTests));
        }

        private void RunTests()
        {
            if (_testsCompleted) return;
            _testsCompleted = true;

            GD.Print("=== Day/Night Cycle System Tests ===");

            TestDayNightCycleInitialization();
            TestTimeProgression();
            TestTimeFormatting();
            TestLightingIntegration();
            TestTimeBasedGameplayMechanics();
            TestSaveLoadIntegration();

            GD.Print("=== Day/Night Cycle Tests Completed ===");
            
            // Clean up after tests
            QueueFree();
        }

        private void TestDayNightCycleInitialization()
        {
            GD.Print("Testing day/night cycle initialization...");

            if (DayNightCycle.Instance == null)
            {
                GD.PrintErr("❌ DayNightCycle instance not found");
                return;
            }

            // Test initial values
            float currentTime = DayNightCycle.Instance.CurrentTime;
            bool isNight = DayNightCycle.Instance.IsNightTime;
            string timePeriod = DayNightCycle.Instance.GetTimePeriod();

            GD.Print($"✅ DayNightCycle initialized - Time: {currentTime:F1}, Period: {timePeriod}, IsNight: {isNight}");
        }

        private void TestTimeProgression()
        {
            GD.Print("Testing time progression...");

            if (DayNightCycle.Instance == null) return;

            float initialTime = DayNightCycle.Instance.CurrentTime;
            
            // Advance time by 6 hours (should change time period)
            DayNightCycle.Instance.AdvanceTime(6.0f);
            
            float newTime = DayNightCycle.Instance.CurrentTime;
            string newTimePeriod = DayNightCycle.Instance.GetTimePeriod();

            GD.Print($"✅ Time progression - Initial: {initialTime:F1}, After +6h: {newTime:F1}, Period: {newTimePeriod}");

            // Reset to noon for other tests
            DayNightCycle.Instance.SetTime(12.0f);
        }

        private void TestTimeFormatting()
        {
            GD.Print("Testing time formatting...");

            if (DayNightCycle.Instance == null) return;

            // Test various times
            float[] testTimes = { 0.0f, 6.5f, 12.0f, 18.25f, 23.75f };
            
            foreach (float time in testTimes)
            {
                DayNightCycle.Instance.SetTime(time);
                string formatted = DayNightCycle.Instance.GetFormattedTime();
                string period = DayNightCycle.Instance.GetTimePeriod();
                
                GD.Print($"  Time {time:F2} -> {formatted} ({period})");
            }

            GD.Print("✅ Time formatting tests completed");
            
            // Reset to noon
            DayNightCycle.Instance.SetTime(12.0f);
        }

        private void TestLightingIntegration()
        {
            GD.Print("Testing lighting system integration...");

            if (DayNightCycle.Instance == null || LightingManager.Instance == null)
            {
                GD.PrintErr("❌ Required systems not found for lighting test");
                return;
            }

            // Test different times and their lighting effects
            float[] testTimes = { 6.0f, 12.0f, 18.0f, 0.0f }; // Dawn, Day, Dusk, Night
            
            foreach (float time in testTimes)
            {
                DayNightCycle.Instance.SetTime(time);
                float intensity = DayNightCycle.Instance.GetLightingIntensity();
                Color ambientColor = DayNightCycle.Instance.GetAmbientColor();
                string period = DayNightCycle.Instance.GetTimePeriod();
                
                GD.Print($"  {period} ({time:F0}:00) - Intensity: {intensity:F1}, Ambient: {ambientColor}");
            }

            GD.Print("✅ Lighting integration tests completed");
            
            // Reset to noon
            DayNightCycle.Instance.SetTime(12.0f);
        }

        private void TestTimeBasedGameplayMechanics()
        {
            GD.Print("Testing time-based gameplay mechanics...");

            if (TimeBasedGameplayManager.Instance == null)
            {
                GD.PrintErr("❌ TimeBasedGameplayManager not found");
                return;
            }

            // Test different times and their gameplay effects
            float[] testTimes = { 6.0f, 12.0f, 18.0f, 0.0f };
            
            foreach (float time in testTimes)
            {
                DayNightCycle.Instance.SetTime(time);
                
                float spawnMultiplier = TimeBasedGameplayManager.Instance.GetEnemySpawnMultiplier();
                float visibilityModifier = TimeBasedGameplayManager.Instance.GetVisibilityModifier();
                float regenMultiplier = TimeBasedGameplayManager.Instance.GetResourceRegenMultiplier();
                string period = DayNightCycle.Instance.GetTimePeriod();
                
                GD.Print($"  {period} - Spawn: {spawnMultiplier:F1}x, Visibility: {visibilityModifier:F1}x, Regen: {regenMultiplier:F1}x");
            }

            GD.Print("✅ Time-based gameplay mechanics tests completed");
            
            // Reset to noon
            DayNightCycle.Instance.SetTime(12.0f);
        }

        private void TestSaveLoadIntegration()
        {
            GD.Print("Testing save/load integration...");

            if (DayNightCycle.Instance == null)
            {
                GD.PrintErr("❌ DayNightCycle not found for save/load test");
                return;
            }

            // Set a specific time
            float testTime = 15.5f; // 3:30 PM
            DayNightCycle.Instance.SetTime(testTime);
            
            // Create save data (simulated)
            var saveData = new GameSaveData
            {
                CurrentTime = DayNightCycle.Instance.CurrentTime,
                DayLengthMinutes = DayNightCycle.Instance.DayLengthMinutes,
                DawnTime = DayNightCycle.Instance.DawnTime,
                DuskTime = DayNightCycle.Instance.DuskTime
            };

            // Verify save data
            if (Mathf.Abs(saveData.CurrentTime - testTime) < 0.1f)
            {
                GD.Print($"✅ Save data correctly captured time: {saveData.CurrentTime:F1}");
            }
            else
            {
                GD.PrintErr($"❌ Save data time mismatch: expected {testTime:F1}, got {saveData.CurrentTime:F1}");
            }

            // Test restoration (change time first)
            DayNightCycle.Instance.SetTime(0.0f);
            
            // Restore from save data
            DayNightCycle.Instance.SetTime(saveData.CurrentTime);
            
            if (Mathf.Abs(DayNightCycle.Instance.CurrentTime - testTime) < 0.1f)
            {
                GD.Print($"✅ Time correctly restored from save: {DayNightCycle.Instance.CurrentTime:F1}");
            }
            else
            {
                GD.PrintErr($"❌ Time restoration failed: expected {testTime:F1}, got {DayNightCycle.Instance.CurrentTime:F1}");
            }

            GD.Print("✅ Save/load integration tests completed");
        }
    }
}