using Godot;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;

namespace SurvivalLooterShooter
{
    /// <summary>
    /// Singleton class that manages all item definitions and recipes loaded from JSON files
    /// </summary>
    public partial class ItemDatabase : Node
    {
        private static ItemDatabase _instance;
        public static ItemDatabase Instance => _instance;

        private Dictionary<string, Item> _items = new Dictionary<string, Item>();
        private Dictionary<string, Recipe> _recipes = new Dictionary<string, Recipe>();
        private Dictionary<string, List<Recipe>> _recipesByOutput = new Dictionary<string, List<Recipe>>();

        public override void _Ready()
        {
            if (_instance == null)
            {
                _instance = this;
                LoadItemsFromJson();
                LoadRecipesFromJson();
                LoadAdvancedRecipesFromJson();
                LoadProgressionRecipesFromJson();
                GD.Print("ItemDatabase initialized successfully");
            }
            else
            {
                QueueFree(); // Ensure only one instance exists
            }
        }

        /// <summary>
        /// Loads item definitions from Data/Items.json
        /// </summary>
        public void LoadItemsFromJson()
        {
            try
            {
                string filePath = "res://Data/Items.json";
                
                if (!Godot.FileAccess.FileExists(filePath))
                {
                    GD.PrintErr($"Items.json file not found at {filePath}");
                    return;
                }

                using var file = Godot.FileAccess.Open(filePath, Godot.FileAccess.ModeFlags.Read);
                if (file == null)
                {
                    GD.PrintErr($"Failed to open Items.json file at {filePath}");
                    return;
                }

                string jsonContent = file.GetAsText();
                var items = JsonSerializer.Deserialize<List<Item>>(jsonContent);

                _items.Clear();
                
                if (items != null)
                {
                    foreach (var item in items)
                    {
                        if (!string.IsNullOrEmpty(item.Id))
                        {
                            _items[item.Id] = item;
                        }
                        else
                        {
                            GD.PrintErr($"Item with empty ID found in Items.json");
                        }
                    }
                }

                GD.Print($"Loaded {_items.Count} items from Items.json");
            }
            catch (Exception ex)
            {
                GD.PrintErr($"Error loading Items.json: {ex.Message}");
            }
        }

        /// <summary>
        /// Loads recipe definitions from Data/Recipes.json
        /// </summary>
        public void LoadRecipesFromJson()
        {
            try
            {
                string filePath = "res://Data/Recipes.json";
                
                if (!Godot.FileAccess.FileExists(filePath))
                {
                    GD.PrintErr($"Recipes.json file not found at {filePath}");
                    return;
                }

                using var file = Godot.FileAccess.Open(filePath, Godot.FileAccess.ModeFlags.Read);
                if (file == null)
                {
                    GD.PrintErr($"Failed to open Recipes.json file at {filePath}");
                    return;
                }

                string jsonContent = file.GetAsText();
                var recipes = JsonSerializer.Deserialize<List<Recipe>>(jsonContent);

                _recipes.Clear();
                _recipesByOutput.Clear();
                
                if (recipes != null)
                {
                    foreach (var recipe in recipes)
                    {
                        if (!string.IsNullOrEmpty(recipe.Id) && recipe.IsValid())
                        {
                            _recipes[recipe.Id] = recipe;
                            
                            // Index recipes by output item for quick lookup
                            string outputId = recipe.Output.Id;
                            if (!_recipesByOutput.ContainsKey(outputId))
                            {
                                _recipesByOutput[outputId] = new List<Recipe>();
                            }
                            _recipesByOutput[outputId].Add(recipe);
                        }
                        else
                        {
                            GD.PrintErr($"Invalid recipe found in Recipes.json: {recipe?.Id ?? "null"}");
                        }
                    }
                }

                GD.Print($"Loaded {_recipes.Count} recipes from Recipes.json");
            }
            catch (Exception ex)
            {
                GD.PrintErr($"Error loading Recipes.json: {ex.Message}");
            }
        }

        /// <summary>
        /// Loads advanced recipe definitions from Data/AdvancedRecipes.json
        /// </summary>
        public void LoadAdvancedRecipesFromJson()
        {
            try
            {
                string filePath = "res://Data/AdvancedRecipes.json";
                
                if (!Godot.FileAccess.FileExists(filePath))
                {
                    GD.Print($"AdvancedRecipes.json file not found at {filePath} - skipping advanced recipes");
                    return;
                }

                using var file = Godot.FileAccess.Open(filePath, Godot.FileAccess.ModeFlags.Read);
                if (file == null)
                {
                    GD.PrintErr($"Failed to open AdvancedRecipes.json file at {filePath}");
                    return;
                }

                string jsonContent = file.GetAsText();
                var advancedRecipes = JsonSerializer.Deserialize<List<AdvancedRecipe>>(jsonContent);

                int loadedCount = 0;
                
                if (advancedRecipes != null)
                {
                    foreach (var recipe in advancedRecipes)
                    {
                        if (!string.IsNullOrEmpty(recipe.Id) && recipe.IsValid())
                        {
                            _recipes[recipe.Id] = recipe;
                            
                            // Index recipes by output item for quick lookup
                            string outputId = recipe.Output.Id;
                            if (!_recipesByOutput.ContainsKey(outputId))
                            {
                                _recipesByOutput[outputId] = new List<Recipe>();
                            }
                            _recipesByOutput[outputId].Add(recipe);
                            loadedCount++;
                        }
                        else
                        {
                            GD.PrintErr($"Invalid advanced recipe found in AdvancedRecipes.json: {recipe?.Id ?? "null"}");
                        }
                    }
                }

                GD.Print($"Loaded {loadedCount} advanced recipes from AdvancedRecipes.json");
            }
            catch (Exception ex)
            {
                GD.PrintErr($"Error loading AdvancedRecipes.json: {ex.Message}");
            }
        }

        /// <summary>
        /// Loads progression recipe definitions from Data/ProgressionRecipes.json
        /// </summary>
        public void LoadProgressionRecipesFromJson()
        {
            try
            {
                string filePath = "res://Data/ProgressionRecipes.json";
                
                if (!Godot.FileAccess.FileExists(filePath))
                {
                    GD.Print($"ProgressionRecipes.json file not found at {filePath} - skipping progression recipes");
                    return;
                }

                using var file = Godot.FileAccess.Open(filePath, Godot.FileAccess.ModeFlags.Read);
                if (file == null)
                {
                    GD.PrintErr($"Failed to open ProgressionRecipes.json file at {filePath}");
                    return;
                }

                string jsonContent = file.GetAsText();
                var progressionRecipes = JsonSerializer.Deserialize<List<Recipe>>(jsonContent);

                int loadedCount = 0;
                
                if (progressionRecipes != null)
                {
                    foreach (var recipe in progressionRecipes)
                    {
                        if (!string.IsNullOrEmpty(recipe.Id) && recipe.IsValid())
                        {
                            _recipes[recipe.Id] = recipe;
                            
                            // Index recipes by output item for quick lookup
                            string outputId = recipe.Output.Id;
                            if (!_recipesByOutput.ContainsKey(outputId))
                            {
                                _recipesByOutput[outputId] = new List<Recipe>();
                            }
                            _recipesByOutput[outputId].Add(recipe);
                            loadedCount++;
                        }
                        else
                        {
                            GD.PrintErr($"Invalid progression recipe found in ProgressionRecipes.json: {recipe?.Id ?? "null"}");
                        }
                    }
                }

                GD.Print($"Loaded {loadedCount} progression recipes from ProgressionRecipes.json");
            }
            catch (Exception ex)
            {
                GD.PrintErr($"Error loading ProgressionRecipes.json: {ex.Message}");
            }
        }

        /// <summary>
        /// Retrieves an item definition by ID
        /// </summary>
        /// <param name="id">The item ID to look up</param>
        /// <returns>The item definition, or null if not found</returns>
        public Item GetItem(string id)
        {
            if (string.IsNullOrEmpty(id))
                return null;

            return _items.TryGetValue(id, out Item item) ? item : null;
        }

        /// <summary>
        /// Retrieves a recipe by ID
        /// </summary>
        /// <param name="id">The recipe ID to look up</param>
        /// <returns>The recipe definition, or null if not found</returns>
        public Recipe GetRecipe(string id)
        {
            if (string.IsNullOrEmpty(id))
                return null;

            return _recipes.TryGetValue(id, out Recipe recipe) ? recipe : null;
        }

        /// <summary>
        /// Finds all recipes that produce a specific item
        /// </summary>
        /// <param name="itemId">The output item ID to search for</param>
        /// <returns>List of recipes that produce the specified item</returns>
        public List<Recipe> GetRecipesByOutput(string itemId)
        {
            if (string.IsNullOrEmpty(itemId))
                return new List<Recipe>();

            return _recipesByOutput.TryGetValue(itemId, out List<Recipe> recipes) 
                ? new List<Recipe>(recipes) // Return a copy to prevent external modification
                : new List<Recipe>();
        }

        /// <summary>
        /// Gets all loaded items
        /// </summary>
        /// <returns>Dictionary of all items keyed by ID</returns>
        public Dictionary<string, Item> GetAllItems()
        {
            return new Dictionary<string, Item>(_items); // Return a copy
        }

        /// <summary>
        /// Gets all loaded recipes
        /// </summary>
        /// <returns>Dictionary of all recipes keyed by ID</returns>
        public Dictionary<string, Recipe> GetAllRecipes()
        {
            return new Dictionary<string, Recipe>(_recipes); // Return a copy
        }

        /// <summary>
        /// Checks if an item exists in the database
        /// </summary>
        /// <param name="id">The item ID to check</param>
        /// <returns>True if the item exists, false otherwise</returns>
        public bool HasItem(string id)
        {
            return !string.IsNullOrEmpty(id) && _items.ContainsKey(id);
        }

        /// <summary>
        /// Checks if a recipe exists in the database
        /// </summary>
        /// <param name="id">The recipe ID to check</param>
        /// <returns>True if the recipe exists, false otherwise</returns>
        public bool HasRecipe(string id)
        {
            return !string.IsNullOrEmpty(id) && _recipes.ContainsKey(id);
        }

        /// <summary>
        /// Gets the total number of loaded items
        /// </summary>
        public int ItemCount => _items.Count;

        /// <summary>
        /// Gets the total number of loaded recipes
        /// </summary>
        public int RecipeCount => _recipes.Count;

        // Mod support methods
        public void RegisterCustomItem(CustomContent customItem)
        {
            var item = new Item
            {
                Id = customItem.ContentId,
                Name = customItem.Name,
                Type = customItem.Properties.GetValueOrDefault("type", "misc").ToString(),
                MaxStack = Convert.ToInt32(customItem.Properties.GetValueOrDefault("max_stack", 1)),
                Metadata = customItem.Properties.ToDictionary(kvp => kvp.Key, kvp => kvp.Value)
            };
            
            _items[item.Id] = item;
            GD.Print($"Registered custom item: {item.Name}");
        }

        public void UnregisterCustomItem(string itemId)
        {
            if (_items.ContainsKey(itemId))
            {
                _items.Remove(itemId);
                GD.Print($"Unregistered custom item: {itemId}");
            }
        }
    }
}