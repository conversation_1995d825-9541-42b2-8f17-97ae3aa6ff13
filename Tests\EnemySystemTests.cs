using Godot;
using System;
using System.Collections.Generic;

namespace SurvivalLooterShooter
{
    /// <summary>
    /// Unit tests for the enemy system foundation
    /// Tests enemy creation, damage handling, and basic functionality
    /// </summary>
    public partial class EnemySystemTests : Node
    {
        private bool _testsCompleted = false;
        private int _testsPassed = 0;
        private int _testsFailed = 0;

        public override void _Ready()
        {
            // Run tests after a short delay to ensure systems are initialized
            CallDeferred(nameof(RunTests));
        }

        private void RunTests()
        {
            GD.Print("=== Enemy System Tests ===");

            TestEnemyCreation();
            TestEnemyInitialization();
            TestEnemyDamage();
            TestEnemyDeath();
            TestEnemyMovement();
            TestEnemyRangeChecks();
            TestEnemyManager();
            TestEnemyDataLoading();

            GD.Print($"=== Enemy System Tests Complete: {_testsPassed} passed, {_testsFailed} failed ===");
            _testsCompleted = true;
        }

        private void TestEnemyCreation()
        {
            GD.Print("Testing enemy creation...");

            try
            {
                var enemy = new Enemy();
                enemy.Name = "TestEnemy";
                AddChild(enemy);

                // Test default values
                AssertTrue(enemy.MaxHealth == 100f, "Default max health should be 100");
                AssertTrue(enemy.CurrentHealth == 100f, "Default current health should be 100");
                AssertTrue(enemy.Damage == 10f, "Default damage should be 10");
                AssertTrue(!enemy.IsDead, "Enemy should not be dead initially");

                enemy.QueueFree();
                GD.Print("✓ Enemy creation test passed");
                _testsPassed++;
            }
            catch (Exception ex)
            {
                GD.PrintErr($"✗ Enemy creation test failed: {ex.Message}");
                _testsFailed++;
            }
        }

        private void TestEnemyInitialization()
        {
            GD.Print("Testing enemy initialization...");

            try
            {
                var enemy = new Enemy();
                enemy.Name = "TestEnemy";
                AddChild(enemy);

                var enemyData = new EnemyData
                {
                    Id = "test_wolf",
                    Name = "Test Wolf",
                    Health = 60f,
                    MaxHealth = 60f,
                    Damage = 15f,
                    Speed = 120f,
                    DetectionRange = 200f,
                    AttackRange = 50f,
                    AIType = "aggressive",
                    Biomes = new List<string> { "forest" },
                    ExperienceReward = 25f,
                    SpawnWeight = 1.0f
                };

                enemy.Initialize(enemyData);

                AssertTrue(enemy.EnemyId == "test_wolf", "Enemy ID should be set correctly");
                AssertTrue(enemy.EnemyName == "Test Wolf", "Enemy name should be set correctly");
                AssertTrue(enemy.MaxHealth == 60f, "Max health should be set correctly");
                AssertTrue(enemy.CurrentHealth == 60f, "Current health should be set correctly");
                AssertTrue(enemy.Damage == 15f, "Damage should be set correctly");
                AssertTrue(enemy.Speed == 120f, "Speed should be set correctly");
                AssertTrue(enemy.DetectionRange == 200f, "Detection range should be set correctly");
                AssertTrue(enemy.AttackRange == 50f, "Attack range should be set correctly");

                enemy.QueueFree();
                GD.Print("✓ Enemy initialization test passed");
                _testsPassed++;
            }
            catch (Exception ex)
            {
                GD.PrintErr($"✗ Enemy initialization test failed: {ex.Message}");
                _testsFailed++;
            }
        }

        private void TestEnemyDamage()
        {
            GD.Print("Testing enemy damage system...");

            try
            {
                var enemy = new Enemy();
                enemy.Name = "TestEnemy";
                AddChild(enemy);

                // Initialize with test data
                var enemyData = new EnemyData
                {
                    Id = "test_enemy",
                    Name = "Test Enemy",
                    Health = 100f,
                    MaxHealth = 100f,
                    Damage = 10f
                };
                enemy.Initialize(enemyData);

                // Test taking damage
                enemy.TakeDamage(30f);
                AssertTrue(enemy.CurrentHealth == 70f, "Health should be reduced by damage amount");
                AssertTrue(!enemy.IsDead, "Enemy should not be dead after partial damage");

                // Test healing
                enemy.Heal(10f);
                AssertTrue(enemy.CurrentHealth == 80f, "Health should be restored by heal amount");

                // Test overheal protection
                enemy.Heal(50f);
                AssertTrue(enemy.CurrentHealth == 100f, "Health should not exceed max health");

                // Test negative damage protection
                enemy.TakeDamage(-10f);
                AssertTrue(enemy.CurrentHealth == 100f, "Negative damage should not heal");

                enemy.QueueFree();
                GD.Print("✓ Enemy damage test passed");
                _testsPassed++;
            }
            catch (Exception ex)
            {
                GD.PrintErr($"✗ Enemy damage test failed: {ex.Message}");
                _testsFailed++;
            }
        }

        private void TestEnemyDeath()
        {
            GD.Print("Testing enemy death system...");

            try
            {
                var enemy = new Enemy();
                enemy.Name = "TestEnemy";
                AddChild(enemy);

                var enemyData = new EnemyData
                {
                    Id = "test_enemy",
                    Name = "Test Enemy",
                    Health = 50f,
                    MaxHealth = 50f
                };
                enemy.Initialize(enemyData);

                bool deathEventFired = false;
                enemy.EnemyDied += () =>
                {
                    deathEventFired = true;
                };

                // Deal lethal damage
                enemy.TakeDamage(60f);

                AssertTrue(enemy.CurrentHealth == 0f, "Health should be 0 after lethal damage");
                AssertTrue(enemy.IsDead, "Enemy should be marked as dead");
                AssertTrue(deathEventFired, "Death event should be fired");

                // Test that dead enemies don't take more damage
                float healthBeforeSecondDamage = enemy.CurrentHealth;
                enemy.TakeDamage(10f);
                AssertTrue(enemy.CurrentHealth == healthBeforeSecondDamage, "Dead enemies should not take damage");

                enemy.QueueFree();
                GD.Print("✓ Enemy death test passed");
                _testsPassed++;
            }
            catch (Exception ex)
            {
                GD.PrintErr($"✗ Enemy death test failed: {ex.Message}");
                _testsFailed++;
            }
        }

        private void TestEnemyMovement()
        {
            GD.Print("Testing enemy movement...");

            try
            {
                var enemy = new Enemy();
                enemy.Name = "TestEnemy";
                AddChild(enemy);

                var enemyData = new EnemyData
                {
                    Id = "test_enemy",
                    Name = "Test Enemy",
                    Speed = 100f
                };
                enemy.Initialize(enemyData);

                Vector2 startPosition = new Vector2(0, 0);
                Vector2 targetPosition = new Vector2(100, 0);
                enemy.GlobalPosition = startPosition;

                // Test AI controller integration
                var aiController = enemy.GetAIController();
                AssertTrue(aiController != null, "Enemy should have AI controller");

                // Test AI state initialization
                AssertTrue(aiController.CurrentState != AIController.AIState.Dead, "AI should not start in dead state");

                // Test stop movement
                enemy.StopMovement();
                AssertTrue(enemy.Velocity == Vector2.Zero, "Enemy should stop moving");

                enemy.QueueFree();
                GD.Print("✓ Enemy movement test passed");
                _testsPassed++;
            }
            catch (Exception ex)
            {
                GD.PrintErr($"✗ Enemy movement test failed: {ex.Message}");
                _testsFailed++;
            }
        }

        private void TestEnemyRangeChecks()
        {
            GD.Print("Testing enemy range checks...");

            try
            {
                var enemy = new Enemy();
                enemy.Name = "TestEnemy";
                AddChild(enemy);

                var enemyData = new EnemyData
                {
                    Id = "test_enemy",
                    Name = "Test Enemy",
                    DetectionRange = 100f,
                    AttackRange = 50f
                };
                enemy.Initialize(enemyData);

                enemy.GlobalPosition = new Vector2(0, 0);
                
                // Create Node2D targets at different positions
                var closeTarget = new Node2D();
                closeTarget.GlobalPosition = new Vector2(30, 0);
                var mediumTarget = new Node2D();
                mediumTarget.GlobalPosition = new Vector2(75, 0);
                var farTarget = new Node2D();
                farTarget.GlobalPosition = new Vector2(150, 0);

                // Test attack range
                AssertTrue(enemy.IsTargetInAttackRange(closeTarget), "Close target should be in attack range");
                AssertTrue(!enemy.IsTargetInAttackRange(mediumTarget), "Medium target should not be in attack range");
                AssertTrue(!enemy.IsTargetInAttackRange(farTarget), "Far target should not be in attack range");

                // Test detection range
                AssertTrue(enemy.IsTargetInDetectionRange(closeTarget), "Close target should be in detection range");
                AssertTrue(enemy.IsTargetInDetectionRange(mediumTarget), "Medium target should be in detection range");
                AssertTrue(!enemy.IsTargetInDetectionRange(farTarget), "Far target should not be in detection range");

                // Test distance calculation
                float distance = enemy.GetDistanceTo(closeTarget);
                AssertTrue(Mathf.Abs(distance - 30f) < 0.1f, "Distance calculation should be accurate");

                enemy.QueueFree();
                GD.Print("✓ Enemy range checks test passed");
                _testsPassed++;
            }
            catch (Exception ex)
            {
                GD.PrintErr($"✗ Enemy range checks test failed: {ex.Message}");
                _testsFailed++;
            }
        }

        private void TestEnemyManager()
        {
            GD.Print("Testing enemy manager...");

            try
            {
                // EnemyManager should be a singleton
                var manager1 = EnemyManager.Instance;
                var manager2 = EnemyManager.Instance;
                AssertTrue(manager1 == manager2, "EnemyManager should be a singleton");

                if (manager1 != null)
                {
                    // Test enemy spawning
                    var spawnPosition = new Vector2(100, 100);
                    var spawnedEnemy = manager1.SpawnEnemy("default_enemy", spawnPosition);

                    if (spawnedEnemy != null)
                    {
                        AssertTrue(spawnedEnemy.GlobalPosition == spawnPosition, "Enemy should spawn at correct position");

                        var activeEnemies = manager1.GetActiveEnemies();
                        AssertTrue(activeEnemies.Contains(spawnedEnemy), "Spawned enemy should be in active enemies list");

                        // Test enemy cleanup
                        manager1.DespawnEnemy(spawnedEnemy);
                        activeEnemies = manager1.GetActiveEnemies();
                        AssertTrue(!activeEnemies.Contains(spawnedEnemy), "Despawned enemy should not be in active enemies list");
                    }
                }

                GD.Print("✓ Enemy manager test passed");
                _testsPassed++;
            }
            catch (Exception ex)
            {
                GD.PrintErr($"✗ Enemy manager test failed: {ex.Message}");
                _testsFailed++;
            }
        }

        private void TestEnemyDataLoading()
        {
            GD.Print("Testing enemy data loading...");

            try
            {
                var manager = EnemyManager.Instance;
                if (manager != null)
                {
                    // Test getting enemy data
                    var wolfData = manager.GetEnemyData("forest_wolf");
                    if (wolfData != null)
                    {
                        AssertTrue(wolfData.Name == "Forest Wolf", "Enemy data should load correctly");
                        AssertTrue(wolfData.Health > 0, "Enemy should have positive health");
                        AssertTrue(wolfData.Damage > 0, "Enemy should have positive damage");
                        AssertTrue(wolfData.Biomes.Count > 0, "Enemy should have valid biomes");
                    }

                    // Test invalid enemy ID
                    var invalidData = manager.GetEnemyData("nonexistent_enemy");
                    AssertTrue(invalidData == null, "Invalid enemy ID should return null");
                }

                GD.Print("✓ Enemy data loading test passed");
                _testsPassed++;
            }
            catch (Exception ex)
            {
                GD.PrintErr($"✗ Enemy data loading test failed: {ex.Message}");
                _testsFailed++;
            }
        }

        private void AssertTrue(bool condition, string message)
        {
            if (!condition)
            {
                throw new Exception($"Assertion failed: {message}");
            }
        }

        public bool AreTestsCompleted()
        {
            return _testsCompleted;
        }

        public int GetTestsPassed()
        {
            return _testsPassed;
        }

        public int GetTestsFailed()
        {
            return _testsFailed;
        }
    }
}