using Godot;
using System.Collections.Generic;

namespace SurvivalLooterShooter
{
    /// <summary>
    /// Test runner for the weather system functionality
    /// </summary>
    public partial class WeatherSystemTests : Node
    {
        private WeatherManager _weatherManager;
        private Label _statusLabel;
        private VBoxContainer _testResults;
        private int _testsPassed = 0;
        private int _totalTests = 0;

        public override void _Ready()
        {
            SetupUI();
            RunTests();
        }

        private void SetupUI()
        {
            // Create UI for test results
            var container = new VBoxContainer();
            AddChild(container);

            var titleLabel = new Label();
            titleLabel.Text = "Weather System Tests";
            container.AddChild(titleLabel);

            _statusLabel = new Label();
            _statusLabel.Text = "Running tests...";
            container.AddChild(_statusLabel);

            _testResults = new VBoxContainer();
            container.AddChild(_testResults);
        }

        private void RunTests()
        {
            GD.Print("Starting Weather System Tests...");

            // Initialize weather manager
            _weatherManager = new WeatherManager();
            AddChild(_weatherManager);

            // Wait a frame for initialization
            GetTree().ProcessFrame += () => {
                TestWeatherManagerInitialization();
                TestWeatherTransitions();
                TestBiomeWeatherPatterns();
                TestSeasonalChanges();
                TestWeatherEffects();
                TestFireCraftingRestrictions();
                TestTemperatureCalculation();
                TestWeatherForecast();

                DisplayResults();
            };
        }

        private void TestWeatherManagerInitialization()
        {
            StartTest("Weather Manager Initialization");
            
            bool passed = _weatherManager != null && 
                         WeatherManager.Instance == _weatherManager &&
                         _weatherManager.CurrentWeather == WeatherManager.WeatherType.Clear &&
                         _weatherManager.CurrentSeason == WeatherManager.Season.Spring;
            
            EndTest(passed, "Weather manager should initialize with default values");
        }

        private void TestWeatherTransitions()
        {
            StartTest("Weather Transitions");
            
            var initialWeather = _weatherManager.CurrentWeather;
            _weatherManager.SetWeather(WeatherManager.WeatherType.LightRain, 1.0f);
            
            bool passed = _weatherManager.CurrentWeather != initialWeather;
            
            EndTest(passed, "Weather should transition when SetWeather is called");
        }

        private void TestBiomeWeatherPatterns()
        {
            StartTest("Biome Weather Patterns");
            
            _weatherManager.SetCurrentBiome("desert");
            var forecast1 = _weatherManager.GetWeatherForecast();
            
            _weatherManager.SetCurrentBiome("tundra");
            var forecast2 = _weatherManager.GetWeatherForecast();
            
            bool passed = forecast1 != null && forecast2 != null && forecast1.Length > 0 && forecast2.Length > 0;
            
            EndTest(passed, "Different biomes should generate different weather patterns");
        }

        private void TestSeasonalChanges()
        {
            StartTest("Seasonal Changes");
            
            var initialSeason = _weatherManager.CurrentSeason;
            
            // Force season change for testing
            var seasonField = typeof(WeatherManager).GetField("CurrentSeason");
            if (seasonField != null)
            {
                seasonField.SetValue(_weatherManager, WeatherManager.Season.Winter);
            }
            
            bool passed = _weatherManager.CurrentSeason == WeatherManager.Season.Winter;
            
            EndTest(passed, "Season should change when forced");
        }

        private void TestWeatherEffects()
        {
            StartTest("Weather Effects");
            
            _weatherManager.SetWeather(WeatherManager.WeatherType.HeavyRain);
            var effects = _weatherManager.GetCurrentWeatherEffects();
            
            bool passed = effects != null && 
                         effects.VisibilityModifier < 1.0f &&
                         effects.MovementSpeedModifier < 1.0f &&
                         effects.ThirstDecayModifier < 1.0f;
            
            EndTest(passed, "Heavy rain should reduce visibility, movement speed, and thirst decay");
        }

        private void TestFireCraftingRestrictions()
        {
            StartTest("Fire Crafting Restrictions");
            
            _weatherManager.SetWeather(WeatherManager.WeatherType.Clear);
            bool clearAllowed = _weatherManager.IsFireCraftingAllowed();
            
            _weatherManager.SetWeather(WeatherManager.WeatherType.HeavyRain);
            bool rainAllowed = _weatherManager.IsFireCraftingAllowed();
            
            bool passed = clearAllowed && !rainAllowed;
            
            EndTest(passed, "Fire crafting should be allowed in clear weather but not in rain");
        }

        private void TestTemperatureCalculation()
        {
            StartTest("Temperature Calculation");
            
            _weatherManager.SetCurrentBiome("desert");
            float desertTemp = _weatherManager.GetCurrentTemperature();
            
            _weatherManager.SetCurrentBiome("tundra");
            float tundraTemp = _weatherManager.GetCurrentTemperature();
            
            bool passed = desertTemp > tundraTemp;
            
            EndTest(passed, "Desert should be warmer than tundra");
        }

        private void TestWeatherForecast()
        {
            StartTest("Weather Forecast");
            
            var forecast = _weatherManager.GetWeatherForecast();
            
            bool passed = forecast != null && forecast.Length == 3; // 3-day forecast
            
            EndTest(passed, "Weather forecast should contain 3 days");
        }

        private void StartTest(string testName)
        {
            _totalTests++;
            GD.Print($"Running test: {testName}");
        }

        private void EndTest(bool passed, string description)
        {
            if (passed)
            {
                _testsPassed++;
                GD.Print($"✓ PASSED: {description}");
                
                var passLabel = new Label();
                passLabel.Text = $"✓ PASSED: {description}";
                passLabel.Modulate = Colors.Green;
                _testResults.AddChild(passLabel);
            }
            else
            {
                GD.Print($"✗ FAILED: {description}");
                
                var failLabel = new Label();
                failLabel.Text = $"✗ FAILED: {description}";
                failLabel.Modulate = Colors.Red;
                _testResults.AddChild(failLabel);
            }
        }

        private void DisplayResults()
        {
            string resultText = $"Tests completed: {_testsPassed}/{_totalTests} passed";
            _statusLabel.Text = resultText;
            
            if (_testsPassed == _totalTests)
            {
                _statusLabel.Modulate = Colors.Green;
                GD.Print($"✓ All weather system tests passed! ({_testsPassed}/{_totalTests})");
            }
            else
            {
                _statusLabel.Modulate = Colors.Red;
                GD.Print($"✗ Some weather system tests failed. ({_testsPassed}/{_totalTests})");
            }
        }
    }
}