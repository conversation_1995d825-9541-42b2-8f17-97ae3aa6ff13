using Godot;
using System;
using System.Collections.Generic;

namespace SurvivalLooterShooter
{
    /// <summary>
    /// Test runner for defense system functionality
    /// Tests turret targeting, alarm systems, and base raid mechanics
    /// </summary>
    public partial class DefenseSystemTests : Node2D
    {
        private DefenseSystem _defenseSystem;
        private List<Enemy> _testEnemies = [];
        private List<Structure> _testStructures = [];
        private Label _statusLabel;
        private Button _spawnEnemyButton;
        private Button _startRaidButton;
        private Button _buildTurretButton;
        private Button _buildWallButton;
        private Button _buildAlarmButton;
        private Timer _testTimer;
        private int _testPhase = 0;

        public override void _Ready()
        {
            SetupUI();
            SetupDefenseSystem();
            SetupTestTimer();
            
            Logger.LogInfo("DefenseSystemTests", "Defense system tests initialized");
        }

        /// <summary>
        /// Sets up the test UI
        /// </summary>
        private void SetupUI()
        {
            // Create status label
            _statusLabel = new Label();
            _statusLabel.Position = new Vector2(10, 10);
            _statusLabel.Size = new Vector2(400, 200);
            _statusLabel.Text = "Defense System Tests\nReady to begin...";
            AddChild(_statusLabel);

            // Create spawn enemy button
            _spawnEnemyButton = new Button();
            _spawnEnemyButton.Position = new Vector2(10, 220);
            _spawnEnemyButton.Size = new Vector2(120, 30);
            _spawnEnemyButton.Text = "Spawn Enemy";
            _spawnEnemyButton.Pressed += OnSpawnEnemyPressed;
            AddChild(_spawnEnemyButton);

            // Create start raid button
            _startRaidButton = new Button();
            _startRaidButton.Position = new Vector2(140, 220);
            _startRaidButton.Size = new Vector2(120, 30);
            _startRaidButton.Text = "Start Raid";
            _startRaidButton.Pressed += OnStartRaidPressed;
            AddChild(_startRaidButton);

            // Create build turret button
            _buildTurretButton = new Button();
            _buildTurretButton.Position = new Vector2(270, 220);
            _buildTurretButton.Size = new Vector2(120, 30);
            _buildTurretButton.Text = "Build Turret";
            _buildTurretButton.Pressed += OnBuildTurretPressed;
            AddChild(_buildTurretButton);

            // Create build wall button
            _buildWallButton = new Button();
            _buildWallButton.Position = new Vector2(10, 260);
            _buildWallButton.Size = new Vector2(120, 30);
            _buildWallButton.Text = "Build Wall";
            _buildWallButton.Pressed += OnBuildWallPressed;
            AddChild(_buildWallButton);

            // Create build alarm button
            _buildAlarmButton = new Button();
            _buildAlarmButton.Position = new Vector2(140, 260);
            _buildAlarmButton.Size = new Vector2(120, 30);
            _buildAlarmButton.Text = "Build Alarm";
            _buildAlarmButton.Pressed += OnBuildAlarmPressed;
            AddChild(_buildAlarmButton);
        }

        /// <summary>
        /// Sets up the defense system for testing
        /// </summary>
        private void SetupDefenseSystem()
        {
            _defenseSystem = new DefenseSystem();
            AddChild(_defenseSystem);

            // Connect to defense system events
            _defenseSystem.ThreatDetected += OnThreatDetected;
            _defenseSystem.ThreatNeutralized += OnThreatNeutralized;
            _defenseSystem.BaseRaidStarted += OnBaseRaidStarted;
            _defenseSystem.BaseRaidEnded += OnBaseRaidEnded;
            _defenseSystem.AlarmActivated += OnAlarmActivated;
        }

        /// <summary>
        /// Sets up the test automation timer
        /// </summary>
        private void SetupTestTimer()
        {
            _testTimer = new Timer();
            _testTimer.WaitTime = 2.0f;
            _testTimer.Timeout += OnTestTimerTimeout;
            AddChild(_testTimer);
            _testTimer.Start();
        }

        /// <summary>
        /// Handles test timer timeout for automated testing
        /// </summary>
        private void OnTestTimerTimeout()
        {
            switch (_testPhase)
            {
                case 0:
                    TestPhase1_BuildDefenses();
                    break;
                case 1:
                    TestPhase2_SpawnEnemies();
                    break;
                case 2:
                    TestPhase3_TestTurretTargeting();
                    break;
                case 3:
                    TestPhase4_TestAlarmSystem();
                    break;
                case 4:
                    TestPhase5_TestRaidMechanics();
                    break;
                case 5:
                    TestPhase6_TestRepairMechanics();
                    break;
                default:
                    _testTimer.Stop();
                    UpdateStatus("All tests completed!");
                    break;
            }
            
            _testPhase++;
        }

        /// <summary>
        /// Test Phase 1: Build defensive structures
        /// </summary>
        private void TestPhase1_BuildDefenses()
        {
            UpdateStatus("Phase 1: Building defensive structures...");
            
            // Build a turret
            BuildTestTurret(new Vector2(200, 200));
            
            // Build walls
            BuildTestWall(new Vector2(100, 200));
            BuildTestWall(new Vector2(300, 200));
            
            // Build alarm system
            BuildTestAlarm(new Vector2(200, 100));
            
            Logger.LogInfo("DefenseSystemTests", "Phase 1 completed: Built defensive structures");
        }

        /// <summary>
        /// Test Phase 2: Spawn test enemies
        /// </summary>
        private void TestPhase2_SpawnEnemies()
        {
            UpdateStatus("Phase 2: Spawning test enemies...");
            
            // Spawn enemies at various distances
            SpawnTestEnemy(new Vector2(400, 200)); // Within turret range
            SpawnTestEnemy(new Vector2(500, 200)); // Outside turret range
            SpawnTestEnemy(new Vector2(200, 400)); // Different angle
            
            Logger.LogInfo("DefenseSystemTests", "Phase 2 completed: Spawned test enemies");
        }

        /// <summary>
        /// Test Phase 3: Test turret targeting
        /// </summary>
        private void TestPhase3_TestTurretTargeting()
        {
            UpdateStatus("Phase 3: Testing turret targeting...");
            
            // Check if turrets have acquired targets
            bool turretsActive = false;
            foreach (var structure in _testStructures)
            {
                if (structure.IsDefensiveTurret && structure.DefensiveTurret != null)
                {
                    var turret = structure.DefensiveTurret;
                    if (turret.CurrentTarget != null)
                    {
                        turretsActive = true;
                        Logger.LogInfo("DefenseSystemTests", $"Turret has target: {turret.CurrentTarget.EnemyName}");
                    }
                }
            }
            
            if (turretsActive)
            {
                Logger.LogInfo("DefenseSystemTests", "Phase 3 completed: Turret targeting working");
            }
            else
            {
                Logger.LogWarning("DefenseSystemTests", "Phase 3 warning: No turret targets found");
            }
        }

        /// <summary>
        /// Test Phase 4: Test alarm system
        /// </summary>
        private void TestPhase4_TestAlarmSystem()
        {
            UpdateStatus("Phase 4: Testing alarm system...");
            
            // Check if alarms are active
            bool alarmsActive = false;
            foreach (var structure in _testStructures)
            {
                if (structure.IsAlarmSystem && structure.AlarmSystem != null)
                {
                    var alarm = structure.AlarmSystem;
                    if (alarm.IsActive)
                    {
                        alarmsActive = true;
                        Logger.LogInfo("DefenseSystemTests", $"Alarm active: {alarm.CurrentAlarmType}");
                    }
                }
            }
            
            if (alarmsActive)
            {
                Logger.LogInfo("DefenseSystemTests", "Phase 4 completed: Alarm system working");
            }
            else
            {
                Logger.LogWarning("DefenseSystemTests", "Phase 4 warning: No active alarms found");
            }
        }

        /// <summary>
        /// Test Phase 5: Test raid mechanics
        /// </summary>
        private void TestPhase5_TestRaidMechanics()
        {
            UpdateStatus("Phase 5: Testing raid mechanics...");
            
            // Manually trigger a raid for testing
            if (_defenseSystem != null)
            {
                // This would normally be triggered by conditions
                Logger.LogInfo("DefenseSystemTests", "Phase 5: Raid mechanics test (manual trigger would go here)");
            }
        }

        /// <summary>
        /// Test Phase 6: Test repair mechanics
        /// </summary>
        private void TestPhase6_TestRepairMechanics()
        {
            UpdateStatus("Phase 6: Testing repair mechanics...");
            
            // Damage a wall and test repair
            foreach (var structure in _testStructures)
            {
                if (structure.IsDefenseWall && structure.DefenseWall != null)
                {
                    var wall = structure.DefenseWall;
                    wall.TakeDamage(50);
                    
                    if (wall.NeedsRepair)
                    {
                        Logger.LogInfo("DefenseSystemTests", "Wall damaged and needs repair - test successful");
                    }
                    break;
                }
            }
            
            Logger.LogInfo("DefenseSystemTests", "Phase 6 completed: Repair mechanics tested");
        }

        /// <summary>
        /// Builds a test turret at the specified position
        /// </summary>
        private void BuildTestTurret(Vector2 position)
        {
            var structure = CreateTestStructure("defensive_turret", position);
            if (structure != null)
            {
                _testStructures.Add(structure);
                Logger.LogInfo("DefenseSystemTests", $"Built test turret at {position}");
            }
        }

        /// <summary>
        /// Builds a test wall at the specified position
        /// </summary>
        private void BuildTestWall(Vector2 position)
        {
            var structure = CreateTestStructure("reinforced_wall", position);
            if (structure != null)
            {
                _testStructures.Add(structure);
                Logger.LogInfo("DefenseSystemTests", $"Built test wall at {position}");
            }
        }

        /// <summary>
        /// Builds a test alarm at the specified position
        /// </summary>
        private void BuildTestAlarm(Vector2 position)
        {
            var structure = CreateTestStructure("alarm_system", position);
            if (structure != null)
            {
                _testStructures.Add(structure);
                Logger.LogInfo("DefenseSystemTests", $"Built test alarm at {position}");
            }
        }

        /// <summary>
        /// Creates a test structure of the specified type
        /// </summary>
        private Structure CreateTestStructure(string structureId, Vector2 position)
        {
            // Get blueprint from building manager
            var buildingManager = BuildingManager.Instance;
            if (buildingManager == null)
            {
                Logger.LogError("DefenseSystemTests", "BuildingManager not found");
                return null;
            }

            var blueprint = buildingManager.GetBlueprint(structureId);
            if (blueprint == null)
            {
                Logger.LogError("DefenseSystemTests", $"Blueprint not found: {structureId}");
                return null;
            }

            // Create structure
            var structure = new Structure();
            AddChild(structure);
            structure.GlobalPosition = position;
            structure.Initialize(blueprint, Vector2.Zero);

            return structure;
        }

        /// <summary>
        /// Spawns a test enemy at the specified position
        /// </summary>
        private void SpawnTestEnemy(Vector2 position)
        {
            var enemy = new Enemy();
            AddChild(enemy);
            enemy.GlobalPosition = position;
            
            // Initialize with test data
            var enemyData = new EnemyData
            {
                Id = "test_enemy",
                Name = "Test Enemy",
                Health = 50f,
                MaxHealth = 50f,
                Damage = 10f,
                Speed = 80f,
                DetectionRange = 100f,
                AttackRange = 30f,
                AIType = "aggressive"
            };
            
            enemy.Initialize(enemyData);
            _testEnemies.Add(enemy);
            
            Logger.LogInfo("DefenseSystemTests", $"Spawned test enemy at {position}");
        }

        /// <summary>
        /// Updates the status display
        /// </summary>
        private void UpdateStatus(string message)
        {
            if (_statusLabel != null)
            {
                var threatAssessment = _defenseSystem?.GetThreatAssessment();
                string status = $"Defense System Tests\n{message}\n\n";
                
                if (threatAssessment != null)
                {
                    status += $"Threats: {threatAssessment["threat_count"]}\n";
                    status += $"Threat Level: {threatAssessment["total_threat_level"]:F1}\n";
                    status += $"Active Turrets: {threatAssessment["active_turrets"]}\n";
                    status += $"Defense Walls: {threatAssessment["defense_walls"]}\n";
                    status += $"Alarm Systems: {threatAssessment["alarm_systems"]}\n";
                    status += $"Raid in Progress: {threatAssessment["raid_in_progress"]}\n";
                }
                
                _statusLabel.Text = status;
            }
        }

        /// <summary>
        /// Button event handlers
        /// </summary>
        private void OnSpawnEnemyPressed()
        {
            Vector2 spawnPos = new Vector2(GD.RandRange(100, 500), GD.RandRange(100, 400));
            SpawnTestEnemy(spawnPos);
        }

        private void OnStartRaidPressed()
        {
            Logger.LogInfo("DefenseSystemTests", "Manual raid start requested");
            // Would trigger raid mechanics here
        }

        private void OnBuildTurretPressed()
        {
            Vector2 buildPos = new Vector2(GD.RandRange(150, 350), GD.RandRange(150, 350));
            BuildTestTurret(buildPos);
        }

        private void OnBuildWallPressed()
        {
            Vector2 buildPos = new Vector2(GD.RandRange(150, 350), GD.RandRange(150, 350));
            BuildTestWall(buildPos);
        }

        private void OnBuildAlarmPressed()
        {
            Vector2 buildPos = new Vector2(GD.RandRange(150, 350), GD.RandRange(150, 350));
            BuildTestAlarm(buildPos);
        }

        /// <summary>
        /// Defense system event handlers
        /// </summary>
        private void OnThreatDetected(Enemy enemy, float threatLevel)
        {
            Logger.LogInfo("DefenseSystemTests", $"Threat detected: {enemy.EnemyName} (Level: {threatLevel:F1})");
            UpdateStatus($"Threat detected: {enemy.EnemyName}");
        }

        private void OnThreatNeutralized(Enemy enemy)
        {
            Logger.LogInfo("DefenseSystemTests", $"Threat neutralized: {enemy.EnemyName}");
            UpdateStatus($"Threat neutralized: {enemy.EnemyName}");
        }

        private void OnBaseRaidStarted(float intensity, int enemyCount)
        {
            Logger.LogInfo("DefenseSystemTests", $"Base raid started! Intensity: {intensity}, Enemies: {enemyCount}");
            UpdateStatus($"RAID STARTED! Intensity: {intensity:F1}");
        }

        private void OnBaseRaidEnded(bool playerVictory, float damageDealt)
        {
            Logger.LogInfo("DefenseSystemTests", $"Base raid ended. Victory: {playerVictory}, Damage: {damageDealt}");
            UpdateStatus($"Raid ended. Victory: {playerVictory}");
        }

        private void OnAlarmActivated(string alarmType, Vector2 position)
        {
            Logger.LogInfo("DefenseSystemTests", $"Alarm activated: {alarmType} at {position}");
            UpdateStatus($"ALARM: {alarmType}");
        }

        public override void _ExitTree()
        {
            // Clean up test objects
            foreach (var enemy in _testEnemies)
            {
                enemy?.QueueFree();
            }
            
            foreach (var structure in _testStructures)
            {
                structure?.QueueFree();
            }
        }
    }
}