using Godot;
using System;
using System.Collections.Generic;
using System.Linq;

namespace SurvivalLooterShooter
{
    /// <summary>
    /// Manages unlockable abilities and enhanced crafting options from skill progression
    /// </summary>
    public partial class AbilitySystem : Node
    {
        public static AbilitySystem Instance { get; private set; }
        
        private Dictionary<string, PlayerAbility> _availableAbilities = new();
        private HashSet<string> _unlockedAbilities = new();
        private Dictionary<string, float> _abilityCooldowns = new();
        private Dictionary<string, CraftingEnhancement> _craftingEnhancements = new();
        private HashSet<string> _activeCraftingEnhancements = new();
        
        [Signal]
        public delegate void AbilityUnlockedEventHandler(string abilityId, string abilityName);
        
        [Signal]
        public delegate void AbilityActivatedEventHandler(string abilityId, float duration);
        
        [Signal]
        public delegate void CraftingEnhancementUnlockedEventHandler(string enhancementId, string description);
        
        public override void _Ready()
        {
            if (Instance == null)
            {
                Instance = this;
                InitializeAbilities();
                InitializeCraftingEnhancements();
                ConnectToEventBus();
            }
            else
            {
                QueueFree();
            }
        }
        
        private void ConnectToEventBus()
        {
            if (EventBus.Instance != null)
            {
                EventBus.Instance.SkillLevelUp += OnSkillLevelUp;
            }
        }
        
        private void InitializeAbilities()
        {
            // Combat Abilities
            _availableAbilities["berserker_mode"] = new PlayerAbility
            {
                Id = "berserker_mode",
                Name = "Berserker Mode",
                Description = "Increases damage and attack speed for 30 seconds",
                Category = SkillType.Combat,
                RequiredSkillLevel = 25,
                RequiredSkill = "weapon_proficiency",
                Cooldown = 120f,
                Duration = 30f,
                Effects = new Dictionary<string, float>
                {
                    ["damage_multiplier"] = 1.5f,
                    ["attack_speed_multiplier"] = 1.3f,
                    ["movement_speed_multiplier"] = 1.2f
                }
            };
            
            _availableAbilities["precision_shot"] = new PlayerAbility
            {
                Id = "precision_shot",
                Name = "Precision Shot",
                Description = "Next shot deals 300% damage and ignores armor",
                Category = SkillType.Combat,
                RequiredSkillLevel = 15,
                RequiredSkill = "critical_hit",
                Cooldown = 45f,
                Duration = 10f,
                Effects = new Dictionary<string, float>
                {
                    ["next_shot_damage"] = 3.0f,
                    ["armor_penetration"] = 1.0f
                }
            };
            
            _availableAbilities["combat_stim"] = new PlayerAbility
            {
                Id = "combat_stim",
                Name = "Combat Stimulant",
                Description = "Instantly reload all weapons and gain temporary damage resistance",
                Category = SkillType.Combat,
                RequiredSkillLevel = 20,
                RequiredSkill = "reload_speed",
                Cooldown = 90f,
                Duration = 15f,
                Effects = new Dictionary<string, float>
                {
                    ["instant_reload"] = 1.0f,
                    ["damage_resistance"] = 0.3f
                }
            };
            
            // Crafting Abilities
            _availableAbilities["master_craftsman"] = new PlayerAbility
            {
                Id = "master_craftsman",
                Name = "Master Craftsman",
                Description = "Next 5 crafted items have guaranteed quality bonus",
                Category = SkillType.Crafting,
                RequiredSkillLevel = 20,
                RequiredSkill = "quality_bonus",
                Cooldown = 300f,
                Duration = 0f, // Instant effect
                Effects = new Dictionary<string, float>
                {
                    ["guaranteed_quality_crafts"] = 5f
                }
            };
            
            _availableAbilities["resource_efficiency"] = new PlayerAbility
            {
                Id = "resource_efficiency",
                Name = "Resource Efficiency",
                Description = "Crafting costs 50% fewer materials for 2 minutes",
                Category = SkillType.Crafting,
                RequiredSkillLevel = 15,
                RequiredSkill = "crafting_efficiency",
                Cooldown = 180f,
                Duration = 120f,
                Effects = new Dictionary<string, float>
                {
                    ["material_cost_reduction"] = 0.5f
                }
            };
            
            // Survival Abilities
            _availableAbilities["second_wind"] = new PlayerAbility
            {
                Id = "second_wind",
                Name = "Second Wind",
                Description = "Instantly restore 50% health and gain temporary stat decay immunity",
                Category = SkillType.Survival,
                RequiredSkillLevel = 18,
                RequiredSkill = "medicine",
                Cooldown = 240f,
                Duration = 60f,
                Effects = new Dictionary<string, float>
                {
                    ["instant_health_restore"] = 0.5f,
                    ["stat_decay_immunity"] = 1.0f
                }
            };
            
            _availableAbilities["forager_instinct"] = new PlayerAbility
            {
                Id = "forager_instinct",
                Name = "Forager's Instinct",
                Description = "Double resource yield and reveal nearby resource nodes for 3 minutes",
                Category = SkillType.Survival,
                RequiredSkillLevel = 12,
                RequiredSkill = "foraging",
                Cooldown = 200f,
                Duration = 180f,
                Effects = new Dictionary<string, float>
                {
                    ["resource_yield_multiplier"] = 2.0f,
                    ["resource_detection_range"] = 50f
                }
            };
            
            // Building Abilities
            _availableAbilities["rapid_construction"] = new PlayerAbility
            {
                Id = "rapid_construction",
                Name = "Rapid Construction",
                Description = "Build structures instantly for the next 2 minutes",
                Category = SkillType.Building,
                RequiredSkillLevel = 25,
                RequiredSkill = "construction",
                Cooldown = 600f,
                Duration = 120f,
                Effects = new Dictionary<string, float>
                {
                    ["instant_building"] = 1.0f
                }
            };
            
            _availableAbilities["structural_insight"] = new PlayerAbility
            {
                Id = "structural_insight",
                Name = "Structural Insight",
                Description = "Next 3 structures built have double durability and efficiency",
                Category = SkillType.Building,
                RequiredSkillLevel = 20,
                RequiredSkill = "durability",
                Cooldown = 300f,
                Duration = 0f,
                Effects = new Dictionary<string, float>
                {
                    ["enhanced_structures"] = 3f,
                    ["durability_multiplier"] = 2.0f,
                    ["efficiency_multiplier"] = 2.0f
                }
            };
        }
        
        private void InitializeCraftingEnhancements()
        {
            // Material Efficiency Enhancements
            _craftingEnhancements["advanced_recycling"] = new CraftingEnhancement
            {
                Id = "advanced_recycling",
                Name = "Advanced Recycling",
                Description = "25% chance to recover materials when dismantling items",
                RequiredSkillLevel = 10,
                RequiredSkill = "crafting_efficiency",
                Effects = new Dictionary<string, float>
                {
                    ["dismantle_recovery_chance"] = 0.25f
                }
            };
            
            _craftingEnhancements["bulk_crafting"] = new CraftingEnhancement
            {
                Id = "bulk_crafting",
                Name = "Bulk Crafting",
                Description = "Craft up to 5 items simultaneously with 10% material discount",
                RequiredSkillLevel = 15,
                RequiredSkill = "crafting_efficiency",
                Effects = new Dictionary<string, float>
                {
                    ["max_bulk_craft"] = 5f,
                    ["bulk_material_discount"] = 0.1f
                }
            };
            
            // Quality Enhancements
            _craftingEnhancements["quality_control"] = new CraftingEnhancement
            {
                Id = "quality_control",
                Name = "Quality Control",
                Description = "Can inspect and improve item quality before finalizing craft",
                RequiredSkillLevel = 12,
                RequiredSkill = "quality_bonus",
                Effects = new Dictionary<string, float>
                {
                    ["quality_inspection"] = 1.0f,
                    ["quality_improvement_chance"] = 0.3f
                }
            };
            
            _craftingEnhancements["masterwork_chance"] = new CraftingEnhancement
            {
                Id = "masterwork_chance",
                Name = "Masterwork Crafting",
                Description = "5% chance to create masterwork items with enhanced stats",
                RequiredSkillLevel = 18,
                RequiredSkill = "quality_bonus",
                Effects = new Dictionary<string, float>
                {
                    ["masterwork_chance"] = 0.05f,
                    ["masterwork_stat_bonus"] = 0.5f
                }
            };
            
            // Speed Enhancements
            _craftingEnhancements["assembly_line"] = new CraftingEnhancement
            {
                Id = "assembly_line",
                Name = "Assembly Line",
                Description = "Queue multiple recipes to craft automatically",
                RequiredSkillLevel = 20,
                RequiredSkill = "crafting_speed",
                Effects = new Dictionary<string, float>
                {
                    ["max_craft_queue"] = 10f,
                    ["queue_speed_bonus"] = 0.2f
                }
            };
            
            // Advanced Recipe Enhancements
            _craftingEnhancements["recipe_modification"] = new CraftingEnhancement
            {
                Id = "recipe_modification",
                Name = "Recipe Modification",
                Description = "Modify existing recipes to use alternative materials",
                RequiredSkillLevel = 25,
                RequiredSkill = "advanced_recipes",
                Effects = new Dictionary<string, float>
                {
                    ["recipe_substitution"] = 1.0f,
                    ["substitution_efficiency"] = 0.9f
                }
            };
        }
        
        private void OnSkillLevelUp(string skillId, int newLevel)
        {
            CheckForAbilityUnlocks(skillId, newLevel);
            CheckForCraftingEnhancementUnlocks(skillId, newLevel);
        }
        
        private void CheckForAbilityUnlocks(string skillId, int skillLevel)
        {
            foreach (var ability in _availableAbilities.Values)
            {
                if (ability.RequiredSkill == skillId && 
                    skillLevel >= ability.RequiredSkillLevel &&
                    _unlockedAbilities.Add(ability.Id))
                {
                    EmitSignal(SignalName.AbilityUnlocked, ability.Id, ability.Name);
                    GD.Print($"Ability unlocked: {ability.Name}");
                }
            }
        }
        
        private void CheckForCraftingEnhancementUnlocks(string skillId, int skillLevel)
        {
            foreach (var enhancement in _craftingEnhancements.Values)
            {
                if (enhancement.RequiredSkill == skillId && 
                    skillLevel >= enhancement.RequiredSkillLevel &&
                    _activeCraftingEnhancements.Add(enhancement.Id))
                {
                    EmitSignal(SignalName.CraftingEnhancementUnlocked, enhancement.Id, enhancement.Description);
                    GD.Print($"Crafting enhancement unlocked: {enhancement.Name}");
                }
            }
        }
        
        public bool ActivateAbility(string abilityId)
        {
            if (!_unlockedAbilities.Contains(abilityId)) return false;
            if (!_availableAbilities.TryGetValue(abilityId, out var ability)) return false;
            if (_abilityCooldowns.ContainsKey(abilityId)) return false; // On cooldown
            
            // Apply ability effects
            ApplyAbilityEffects(ability);
            
            // Start cooldown
            _abilityCooldowns[abilityId] = ability.Cooldown;
            
            // Start duration timer if applicable
            if (ability.Duration > 0)
            {
                GetTree().CreateTimer(ability.Duration).Timeout += () => RemoveAbilityEffects(ability);
            }
            
            EmitSignal(SignalName.AbilityActivated, abilityId, ability.Duration);
            GD.Print($"Ability activated: {ability.Name}");
            
            return true;
        }
        
        private void ApplyAbilityEffects(PlayerAbility ability)
        {
            foreach (var effect in ability.Effects)
            {
                switch (effect.Key)
                {
                    case "damage_multiplier":
                    case "attack_speed_multiplier":
                    case "movement_speed_multiplier":
                        // Apply to combat system
                        EventBus.Instance?.EmitCombatBuffApplied(effect.Key, effect.Value, ability.Duration);
                        break;
                        
                    case "instant_reload":
                        // Trigger instant reload
                        EventBus.Instance?.EmitInstantReloadTriggered();
                        break;
                        
                    case "instant_health_restore":
                        // Restore health percentage
                        EventBus.Instance?.EmitHealthRestored(effect.Value);
                        break;
                        
                    case "guaranteed_quality_crafts":
                        // Set guaranteed quality counter
                        EventBus.Instance?.EmitCraftingBuffApplied("guaranteed_quality", (int)effect.Value);
                        break;
                        
                    case "material_cost_reduction":
                        // Apply crafting cost reduction
                        EventBus.Instance?.EmitCraftingBuffApplied("cost_reduction", effect.Value);
                        break;
                        
                    case "resource_yield_multiplier":
                        // Apply harvesting bonus
                        EventBus.Instance?.EmitHarvestingBuffApplied("yield_multiplier", effect.Value);
                        break;
                        
                    case "instant_building":
                        // Enable instant building
                        EventBus.Instance?.EmitBuildingBuffApplied("instant_build", 1.0f);
                        break;
                }
            }
        }
        
        private void RemoveAbilityEffects(PlayerAbility ability)
        {
            foreach (var effect in ability.Effects)
            {
                switch (effect.Key)
                {
                    case "damage_multiplier":
                    case "attack_speed_multiplier":
                    case "movement_speed_multiplier":
                        EventBus.Instance?.EmitCombatBuffRemoved(effect.Key);
                        break;
                        
                    case "material_cost_reduction":
                        EventBus.Instance?.EmitCraftingBuffRemoved("cost_reduction");
                        break;
                        
                    case "resource_yield_multiplier":
                        EventBus.Instance?.EmitHarvestingBuffRemoved("yield_multiplier");
                        break;
                        
                    case "instant_building":
                        EventBus.Instance?.EmitBuildingBuffRemoved("instant_build");
                        break;
                }
            }
            
            GD.Print($"Ability effects removed: {ability.Name}");
        }
        
        public override void _Process(double delta)
        {
            // Update ability cooldowns
            var cooldownKeys = _abilityCooldowns.Keys.ToList();
            foreach (var abilityId in cooldownKeys)
            {
                _abilityCooldowns[abilityId] -= (float)delta;
                if (_abilityCooldowns[abilityId] <= 0)
                {
                    _abilityCooldowns.Remove(abilityId);
                }
            }
        }
        
        // Public API methods
        public bool IsAbilityUnlocked(string abilityId)
        {
            return _unlockedAbilities.Contains(abilityId);
        }
        
        public bool IsAbilityOnCooldown(string abilityId)
        {
            return _abilityCooldowns.ContainsKey(abilityId);
        }
        
        public float GetAbilityCooldownRemaining(string abilityId)
        {
            return _abilityCooldowns.GetValueOrDefault(abilityId, 0f);
        }
        
        public bool IsCraftingEnhancementActive(string enhancementId)
        {
            return _activeCraftingEnhancements.Contains(enhancementId);
        }
        
        public float GetCraftingEnhancementValue(string enhancementId, string effectType)
        {
            if (!_activeCraftingEnhancements.Contains(enhancementId)) return 0f;
            if (!_craftingEnhancements.TryGetValue(enhancementId, out var enhancement)) return 0f;
            
            return enhancement.Effects.GetValueOrDefault(effectType, 0f);
        }
        
        public List<PlayerAbility> GetUnlockedAbilities()
        {
            return _unlockedAbilities
                .Where(id => _availableAbilities.ContainsKey(id))
                .Select(id => _availableAbilities[id])
                .ToList();
        }
        
        public List<CraftingEnhancement> GetActiveCraftingEnhancements()
        {
            return _activeCraftingEnhancements
                .Where(id => _craftingEnhancements.ContainsKey(id))
                .Select(id => _craftingEnhancements[id])
                .ToList();
        }
        
        public Dictionary<string, object> GetSaveData()
        {
            return new Dictionary<string, object>
            {
                ["unlocked_abilities"] = _unlockedAbilities.ToList(),
                ["active_crafting_enhancements"] = _activeCraftingEnhancements.ToList(),
                ["ability_cooldowns"] = _abilityCooldowns
            };
        }
        
        public void LoadSaveData(Dictionary<string, object> saveData)
        {
            if (saveData.TryGetValue("unlocked_abilities", out var abilities))
            {
                _unlockedAbilities = new HashSet<string>((List<string>)abilities);
            }
            
            if (saveData.TryGetValue("active_crafting_enhancements", out var enhancements))
            {
                _activeCraftingEnhancements = new HashSet<string>((List<string>)enhancements);
            }
            
            if (saveData.TryGetValue("ability_cooldowns", out var cooldowns))
            {
                _abilityCooldowns = (Dictionary<string, float>)cooldowns;
            }
        }
    }
    
    // Supporting data structures
    [Serializable]
    public class PlayerAbility
    {
        public string Id { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public SkillType Category { get; set; }
        public string RequiredSkill { get; set; }
        public int RequiredSkillLevel { get; set; }
        public float Cooldown { get; set; }
        public float Duration { get; set; }
        public Dictionary<string, float> Effects { get; set; } = new();
    }
    
    [Serializable]
    public class CraftingEnhancement
    {
        public string Id { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public string RequiredSkill { get; set; }
        public int RequiredSkillLevel { get; set; }
        public Dictionary<string, float> Effects { get; set; } = new();
    }
}