[gd_scene load_steps=3 format=3 uid="uid://c2d3e4f5g6h7i"]

[ext_resource type="Script" uid="uid://dmo8dwnwv8dvo" path="res://Scripts/InventorySlotUI.cs" id="1_2b3c4"]

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_1"]
bg_color = Color(0.3, 0.3, 0.3, 1)
border_width_left = 1
border_width_top = 1
border_width_right = 1
border_width_bottom = 1
border_color = Color(0.6, 0.6, 0.6, 1)
corner_radius_top_left = 3
corner_radius_top_right = 3
corner_radius_bottom_right = 3
corner_radius_bottom_left = 3

[node name="InventorySlotUI" type="Control"]
custom_minimum_size = Vector2(64, 64)
layout_mode = 3
anchors_preset = 0
script = ExtResource("1_2b3c4")

[node name="Background" type="Panel" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
theme_override_styles/panel = SubResource("StyleBoxFlat_1")

[node name="ItemIcon" type="TextureRect" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 4.0
offset_top = 4.0
offset_right = -4.0
offset_bottom = -4.0
grow_horizontal = 2
grow_vertical = 2
expand_mode = 1
stretch_mode = 5

[node name="QuantityLabel" type="Label" parent="."]
layout_mode = 1
anchors_preset = 3
anchor_left = 1.0
anchor_top = 1.0
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = -20.0
offset_top = -20.0
grow_horizontal = 0
grow_vertical = 0
text = "1"
horizontal_alignment = 2
vertical_alignment = 2

[node name="DurabilityBar" type="ProgressBar" parent="."]
visible = false
layout_mode = 1
anchors_preset = 7
anchor_left = 0.5
anchor_top = 1.0
anchor_right = 0.5
anchor_bottom = 1.0
offset_left = -25.0
offset_top = -8.0
offset_right = 25.0
offset_bottom = -4.0
grow_horizontal = 2
grow_vertical = 0
step = 1.0
value = 100.0
show_percentage = false
