[docks]

dock_3_selected_tab_idx=0
dock_4_selected_tab_idx=0
dock_5_selected_tab_idx=0
dock_floating={}
dock_filesystem_h_split_offset=240
dock_filesystem_v_split_offset=0
dock_filesystem_display_mode=0
dock_filesystem_file_sort=0
dock_filesystem_file_list_display_mode=1
dock_filesystem_selected_paths=PackedStringArray("res://Scenes/Main.tscn")
dock_filesystem_uncollapsed_paths=PackedStringArray("Favorites", "res://", "res://Scripts/", "res://Scenes/", "res://Data/")
dock_node_current_tab=0
dock_history_include_scene=true
dock_history_include_global=true
dock_bottom=[]
dock_closed=[]
dock_split_2=0
dock_split_3=0
dock_hsplit_1=0
dock_hsplit_2=270
dock_hsplit_3=-270
dock_hsplit_4=0
dock_3="Scene,Import"
dock_4="FileSystem"
dock_5="Inspector,Node,History"

[EditorNode]

open_scenes=PackedStringArray("res://Scenes/Main.tscn", "res://Scenes/SurvivalHUD.tscn", "res://Scenes/CraftingUI.tscn", "res://Scenes/ItemPickup.tscn", "res://Scenes/InventoryUI.tscn", "res://Scenes/InventorySlotUI.tscn", "res://Scenes/Enemy.tscn", "res://Scenes/ResourceHarvestingDemo.tscn", "res://Scenes/Structure.tscn", "res://Scenes/CraftingStationUI.tscn", "res://Scenes/CraftingStationDemo.tscn", "res://Scenes/BuildingUI.tscn", "res://Scenes/StorageContainerUI.tscn", "res://Scenes/SettingsMenu.tscn", "res://Scenes/SkillUI.tscn")
current_scene="res://Scenes/Main.tscn"
center_split_offset=-291
selected_default_debugger_tab_idx=1
selected_main_editor_idx=0
selected_bottom_panel_item=0

[EditorWindow]

screen=1
mode="maximized"
position=Vector2i(2560, 23)

[ScriptEditor]

open_scripts=["res://Scripts/Item.cs", "res://Data/Items.json", "res://Data/LootTables.json", "res://Scripts/Recipe.cs"]
selected_script="res://Data/Items.json"
open_help=[]
script_split_offset=200
list_split_offset=0
zoom_factor=1.0

[GameView]

floating_window_rect=Rect2i(0, 0, 0, 0)
floating_window_screen=-1

[ShaderEditor]

open_shaders=[]
split_offset=200
selected_shader=""
text_shader_zoom_factor=1.0
