using Godot;
using SurvivalLooterShooter;
using System;
using System.Collections.Generic;

public partial class EndgameContentTests : Node
{
    public override void _Ready()
    {
        GD.Print("Starting Endgame Content Tests...");
        
        TestQuestSystem();
        TestBossSystem();
        TestSpecialEvents();
        TestNewGamePlus();
        TestLeaderboards();
        TestModSystem();
        
        GD.Print("Endgame Content Tests completed!");
    }

    private void TestQuestSystem()
    {
        GD.Print("Testing Quest System...");
        
        var questManager = new QuestManager();
        AddChild(questManager);
        
        // Generate a test quest
        questManager.GenerateRandomQuest();
        
        var availableQuests = questManager.GetAvailableQuests();
        GD.Print($"Generated {availableQuests.Count} available quests");
        
        if (availableQuests.Count > 0)
        {
            var testQuest = availableQuests[0];
            questManager.AcceptQuest(testQuest);
            
            var activeQuests = questManager.GetActiveQuests();
            GD.Print($"Active quests: {activeQuests.Count}");
            
            // Simulate quest progress
            testQuest.UpdateObjective("test_target", 1);
        }
    }

    private void TestBossSystem()
    {
        GD.Print("Testing Boss System...");
        
        var bossEnemy = new BossEnemy();
        bossEnemy.BossType = BossType.AlphaWolf;
        AddChild(bossEnemy);
        
        GD.Print($"Boss created: {bossEnemy.BossType}");
        
        // Test boss abilities
        bossEnemy.TakeDamage(bossEnemy.MaxHealth * 0.8f); // Trigger rage mode
        
        bossEnemy.QueueFree();
    }

    private void TestSpecialEvents()
    {
        GD.Print("Testing Special Events...");
        
        var eventManager = new SpecialEventManager();
        AddChild(eventManager);
        
        // Force start a test event
        eventManager.ForceStartEvent(SpecialEventType.BloodMoon);
        
        var activeEvents = eventManager.GetActiveEvents();
        GD.Print($"Active special events: {activeEvents.Count}");
    }

    private void TestNewGamePlus()
    {
        GD.Print("Testing New Game Plus...");
        
        var ngPlusManager = new NewGamePlusManager();
        AddChild(ngPlusManager);
        
        var stats = ngPlusManager.GetNGPlusStats();
        GD.Print($"NG+ Level: {stats["plus_level"]}");
        GD.Print($"Difficulty Multiplier: {stats["difficulty_multiplier"]}");
        
        var canStart = ngPlusManager.CanStartNewGamePlus();
        GD.Print($"Can start NG+: {canStart}");
    }

    private void TestLeaderboards()
    {
        GD.Print("Testing Leaderboards...");
        
        var leaderboardManager = new LeaderboardManager();
        AddChild(leaderboardManager);
        
        // Update some test scores
        leaderboardManager.UpdateLeaderboard(LeaderboardCategory.EnemiesKilled, 100f);
        leaderboardManager.UpdateLeaderboard(LeaderboardCategory.SurvivalTime, 3600f);
        
        var leaderboard = leaderboardManager.GetLeaderboard(LeaderboardCategory.EnemiesKilled);
        GD.Print($"Leaderboard entries: {leaderboard.Count}");
        
        var playerRanks = leaderboardManager.GetPlayerRanks();
        foreach (var rank in playerRanks)
        {
            GD.Print($"{rank.Key}: Rank {rank.Value}");
        }
    }

    private void TestModSystem()
    {
        GD.Print("Testing Mod System...");
        
        var modManager = new ModManager();
        AddChild(modManager);
        
        // Create a test mod template
        modManager.CreateModTemplate("test_mod", "Test Mod", "Test Author");
        
        var loadedMods = modManager.GetLoadedMods();
        GD.Print($"Loaded mods: {loadedMods.Count}");
        
        // Test custom content
        var customItems = modManager.GetCustomContent("item");
        GD.Print($"Custom items: {customItems.Count}");
    }
}