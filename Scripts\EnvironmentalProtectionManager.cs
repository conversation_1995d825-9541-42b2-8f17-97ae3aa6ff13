using Godot;
using System;
using System.Collections.Generic;

namespace SurvivalLooterShooter
{
    /// <summary>
    /// Manages comprehensive environmental protection from clothing, shelter, and equipment
    /// Integrates with temperature, weather, and hazard systems
    /// </summary>
    public partial class EnvironmentalProtectionManager : Node
    {
        private static EnvironmentalProtectionManager _instance;
        public static EnvironmentalProtectionManager Instance => _instance;

        // Protection sources
        private Dictionary<string, ProtectionSource> _protectionSources = new Dictionary<string, ProtectionSource>();
        
        // Current protection values
        private EnvironmentalProtection _currentProtection = new EnvironmentalProtection();
        
        // Shelter detection
        private bool _isInShelter = false;
        private ShelterType _currentShelterType = ShelterType.None;
        private Timer _shelterCheckTimer;
        
        // Events
        [Signal] public delegate void ProtectionUpdatedEventHandler(string protectionDataJson);
        [Signal] public delegate void ShelterStatusChangedEventHandler(bool inShelter, string shelterType);
        [Signal] public delegate void ProtectionBreachedEventHandler(string protectionType, float severity);

        public enum ShelterType
        {
            None,
            BasicShelter,    // Wooden hut, tent
            ReinforcedShelter, // Stone building, reinforced structure
            UndergroundShelter, // Cave, bunker
            AdvancedShelter   // High-tech shelter with climate control
        }

        public override void _Ready()
        {
            // Singleton setup
            if (_instance == null)
            {
                _instance = this;
                AddToGroup("environmental_protection_manager");
                Logger.LogInfo("EnvironmentalProtectionManager", "EnvironmentalProtectionManager singleton initialized");
            }
            else
            {
                Logger.LogError("EnvironmentalProtectionManager", "Multiple EnvironmentalProtectionManager instances detected! Removing duplicate.");
                QueueFree();
                return;
            }

            InitializeProtectionSources();
            SetupShelterDetection();
            ConnectToSystems();
            
            Logger.LogInfo("EnvironmentalProtectionManager", "Environmental protection manager initialized");
        }

        /// <summary>
        /// Initializes protection sources
        /// </summary>
        private void InitializeProtectionSources()
        {
            // Initialize with default values
            _protectionSources["clothing"] = new ProtectionSource
            {
                Name = "Clothing",
                ColdProtection = 0f,
                HeatProtection = 0f,
                WaterResistance = 0f,
                WindResistance = 0f,
                RadiationProtection = 0f,
                ToxicGasProtection = 0f,
                IsActive = true
            };

            _protectionSources["shelter"] = new ProtectionSource
            {
                Name = "Shelter",
                ColdProtection = 0f,
                HeatProtection = 0f,
                WaterResistance = 0f,
                WindResistance = 0f,
                RadiationProtection = 0f,
                ToxicGasProtection = 0f,
                IsActive = false
            };

            UpdateTotalProtection();
        }

        /// <summary>
        /// Sets up shelter detection timer
        /// </summary>
        private void SetupShelterDetection()
        {
            _shelterCheckTimer = new Timer();
            AddChild(_shelterCheckTimer);
            _shelterCheckTimer.WaitTime = 2f; // Check shelter status every 2 seconds
            _shelterCheckTimer.Autostart = true;
            _shelterCheckTimer.Timeout += CheckShelterStatus;
        }

        /// <summary>
        /// Connects to other systems
        /// </summary>
        private void ConnectToSystems()
        {
            // Connect to clothing system
            if (ClothingProtectionSystem.Instance != null)
            {
                ClothingProtectionSystem.Instance.ProtectionUpdated += OnClothingProtectionUpdated;
            }

            // Connect to temperature system
            if (TemperatureSystem.Instance != null)
            {
                ProtectionUpdated += OnProtectionUpdated;
            }

            // Connect to environmental hazards
            if (EnvironmentalHazardSystem.Instance != null)
            {
                EnvironmentalHazardSystem.Instance.HazardStarted += OnHazardStarted;
            }

            // Connect to natural disasters
            if (NaturalDisasterSystem.Instance != null)
            {
                NaturalDisasterSystem.Instance.DisasterStarted += OnDisasterStarted;
            }
        }

        /// <summary>
        /// Updates clothing protection values
        /// </summary>
        private void OnClothingProtectionUpdated(float coldProtection, float heatProtection, 
            float waterResistance, float windResistance)
        {
            var clothingSource = _protectionSources["clothing"];
            clothingSource.ColdProtection = coldProtection;
            clothingSource.HeatProtection = heatProtection;
            clothingSource.WaterResistance = waterResistance;
            clothingSource.WindResistance = windResistance;
            
            // Estimate other protections based on clothing quality
            float avgProtection = (coldProtection + heatProtection + waterResistance + windResistance) / 4f;
            clothingSource.RadiationProtection = avgProtection * 0.3f; // Clothing provides some radiation protection
            clothingSource.ToxicGasProtection = avgProtection * 0.2f;  // Limited gas protection
            
            UpdateTotalProtection();
        }

        /// <summary>
        /// Checks current shelter status
        /// </summary>
        private void CheckShelterStatus()
        {
            // This would normally check player position against shelter areas
            // For now, we'll simulate shelter detection
            bool wasInShelter = _isInShelter;
            ShelterType previousShelterType = _currentShelterType;
            
            // TODO: Implement actual shelter detection based on player position and built structures
            // For now, this is a placeholder that could be triggered by building system
            
            if (wasInShelter != _isInShelter || previousShelterType != _currentShelterType)
            {
                UpdateShelterProtection();
                EmitSignal(SignalName.ShelterStatusChanged, _isInShelter, _currentShelterType.ToString());
                
                Logger.LogInfo("EnvironmentalProtectionManager", 
                    $"Shelter status changed: {_isInShelter} ({_currentShelterType})");
            }
        }

        /// <summary>
        /// Updates shelter protection based on current shelter type
        /// </summary>
        private void UpdateShelterProtection()
        {
            var shelterSource = _protectionSources["shelter"];
            shelterSource.IsActive = _isInShelter;

            if (_isInShelter)
            {
                switch (_currentShelterType)
                {
                    case ShelterType.BasicShelter:
                        shelterSource.ColdProtection = 0.4f;
                        shelterSource.HeatProtection = 0.3f;
                        shelterSource.WaterResistance = 0.8f;
                        shelterSource.WindResistance = 0.7f;
                        shelterSource.RadiationProtection = 0.1f;
                        shelterSource.ToxicGasProtection = 0.2f;
                        break;

                    case ShelterType.ReinforcedShelter:
                        shelterSource.ColdProtection = 0.7f;
                        shelterSource.HeatProtection = 0.6f;
                        shelterSource.WaterResistance = 0.9f;
                        shelterSource.WindResistance = 0.9f;
                        shelterSource.RadiationProtection = 0.3f;
                        shelterSource.ToxicGasProtection = 0.4f;
                        break;

                    case ShelterType.UndergroundShelter:
                        shelterSource.ColdProtection = 0.8f;
                        shelterSource.HeatProtection = 0.8f;
                        shelterSource.WaterResistance = 0.6f;
                        shelterSource.WindResistance = 1.0f;
                        shelterSource.RadiationProtection = 0.8f;
                        shelterSource.ToxicGasProtection = 0.7f;
                        break;

                    case ShelterType.AdvancedShelter:
                        shelterSource.ColdProtection = 0.9f;
                        shelterSource.HeatProtection = 0.9f;
                        shelterSource.WaterResistance = 1.0f;
                        shelterSource.WindResistance = 1.0f;
                        shelterSource.RadiationProtection = 0.9f;
                        shelterSource.ToxicGasProtection = 0.9f;
                        break;

                    default:
                        // No shelter protection
                        shelterSource.ColdProtection = 0f;
                        shelterSource.HeatProtection = 0f;
                        shelterSource.WaterResistance = 0f;
                        shelterSource.WindResistance = 0f;
                        shelterSource.RadiationProtection = 0f;
                        shelterSource.ToxicGasProtection = 0f;
                        break;
                }
            }
            else
            {
                // No shelter protection
                shelterSource.ColdProtection = 0f;
                shelterSource.HeatProtection = 0f;
                shelterSource.WaterResistance = 0f;
                shelterSource.WindResistance = 0f;
                shelterSource.RadiationProtection = 0f;
                shelterSource.ToxicGasProtection = 0f;
            }

            UpdateTotalProtection();
        }

        /// <summary>
        /// Updates total protection values from all sources
        /// </summary>
        private void UpdateTotalProtection()
        {
            _currentProtection = new EnvironmentalProtection();

            foreach (var source in _protectionSources.Values)
            {
                if (source.IsActive)
                {
                    // Use maximum protection from all sources (they don't stack linearly)
                    _currentProtection.ColdProtection = Mathf.Max(_currentProtection.ColdProtection, source.ColdProtection);
                    _currentProtection.HeatProtection = Mathf.Max(_currentProtection.HeatProtection, source.HeatProtection);
                    _currentProtection.WaterResistance = Mathf.Max(_currentProtection.WaterResistance, source.WaterResistance);
                    _currentProtection.WindResistance = Mathf.Max(_currentProtection.WindResistance, source.WindResistance);
                    _currentProtection.RadiationProtection = Mathf.Max(_currentProtection.RadiationProtection, source.RadiationProtection);
                    _currentProtection.ToxicGasProtection = Mathf.Max(_currentProtection.ToxicGasProtection, source.ToxicGasProtection);
                }
            }

            // Emit protection update
            string protectionJson = System.Text.Json.JsonSerializer.Serialize(_currentProtection);
            EmitSignal(SignalName.ProtectionUpdated, protectionJson);

            Logger.LogInfo("EnvironmentalProtectionManager", 
                $"Protection updated - Cold: {_currentProtection.ColdProtection:F2}, Heat: {_currentProtection.HeatProtection:F2}");
        }

        /// <summary>
        /// Handles protection system updates
        /// </summary>
        private void OnProtectionUpdated(string protectionDataJson)
        {
            // Update temperature system with current protection
            if (TemperatureSystem.Instance != null)
            {
                float avgProtection = (_currentProtection.ColdProtection + _currentProtection.HeatProtection) / 2f;
                TemperatureSystem.Instance.SetClothingProtection(avgProtection);
                TemperatureSystem.Instance.SetShelterProtection(_isInShelter, 
                    (_currentProtection.ColdProtection + _currentProtection.HeatProtection) / 2f);
            }
        }

        /// <summary>
        /// Handles environmental hazard events
        /// </summary>
        private void OnHazardStarted(string hazardType, float severity, float duration)
        {
            CheckProtectionBreach(hazardType, severity);
        }

        /// <summary>
        /// Handles natural disaster events
        /// </summary>
        private void OnDisasterStarted(string disasterType, float magnitude, float duration)
        {
            CheckProtectionBreach(disasterType, magnitude);
        }

        /// <summary>
        /// Checks if current protection is sufficient for the threat
        /// </summary>
        private void CheckProtectionBreach(string threatType, float severity)
        {
            float requiredProtection = severity * 0.8f; // Need 80% of threat severity in protection
            bool protectionBreached = false;
            string breachedType = "";

            switch (threatType.ToLower())
            {
                case "lightning":
                case "tornado":
                case "megastorm":
                    if (_currentProtection.WindResistance < requiredProtection)
                    {
                        protectionBreached = true;
                        breachedType = "wind";
                    }
                    break;

                case "blizzard":
                case "superblizzard":
                    if (_currentProtection.ColdProtection < requiredProtection)
                    {
                        protectionBreached = true;
                        breachedType = "cold";
                    }
                    break;

                case "wildfire":
                case "volcaniceruption":
                    if (_currentProtection.HeatProtection < requiredProtection)
                    {
                        protectionBreached = true;
                        breachedType = "heat";
                    }
                    break;

                case "flood":
                case "tsunami":
                    if (_currentProtection.WaterResistance < requiredProtection)
                    {
                        protectionBreached = true;
                        breachedType = "water";
                    }
                    break;

                case "solarflare":
                    if (_currentProtection.RadiationProtection < requiredProtection)
                    {
                        protectionBreached = true;
                        breachedType = "radiation";
                    }
                    break;
            }

            if (protectionBreached)
            {
                EmitSignal(SignalName.ProtectionBreached, breachedType, severity);
                Logger.LogWarning("EnvironmentalProtectionManager", 
                    $"Protection breached! {breachedType} protection insufficient for {threatType}");
            }
        }

        #region Public API

        /// <summary>
        /// Gets current environmental protection data
        /// </summary>
        public EnvironmentalProtection GetCurrentProtection()
        {
            return _currentProtection;
        }

        /// <summary>
        /// Sets shelter status manually (called by building system)
        /// </summary>
        public void SetShelterStatus(bool inShelter, ShelterType shelterType = ShelterType.BasicShelter)
        {
            _isInShelter = inShelter;
            _currentShelterType = inShelter ? shelterType : ShelterType.None;
            UpdateShelterProtection();
        }

        /// <summary>
        /// Adds a custom protection source
        /// </summary>
        public void AddProtectionSource(string sourceId, ProtectionSource source)
        {
            _protectionSources[sourceId] = source;
            UpdateTotalProtection();
        }

        /// <summary>
        /// Removes a protection source
        /// </summary>
        public void RemoveProtectionSource(string sourceId)
        {
            if (_protectionSources.ContainsKey(sourceId))
            {
                _protectionSources.Remove(sourceId);
                UpdateTotalProtection();
            }
        }

        /// <summary>
        /// Gets protection effectiveness against a specific threat
        /// </summary>
        public float GetProtectionEffectiveness(string threatType)
        {
            return threatType.ToLower() switch
            {
                "cold" => _currentProtection.ColdProtection,
                "heat" => _currentProtection.HeatProtection,
                "water" => _currentProtection.WaterResistance,
                "wind" => _currentProtection.WindResistance,
                "radiation" => _currentProtection.RadiationProtection,
                "toxicgas" => _currentProtection.ToxicGasProtection,
                _ => 0f
            };
        }

        /// <summary>
        /// Checks if player is adequately protected against a threat
        /// </summary>
        public bool IsProtectedAgainst(string threatType, float threatSeverity)
        {
            float protection = GetProtectionEffectiveness(threatType);
            return protection >= (threatSeverity * 0.8f);
        }

        #endregion

        public override void _ExitTree()
        {
            if (_instance == this)
            {
                _instance = null;
            }
        }
    }

    /// <summary>
    /// Data structure for environmental protection values
    /// </summary>
    public class EnvironmentalProtection
    {
        public float ColdProtection { get; set; } = 0f;
        public float HeatProtection { get; set; } = 0f;
        public float WaterResistance { get; set; } = 0f;
        public float WindResistance { get; set; } = 0f;
        public float RadiationProtection { get; set; } = 0f;
        public float ToxicGasProtection { get; set; } = 0f;
    }

    /// <summary>
    /// Data structure for a protection source
    /// </summary>
    public class ProtectionSource
    {
        public string Name { get; set; } = "";
        public float ColdProtection { get; set; } = 0f;
        public float HeatProtection { get; set; } = 0f;
        public float WaterResistance { get; set; } = 0f;
        public float WindResistance { get; set; } = 0f;
        public float RadiationProtection { get; set; } = 0f;
        public float ToxicGasProtection { get; set; } = 0f;
        public bool IsActive { get; set; } = true;
    }
}