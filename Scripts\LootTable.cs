using Godot;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;

namespace SurvivalLooterShooter
{
    /// <summary>
    /// Represents a loot table with weighted random item generation
    /// Supports different rarity tiers and configurable drop chances
    /// </summary>
    [System.Serializable]
    public class EnemyLootTable
    {
        public string Id { get; set; } = "";
        public string Name { get; set; } = "";
        public List<EnemyLootTableItem> Items { get; set; } = new List<EnemyLootTableItem>();
        public float TotalWeight { get; private set; } = 0f;

        /// <summary>
        /// Calculates the total weight of all items in the loot table
        /// </summary>
        public void CalculateTotalWeight()
        {
            TotalWeight = Items.Sum(item => item.Weight);
        }

        /// <summary>
        /// Generates loot drops based on the loot table configuration
        /// </summary>
        public List<EnhancedLootDrop> GenerateLoot(Random random = null)
        {
            if (random == null) random = new Random();
            var drops = new List<EnhancedLootDrop>();

            if (Items.Count == 0 || TotalWeight <= 0)
            {
                return drops;
            }

            foreach (var item in Items)
            {
                // Check if this item should drop based on its individual chance
                if (random.NextDouble() <= item.DropChance)
                {
                    int quantity = random.Next(item.QuantityRange[0], item.QuantityRange[1] + 1);
                    if (quantity > 0)
                    {
                        drops.Add(new EnhancedLootDrop
                        {
                            Item = item.ItemId,
                            Chance = 1.0f, // Already determined to drop
                            Quantity = new List<int> { quantity, quantity },
                            Tier = item.Tier,
                            IsRare = IsRareTier(item.Tier)
                        });
                    }
                }
            }

            return drops;
        }

        /// <summary>
        /// Generates a single weighted random item from the loot table
        /// </summary>
        public EnhancedLootDrop GenerateWeightedRandomItem(Random random = null)
        {
            if (random == null) random = new Random();
            
            if (Items.Count == 0 || TotalWeight <= 0)
            {
                return null;
            }

            float randomValue = (float)(random.NextDouble() * TotalWeight);
            float currentWeight = 0f;

            foreach (var item in Items)
            {
                currentWeight += item.Weight;
                if (randomValue <= currentWeight)
                {
                    int quantity = random.Next(item.QuantityRange[0], item.QuantityRange[1] + 1);
                    return new EnhancedLootDrop
                    {
                        Item = item.ItemId,
                        Chance = 1.0f,
                        Quantity = new List<int> { quantity, quantity },
                        Tier = item.Tier,
                        IsRare = IsRareTier(item.Tier)
                    };
                }
            }

            // Fallback to first item if something goes wrong
            var fallbackItem = Items.First();
            int fallbackQuantity = random.Next(fallbackItem.QuantityRange[0], fallbackItem.QuantityRange[1] + 1);
            return new EnhancedLootDrop
            {
                Item = fallbackItem.ItemId,
                Chance = 1.0f,
                Quantity = new List<int> { fallbackQuantity, fallbackQuantity },
                Tier = fallbackItem.Tier,
                IsRare = IsRareTier(fallbackItem.Tier)
            };
        }

        /// <summary>
        /// Determines if a tier is considered rare
        /// </summary>
        private bool IsRareTier(string tier)
        {
            return tier.ToLower() switch
            {
                "rare" => true,
                "epic" => true,
                "legendary" => true,
                _ => false
            };
        }

        /// <summary>
        /// Gets items of a specific tier
        /// </summary>
        public List<EnemyLootTableItem> GetItemsByTier(string tier)
        {
            return Items.Where(item => string.Equals(item.Tier, tier, StringComparison.OrdinalIgnoreCase)).ToList();
        }

        /// <summary>
        /// Adds an item to the loot table
        /// </summary>
        public void AddItem(EnemyLootTableItem item)
        {
            Items.Add(item);
            CalculateTotalWeight();
        }

        /// <summary>
        /// Removes an item from the loot table
        /// </summary>
        public bool RemoveItem(string itemId)
        {
            bool removed = Items.RemoveAll(item => item.ItemId == itemId) > 0;
            if (removed)
            {
                CalculateTotalWeight();
            }
            return removed;
        }
    }

    /// <summary>
    /// Represents an item in a loot table with weight and drop configuration
    /// </summary>
    [System.Serializable]
    public class EnemyLootTableItem
    {
        public string ItemId { get; set; } = "";
        public float Weight { get; set; } = 1.0f;
        public string Tier { get; set; } = "Common";
        public List<int> QuantityRange { get; set; } = new List<int> { 1, 1 };
        public float DropChance { get; set; } = 1.0f;
        public int RespawnTime { get; set; } = 3600; // In seconds
        public Dictionary<string, object> SpecialProperties { get; set; } = new Dictionary<string, object>();
    }

    /// <summary>
    /// Enhanced loot drop with additional properties for rare items
    /// </summary>
    [System.Serializable]
    public class EnhancedLootDrop
    {
        public string Item { get; set; } = "";
        public float Chance { get; set; } = 0.5f;
        public List<int> Quantity { get; set; } = new List<int> { 1, 1 };
        public string Tier { get; set; } = "Common";
        public bool IsRare { get; set; } = false;
        public Dictionary<string, object> SpecialProperties { get; set; } = new Dictionary<string, object>();
        public float DurabilityModifier { get; set; } = 1.0f;
        public List<string> Enchantments { get; set; } = new List<string>();
    }

    /// <summary>
    /// Manages loot tables and provides loot generation functionality
    /// </summary>
    public partial class LootTableManager : Node
    {
        private static LootTableManager _instance;
        public static LootTableManager Instance => _instance;

        private Dictionary<string, EnemyLootTable> _lootTables = new Dictionary<string, EnemyLootTable>();
        private Random _random = new Random();

        // Rare item generation settings
        [Export] public float RareItemBaseChance { get; set; } = 0.05f;
        [Export] public float EpicItemBaseChance { get; set; } = 0.01f;
        [Export] public float LegendaryItemBaseChance { get; set; } = 0.001f;

        public override void _Ready()
        {
            // Singleton setup
            if (_instance == null)
            {
                _instance = this;
                AddToGroup("loot_table_manager");
                Logger.LogInfo("LootTableManager", "LootTableManager singleton initialized");
            }
            else
            {
                Logger.LogError("LootTableManager", "Multiple LootTableManager instances detected! Removing duplicate.");
                QueueFree();
                return;
            }

            LoadLootTables();
        }

        /// <summary>
        /// Loads loot tables from JSON configuration
        /// </summary>
        private void LoadLootTables()
        {
            try
            {
                string filePath = "res://Data/LootTables.json";
                
                if (!FileAccess.FileExists(filePath))
                {
                    Logger.LogError("LootTableManager", $"Loot tables file not found: {filePath}");
                    CreateDefaultLootTables();
                    return;
                }

                using var file = FileAccess.Open(filePath, FileAccess.ModeFlags.Read);
                if (file == null)
                {
                    Logger.LogError("LootTableManager", $"Failed to open loot tables file: {filePath}");
                    CreateDefaultLootTables();
                    return;
                }

                string jsonContent = file.GetAsText();
                var lootData = JsonSerializer.Deserialize<LootTableData>(jsonContent);

                if (lootData?.LootTables == null)
                {
                    Logger.LogError("LootTableManager", "Failed to deserialize loot table data");
                    CreateDefaultLootTables();
                    return;
                }

                _lootTables.Clear();
                foreach (var lootTable in lootData.LootTables)
                {
                    if (string.IsNullOrEmpty(lootTable.Id))
                    {
                        Logger.LogWarning("LootTableManager", "Skipping loot table with empty ID");
                        continue;
                    }

                    // Convert JSON format to internal format
                    var convertedTable = ConvertFromJsonFormat(lootTable);
                    convertedTable.CalculateTotalWeight();
                    
                    _lootTables[lootTable.Id] = convertedTable;
                    Logger.LogInfo("LootTableManager", $"Loaded loot table: {lootTable.Name} ({lootTable.Id}) with {lootTable.Items.Count} items");
                }

                Logger.LogInfo("LootTableManager", $"Successfully loaded {_lootTables.Count} loot tables");
            }
            catch (Exception ex)
            {
                Logger.LogException("LootTableManager", ex, "LoadLootTables");
                CreateDefaultLootTables();
            }
        }

        /// <summary>
        /// Converts JSON loot table format to internal format
        /// </summary>
        private EnemyLootTable ConvertFromJsonFormat(JsonLootTable jsonTable)
        {
            var lootTable = new EnemyLootTable
            {
                Id = jsonTable.Id,
                Name = jsonTable.Name
            };

            foreach (var jsonItem in jsonTable.Items)
            {
                var lootItem = new EnemyLootTableItem
                {
                    ItemId = jsonItem.ItemId,
                    Weight = jsonItem.Weight,
                    Tier = jsonItem.Tier,
                    QuantityRange = jsonItem.QuantityRange,
                    DropChance = CalculateDropChanceFromWeight(jsonItem.Weight, jsonItem.Tier),
                    RespawnTime = jsonItem.RespawnTime
                };

                lootTable.AddItem(lootItem);
            }

            return lootTable;
        }

        /// <summary>
        /// Calculates drop chance based on weight and tier
        /// </summary>
        private float CalculateDropChanceFromWeight(float weight, string tier)
        {
            float baseChance = tier.ToLower() switch
            {
                "common" => 0.8f,
                "uncommon" => 0.5f,
                "rare" => 0.2f,
                "epic" => 0.05f,
                "legendary" => 0.01f,
                _ => 0.5f
            };

            // Adjust based on weight (higher weight = higher chance)
            float weightMultiplier = Mathf.Clamp(weight / 10.0f, 0.1f, 2.0f);
            return Mathf.Clamp(baseChance * weightMultiplier, 0.01f, 1.0f);
        }

        /// <summary>
        /// Creates default loot tables if loading fails
        /// </summary>
        private void CreateDefaultLootTables()
        {
            Logger.LogInfo("LootTableManager", "Creating default loot tables");

            // Create a basic enemy loot table
            var enemyLootTable = new EnemyLootTable
            {
                Id = "basic_enemy_loot",
                Name = "Basic Enemy Loot"
            };

            enemyLootTable.AddItem(new EnemyLootTableItem
            {
                ItemId = "raw_meat",
                Weight = 10.0f,
                Tier = "Common",
                QuantityRange = new List<int> { 1, 3 },
                DropChance = 0.7f
            });

            enemyLootTable.AddItem(new EnemyLootTableItem
            {
                ItemId = "bone",
                Weight = 5.0f,
                Tier = "Common",
                QuantityRange = new List<int> { 1, 2 },
                DropChance = 0.4f
            });

            _lootTables["basic_enemy_loot"] = enemyLootTable;
        }

        /// <summary>
        /// Gets a loot table by ID
        /// </summary>
        public EnemyLootTable GetLootTable(string id)
        {
            return _lootTables.ContainsKey(id) ? _lootTables[id] : null;
        }

        /// <summary>
        /// Generates loot from a specific loot table
        /// </summary>
        public List<EnhancedLootDrop> GenerateLoot(string lootTableId)
        {
            var lootTable = GetLootTable(lootTableId);
            if (lootTable == null)
            {
                Logger.LogWarning("LootTableManager", $"Loot table not found: {lootTableId}");
                return new List<EnhancedLootDrop>();
            }

            var loot = lootTable.GenerateLoot(_random);
            
            // Apply rare item enhancements
            foreach (var drop in loot)
            {
                if (drop.IsRare)
                {
                    ApplyRareItemEnhancements(drop);
                }
            }

            return loot;
        }

        /// <summary>
        /// Applies special enhancements to rare items
        /// </summary>
        private void ApplyRareItemEnhancements(EnhancedLootDrop drop)
        {
            switch (drop.Tier.ToLower())
            {
                case "rare":
                    drop.DurabilityModifier = 1.2f;
                    if (_random.NextDouble() < 0.3f)
                    {
                        drop.Enchantments.Add("enhanced");
                    }
                    break;
                    
                case "epic":
                    drop.DurabilityModifier = 1.5f;
                    drop.Enchantments.Add("superior");
                    if (_random.NextDouble() < 0.5f)
                    {
                        drop.Enchantments.Add("enhanced");
                    }
                    break;
                    
                case "legendary":
                    drop.DurabilityModifier = 2.0f;
                    drop.Enchantments.Add("legendary");
                    drop.Enchantments.Add("superior");
                    drop.Enchantments.Add("enhanced");
                    break;
            }

            // Add special properties based on enchantments
            foreach (var enchantment in drop.Enchantments)
            {
                drop.SpecialProperties[enchantment] = true;
            }
        }

        /// <summary>
        /// Gets all available loot table IDs
        /// </summary>
        public List<string> GetLootTableIds()
        {
            return new List<string>(_lootTables.Keys);
        }

        public override void _ExitTree()
        {
            if (_instance == this)
            {
                _instance = null;
            }
        }
    }

    /// <summary>
    /// JSON data structure for loading loot tables
    /// </summary>
    [System.Serializable]
    public class LootTableData
    {
        public List<JsonLootTable> LootTables { get; set; } = new List<JsonLootTable>();
    }

    /// <summary>
    /// JSON representation of a loot table
    /// </summary>
    [System.Serializable]
    public class JsonLootTable
    {
        public string Id { get; set; } = "";
        public string Name { get; set; } = "";
        public List<JsonLootTableItem> Items { get; set; } = new List<JsonLootTableItem>();
    }

    /// <summary>
    /// JSON representation of a loot table item
    /// </summary>
    [System.Serializable]
    public class JsonLootTableItem
    {
        public string ItemId { get; set; } = "";
        public float Weight { get; set; } = 1.0f;
        public string Tier { get; set; } = "Common";
        public List<int> QuantityRange { get; set; } = new List<int> { 1, 1 };
        public int RespawnTime { get; set; } = 3600;
    }
}