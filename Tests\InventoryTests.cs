using Godot;
using System.Collections.Generic;

namespace SurvivalLooterShooter.Tests
{
    /// <summary>
    /// Unit tests for Inventory and InventorySlot classes
    /// </summary>
    public partial class InventoryTests : Node
    {
        private Inventory _inventory;
        private ItemDatabase _itemDatabase;

        public override void _Ready()
        {
            // Initialize test environment
            SetupTestEnvironment();
            
            // Run all tests
            RunAllTests();
        }

        private void SetupTestEnvironment()
        {
            GD.Print("Setting up inventory test environment...");
            
            // Create test inventory
            _inventory = new Inventory();
            AddChild(_inventory);

            // Ensure ItemDatabase is available
            _itemDatabase = ItemDatabase.Instance;
            if (_itemDatabase == null)
            {
                GD.PrintErr("ItemDatabase not available for testing");
                return;
            }

            GD.Print("Test environment setup complete");
        }

        private void RunAllTests()
        {
            GD.Print("=== Starting Inventory Tests ===");

            // InventorySlot tests
            TestInventorySlotCreation();
            TestInventorySlotMetadata();
            TestInventorySlotStacking();
            TestInventorySlotCloning();

            // Inventory tests
            TestInventoryAddItem();
            TestInventoryRemoveItem();
            TestInventoryStacking();
            TestInventoryCanAddItem();
            TestInventoryEquipment();
            TestInventoryQuantityTracking();
            TestInventoryErrorHandling();

            GD.Print("=== Inventory Tests Complete ===");
        }

        #region InventorySlot Tests

        private void TestInventorySlotCreation()
        {
            GD.Print("Testing InventorySlot creation...");

            // Test empty slot
            var emptySlot = new InventorySlot();
            Assert(emptySlot.IsEmpty, "Empty slot should be empty");
            Assert(emptySlot.Quantity == 0, "Empty slot quantity should be 0");

            // Test slot with data
            var metadata = new Dictionary<string, object> { { "durability", 100 } };
            var slot = new InventorySlot("test_item", 5, metadata);
            Assert(!slot.IsEmpty, "Slot with data should not be empty");
            Assert(slot.ItemId == "test_item", "Item ID should match");
            Assert(slot.Quantity == 5, "Quantity should match");
            Assert(slot.GetMetadata<int>("durability") == 100, "Metadata should be accessible");

            GD.Print("✓ InventorySlot creation tests passed");
        }

        private void TestInventorySlotMetadata()
        {
            GD.Print("Testing InventorySlot metadata operations...");

            var slot = new InventorySlot("test_item", 1);
            
            // Test setting and getting metadata
            slot.SetMetadata("durability", 75);
            slot.SetMetadata("enchantment", "fire");
            
            Assert(slot.GetMetadata<int>("durability") == 75, "Should get correct durability");
            Assert(slot.GetMetadata<string>("enchantment") == "fire", "Should get correct enchantment");
            Assert(slot.GetMetadata<int>("missing", 42) == 42, "Should return default for missing key");

            GD.Print("✓ InventorySlot metadata tests passed");
        }

        private void TestInventorySlotStacking()
        {
            GD.Print("Testing InventorySlot stacking logic...");

            var slot = new InventorySlot("stackable_item", 5);
            
            // Test stacking with same item
            Assert(slot.CanStackWith("stackable_item"), "Should be able to stack with same item");
            Assert(!slot.CanStackWith("different_item"), "Should not stack with different item");
            Assert(!slot.CanStackWith(""), "Should not stack with empty item ID");

            // Test empty slot
            var emptySlot = new InventorySlot();
            Assert(!emptySlot.CanStackWith("any_item"), "Empty slot should not stack");

            GD.Print("✓ InventorySlot stacking tests passed");
        }

        private void TestInventorySlotCloning()
        {
            GD.Print("Testing InventorySlot cloning...");

            var metadata = new Dictionary<string, object> { { "durability", 50 } };
            var original = new InventorySlot("test_item", 3, metadata);
            var clone = original.Clone();

            Assert(clone.ItemId == original.ItemId, "Cloned item ID should match");
            Assert(clone.Quantity == original.Quantity, "Cloned quantity should match");
            Assert(clone.GetMetadata<int>("durability") == 50, "Cloned metadata should match");
            
            // Test that they are separate objects
            clone.Quantity = 10;
            Assert(original.Quantity == 3, "Original should not be affected by clone changes");

            GD.Print("✓ InventorySlot cloning tests passed");
        }

        #endregion

        #region Inventory Tests

        private void TestInventoryAddItem()
        {
            GD.Print("Testing Inventory AddItem functionality...");

            _inventory.Clear();

            // Test adding valid item
            bool result = _inventory.AddItem("cloth", 10);
            Assert(result, "Should successfully add valid item");
            Assert(_inventory.GetItemQuantity("cloth") == 10, "Should have correct quantity");

            // Test adding more of the same item
            result = _inventory.AddItem("cloth", 5);
            Assert(result, "Should successfully add more of same item");
            Assert(_inventory.GetItemQuantity("cloth") == 15, "Should have combined quantity");

            // Test adding invalid item
            result = _inventory.AddItem("", 5);
            Assert(!result, "Should fail to add item with empty ID");

            result = _inventory.AddItem("nonexistent_item", 5);
            Assert(!result, "Should fail to add nonexistent item");

            GD.Print("✓ Inventory AddItem tests passed");
        }

        private void TestInventoryRemoveItem()
        {
            GD.Print("Testing Inventory RemoveItem functionality...");

            _inventory.Clear();
            _inventory.AddItem("cloth", 20);

            // Test removing valid quantity
            bool result = _inventory.RemoveItem("cloth", 5);
            Assert(result, "Should successfully remove valid quantity");
            Assert(_inventory.GetItemQuantity("cloth") == 15, "Should have correct remaining quantity");

            // Test removing all items
            result = _inventory.RemoveItem("cloth", 15);
            Assert(result, "Should successfully remove all items");
            Assert(_inventory.GetItemQuantity("cloth") == 0, "Should have no items left");

            // Test removing more than available
            _inventory.AddItem("cloth", 5);
            result = _inventory.RemoveItem("cloth", 10);
            Assert(!result, "Should fail to remove more than available");
            Assert(_inventory.GetItemQuantity("cloth") == 5, "Quantity should be unchanged");

            GD.Print("✓ Inventory RemoveItem tests passed");
        }

        private void TestInventoryStacking()
        {
            GD.Print("Testing Inventory stacking logic...");

            _inventory.Clear();

            // Add stackable items up to max stack
            var clothItem = _itemDatabase.GetItem("cloth");
            if (clothItem != null)
            {
                int maxStack = clothItem.MaxStack;
                
                // Add items up to max stack
                _inventory.AddItem("cloth", maxStack);
                Assert(_inventory.GetItemQuantity("cloth") == maxStack, "Should have max stack quantity");

                // Add more items (should create new stack if needed)
                _inventory.AddItem("cloth", 5);
                int expectedTotal = maxStack + 5;
                Assert(_inventory.GetItemQuantity("cloth") == expectedTotal, "Should handle overflow stacking");
            }

            GD.Print("✓ Inventory stacking tests passed");
        }

        private void TestInventoryCanAddItem()
        {
            GD.Print("Testing Inventory CanAddItem validation...");

            _inventory.Clear();

            // Test valid additions
            Assert(_inventory.CanAddItem("cloth", 10), "Should be able to add valid item");
            Assert(_inventory.CanAddItem("alcohol", 1), "Should be able to add different item");

            // Test invalid additions
            Assert(!_inventory.CanAddItem("", 5), "Should not be able to add empty item ID");
            Assert(!_inventory.CanAddItem("cloth", 0), "Should not be able to add zero quantity");
            Assert(!_inventory.CanAddItem("cloth", -5), "Should not be able to add negative quantity");
            Assert(!_inventory.CanAddItem("nonexistent_item", 1), "Should not be able to add nonexistent item");

            GD.Print("✓ Inventory CanAddItem tests passed");
        }

        private void TestInventoryEquipment()
        {
            GD.Print("Testing Inventory equipment system...");

            _inventory.Clear();

            // Add a weapon to inventory
            _inventory.AddItem("assault_rifle", 1);

            // Test equipping weapon
            bool result = _inventory.EquipItem("assault_rifle", "weapon");
            Assert(result, "Should successfully equip weapon");

            var equippedWeapon = _inventory.GetEquippedWeapon();
            Assert(equippedWeapon != null, "Should have equipped weapon");
            Assert(equippedWeapon.ItemId == "assault_rifle", "Should have correct equipped weapon");

            // Test that item was removed from inventory
            Assert(_inventory.GetItemQuantity("assault_rifle") == 0, "Item should be removed from inventory when equipped");

            // Test equipping different weapon (should unequip current)
            // Since we don't have a pistol in test data, let's add another assault rifle
            _inventory.AddItem("assault_rifle", 1);
            var currentEquipped = _inventory.GetEquippedWeapon();
            Assert(currentEquipped != null, "Should still have weapon equipped");
            
            // Previous weapon should be back in inventory when we equipped the new one
            Assert(_inventory.GetItemQuantity("assault_rifle") == 1, "Should have one assault rifle in inventory");

            GD.Print("✓ Inventory equipment tests passed");
        }

        private void TestInventoryQuantityTracking()
        {
            GD.Print("Testing Inventory quantity tracking...");

            _inventory.Clear();

            // Test initial state
            Assert(_inventory.GetItemQuantity("cloth") == 0, "Should have zero quantity initially");
            Assert(!_inventory.HasItem("cloth"), "Should not have item initially");

            // Add items and test tracking
            _inventory.AddItem("cloth", 15);
            _inventory.AddItem("alcohol", 8);

            Assert(_inventory.GetItemQuantity("cloth") == 15, "Should track cloth quantity");
            Assert(_inventory.GetItemQuantity("alcohol") == 8, "Should track alcohol quantity");
            Assert(_inventory.HasItem("cloth", 10), "Should have enough cloth");
            Assert(!_inventory.HasItem("cloth", 20), "Should not have too much cloth");

            // Test unique item count
            Assert(_inventory.GetUniqueItemCount() == 2, "Should have 2 unique items");

            GD.Print("✓ Inventory quantity tracking tests passed");
        }

        private void TestInventoryErrorHandling()
        {
            GD.Print("Testing Inventory error handling...");

            _inventory.Clear();

            // Test null/empty parameters
            Assert(!_inventory.AddItem(null, 5), "Should handle null item ID");
            Assert(!_inventory.AddItem("", 5), "Should handle empty item ID");
            Assert(!_inventory.RemoveItem(null, 5), "Should handle null item ID for removal");
            Assert(!_inventory.RemoveItem("", 5), "Should handle empty item ID for removal");

            // Test invalid quantities
            Assert(!_inventory.AddItem("cloth", 0), "Should handle zero quantity");
            Assert(!_inventory.AddItem("cloth", -5), "Should handle negative quantity");
            Assert(!_inventory.RemoveItem("cloth", 0), "Should handle zero removal quantity");
            Assert(!_inventory.RemoveItem("cloth", -5), "Should handle negative removal quantity");

            // Test equipment errors
            Assert(!_inventory.EquipItem("nonexistent_item"), "Should handle equipping nonexistent item");
            Assert(!_inventory.EquipItem("cloth"), "Should handle equipping item not in inventory");

            GD.Print("✓ Inventory error handling tests passed");
        }

        #endregion

        #region Test Utilities

        private void Assert(bool condition, string message)
        {
            if (!condition)
            {
                GD.PrintErr($"ASSERTION FAILED: {message}");
            }
        }

        #endregion
    }
}