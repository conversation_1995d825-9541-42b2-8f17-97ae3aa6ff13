[folding]

node_unfolds=[NodeP<PERSON>("."), PackedStringArray("Layout", "Mouse"), Node<PERSON>ath("Background"), PackedStringArray("Layout", "Theme Overrides"), NodePath("Background/VBoxContainer"), PackedStringArray("Layout"), NodePath("Background/VBoxContainer/TitleLabel"), PackedStringArray("Layout"), NodePath("Background/VBoxContainer/HSeparator"), PackedStringArray("Layout"), NodePath("Background/VBoxContainer/MainContainer"), PackedStringArray("Layout"), NodePath("Background/VBoxContainer/MainContainer/RecipeList"), PackedStringArray("Layout"), NodePath("Background/VBoxContainer/MainContainer/RecipeList/RecipeListLabel"), PackedStringArray("Layout"), NodePath("Background/VBoxContainer/MainContainer/RecipeList/RecipeScrollContainer"), PackedStringArray("Layout"), NodePath("Background/VBoxContainer/MainContainer/RecipeList/RecipeScrollContainer/RecipeContainer"), PackedStringArray("Layout"), NodePath("Background/VBoxContainer/MainContainer/RecipeDetails"), PackedStringArray("Layout"), NodePath("Background/VBoxContainer/MainContainer/RecipeDetails/RecipeDetailsLabel"), PackedStringArray("Layout"), NodePath("Background/VBoxContainer/MainContainer/RecipeDetails/SelectedRecipePanel"), PackedStringArray("Layout"), NodePath("Background/VBoxContainer/MainContainer/RecipeDetails/SelectedRecipePanel/SelectedRecipeContainer"), PackedStringArray("Layout"), NodePath("Background/VBoxContainer/MainContainer/RecipeDetails/SelectedRecipePanel/SelectedRecipeContainer/RecipeNameLabel"), PackedStringArray("Layout"), NodePath("Background/VBoxContainer/MainContainer/RecipeDetails/SelectedRecipePanel/SelectedRecipeContainer/HSeparator2"), PackedStringArray("Layout"), NodePath("Background/VBoxContainer/MainContainer/RecipeDetails/SelectedRecipePanel/SelectedRecipeContainer/MaterialsLabel"), PackedStringArray("Layout"), NodePath("Background/VBoxContainer/MainContainer/RecipeDetails/SelectedRecipePanel/SelectedRecipeContainer/MaterialsContainer"), PackedStringArray("Layout"), NodePath("Background/VBoxContainer/MainContainer/RecipeDetails/SelectedRecipePanel/SelectedRecipeContainer/HSeparator3"), PackedStringArray("Layout"), NodePath("Background/VBoxContainer/MainContainer/RecipeDetails/SelectedRecipePanel/SelectedRecipeContainer/OutputLabel"), PackedStringArray("Layout"), NodePath("Background/VBoxContainer/MainContainer/RecipeDetails/SelectedRecipePanel/SelectedRecipeContainer/HSeparator4"), PackedStringArray("Layout"), NodePath("Background/VBoxContainer/MainContainer/RecipeDetails/SelectedRecipePanel/SelectedRecipeContainer/CraftButton"), PackedStringArray("Layout"), NodePath("Background/CloseButton"), PackedStringArray("Layout")]
resource_unfolds=["res://Scenes/CraftingUI.tscn::StyleBoxFlat_1", PackedStringArray("Resource", "Border Width", "Border", "Corner Radius")]
nodes_folded=[]
