using Godot;
using System;
using System.Collections.Generic;
using System.Linq;

namespace SurvivalLooterShooter
{
    /// <summary>
    /// Handles recipe validation and item transformation for the crafting system
    /// </summary>
    public partial class CraftingSystem : Node
    {
        public static CraftingSystem Instance { get; private set; }
        private Inventory _inventory;
        private Dictionary<string, Recipe> _recipes = new();

        // Events for crafting system
        [Signal]
        public delegate void ItemCraftedEventHandler(string recipeId, string outputItemId, int outputQuantity);
        
        [Signal]
        public delegate void CraftingFailedEventHandler(string recipeId, string reason);

        public override void _Ready()
        {
            if (Instance == null)
            {
                Instance = this;
                GD.Print("CraftingSystem singleton initialized");
            }
            else
            {
                GD.PrintErr("Multiple CraftingSystem instances detected! Removing duplicate.");
                QueueFree();
            }
            // Inventory will be set via SetInventory() method by GameManager
            // No need to auto-find nodes since GameManager handles initialization
        }

        /// <summary>
        /// Sets the inventory reference for the crafting system
        /// </summary>
        /// <param name="inventory">The inventory instance to use</param>
        public void SetInventory(Inventory inventory)
        {
            _inventory = inventory;
        }

        /// <summary>
        /// Gets all recipes that can currently be crafted based on inventory contents
        /// </summary>
        /// <returns>List of craftable recipes</returns>
        public List<Recipe> GetAvailableRecipes()
        {
            var availableRecipes = new List<Recipe>();
            
            if (ItemDatabase.Instance == null)
            {
                GD.PrintErr("CraftingSystem: ItemDatabase not available");
                return availableRecipes;
            }

            var allRecipes = ItemDatabase.Instance.GetAllRecipes();
            
            foreach (var recipe in allRecipes.Values)
            {
                // Check if recipe is unlocked through progression system
                if (ProgressionRewardSystem.Instance != null && 
                    !ProgressionRewardSystem.Instance.IsRecipeUnlocked(recipe.Id))
                {
                    continue; // Skip locked recipes
                }
                
                if (CanCraft(recipe))
                {
                    availableRecipes.Add(recipe);
                }
            }

            return availableRecipes;
        }

        /// <summary>
        /// Checks if a recipe can be crafted with current inventory contents
        /// </summary>
        /// <param name="recipe">The recipe to check</param>
        /// <returns>True if the recipe can be crafted, false otherwise</returns>
        public bool CanCraft(Recipe recipe)
        {
            if (recipe == null || !recipe.IsValid())
            {
                return false;
            }

            if (_inventory == null)
            {
                GD.PrintErr("CraftingSystem: Inventory not set");
                return false;
            }

            // Check if we have all required input materials
            foreach (var input in recipe.Inputs)
            {
                int availableQuantity = _inventory.GetItemQuantity(input.Id);
                if (availableQuantity < input.Amount)
                {
                    return false;
                }
            }

            // Check if we can add the output item to inventory
            if (!_inventory.CanAddItem(recipe.Output.Id, recipe.Output.Amount))
            {
                return false;
            }

            return true;
        }

        /// <summary>
        /// Attempts to craft an item using the specified recipe
        /// </summary>
        /// <param name="recipe">The recipe to craft</param>
        /// <returns>True if crafting was successful, false otherwise</returns>
        public bool CraftItem(Recipe recipe)
        {
            if (recipe == null || !recipe.IsValid())
            {
                string reason = "Invalid recipe";
                GD.PrintErr($"CraftingSystem: {reason}");
                EmitSignal(SignalName.CraftingFailed, recipe?.Id ?? "unknown", reason);
                return false;
            }

            if (_inventory == null)
            {
                string reason = "Inventory not available";
                GD.PrintErr($"CraftingSystem: {reason}");
                EmitSignal(SignalName.CraftingFailed, recipe.Id, reason);
                return false;
            }

            // Double-check that we can craft this recipe
            if (!CanCraft(recipe))
            {
                string reason = "Cannot craft recipe - missing materials or insufficient inventory space";
                GD.PrintErr($"CraftingSystem: {reason} for recipe {recipe.Id}");
                EmitSignal(SignalName.CraftingFailed, recipe.Id, reason);
                
                // Emit event bus event
                EventBus.Instance?.EmitSignal(EventBus.SignalName.CraftingFailed, recipe.Id, reason);
                
                return false;
            }

            // Check weather restrictions for fire-based crafting
            if (IsFireBasedRecipe(recipe) && WeatherManager.Instance != null && !WeatherManager.Instance.IsFireCraftingAllowed())
            {
                string reason = "Cannot craft fire-based recipes in current weather conditions";
                GD.PrintErr($"CraftingSystem: {reason} for recipe {recipe.Id}");
                EmitSignal(SignalName.CraftingFailed, recipe.Id, reason);
                
                // Emit event bus event
                EventBus.Instance?.EmitSignal(EventBus.SignalName.CraftingFailed, recipe.Id, reason);
                
                return false;
            }

            try
            {
                // Apply skill bonuses for material efficiency
                var actualInputs = ApplyCraftingSkillBonuses(recipe);
                
                // Remove input materials from inventory (with potential savings)
                foreach (var input in actualInputs)
                {
                    if (!_inventory.RemoveItem(input.Id, input.Amount))
                    {
                        // This should not happen if CanCraft returned true, but handle it gracefully
                        string reason = $"Failed to remove input material: {input.Id} x{input.Amount}";
                        GD.PrintErr($"CraftingSystem: {reason}");
                        EmitSignal(SignalName.CraftingFailed, recipe.Id, reason);
                        
                        // Try to rollback any materials we already removed
                        RollbackCrafting(recipe, input);
                        return false;
                    }
                }

                // Calculate output quantity with skill bonuses
                int outputQuantity = CalculateOutputQuantity(recipe);

                // Add output item to inventory
                if (!_inventory.AddItem(recipe.Output.Id, outputQuantity))
                {
                    // This should not happen if CanCraft returned true, but handle it gracefully
                    string reason = $"Failed to add output item: {recipe.Output.Id} x{outputQuantity}";
                    GD.PrintErr($"CraftingSystem: {reason}");
                    EmitSignal(SignalName.CraftingFailed, recipe.Id, reason);
                    
                    // Rollback all input materials
                    RollbackCrafting(recipe, null);
                    return false;
                }

                // Crafting successful
                var outputItem = ItemDatabase.Instance?.GetItem(recipe.Output.Id);
                GD.Print($"Successfully crafted {outputQuantity} x {outputItem?.Name ?? recipe.Output.Id} using recipe {recipe.Id}");
                
                EmitSignal(SignalName.ItemCrafted, recipe.Id, recipe.Output.Id, outputQuantity);
                
                // Emit event bus event
                EventBus.Instance?.EmitItemCrafted(recipe.Id, recipe.Output.Id, outputQuantity);
                
                return true;
            }
            catch (Exception ex)
            {
                string reason = $"Unexpected error during crafting: {ex.Message}";
                GD.PrintErr($"CraftingSystem: {reason}");
                EmitSignal(SignalName.CraftingFailed, recipe.Id, reason);
                return false;
            }
        }

        /// <summary>
        /// Gets a list of materials that are missing for a specific recipe
        /// </summary>
        /// <param name="recipe">The recipe to check</param>
        /// <returns>Dictionary of missing materials with item ID as key and missing quantity as value</returns>
        public Dictionary<string, int> GetMissingMaterials(Recipe recipe)
        {
            var missingMaterials = new Dictionary<string, int>();

            if (recipe == null || !recipe.IsValid())
            {
                return missingMaterials;
            }

            if (_inventory == null)
            {
                // If no inventory, all materials are missing
                foreach (var input in recipe.Inputs)
                {
                    missingMaterials[input.Id] = input.Amount;
                }
                return missingMaterials;
            }

            // Check each required input
            foreach (var input in recipe.Inputs)
            {
                int availableQuantity = _inventory.GetItemQuantity(input.Id);
                int requiredQuantity = input.Amount;

                if (availableQuantity < requiredQuantity)
                {
                    missingMaterials[input.Id] = requiredQuantity - availableQuantity;
                }
            }

            return missingMaterials;
        }

        /// <summary>
        /// Gets a formatted string describing missing materials for UI display
        /// </summary>
        /// <param name="recipe">The recipe to check</param>
        /// <returns>Formatted string describing missing materials</returns>
        public string GetMissingMaterialsText(Recipe recipe)
        {
            var missingMaterials = GetMissingMaterials(recipe);
            
            if (missingMaterials.Count == 0)
            {
                return "All materials available";
            }

            var missingItems = new List<string>();
            foreach (var kvp in missingMaterials)
            {
                var item = ItemDatabase.Instance?.GetItem(kvp.Key);
                string itemName = item?.Name ?? kvp.Key;
                missingItems.Add($"{itemName} x{kvp.Value}");
            }

            return $"Missing: {string.Join(", ", missingItems)}";
        }

        /// <summary>
        /// Gets all recipes that produce a specific output item
        /// </summary>
        /// <param name="itemId">The item ID to search for</param>
        /// <returns>List of recipes that produce the specified item</returns>
        public List<Recipe> GetRecipesForItem(string itemId)
        {
            if (ItemDatabase.Instance == null)
            {
                return new List<Recipe>();
            }

            return ItemDatabase.Instance.GetRecipesByOutput(itemId);
        }

        /// <summary>
        /// Checks if any recipe exists that can produce the specified item
        /// </summary>
        /// <param name="itemId">The item ID to check</param>
        /// <returns>True if at least one recipe can produce the item</returns>
        public bool CanCraftItem(string itemId)
        {
            var recipes = GetRecipesForItem(itemId);
            return recipes.Any(recipe => CanCraft(recipe));
        }

        /// <summary>
        /// Gets the total quantity of an item that can be crafted with current materials
        /// </summary>
        /// <param name="itemId">The item ID to check</param>
        /// <returns>Maximum quantity that can be crafted</returns>
        public int GetMaxCraftableQuantity(string itemId)
        {
            var recipes = GetRecipesForItem(itemId);
            int maxQuantity = 0;

            foreach (var recipe in recipes)
            {
                if (CanCraft(recipe))
                {
                    // Calculate how many times we can craft this recipe
                    int craftableCount = int.MaxValue;
                    
                    foreach (var input in recipe.Inputs)
                    {
                        int availableQuantity = _inventory?.GetItemQuantity(input.Id) ?? 0;
                        int timesCanCraft = availableQuantity / input.Amount;
                        craftableCount = Math.Min(craftableCount, timesCanCraft);
                    }

                    if (craftableCount != int.MaxValue)
                    {
                        int totalOutput = craftableCount * recipe.Output.Amount;
                        maxQuantity = Math.Max(maxQuantity, totalOutput);
                    }
                }
            }

            return maxQuantity;
        }

        /// <summary>
        /// Attempts to rollback a failed crafting operation by restoring consumed materials
        /// </summary>
        /// <param name="recipe">The recipe that failed</param>
        /// <param name="failedAtInput">The input that caused the failure, or null if failure was at output</param>
        private void RollbackCrafting(Recipe recipe, RecipeInput failedAtInput)
        {
            GD.Print($"Attempting to rollback crafting for recipe {recipe.Id}");

            // Restore materials that were consumed before the failure
            foreach (var input in recipe.Inputs)
            {
                // Stop at the input that caused the failure
                if (failedAtInput != null && input == failedAtInput)
                {
                    break;
                }

                // Try to restore the material
                if (!_inventory.AddItem(input.Id, input.Amount))
                {
                    GD.PrintErr($"Failed to rollback material {input.Id} x{input.Amount} during crafting rollback");
                }
            }
        }

        /// <summary>
        /// Applies crafting skill bonuses to reduce material costs
        /// </summary>
        private List<RecipeInput> ApplyCraftingSkillBonuses(Recipe recipe)
        {
            var actualInputs = new List<RecipeInput>();
            
            foreach (var input in recipe.Inputs)
            {
                int actualAmount = input.Amount;
                
                // Apply material efficiency skill bonus
                if (SkillManager.Instance != null)
                {
                    float materialSavings = SkillManager.Instance.GetSkillBonus("crafting_efficiency", "material_savings");
                    if (GD.Randf() < materialSavings)
                    {
                        actualAmount = Math.Max(1, actualAmount - 1); // Save at least 1 material
                    }
                }
                
                // Apply ability system crafting enhancements
                if (AbilitySystem.Instance != null)
                {
                    // Check for material cost reduction from abilities
                    if (AbilitySystem.Instance.IsCraftingEnhancementActive("resource_efficiency"))
                    {
                        float costReduction = AbilitySystem.Instance.GetCraftingEnhancementValue("resource_efficiency", "material_cost_reduction");
                        actualAmount = Math.Max(1, (int)(actualAmount * (1f - costReduction)));
                    }
                    
                    // Check for bulk crafting discount
                    if (AbilitySystem.Instance.IsCraftingEnhancementActive("bulk_crafting"))
                    {
                        float bulkDiscount = AbilitySystem.Instance.GetCraftingEnhancementValue("bulk_crafting", "bulk_material_discount");
                        actualAmount = Math.Max(1, (int)(actualAmount * (1f - bulkDiscount)));
                    }
                }
                
                actualInputs.Add(new RecipeInput { Id = input.Id, Amount = actualAmount });
            }
            
            return actualInputs;
        }
        
        /// <summary>
        /// Calculates output quantity with skill bonuses
        /// </summary>
        private int CalculateOutputQuantity(Recipe recipe)
        {
            int baseQuantity = recipe.Output.Amount;
            
            if (SkillManager.Instance == null) return baseQuantity;
            
            // Apply quality bonus skill - chance for extra output
            float qualityChance = SkillManager.Instance.GetSkillBonus("quality_bonus", "quality_chance");
            if (GD.Randf() < qualityChance)
            {
                baseQuantity += 1; // Bonus item
            }
            
            // Apply ability system enhancements
            if (AbilitySystem.Instance != null)
            {
                // Check for masterwork crafting chance
                if (AbilitySystem.Instance.IsCraftingEnhancementActive("masterwork_chance"))
                {
                    float masterworkChance = AbilitySystem.Instance.GetCraftingEnhancementValue("masterwork_chance", "masterwork_chance");
                    if (GD.Randf() < masterworkChance)
                    {
                        baseQuantity = (int)(baseQuantity * 1.5f); // 50% more output for masterwork
                        GD.Print("Masterwork item crafted!");
                    }
                }
                
                // Check for guaranteed quality crafts from abilities
                if (AbilitySystem.Instance.IsCraftingEnhancementActive("quality_control"))
                {
                    float qualityImprovement = AbilitySystem.Instance.GetCraftingEnhancementValue("quality_control", "quality_improvement_chance");
                    if (GD.Randf() < qualityImprovement)
                    {
                        baseQuantity += 1; // Quality improvement bonus
                    }
                }
            }
            
            return baseQuantity;
        }

        /// <summary>
        /// Checks if a recipe requires fire-based crafting
        /// </summary>
        private bool IsFireBasedRecipe(Recipe recipe)
        {
            // Check recipe type for fire-based crafting
            if (!string.IsNullOrEmpty(recipe.RecipeType))
            {
                string recipeType = recipe.RecipeType.ToLower();
                if (recipeType.Contains("fire") || recipeType.Contains("forge") || 
                    recipeType.Contains("smelting") || recipeType.Contains("cooking"))
                {
                    return true;
                }
            }
            
            // Check if any input materials suggest fire-based crafting
            foreach (var input in recipe.Inputs)
            {
                if (input.Id.Contains("coal") || input.Id.Contains("fuel") || 
                    input.Id.Contains("fire") || input.Id.Contains("wood"))
                {
                    return true;
                }
            }
            
            // Check if output suggests fire-based crafting
            if (recipe.Output.Id.Contains("smelted") || recipe.Output.Id.Contains("forged") || 
                recipe.Output.Id.Contains("cooked") || recipe.Output.Id.Contains("baked"))
            {
                return true;
            }
            
            return false;
        }

        /// <summary>
        /// Calculates crafting time with weather and skill modifiers
        /// </summary>
        public float GetCraftingTime(Recipe recipe)
        {
            float baseCraftingTime = recipe.CraftingTime;
            
            // Apply weather effects
            if (WeatherManager.Instance != null)
            {
                var weatherEffects = WeatherManager.Instance.GetCurrentWeatherEffects();
                baseCraftingTime /= weatherEffects.CraftingSpeedModifier;
            }
            
            // Apply skill bonuses
            if (SkillManager.Instance != null)
            {
                float speedBonus = SkillManager.Instance.GetSkillBonus("crafting_speed", "speed_bonus");
                baseCraftingTime /= (1.0f + speedBonus);
            }
            
            return Math.Max(0.1f, baseCraftingTime); // Minimum 0.1 seconds
        }

        public override string ToString()
        {
            int availableRecipes = GetAvailableRecipes().Count;
            int totalRecipes = ItemDatabase.Instance?.RecipeCount ?? 0;
            return $"CraftingSystem: {availableRecipes}/{totalRecipes} recipes available";
        }

        // Mod support and endgame methods
        public void RegisterCustomRecipe(CustomContent customRecipe)
        {
            var recipe = new Recipe
            {
                Id = customRecipe.ContentId,
                Inputs = new List<RecipeInput>(),
                Output = new RecipeOutput()
            };
            
            // Parse inputs from custom content
            if (customRecipe.Properties.ContainsKey("inputs"))
            {
                // This would parse the inputs from the custom content
                GD.Print($"Parsing custom recipe inputs for: {customRecipe.Name}");
            }
            
            _recipes[recipe.Id] = recipe;
            GD.Print($"Registered custom recipe: {customRecipe.Name}");
        }

        public void UnregisterCustomRecipe(string recipeId)
        {
            if (_recipes.ContainsKey(recipeId))
            {
                _recipes.Remove(recipeId);
                GD.Print($"Unregistered custom recipe: {recipeId}");
            }
        }

        public List<string> GetUnlockedRecipes()
        {
            // Return list of unlocked recipe IDs
            return _recipes.Keys.ToList();
        }

        public void UnlockRecipe(string recipeId)
        {
            // Unlock specific recipe
            GD.Print($"Unlocked recipe: {recipeId}");
        }

        public void UnlockAdvancedCrafting()
        {
            // Unlock advanced crafting features for NG+
            GD.Print("Advanced crafting unlocked");
        }
    }
}