C:\Users\<USER>\Desktop\project1\.godot\mono\temp\obj\Debug\SurvivalLooterShooter.csproj.AssemblyReference.cache
C:\Users\<USER>\Desktop\project1\.godot\mono\temp\obj\Debug\SurvivalLooterShooter.GeneratedMSBuildEditorConfig.editorconfig
C:\Users\<USER>\Desktop\project1\.godot\mono\temp\obj\Debug\SurvivalLooterShooter.AssemblyInfoInputs.cache
C:\Users\<USER>\Desktop\project1\.godot\mono\temp\obj\Debug\SurvivalLooterShooter.AssemblyInfo.cs
C:\Users\<USER>\Desktop\project1\.godot\mono\temp\obj\Debug\SurvivalLooterShooter.csproj.CoreCompileInputs.cache
C:\Users\<USER>\Desktop\project1\.godot\mono\temp\bin\Debug\SurvivalLooterShooter.deps.json
C:\Users\<USER>\Desktop\project1\.godot\mono\temp\bin\Debug\SurvivalLooterShooter.runtimeconfig.json
C:\Users\<USER>\Desktop\project1\.godot\mono\temp\bin\Debug\SurvivalLooterShooter.dll
C:\Users\<USER>\Desktop\project1\.godot\mono\temp\bin\Debug\SurvivalLooterShooter.pdb
C:\Users\<USER>\Desktop\project1\.godot\mono\temp\bin\Debug\GodotSharp.dll
C:\Users\<USER>\Desktop\project1\.godot\mono\temp\bin\Debug\GodotSharpEditor.dll
C:\Users\<USER>\Desktop\project1\.godot\mono\temp\obj\Debug\Survival.EF33C182.Up2Date
C:\Users\<USER>\Desktop\project1\.godot\mono\temp\obj\Debug\SurvivalLooterShooter.dll
C:\Users\<USER>\Desktop\project1\.godot\mono\temp\obj\Debug\refint\SurvivalLooterShooter.dll
C:\Users\<USER>\Desktop\project1\.godot\mono\temp\obj\Debug\SurvivalLooterShooter.pdb
C:\Users\<USER>\Desktop\project1\.godot\mono\temp\obj\Debug\SurvivalLooterShooter.genruntimeconfig.cache
C:\Users\<USER>\Desktop\project1\.godot\mono\temp\obj\Debug\ref\SurvivalLooterShooter.dll
