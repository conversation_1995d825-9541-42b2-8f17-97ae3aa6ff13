using Godot;

namespace SurvivalLooterShooter.Tests
{
    /// <summary>
    /// Simple test runner for ItemDatabase tests
    /// </summary>
    public partial class TestRunner : Node
    {
        public override void _Ready()
        {
            GD.Print("Starting All Tests...");
            
            // Create and run ItemDatabase tests
            var itemDbTestNode = new ItemDatabaseTests();
            AddChild(itemDbTestNode);
            
            // Create and run Inventory tests
            var inventoryTestNode = new InventoryTests();
            AddChild(inventoryTestNode);
            
            // Create and run CraftingSystem tests
            var craftingTestNode = new CraftingSystemTests();
            AddChild(craftingTestNode);
            
            // Create and run WeaponSystem tests
            var weaponTestNode = new WeaponSystemTests();
            AddChild(weaponTestNode);
            
            // Exit after a longer delay to allow all tests to complete
            GetTree().CreateTimer(10.0).Timeout += () => GetTree().Quit();
        }
    }
}