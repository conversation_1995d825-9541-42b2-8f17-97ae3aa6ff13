using Godot;

namespace SurvivalLooterShooter
{
    /// <summary>
    /// Test runner to verify EventBus functionality
    /// </summary>
    public partial class EventBusTestRunner : Node
    {
        private bool _testsPassed = true;
        private int _eventsReceived = 0;

        public override void _Ready()
        {
            GD.Print("Starting EventBus tests...");
            
            // Wait a frame for EventBus to be ready
            CallDeferred(nameof(RunTests));
        }

        private void RunTests()
        {
            if (EventBus.Instance == null)
            {
                GD.PrintErr("❌ EventBus instance not found!");
                _testsPassed = false;
                return;
            }

            GD.Print("✅ EventBus instance found");

            // Test event subscription and emission
            TestEventSubscription();
            
            // Wait a moment then check results
            GetTree().CreateTimer(0.1f).Timeout += CheckTestResults;
        }

        private void TestEventSubscription()
        {
            // Subscribe to some events
            EventBus.Instance.ItemAdded += OnTestItemAdded;
            EventBus.Instance.StatChanged += OnTestStatChanged;
            EventBus.Instance.WeaponFired += OnTestWeaponFired;

            // Emit test events
            EventBus.Instance.EmitItemAdded("test_item", 5);
            EventBus.Instance.EmitStatChanged("health", 80f, 100f, 90f);
            EventBus.Instance.EmitWeaponFired("test_weapon", "Test Weapon", 25f, 15);

            GD.Print("✅ Test events emitted");
        }

        private void OnTestItemAdded(string itemId, int quantity)
        {
            if (itemId == "test_item" && quantity == 5)
            {
                GD.Print("✅ ItemAdded event received correctly");
                _eventsReceived++;
            }
            else
            {
                GD.PrintErr($"❌ ItemAdded event received with wrong data: {itemId}, {quantity}");
                _testsPassed = false;
            }
        }

        private void OnTestStatChanged(string statName, float currentValue, float maxValue, float previousValue)
        {
            if (statName == "health" && currentValue == 80f && maxValue == 100f && previousValue == 90f)
            {
                GD.Print("✅ StatChanged event received correctly");
                _eventsReceived++;
            }
            else
            {
                GD.PrintErr($"❌ StatChanged event received with wrong data: {statName}, {currentValue}, {maxValue}, {previousValue}");
                _testsPassed = false;
            }
        }

        private void OnTestWeaponFired(string weaponId, string weaponName, float damage, int remainingAmmo)
        {
            if (weaponId == "test_weapon" && weaponName == "Test Weapon" && damage == 25f && remainingAmmo == 15)
            {
                GD.Print("✅ WeaponFired event received correctly");
                _eventsReceived++;
            }
            else
            {
                GD.PrintErr($"❌ WeaponFired event received with wrong data: {weaponId}, {weaponName}, {damage}, {remainingAmmo}");
                _testsPassed = false;
            }
        }

        private void CheckTestResults()
        {
            // Unsubscribe from events
            if (EventBus.Instance != null)
            {
                EventBus.Instance.ItemAdded -= OnTestItemAdded;
                EventBus.Instance.StatChanged -= OnTestStatChanged;
                EventBus.Instance.WeaponFired -= OnTestWeaponFired;
            }

            if (_testsPassed && _eventsReceived == 3)
            {
                GD.Print("🎉 All EventBus tests passed!");
            }
            else
            {
                GD.PrintErr($"❌ EventBus tests failed. Events received: {_eventsReceived}/3, Tests passed: {_testsPassed}");
            }

            // Clean up
            QueueFree();
        }
    }
}