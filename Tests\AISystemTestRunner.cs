using Godot;
using System;

namespace SurvivalLooterShooter
{
    /// <summary>
    /// Test runner for AI system integration tests
    /// Provides easy access to run AI-related tests
    /// </summary>
    public partial class AISystemTestRunner : Node2D
    {
        private Label _instructionLabel;
        private bool _testsRunning = false;

        public override void _Ready()
        {
            SetupUI();
            Logger.LogInfo("AISystemTestRunner", "AI System Test Runner initialized");
        }

        /// <summary>
        /// Sets up the UI for the test runner
        /// </summary>
        private void SetupUI()
        {
            // Create instruction label
            _instructionLabel = new Label();
            _instructionLabel.Text = "AI System Test Runner\n\nPress 1: Run AI Controller Tests\nPress 2: Run Enemy System Tests\nPress ESC: Exit";
            _instructionLabel.Position = new Vector2(50, 50);
            _instructionLabel.AddThemeColorOverride("font_color", Colors.White);
            AddChild(_instructionLabel);
        }

        public override void _Input(InputEvent @event)
        {
            if (@event is InputEventKey keyEvent && keyEvent.Pressed && !_testsRunning)
            {
                switch (keyEvent.Keycode)
                {
                    case Key.Key1:
                        RunAIControllerTests();
                        break;
                    case Key.Key2:
                        RunEnemySystemTests();
                        break;
                    case Key.Escape:
                        GetTree().Quit();
                        break;
                }
            }
        }

        /// <summary>
        /// Runs AI Controller tests
        /// </summary>
        private void RunAIControllerTests()
        {
            if (_testsRunning) return;

            _testsRunning = true;
            Logger.LogInfo("AISystemTestRunner", "Loading AI Controller tests...");

            try
            {
                var testScene = GD.Load<PackedScene>("res://Tests/AIControllerTests.tscn");
                if (testScene != null)
                {
                    GetTree().ChangeSceneToPacked(testScene);
                }
                else
                {
                    Logger.LogError("AISystemTestRunner", "Could not load AI Controller test scene");
                    _testsRunning = false;
                }
            }
            catch (Exception ex)
            {
                Logger.LogException("AISystemTestRunner", ex, "RunAIControllerTests");
                _testsRunning = false;
            }
        }

        /// <summary>
        /// Runs Enemy System tests
        /// </summary>
        private void RunEnemySystemTests()
        {
            if (_testsRunning) return;

            _testsRunning = true;
            Logger.LogInfo("AISystemTestRunner", "Loading Enemy System tests...");

            try
            {
                var testScene = GD.Load<PackedScene>("res://Tests/EnemySystemTests.tscn");
                if (testScene != null)
                {
                    GetTree().ChangeSceneToPacked(testScene);
                }
                else
                {
                    Logger.LogError("AISystemTestRunner", "Could not load Enemy System test scene");
                    _testsRunning = false;
                }
            }
            catch (Exception ex)
            {
                Logger.LogException("AISystemTestRunner", ex, "RunEnemySystemTests");
                _testsRunning = false;
            }
        }

        public override void _Draw()
        {
            // Draw background
            DrawRect(new Rect2(0, 0, GetViewportRect().Size), new Color(0.1f, 0.1f, 0.2f));
        }
    }
}