using Godot;

namespace SurvivalLooterShooter
{
    /// <summary>
    /// HUD controller that displays survival stats and weapon information
    /// </summary>
    public partial class SurvivalHUD : Control
    {
        // Survival stats UI elements
        private ProgressBar _healthBar;
        private Label _healthValue;
        private ProgressBar _hungerBar;
        private Label _hungerValue;
        private ProgressBar _thirstBar;
        private Label _thirstValue;
        private ProgressBar _staminaBar;
        private Label _staminaValue;
        
        // Weapon UI elements
        private Label _weaponNameLabel;
        private Label _ammoValue;
        private ProgressBar _conditionBar;
        private Label _reloadingLabel;
        
        // Time UI elements
        private Label _timeLabel;
        private Label _timePeriodLabel;
        
        // System references
        private SurvivalStatsSystem _survivalStatsSystem;
        private WeaponController _weaponController;

        public override void _Ready()
        {
            // Get UI element references
            GetUIReferences();
            
            // Initialize with default values
            UpdateAllStats();
            UpdateWeaponDisplay();
        }

        /// <summary>
        /// Gets references to all UI elements
        /// </summary>
        private void GetUIReferences()
        {
            // Survival stats elements
            _healthBar = GetNode<ProgressBar>("StatsContainer/StatsBackground/StatsVBox/HealthContainer/HealthBar");
            _healthValue = GetNode<Label>("StatsContainer/StatsBackground/StatsVBox/HealthContainer/HealthValue");
            _hungerBar = GetNode<ProgressBar>("StatsContainer/StatsBackground/StatsVBox/HungerContainer/HungerBar");
            _hungerValue = GetNode<Label>("StatsContainer/StatsBackground/StatsVBox/HungerContainer/HungerValue");
            _thirstBar = GetNode<ProgressBar>("StatsContainer/StatsBackground/StatsVBox/ThirstContainer/ThirstBar");
            _thirstValue = GetNode<Label>("StatsContainer/StatsBackground/StatsVBox/ThirstContainer/ThirstValue");
            _staminaBar = GetNode<ProgressBar>("StatsContainer/StatsBackground/StatsVBox/StaminaContainer/StaminaBar");
            _staminaValue = GetNode<Label>("StatsContainer/StatsBackground/StatsVBox/StaminaContainer/StaminaValue");
            
            // Weapon elements
            _weaponNameLabel = GetNode<Label>("WeaponContainer/WeaponBackground/WeaponVBox/WeaponNameLabel");
            _ammoValue = GetNode<Label>("WeaponContainer/WeaponBackground/WeaponVBox/AmmoContainer/AmmoValue");
            _conditionBar = GetNode<ProgressBar>("WeaponContainer/WeaponBackground/WeaponVBox/ConditionContainer/ConditionBar");
            _reloadingLabel = GetNode<Label>("WeaponContainer/WeaponBackground/WeaponVBox/ReloadingLabel");
            
            // Time elements
            _timeLabel = GetNode<Label>("TimeContainer/TimeBackground/TimeVBox/TimeLabel");
            _timePeriodLabel = GetNode<Label>("TimeContainer/TimeBackground/TimeVBox/TimePeriodLabel");
        }

        /// <summary>
        /// Initializes the HUD with system references
        /// </summary>
        public void Initialize(SurvivalStatsSystem survivalStatsSystem, WeaponController weaponController)
        {
            _survivalStatsSystem = survivalStatsSystem;
            _weaponController = weaponController;
            
            // Connect to survival stats events (keep for backward compatibility)
            if (_survivalStatsSystem != null)
            {
                _survivalStatsSystem.Health.StatChanged += OnHealthChanged;
                _survivalStatsSystem.Hunger.StatChanged += OnHungerChanged;
                _survivalStatsSystem.Thirst.StatChanged += OnThirstChanged;
                _survivalStatsSystem.Stamina.StatChanged += OnStaminaChanged;
            }
            
            // Connect to weapon events (keep for backward compatibility)
            if (_weaponController != null)
            {
                _weaponController.WeaponFired += OnWeaponFired;
                _weaponController.WeaponReloadStarted += OnWeaponReloadStarted;
                _weaponController.WeaponReloadCompleted += OnWeaponReloadCompleted;
                _weaponController.WeaponSwitched += OnWeaponSwitched;
            }
            
            // Connect to EventBus events for more responsive updates
            if (EventBus.Instance != null)
            {
                EventBus.Instance.StatChanged += OnEventBusStatChanged;
                EventBus.Instance.WeaponFired += OnEventBusWeaponFired;
                EventBus.Instance.WeaponReloadStarted += OnEventBusWeaponReloadStarted;
                EventBus.Instance.WeaponReloadCompleted += OnEventBusWeaponReloadCompleted;
                EventBus.Instance.WeaponSwitched += OnEventBusWeaponSwitched;
            }
            
            // Connect to day/night cycle events
            if (DayNightCycle.Instance != null)
            {
                DayNightCycle.Instance.TimeChanged += OnTimeChanged;
            }
            
            // Update displays with current values
            UpdateAllStats();
            UpdateWeaponDisplay();
            UpdateTimeDisplay();
            
            GD.Print("Survival HUD initialized");
        }

        /// <summary>
        /// Updates all survival stat displays
        /// </summary>
        private void UpdateAllStats()
        {
            if (_survivalStatsSystem != null)
            {
                UpdateHealthDisplay(_survivalStatsSystem.Health.CurrentValue, _survivalStatsSystem.Health.MaxValue);
                UpdateHungerDisplay(_survivalStatsSystem.Hunger.CurrentValue, _survivalStatsSystem.Hunger.MaxValue);
                UpdateThirstDisplay(_survivalStatsSystem.Thirst.CurrentValue, _survivalStatsSystem.Thirst.MaxValue);
                UpdateStaminaDisplay(_survivalStatsSystem.Stamina.CurrentValue, _survivalStatsSystem.Stamina.MaxValue);
            }
        }

        /// <summary>
        /// Updates the health bar and value display
        /// </summary>
        private void UpdateHealthDisplay(float currentValue, float maxValue)
        {
            if (_healthBar != null && _healthValue != null)
            {
                _healthBar.MaxValue = maxValue;
                _healthBar.Value = currentValue;
                _healthValue.Text = $"{currentValue:F0}/{maxValue:F0}";
                
                // Color coding for health
                if (currentValue / maxValue < 0.25f)
                {
                    _healthBar.Modulate = Colors.Red;
                }
                else if (currentValue / maxValue < 0.5f)
                {
                    _healthBar.Modulate = Colors.Orange;
                }
                else
                {
                    _healthBar.Modulate = Colors.Green;
                }
            }
        }

        /// <summary>
        /// Updates the hunger bar and value display
        /// </summary>
        private void UpdateHungerDisplay(float currentValue, float maxValue)
        {
            if (_hungerBar != null && _hungerValue != null)
            {
                _hungerBar.MaxValue = maxValue;
                _hungerBar.Value = currentValue;
                _hungerValue.Text = $"{currentValue:F0}/{maxValue:F0}";
                
                // Color coding for hunger
                if (currentValue / maxValue < 0.25f)
                {
                    _hungerBar.Modulate = Colors.Red;
                }
                else if (currentValue / maxValue < 0.5f)
                {
                    _hungerBar.Modulate = Colors.Orange;
                }
                else
                {
                    _hungerBar.Modulate = Colors.Yellow;
                }
            }
        }

        /// <summary>
        /// Updates the thirst bar and value display
        /// </summary>
        private void UpdateThirstDisplay(float currentValue, float maxValue)
        {
            if (_thirstBar != null && _thirstValue != null)
            {
                _thirstBar.MaxValue = maxValue;
                _thirstBar.Value = currentValue;
                _thirstValue.Text = $"{currentValue:F0}/{maxValue:F0}";
                
                // Color coding for thirst
                if (currentValue / maxValue < 0.25f)
                {
                    _thirstBar.Modulate = Colors.Red;
                }
                else if (currentValue / maxValue < 0.5f)
                {
                    _thirstBar.Modulate = Colors.Orange;
                }
                else
                {
                    _thirstBar.Modulate = Colors.Blue;
                }
            }
        }

        /// <summary>
        /// Updates the stamina bar and value display
        /// </summary>
        private void UpdateStaminaDisplay(float currentValue, float maxValue)
        {
            if (_staminaBar != null && _staminaValue != null)
            {
                _staminaBar.MaxValue = maxValue;
                _staminaBar.Value = currentValue;
                _staminaValue.Text = $"{currentValue:F0}/{maxValue:F0}";
                
                // Color coding for stamina
                if (currentValue / maxValue < 0.25f)
                {
                    _staminaBar.Modulate = Colors.Red;
                }
                else if (currentValue / maxValue < 0.5f)
                {
                    _staminaBar.Modulate = Colors.Orange;
                }
                else
                {
                    _staminaBar.Modulate = Colors.Cyan;
                }
            }
        }

        /// <summary>
        /// Updates the weapon display with current weapon information
        /// </summary>
        private void UpdateWeaponDisplay()
        {
            if (_weaponController == null)
            {
                ShowNoWeapon();
                return;
            }
            
            var weaponInfo = _weaponController.GetWeaponInfo();
            if (weaponInfo == null)
            {
                ShowNoWeapon();
                return;
            }
            
            // Update weapon name
            if (_weaponNameLabel != null)
            {
                _weaponNameLabel.Text = weaponInfo.Name;
            }
            
            // Update ammo display
            if (_ammoValue != null)
            {
                _ammoValue.Text = $"{weaponInfo.CurrentAmmo}/{weaponInfo.MagazineSize}";
            }
            
            // Update condition bar
            if (_conditionBar != null)
            {
                _conditionBar.Value = weaponInfo.Condition;
                
                // Color coding for weapon condition
                if (weaponInfo.Condition < 25f)
                {
                    _conditionBar.Modulate = Colors.Red;
                }
                else if (weaponInfo.Condition < 50f)
                {
                    _conditionBar.Modulate = Colors.Orange;
                }
                else
                {
                    _conditionBar.Modulate = Colors.Green;
                }
            }
            
            // Update reloading status
            if (_reloadingLabel != null)
            {
                _reloadingLabel.Visible = weaponInfo.IsReloading;
            }
        }

        /// <summary>
        /// Shows the no weapon state
        /// </summary>
        private void ShowNoWeapon()
        {
            if (_weaponNameLabel != null)
                _weaponNameLabel.Text = "No Weapon";
            
            if (_ammoValue != null)
                _ammoValue.Text = "0/0";
            
            if (_conditionBar != null)
            {
                _conditionBar.Value = 0;
                _conditionBar.Modulate = Colors.Gray;
            }
            
            if (_reloadingLabel != null)
                _reloadingLabel.Visible = false;
        }

        /// <summary>
        /// Updates the time display with current time and period
        /// </summary>
        private void UpdateTimeDisplay()
        {
            if (DayNightCycle.Instance == null) return;

            if (_timeLabel != null)
            {
                _timeLabel.Text = DayNightCycle.Instance.GetFormattedTime();
            }

            if (_timePeriodLabel != null)
            {
                string timePeriod = DayNightCycle.Instance.GetTimePeriod();
                _timePeriodLabel.Text = timePeriod;
                
                // Color code the time period
                switch (timePeriod.ToLower())
                {
                    case "dawn":
                        _timePeriodLabel.Modulate = new Color(1.0f, 0.8f, 0.6f); // Orange
                        break;
                    case "day":
                        _timePeriodLabel.Modulate = Colors.Yellow;
                        break;
                    case "dusk":
                        _timePeriodLabel.Modulate = new Color(1.0f, 0.6f, 0.4f); // Red-orange
                        break;
                    case "night":
                        _timePeriodLabel.Modulate = new Color(0.7f, 0.7f, 1.0f); // Light blue
                        break;
                    default:
                        _timePeriodLabel.Modulate = Colors.White;
                        break;
                }
            }
        }

        #region Event Handlers

        private void OnHealthChanged(float currentValue, float maxValue)
        {
            UpdateHealthDisplay(currentValue, maxValue);
        }

        private void OnHungerChanged(float currentValue, float maxValue)
        {
            UpdateHungerDisplay(currentValue, maxValue);
        }

        private void OnThirstChanged(float currentValue, float maxValue)
        {
            UpdateThirstDisplay(currentValue, maxValue);
        }

        private void OnStaminaChanged(float currentValue, float maxValue)
        {
            UpdateStaminaDisplay(currentValue, maxValue);
        }

        private void OnWeaponFired(string weaponName, float damage)
        {
            // Update weapon display after firing (ammo count, condition)
            UpdateWeaponDisplay();
        }

        private void OnWeaponReloadStarted(string weaponName, float reloadTime)
        {
            // Show reloading indicator
            if (_reloadingLabel != null)
                _reloadingLabel.Visible = true;
        }

        private void OnWeaponReloadCompleted(string weaponName)
        {
            // Hide reloading indicator and update ammo display
            if (_reloadingLabel != null)
                _reloadingLabel.Visible = false;
            
            UpdateWeaponDisplay();
        }

        private void OnWeaponSwitched(string oldWeaponName, string newWeaponName)
        {
            // Update entire weapon display when weapon changes
            UpdateWeaponDisplay();
        }

        #endregion

        public override void _ExitTree()
        {
            // Disconnect from events to prevent memory leaks
            if (_survivalStatsSystem != null)
            {
                _survivalStatsSystem.Health.StatChanged -= OnHealthChanged;
                _survivalStatsSystem.Hunger.StatChanged -= OnHungerChanged;
                _survivalStatsSystem.Thirst.StatChanged -= OnThirstChanged;
                _survivalStatsSystem.Stamina.StatChanged -= OnStaminaChanged;
            }
            
            if (_weaponController != null)
            {
                _weaponController.WeaponFired -= OnWeaponFired;
                _weaponController.WeaponReloadStarted -= OnWeaponReloadStarted;
                _weaponController.WeaponReloadCompleted -= OnWeaponReloadCompleted;
                _weaponController.WeaponSwitched -= OnWeaponSwitched;
            }
            
            // Disconnect from EventBus events
            if (EventBus.Instance != null)
            {
                EventBus.Instance.StatChanged -= OnEventBusStatChanged;
                EventBus.Instance.WeaponFired -= OnEventBusWeaponFired;
                EventBus.Instance.WeaponReloadStarted -= OnEventBusWeaponReloadStarted;
                EventBus.Instance.WeaponReloadCompleted -= OnEventBusWeaponReloadCompleted;
                EventBus.Instance.WeaponSwitched -= OnEventBusWeaponSwitched;
            }
            
            // Disconnect from day/night cycle events
            if (DayNightCycle.Instance != null)
            {
                DayNightCycle.Instance.TimeChanged -= OnTimeChanged;
            }
        }

        #region EventBus Event Handlers

        /// <summary>
        /// Handles EventBus stat changed events
        /// </summary>
        private void OnEventBusStatChanged(string statName, float currentValue, float maxValue, float previousValue)
        {
            switch (statName.ToLower())
            {
                case "health":
                    UpdateHealthDisplay(currentValue, maxValue);
                    break;
                case "hunger":
                    UpdateHungerDisplay(currentValue, maxValue);
                    break;
                case "thirst":
                    UpdateThirstDisplay(currentValue, maxValue);
                    break;
                case "stamina":
                    UpdateStaminaDisplay(currentValue, maxValue);
                    break;
            }
        }

        /// <summary>
        /// Handles EventBus weapon fired events
        /// </summary>
        private void OnEventBusWeaponFired(string weaponId, string weaponName, float damage, int remainingAmmo)
        {
            UpdateWeaponDisplay();
        }

        /// <summary>
        /// Handles EventBus weapon reload started events
        /// </summary>
        private void OnEventBusWeaponReloadStarted(string weaponId, string weaponName, float reloadTime)
        {
            if (_reloadingLabel != null)
                _reloadingLabel.Visible = true;
        }

        /// <summary>
        /// Handles EventBus weapon reload completed events
        /// </summary>
        private void OnEventBusWeaponReloadCompleted(string weaponId, string weaponName, int newAmmoCount)
        {
            if (_reloadingLabel != null)
                _reloadingLabel.Visible = false;
            
            UpdateWeaponDisplay();
        }

        /// <summary>
        /// Handles EventBus weapon switched events
        /// </summary>
        private void OnEventBusWeaponSwitched(string oldWeaponId, string newWeaponId, string oldWeaponName, string newWeaponName)
        {
            UpdateWeaponDisplay();
        }

        #endregion

        #region Day/Night Cycle Event Handlers

        /// <summary>
        /// Handles time changed events from the day/night cycle
        /// </summary>
        private void OnTimeChanged(float currentTime, bool isNight)
        {
            UpdateTimeDisplay();
        }

        #endregion
    }
}