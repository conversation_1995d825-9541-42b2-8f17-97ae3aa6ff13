[gd_scene load_steps=2 format=3 uid="uid://doia33kq5my13"]

[ext_resource type="Script" path="res://Tests/EnvironmentalHazardIntegrationTests.cs" id="1_abcdef"]

[node name="EnvironmentalHazardIntegrationTests" type="Node"]
script = ExtResource("1_abcdef")

[node name="SurvivalStatsSystem" type="Node" parent="."]
script = ExtResource("res://Scripts/SurvivalStatsSystem.cs")

[node name="WeatherManager" type="Node" parent="."]
script = ExtResource("res://Scripts/WeatherManager.cs")

[node name="TemperatureSystem" type="Node" parent="."]
script = ExtResource("res://Scripts/TemperatureSystem.cs")

[node name="EnvironmentalHazardSystem" type="Node" parent="."]
script = ExtResource("res://Scripts/EnvironmentalHazardSystem.cs")

[node name="NaturalDisasterSystem" type="Node" parent="."]
script = ExtResource("res://Scripts/NaturalDisasterSystem.cs")

[node name="EnvironmentalProtectionManager" type="Node" parent="."]
script = ExtResource("res://Scripts/EnvironmentalProtectionManager.cs")

[node name="SeasonalResourceSystem" type="Node" parent="."]
script = ExtResource("res://Scripts/SeasonalResourceSystem.cs")

[node name="ClothingProtectionSystem" type="Node" parent="."]
script = ExtResource("res://Scripts/ClothingProtectionSystem.cs")

[node name="EventBus" type="Node" parent="."]
script = ExtResource("res://Scripts/EventBus.cs")

[node name="ItemDatabase" type="Node" parent="."]
script = ExtResource("res://Scripts/ItemDatabase.cs")

[node name="Logger" type="Node" parent="."]
script = ExtResource("res://Scripts/Logger.cs")

[node name="DayNightCycle" type="Node" parent="."]
script = ExtResource("res://Scripts/DayNightCycle.cs")