using Godot;
using System;
using System.Collections.Generic;
using System.Linq;

namespace SurvivalLooterShooter
{
    /// <summary>
    /// Manages defensive structures, automated defenses, and base security
    /// Coordinates turret targeting, alarm systems, and base raid mechanics
    /// </summary>
    public partial class DefenseSystem : Node
    {
        private static DefenseSystem _instance;
        public static DefenseSystem Instance => _instance;

        // Defense structures tracking
        private readonly List<DefensiveTurret> _activeTurrets = [];
        private readonly List<DefenseWall> _defenseWalls = [];
        private readonly List<DefenseGate> _defenseGates = [];
        private readonly List<AlarmSystem> _alarmSystems = [];

        // Threat detection
        private readonly HashSet<Enemy> _detectedThreats = [];
        private readonly Dictionary<Enemy, float> _threatLevels = [];
        private Vector2 _baseCenter = Vector2.Zero;
        private float _baseRadius = 300f;

        // Base raid system
        private bool _raidInProgress = false;
        private float _raidIntensity = 0f;
        private Timer _raidTimer;
        private Timer _threatScanTimer;
        private int _consecutiveRaidDays = 0;

        // Defense configuration
        [Export] public float ThreatScanInterval { get; set; } = 2.0f;
        [Export] public float BaseDefenseRadius { get; set; } = 300f;
        [Export] public float RaidCooldownHours { get; set; } = 24f;
        [Export] public float MaxRaidIntensity { get; set; } = 10f;

        // Events
        [Signal] public delegate void ThreatDetectedEventHandler(Enemy enemy, float threatLevel);
        [Signal] public delegate void ThreatNeutralizedEventHandler(Enemy enemy);
        [Signal] public delegate void BaseRaidStartedEventHandler(float intensity, int enemyCount);
        [Signal] public delegate void BaseRaidEndedEventHandler(bool playerVictory, float damageDealt);
        [Signal] public delegate void AlarmActivatedEventHandler(string alarmType, Vector2 position);
        [Signal] public delegate void DefenseStructureDamagedEventHandler(Structure structure, float damage);
        [Signal] public delegate void DefenseStructureDestroyedEventHandler(Structure structure);

        public bool IsRaidInProgress => _raidInProgress;
        public float CurrentRaidIntensity => _raidIntensity;
        public int ActiveTurretCount => _activeTurrets.Count;
        public int DetectedThreatCount => _detectedThreats.Count;

        public override void _Ready()
        {
            if (_instance == null)
            {
                _instance = this;
                AddToGroup("defense_system");
                Logger.LogInfo("DefenseSystem", "DefenseSystem initialized");
            }
            else
            {
                Logger.LogError("DefenseSystem", "Multiple DefenseSystem instances detected! Removing duplicate.");
                QueueFree();
                return;
            }

            SetupTimers();
            ConnectToEventBus();
            CalculateBaseCenter();
        }

        /// <summary>
        /// Sets up timers for threat scanning and raid management
        /// </summary>
        private void SetupTimers()
        {
            // Threat scanning timer
            _threatScanTimer = new Timer();
            _threatScanTimer.Name = "ThreatScanTimer";
            _threatScanTimer.WaitTime = ThreatScanInterval;
            _threatScanTimer.Autostart = true;
            _threatScanTimer.Timeout += OnThreatScanTimeout;
            AddChild(_threatScanTimer);

            // Raid management timer
            _raidTimer = new Timer();
            _raidTimer.Name = "RaidTimer";
            _raidTimer.WaitTime = 300f; // Check for raids every 5 minutes
            _raidTimer.Autostart = true;
            _raidTimer.Timeout += OnRaidTimerTimeout;
            AddChild(_raidTimer);
        }

        /// <summary>
        /// Connects to EventBus for system integration
        /// </summary>
        private void ConnectToEventBus()
        {
            if (EventBus.Instance != null)
            {
                EventBus.Instance.DayNightChanged += OnDayNightChanged;
                EventBus.Instance.PlayerLevelChanged += OnPlayerLevelChanged;
            }
        }

        private void OnDayNightChanged(float currentTime, bool isNight)
        {
            throw new NotImplementedException();
        }

        /// <summary>
        /// Calculates the center of the player's base from existing structures
        /// </summary>
        private void CalculateBaseCenter()
        {
            var buildingManager = BuildingManager.Instance;
            if (buildingManager == null) return;

            var allStructures = buildingManager.GetAllBlueprints();
            if (allStructures.Count == 0)
            {
                _baseCenter = Vector2.Zero;
                return;
            }

            // Calculate center from foundation structures
            var foundations = new List<Vector2>();
            foreach (var structure in GetTree().GetNodesInGroup("structures"))
            {
                if (structure is Structure s && s.Blueprint.Type == "foundation")
                {
                    foundations.Add(s.GlobalPosition);
                }
            }

            if (foundations.Count > 0)
            {
                Vector2 sum = Vector2.Zero;
                foreach (var pos in foundations)
                {
                    sum += pos;
                }
                _baseCenter = sum / foundations.Count;
            }

            Logger.LogInfo("DefenseSystem", $"Base center calculated at {_baseCenter}");
        }

        /// <summary>
        /// Registers a defensive turret with the system
        /// </summary>
        public void RegisterTurret(DefensiveTurret turret)
        {
            if (turret == null || _activeTurrets.Contains(turret)) return;

            _activeTurrets.Add(turret);
            turret.TurretDestroyed += OnTurretDestroyed;
            Logger.LogInfo("DefenseSystem", $"Registered defensive turret at {turret.GlobalPosition}");
        }

        /// <summary>
        /// Unregisters a defensive turret from the system
        /// </summary>
        public void UnregisterTurret(DefensiveTurret turret)
        {
            if (turret == null) return;

            _activeTurrets.Remove(turret);
            turret.TurretDestroyed -= OnTurretDestroyed;
            Logger.LogInfo("DefenseSystem", $"Unregistered defensive turret");
        }

        /// <summary>
        /// Registers a defense wall with the system
        /// </summary>
        public void RegisterDefenseWall(DefenseWall wall)
        {
            if (wall == null || _defenseWalls.Contains(wall)) return;

            _defenseWalls.Add(wall);
            wall.WallDestroyed += OnDefenseWallDestroyed;
            Logger.LogInfo("DefenseSystem", $"Registered defense wall at {wall.GlobalPosition}");
        }

        /// <summary>
        /// Unregisters a defense wall from the system
        /// </summary>
        public void UnregisterDefenseWall(DefenseWall wall)
        {
            if (wall == null) return;

            _defenseWalls.Remove(wall);
            wall.WallDestroyed -= OnDefenseWallDestroyed;
        }

        /// <summary>
        /// Registers an alarm system with the defense network
        /// </summary>
        public void RegisterAlarmSystem(AlarmSystem alarm)
        {
            if (alarm == null || _alarmSystems.Contains(alarm)) return;

            _alarmSystems.Add(alarm);
            alarm.AlarmTriggered += OnAlarmTriggered;
            Logger.LogInfo("DefenseSystem", $"Registered alarm system at {alarm.GlobalPosition}");
        }

        /// <summary>
        /// Unregisters an alarm system from the defense network
        /// </summary>
        public void UnregisterAlarmSystem(AlarmSystem alarm)
        {
            if (alarm == null) return;

            _alarmSystems.Remove(alarm);
            alarm.AlarmTriggered -= OnAlarmTriggered;
        }

        /// <summary>
        /// Handles threat scanning timer timeout
        /// </summary>
        private void OnThreatScanTimeout()
        {
            ScanForThreats();
            UpdateTurretTargets();
            CheckAlarmConditions();
        }

        /// <summary>
        /// Scans for enemy threats within base defense radius
        /// </summary>
        private void ScanForThreats()
        {
            var enemies = GetTree().GetNodesInGroup("enemies").Cast<Enemy>().ToList();
            var currentThreats = new HashSet<Enemy>();

            foreach (var enemy in enemies)
            {
                if (enemy == null || enemy.IsDead) continue;

                float distanceToBase = enemy.GlobalPosition.DistanceTo(_baseCenter);
                if (distanceToBase <= BaseDefenseRadius)
                {
                    currentThreats.Add(enemy);
                    
                    // Calculate threat level based on distance and enemy stats
                    float threatLevel = CalculateThreatLevel(enemy, distanceToBase);
                    _threatLevels[enemy] = threatLevel;

                    // Emit event for new threats
                    if (!_detectedThreats.Contains(enemy))
                    {
                        EmitSignal(SignalName.ThreatDetected, enemy, threatLevel);
                        Logger.LogInfo("DefenseSystem", $"New threat detected: {enemy.EnemyName} (Level: {threatLevel:F1})");
                    }
                }
            }

            // Check for neutralized threats
            var neutralizedThreats = _detectedThreats.Except(currentThreats).ToList();
            foreach (var threat in neutralizedThreats)
            {
                EmitSignal(SignalName.ThreatNeutralized, threat);
                _threatLevels.Remove(threat);
            }

            _detectedThreats.Clear();
            foreach (var threat in currentThreats)
            {
                _detectedThreats.Add(threat);
            }
        }

        /// <summary>
        /// Calculates threat level for an enemy based on various factors
        /// </summary>
        private float CalculateThreatLevel(Enemy enemy, float distanceToBase)
        {
            float baseThreat = enemy.Damage / 10f; // Base threat from damage
            float distanceFactor = 1f - (distanceToBase / BaseDefenseRadius); // Closer = higher threat
            float healthFactor = enemy.CurrentHealth / 100f; // More health = higher threat
            
            return (baseThreat + distanceFactor + healthFactor) * 2f;
        }

        /// <summary>
        /// Updates turret targeting systems
        /// </summary>
        private void UpdateTurretTargets()
        {
            foreach (var turret in _activeTurrets)
            {
                if (turret == null || turret.IsDestroyed) continue;

                // Find best target for this turret
                Enemy bestTarget = FindBestTargetForTurret(turret);
                turret.SetTarget(bestTarget);
            }
        }

        /// <summary>
        /// Finds the best target for a specific turret
        /// </summary>
        private Enemy FindBestTargetForTurret(DefensiveTurret turret)
        {
            Enemy bestTarget = null;
            float highestPriority = 0f;

            foreach (var threat in _detectedThreats)
            {
                if (threat == null || threat.IsDead) continue;

                float distanceToTurret = threat.GlobalPosition.DistanceTo(turret.GlobalPosition);
                if (distanceToTurret > turret.Range) continue;

                // Calculate targeting priority
                float priority = CalculateTargetPriority(threat, turret, distanceToTurret);
                if (priority > highestPriority)
                {
                    highestPriority = priority;
                    bestTarget = threat;
                }
            }

            return bestTarget;
        }

        /// <summary>
        /// Calculates targeting priority for an enemy relative to a turret
        /// </summary>
        private float CalculateTargetPriority(Enemy enemy, DefensiveTurret turret, float distance)
        {
            float threatLevel = _threatLevels.GetValueOrDefault(enemy, 1f);
            float distanceFactor = 1f - (distance / turret.Range); // Closer targets prioritized
            float healthFactor = 1f / (enemy.CurrentHealth / 100f); // Lower health prioritized
            
            return threatLevel * distanceFactor * healthFactor;
        }

        /// <summary>
        /// Checks conditions for alarm activation
        /// </summary>
        private void CheckAlarmConditions()
        {
            if (_detectedThreats.Count == 0) return;

            // Activate alarms based on threat level
            float totalThreatLevel = _threatLevels.Values.Sum();
            
            if (totalThreatLevel > 5f) // High threat threshold
            {
                ActivateAlarms("high_threat");
            }
            else if (totalThreatLevel > 2f) // Medium threat threshold
            {
                ActivateAlarms("medium_threat");
            }
        }

        /// <summary>
        /// Activates alarm systems of specified type
        /// </summary>
        private void ActivateAlarms(string alarmType)
        {
            foreach (var alarm in _alarmSystems)
            {
                if (alarm != null && !alarm.IsActive)
                {
                    alarm.Activate(alarmType);
                }
            }
        }

        /// <summary>
        /// Handles raid timer timeout - checks for raid conditions
        /// </summary>
        private void OnRaidTimerTimeout()
        {
            if (_raidInProgress) return;

            // Check if conditions are met for a base raid
            if (ShouldTriggerRaid())
            {
                StartBaseRaid();
            }
        }

        /// <summary>
        /// Determines if conditions are met to trigger a base raid
        /// </summary>
        private bool ShouldTriggerRaid()
        {
            // Check if player has built enough structures to warrant raids
            var structureCount = GetTree().GetNodesInGroup("structures").Count;
            if (structureCount < 5) return false;

            // Check if enough time has passed since last raid
            // (This would normally check save data for last raid time)
            
            // Check player level - higher level = more likely raids
            var gameManager = GetNode<GameManager>("/root/GameManager");
            if (gameManager == null) return false;

            // Simple probability based on various factors
            float raidChance = 0.1f; // Base 10% chance
            
            // Increase chance based on consecutive days
            raidChance += _consecutiveRaidDays * 0.05f;
            
            // Increase chance at night
            if (IsNightTime())
            {
                raidChance *= 2f;
            }

            return GD.Randf() < raidChance;
        }

        /// <summary>
        /// Starts a base raid event
        /// </summary>
        private void StartBaseRaid()
        {
            _raidInProgress = true;
            _raidIntensity = CalculateRaidIntensity();
            
            int enemyCount = Mathf.RoundToInt(_raidIntensity * 2f);
            
            EmitSignal(SignalName.BaseRaidStarted, _raidIntensity, enemyCount);
            Logger.LogInfo("DefenseSystem", $"Base raid started! Intensity: {_raidIntensity:F1}, Enemies: {enemyCount}");

            // Spawn raid enemies
            SpawnRaidEnemies(enemyCount);

            // Set raid duration
            var raidDurationTimer = new Timer();
            raidDurationTimer.WaitTime = 300f + (_raidIntensity * 60f); // 5-15 minutes based on intensity
            raidDurationTimer.OneShot = true;
            raidDurationTimer.Timeout += EndBaseRaid;
            AddChild(raidDurationTimer);
            raidDurationTimer.Start();

            // Activate all alarms
            ActivateAlarms("raid");
        }

        /// <summary>
        /// Calculates raid intensity based on player progress and base strength
        /// </summary>
        private float CalculateRaidIntensity()
        {
            float intensity = 1f;

            // Factor in player level
            var gameManager = GetNode<GameManager>("/root/GameManager");
            if (gameManager != null)
            {
                // intensity += playerLevel * 0.2f; // Would get from game manager
            }

            // Factor in base defenses
            intensity += _activeTurrets.Count * 0.5f;
            intensity += _defenseWalls.Count * 0.2f;

            // Factor in consecutive raid days
            intensity += _consecutiveRaidDays * 0.3f;

            return Mathf.Min(intensity, MaxRaidIntensity);
        }

        /// <summary>
        /// Spawns enemies for the raid around the base perimeter
        /// </summary>
        private void SpawnRaidEnemies(int enemyCount)
        {
            var enemyManager = EnemyManager.Instance;
            if (enemyManager == null) return;

            for (int i = 0; i < enemyCount; i++)
            {
                // Spawn enemies around the base perimeter
                float angle = (float)(GD.Randi() % 360) * Mathf.Pi / 180f;
                float spawnDistance = BaseDefenseRadius + 50f + (GD.Randf() * 100f);
                
                Vector2 spawnPosition = _baseCenter + new Vector2(
                    Mathf.Cos(angle) * spawnDistance,
                    Mathf.Sin(angle) * spawnDistance
                );

                // Spawn appropriate enemy type based on raid intensity
                string enemyType = SelectRaidEnemyType(_raidIntensity);
                var enemy = enemyManager.SpawnEnemy(enemyType, spawnPosition);
                
                if (enemy != null)
                {
                    // Make raid enemies more aggressive
                    enemy.Speed *= 1.2f;
                    enemy.Damage *= 1.1f;
                    enemy.DetectionRange *= 1.5f;
                }
            }
        }

        /// <summary>
        /// Selects appropriate enemy type for raid based on intensity
        /// </summary>
        private string SelectRaidEnemyType(float intensity)
        {
            if (intensity > 7f) return "raid_boss";
            if (intensity > 5f) return "heavy_raider";
            if (intensity > 3f) return "raider";
            return "bandit";
        }

        /// <summary>
        /// Ends the current base raid
        /// </summary>
        private void EndBaseRaid()
        {
            if (!_raidInProgress) return;

            _raidInProgress = false;
            
            // Calculate raid results
            bool playerVictory = CalculateRaidOutcome();
            float damageDealt = CalculateTotalDamageDealt();

            EmitSignal(SignalName.BaseRaidEnded, playerVictory, damageDealt);
            
            if (playerVictory)
            {
                Logger.LogInfo("DefenseSystem", "Base raid successfully defended!");
                _consecutiveRaidDays = 0;
            }
            else
            {
                Logger.LogInfo("DefenseSystem", "Base raid caused significant damage!");
                _consecutiveRaidDays++;
            }

            // Deactivate alarms
            foreach (var alarm in _alarmSystems)
            {
                alarm?.Deactivate();
            }

            _raidIntensity = 0f;
        }

        /// <summary>
        /// Calculates the outcome of the raid
        /// </summary>
        private bool CalculateRaidOutcome()
        {
            // Count remaining raid enemies
            var remainingEnemies = GetTree().GetNodesInGroup("enemies").Cast<Enemy>()
                .Count(e => e != null && !e.IsDead && e.GlobalPosition.DistanceTo(_baseCenter) <= BaseDefenseRadius * 2f);

            // Count damaged/destroyed structures
            var damagedStructures = GetTree().GetNodesInGroup("structures").Cast<Structure>()
                .Count(s => s != null && s.HealthPercentage < 0.8f);

            // Player wins if most enemies are defeated and structures are mostly intact
            return remainingEnemies < 3 && damagedStructures < 5;
        }

        /// <summary>
        /// Calculates total damage dealt to base structures during raid
        /// </summary>
        private float CalculateTotalDamageDealt()
        {
            float totalDamage = 0f;
            
            foreach (var structure in GetTree().GetNodesInGroup("structures").Cast<Structure>())
            {
                if (structure != null)
                {
                    float damagePercent = 1f - structure.HealthPercentage;
                    totalDamage += damagePercent * structure.MaxHealth;
                }
            }

            return totalDamage;
        }

        /// <summary>
        /// Checks if it's currently night time
        /// </summary>
        private bool IsNightTime()
        {
            var dayNightCycle = GetNode<DayNightCycle>("/root/Main/DayNightCycle");
            return dayNightCycle?.IsNightTime ?? false;
        }

        /// <summary>
        /// Handles day/night cycle changes
        /// </summary>
        private void OnDayNightChanged(bool isNight, float timeOfDay)
        {
            if (isNight)
            {
                // Increase threat scanning frequency at night
                _threatScanTimer.WaitTime = ThreatScanInterval * 0.5f;
            }
            else
            {
                // Normal scanning frequency during day
                _threatScanTimer.WaitTime = ThreatScanInterval;
            }
        }

        /// <summary>
        /// Handles player level changes
        /// </summary>
        private void OnPlayerLevelChanged(int newLevel, float experience)
        {
            // Adjust base defense parameters based on player level
            BaseDefenseRadius = 300f + (newLevel * 10f);
            Logger.LogInfo("DefenseSystem", $"Defense radius adjusted to {BaseDefenseRadius} for level {newLevel}");
        }

        /// <summary>
        /// Handles turret destruction events
        /// </summary>
        private void OnTurretDestroyed(DefensiveTurret turret)
        {
            UnregisterTurret(turret);
            EmitSignal(SignalName.DefenseStructureDestroyed, turret.GetStructure());
        }

        /// <summary>
        /// Handles defense wall destruction events
        /// </summary>
        private void OnDefenseWallDestroyed(DefenseWall wall)
        {
            UnregisterDefenseWall(wall);
            EmitSignal(SignalName.DefenseStructureDestroyed, wall.GetStructure());
        }

        /// <summary>
        /// Handles alarm system triggers
        /// </summary>
        private void OnAlarmTriggered(string alarmType, Vector2 position)
        {
            EmitSignal(SignalName.AlarmActivated, alarmType, position);
            Logger.LogInfo("DefenseSystem", $"Alarm activated: {alarmType} at {position}");
        }

        /// <summary>
        /// Gets all active defensive structures
        /// </summary>
        public List<Structure> GetAllDefensiveStructures()
        {
            var structures = new List<Structure>();
            
            foreach (var turret in _activeTurrets)
            {
                if (turret?.GetStructure() != null)
                    structures.Add(turret.GetStructure());
            }
            
            foreach (var wall in _defenseWalls)
            {
                if (wall?.GetStructure() != null)
                    structures.Add(wall.GetStructure());
            }

            return structures;
        }

        /// <summary>
        /// Gets current threat assessment
        /// </summary>
        public Dictionary<string, object> GetThreatAssessment()
        {
            return new Dictionary<string, object>
            {
                ["threat_count"] = _detectedThreats.Count,
                ["total_threat_level"] = _threatLevels.Values.Sum(),
                ["raid_in_progress"] = _raidInProgress,
                ["raid_intensity"] = _raidIntensity,
                ["active_turrets"] = _activeTurrets.Count,
                ["defense_walls"] = _defenseWalls.Count,
                ["alarm_systems"] = _alarmSystems.Count
            };
        }
    }
}