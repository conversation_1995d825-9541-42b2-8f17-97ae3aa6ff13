using Godot;
using System.Collections.Generic;
using System.Linq;

namespace SurvivalLooterShooter
{
    /// <summary>
    /// Crafting UI controller that manages the crafting interface and interactions
    /// </summary>
    public partial class CraftingUI : Control
    {
        private Panel _background;
        private VBoxContainer _recipeContainer;
        private VBoxContainer _selectedRecipeContainer;
        private Label _recipeNameLabel;
        private VBoxContainer _materialsContainer;
        private HBoxContainer _outputContainer;
        private Button _craftButton;
        private Button _closeButton;
        
        private CraftingSystem _craftingSystem;
        private Inventory _inventory;
        private InventoryUI _inventoryUI;
        
        private Recipe _selectedRecipe;
        private Dictionary<string, Button> _recipeButtons = new Dictionary<string, Button>();
        
        private bool _isVisible = false;

        // Colors for visual feedback
        private readonly Color _availableColor = new Color(0.2f, 0.8f, 0.2f); // Green
        private readonly Color _unavailableColor = new Color(0.8f, 0.2f, 0.2f); // Red
        private readonly Color _normalColor = new Color(0.4f, 0.4f, 0.4f); // Gray

        public override void _Ready()
        {
            // Get references to child nodes
            _background = GetNode<Panel>("Background");
            _recipeContainer = GetNode<VBoxContainer>("Background/VBoxContainer/MainContainer/RecipeList/RecipeScrollContainer/RecipeContainer");
            _selectedRecipeContainer = GetNode<VBoxContainer>("Background/VBoxContainer/MainContainer/RecipeDetails/SelectedRecipePanel/SelectedRecipeContainer");
            _recipeNameLabel = GetNode<Label>("Background/VBoxContainer/MainContainer/RecipeDetails/SelectedRecipePanel/SelectedRecipeContainer/RecipeNameLabel");
            _materialsContainer = GetNode<VBoxContainer>("Background/VBoxContainer/MainContainer/RecipeDetails/SelectedRecipePanel/SelectedRecipeContainer/MaterialsContainer");
            _outputContainer = GetNode<HBoxContainer>("Background/VBoxContainer/MainContainer/RecipeDetails/SelectedRecipePanel/SelectedRecipeContainer/OutputContainer");
            _craftButton = GetNode<Button>("Background/VBoxContainer/MainContainer/RecipeDetails/SelectedRecipePanel/SelectedRecipeContainer/CraftButton");
            _closeButton = GetNode<Button>("Background/CloseButton");
            
            // Initially hide the crafting UI
            Visible = false;
            _isVisible = false;
        }

        /// <summary>
        /// Initializes the crafting UI with required systems
        /// </summary>
        /// <param name="craftingSystem">The crafting system to use</param>
        /// <param name="inventory">The inventory system</param>
        /// <param name="inventoryUI">The inventory UI for updates</param>
        public void Initialize(CraftingSystem craftingSystem, Inventory inventory, InventoryUI inventoryUI)
        {
            _craftingSystem = craftingSystem;
            _inventory = inventory;
            _inventoryUI = inventoryUI;
            
            if (_craftingSystem != null)
            {
                // Connect to crafting system signals (keep for backward compatibility)
                _craftingSystem.ItemCrafted += OnItemCrafted;
                _craftingSystem.CraftingFailed += OnCraftingFailed;
            }
            
            if (_inventory != null)
            {
                // Connect to inventory changes to update recipe availability (keep for backward compatibility)
                _inventory.InventoryChanged += OnInventoryChanged;
            }
            
            // Connect to EventBus events for more responsive updates
            if (EventBus.Instance != null)
            {
                EventBus.Instance.ItemCrafted += OnEventBusItemCrafted;
                EventBus.Instance.CraftingFailed += OnEventBusCraftingFailed;
                EventBus.Instance.ItemAdded += OnEventBusItemAdded;
                EventBus.Instance.ItemRemoved += OnEventBusItemRemoved;
            }
        }

        /// <summary>
        /// Toggles the crafting UI visibility
        /// </summary>
        public void ToggleCrafting()
        {
            _isVisible = !_isVisible;
            Visible = _isVisible;
            
            if (_isVisible)
            {
                UpdateRecipeList();
            }
        }

        /// <summary>
        /// Shows the crafting UI
        /// </summary>
        public void ShowCrafting()
        {
            _isVisible = true;
            Visible = true;
            UpdateRecipeList();
        }

        /// <summary>
        /// Hides the crafting UI
        /// </summary>
        public void HideCrafting()
        {
            _isVisible = false;
            Visible = false;
            ClearSelectedRecipe();
        }

        /// <summary>
        /// Updates the recipe list display
        /// </summary>
        private void UpdateRecipeList()
        {
            if (_craftingSystem == null || ItemDatabase.Instance == null)
                return;

            // Clear existing recipe buttons
            foreach (var button in _recipeButtons.Values)
            {
                button.QueueFree();
            }
            _recipeButtons.Clear();

            // Get all recipes from the database
            var allRecipes = ItemDatabase.Instance.GetAllRecipes();
            var availableRecipes = _craftingSystem.GetAvailableRecipes();
            var availableRecipeIds = new HashSet<string>(availableRecipes.Select(r => r.Id));

            foreach (var recipe in allRecipes.Values)
            {
                CreateRecipeButton(recipe, availableRecipeIds.Contains(recipe.Id));
            }
        }

        /// <summary>
        /// Creates a recipe button for the recipe list
        /// </summary>
        /// <param name="recipe">The recipe to create a button for</param>
        /// <param name="isAvailable">Whether the recipe can currently be crafted</param>
        private void CreateRecipeButton(Recipe recipe, bool isAvailable)
        {
            var button = new Button();
            
            // Get output item for display
            var outputItem = ItemDatabase.Instance?.GetItem(recipe.Output.Id);
            string displayName = outputItem?.Name ?? recipe.Output.Id;
            
            button.Text = $"{displayName} x{recipe.Output.Amount}";
            button.SizeFlagsHorizontal = Control.SizeFlags.ExpandFill;
            
            // Set button color based on availability
            var styleBox = new StyleBoxFlat();
            styleBox.BgColor = isAvailable ? _availableColor : _unavailableColor;
            styleBox.BorderWidthLeft = 1;
            styleBox.BorderWidthTop = 1;
            styleBox.BorderWidthRight = 1;
            styleBox.BorderWidthBottom = 1;
            styleBox.BorderColor = Colors.Gray;
            styleBox.CornerRadiusTopLeft = 3;
            styleBox.CornerRadiusTopRight = 3;
            styleBox.CornerRadiusBottomLeft = 3;
            styleBox.CornerRadiusBottomRight = 3;
            
            button.AddThemeStyleboxOverride("normal", styleBox);
            button.AddThemeStyleboxOverride("hover", styleBox);
            button.AddThemeStyleboxOverride("pressed", styleBox);
            
            // Connect button signal
            button.Pressed += () => OnRecipeSelected(recipe);
            
            // Add to container and store reference
            _recipeContainer.AddChild(button);
            _recipeButtons[recipe.Id] = button;
        }

        /// <summary>
        /// Handles recipe selection
        /// </summary>
        /// <param name="recipe">The selected recipe</param>
        private void OnRecipeSelected(Recipe recipe)
        {
            _selectedRecipe = recipe;
            UpdateRecipeDetails();
        }

        /// <summary>
        /// Updates the recipe details panel
        /// </summary>
        private void UpdateRecipeDetails()
        {
            if (_selectedRecipe == null)
            {
                ClearSelectedRecipe();
                return;
            }

            // Get output item for display
            var outputItem = ItemDatabase.Instance?.GetItem(_selectedRecipe.Output.Id);
            string displayName = outputItem?.Name ?? _selectedRecipe.Output.Id;
            
            _recipeNameLabel.Text = $"Craft {displayName}";

            // Clear existing materials display
            foreach (Node child in _materialsContainer.GetChildren())
            {
                child.QueueFree();
            }

            // Display required materials
            foreach (var input in _selectedRecipe.Inputs)
            {
                var materialItem = ItemDatabase.Instance?.GetItem(input.Id);
                string materialName = materialItem?.Name ?? input.Id;
                
                int availableQuantity = _inventory?.GetItemQuantity(input.Id) ?? 0;
                int requiredQuantity = input.Amount;
                
                var materialLabel = new Label();
                materialLabel.Text = $"{materialName}: {availableQuantity}/{requiredQuantity}";
                
                // Color code based on availability
                if (availableQuantity >= requiredQuantity)
                {
                    materialLabel.AddThemeColorOverride("font_color", _availableColor);
                }
                else
                {
                    materialLabel.AddThemeColorOverride("font_color", _unavailableColor);
                }
                
                _materialsContainer.AddChild(materialLabel);
            }

            // Clear existing output display
            foreach (Node child in _outputContainer.GetChildren())
            {
                child.QueueFree();
            }

            // Display output
            var outputLabel = new Label();
            outputLabel.Text = $"{displayName} x{_selectedRecipe.Output.Amount}";
            _outputContainer.AddChild(outputLabel);

            // Update craft button
            bool canCraft = _craftingSystem?.CanCraft(_selectedRecipe) ?? false;
            _craftButton.Disabled = !canCraft;
            _craftButton.Text = canCraft ? "Craft Item" : "Cannot Craft";
        }

        /// <summary>
        /// Clears the selected recipe display
        /// </summary>
        private void ClearSelectedRecipe()
        {
            _selectedRecipe = null;
            _recipeNameLabel.Text = "Select a recipe";
            
            // Clear materials display
            foreach (Node child in _materialsContainer.GetChildren())
            {
                child.QueueFree();
            }
            
            // Clear output display
            foreach (Node child in _outputContainer.GetChildren())
            {
                child.QueueFree();
            }
            
            _craftButton.Disabled = true;
            _craftButton.Text = "Craft Item";
        }

        /// <summary>
        /// Handles inventory changes to update recipe availability
        /// </summary>
        private void OnInventoryChanged(string itemId, int quantityChange)
        {
            if (_isVisible)
            {
                // Update recipe list colors
                UpdateRecipeList();
                
                // Update selected recipe details if any
                if (_selectedRecipe != null)
                {
                    UpdateRecipeDetails();
                }
            }
        }

        /// <summary>
        /// Handles successful crafting
        /// </summary>
        private void OnItemCrafted(string recipeId, string outputItemId, int outputQuantity)
        {
            var outputItem = ItemDatabase.Instance?.GetItem(outputItemId);
            string itemName = outputItem?.Name ?? outputItemId;
            
            GD.Print($"Successfully crafted {outputQuantity} x {itemName}");
            
            // Update displays
            if (_isVisible)
            {
                UpdateRecipeList();
                if (_selectedRecipe != null)
                {
                    UpdateRecipeDetails();
                }
            }
            
            // Update inventory UI if available
            _inventoryUI?.UpdateInventoryDisplay();
        }

        /// <summary>
        /// Handles crafting failures
        /// </summary>
        private void OnCraftingFailed(string recipeId, string reason)
        {
            GD.PrintErr($"Crafting failed for recipe {recipeId}: {reason}");
            
            // Update displays in case something changed
            if (_isVisible)
            {
                UpdateRecipeList();
                if (_selectedRecipe != null)
                {
                    UpdateRecipeDetails();
                }
            }
        }

        /// <summary>
        /// Handles the craft button press
        /// </summary>
        private void _on_craft_button_pressed()
        {
            if (_selectedRecipe != null && _craftingSystem != null)
            {
                _craftingSystem.CraftItem(_selectedRecipe);
            }
        }

        /// <summary>
        /// Handles the close button press
        /// </summary>
        private void _on_close_button_pressed()
        {
            HideCrafting();
        }

        /// <summary>
        /// Handles input events for keyboard shortcuts
        /// </summary>
        public override void _Input(InputEvent @event)
        {
            if (@event.IsActionPressed("open_crafting"))
            {
                ToggleCrafting();
                GetViewport().SetInputAsHandled();
            }
        }

        /// <summary>
        /// Cleanup when the node is removed
        /// </summary>
        public override void _ExitTree()
        {
            if (_craftingSystem != null)
            {
                _craftingSystem.ItemCrafted -= OnItemCrafted;
                _craftingSystem.CraftingFailed -= OnCraftingFailed;
            }
            
            if (_inventory != null)
            {
                _inventory.InventoryChanged -= OnInventoryChanged;
            }
            
            // Disconnect from EventBus events
            if (EventBus.Instance != null)
            {
                EventBus.Instance.ItemCrafted -= OnEventBusItemCrafted;
                EventBus.Instance.CraftingFailed -= OnEventBusCraftingFailed;
                EventBus.Instance.ItemAdded -= OnEventBusItemAdded;
                EventBus.Instance.ItemRemoved -= OnEventBusItemRemoved;
            }
        }

        #region EventBus Event Handlers

        /// <summary>
        /// Handles EventBus item crafted events
        /// </summary>
        private void OnEventBusItemCrafted(string recipeId, string outputItemId, int outputQuantity)
        {
            var outputItem = ItemDatabase.Instance?.GetItem(outputItemId);
            string itemName = outputItem?.Name ?? outputItemId;
            
            GD.Print($"Successfully crafted {outputQuantity} x {itemName}");
            
            // Update displays
            if (_isVisible)
            {
                UpdateRecipeList();
                if (_selectedRecipe != null)
                {
                    UpdateRecipeDetails();
                }
            }
            
            // Update inventory UI if available
            _inventoryUI?.UpdateInventoryDisplay();
        }

        /// <summary>
        /// Handles EventBus crafting failed events
        /// </summary>
        private void OnEventBusCraftingFailed(string recipeId, string reason)
        {
            GD.PrintErr($"Crafting failed for recipe {recipeId}: {reason}");
            
            // Update displays in case something changed
            if (_isVisible)
            {
                UpdateRecipeList();
                if (_selectedRecipe != null)
                {
                    UpdateRecipeDetails();
                }
            }
        }

        /// <summary>
        /// Handles EventBus item added events
        /// </summary>
        private void OnEventBusItemAdded(string itemId, int quantity)
        {
            if (_isVisible)
            {
                // Update recipe list colors
                UpdateRecipeList();
                
                // Update selected recipe details if any
                if (_selectedRecipe != null)
                {
                    UpdateRecipeDetails();
                }
            }
        }

        /// <summary>
        /// Handles EventBus item removed events
        /// </summary>
        private void OnEventBusItemRemoved(string itemId, int quantity)
        {
            if (_isVisible)
            {
                // Update recipe list colors
                UpdateRecipeList();
                
                // Update selected recipe details if any
                if (_selectedRecipe != null)
                {
                    UpdateRecipeDetails();
                }
            }
        }

        #endregion
    }
}