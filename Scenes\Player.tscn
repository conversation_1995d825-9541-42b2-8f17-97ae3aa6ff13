[gd_scene load_steps=5 format=3 uid="uid://elw7ow57ftup"]

[ext_resource type="Script" uid="uid://dyclkls0o67hd" path="res://Scripts/PlayerController.cs" id="1_player"]
[ext_resource type="Texture2D" uid="uid://dtpj5e001mvug" path="res://icon.svg" id="2_icon"]

[sub_resource type="RectangleShape2D" id="RectangleShape2D_player"]
size = Vector2(32, 32)

[sub_resource type="RectangleShape2D" id="RectangleShape2D_interaction"]
size = Vector2(64, 64)

[node name="Player" type="CharacterBody2D"]
script = ExtResource("1_player")

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
shape = SubResource("RectangleShape2D_player")

[node name="Sprite2D" type="Sprite2D" parent="."]
modulate = Color(0, 1, 0, 1)
texture = ExtResource("2_icon")

[node name="Camera2D" type="Camera2D" parent="."]

[node name="InteractionArea" type="Area2D" parent="."]

[node name="InteractionCollision" type="CollisionShape2D" parent="InteractionArea"]
shape = SubResource("RectangleShape2D_interaction")
