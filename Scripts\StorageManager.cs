using Godot;
using System;
using System.Collections.Generic;
using System.Linq;

namespace SurvivalLooterShooter
{
    /// <summary>
    /// Manages all storage containers in the game world
    /// </summary>
    public partial class StorageManager : Node
    {
        private static StorageManager _instance;
        public static StorageManager Instance => _instance;

        // All storage containers in the world
        private readonly Dictionary<string, StorageContainer> _containers = new();
        
        // Container templates for different structure types
        private readonly Dictionary<string, ContainerTemplate> _containerTemplates = new();
        
        // Shared storage networks (for connected containers)
        private readonly Dictionary<string, List<string>> _storageNetworks = new();

        // Events
        [Signal]
        public delegate void ContainerRegisteredEventHandler(string containerId);
        
        [Signal]
        public delegate void ContainerUnregisteredEventHandler(string containerId);
        
        [Signal]
        public delegate void StorageNetworkCreatedEventHandler(string networkId);

        public override void _Ready()
        {
            if (_instance == null)
            {
                _instance = this;
                AddToGroup("storage_manager");
                
                InitializeContainerTemplates();
                
                Logger.LogInfo("StorageManager", "StorageManager initialized");
            }
            else
            {
                GD.PrintErr("Multiple StorageManager instances detected! Removing duplicate.");
                QueueFree();
            }
        }

        /// <summary>
        /// Initializes container templates based on structure types
        /// </summary>
        private void InitializeContainerTemplates()
        {
            // Basic storage chest
            _containerTemplates["storage_chest"] = new ContainerTemplate
            {
                Name = "Storage Chest",
                BaseCapacity = 50,
                Type = ContainerType.Basic,
                AllowedItemTypes = new HashSet<string>(), // All items allowed
                DefaultSortMode = ContainerSortMode.Type,
                CanBeLocked = true
            };

            // Weapon locker
            _containerTemplates["weapon_locker"] = new ContainerTemplate
            {
                Name = "Weapon Locker",
                BaseCapacity = 20,
                Type = ContainerType.Specialized,
                AllowedItemTypes = new HashSet<string> { "weapon", "ammunition" },
                DefaultSortMode = ContainerSortMode.Name,
                CanBeLocked = true
            };

            // Material storage
            _containerTemplates["material_storage"] = new ContainerTemplate
            {
                Name = "Material Storage",
                BaseCapacity = 100,
                Type = ContainerType.Basic,
                AllowedItemTypes = new HashSet<string> { "material", "resource" },
                DefaultSortMode = ContainerSortMode.Type,
                CanBeLocked = false
            };

            // Secure vault
            _containerTemplates["secure_vault"] = new ContainerTemplate
            {
                Name = "Secure Vault",
                BaseCapacity = 30,
                Type = ContainerType.Secure,
                AllowedItemTypes = new HashSet<string>(), // All items allowed
                DefaultSortMode = ContainerSortMode.Value,
                CanBeLocked = true
            };

            Logger.LogInfo("StorageManager", $"Initialized {_containerTemplates.Count} container templates");
        }

        /// <summary>
        /// Creates a storage container for a structure
        /// </summary>
        public StorageContainer CreateContainerForStructure(Structure structure)
        {
            if (structure?.Blueprint == null)
            {
                Logger.LogError("StorageManager", "Cannot create container: structure or blueprint is null");
                return null;
            }

            if (structure.Blueprint.Type != "storage")
            {
                Logger.LogWarning("StorageManager", $"Structure {structure.StructureId} is not a storage type");
                return null;
            }

            // Get container template
            if (!_containerTemplates.TryGetValue(structure.StructureId, out var template))
            {
                // Use default template
                template = _containerTemplates["storage_chest"];
            }

            // Calculate capacity based on structure level
            int capacity = template.BaseCapacity + (structure.CurrentLevel * 20);

            // Create container
            var container = new StorageContainer();
            structure.AddChild(container);
            
            string containerId = $"{structure.StructureId}_{structure.GridPosition.X}_{structure.GridPosition.Y}";
            container.Initialize(containerId, template.Name, capacity, template.Type);

            // Apply template settings
            if (template.AllowedItemTypes.Count > 0)
            {
                container.SetItemFilters(template.AllowedItemTypes.ToArray());
            }
            
            container.SetAutoSort(true, template.DefaultSortMode);

            // Register container
            RegisterContainer(container);

            Logger.LogInfo("StorageManager", $"Created container {containerId} for structure {structure.StructureId}");
            return container;
        }

        /// <summary>
        /// Registers a storage container with the manager
        /// </summary>
        public void RegisterContainer(StorageContainer container)
        {
            if (container == null || string.IsNullOrEmpty(container.ContainerId))
            {
                Logger.LogError("StorageManager", "Cannot register null container or container without ID");
                return;
            }

            _containers[container.ContainerId] = container;
            EmitSignal(SignalName.ContainerRegistered, container.ContainerId);
            
            Logger.LogInfo("StorageManager", $"Registered container: {container.ContainerId}");
        }

        /// <summary>
        /// Unregisters a storage container
        /// </summary>
        public void UnregisterContainer(string containerId)
        {
            if (_containers.Remove(containerId))
            {
                // Remove from any storage networks
                foreach (var network in _storageNetworks.Values)
                {
                    network.Remove(containerId);
                }

                EmitSignal(SignalName.ContainerUnregistered, containerId);
                Logger.LogInfo("StorageManager", $"Unregistered container: {containerId}");
            }
        }

        /// <summary>
        /// Gets a container by ID
        /// </summary>
        public StorageContainer GetContainer(string containerId)
        {
            return _containers.TryGetValue(containerId, out var container) ? container : null;
        }

        /// <summary>
        /// Gets all containers within a radius of a position
        /// </summary>
        public List<StorageContainer> GetContainersInRadius(Vector2 position, float radius)
        {
            var containers = new List<StorageContainer>();
            
            foreach (var container in _containers.Values)
            {
                // Get position from parent structure if available
                Vector2 containerPosition = Vector2.Zero;
                if (container.GetParent() is Structure structure)
                {
                    containerPosition = structure.Position;
                }
                
                if (containerPosition.DistanceTo(position) <= radius)
                {
                    containers.Add(container);
                }
            }
            
            return containers;
        }

        /// <summary>
        /// Gets all containers of a specific type
        /// </summary>
        public List<StorageContainer> GetContainersByType(ContainerType type)
        {
            return _containers.Values.Where(c => c.Type == type).ToList();
        }

        /// <summary>
        /// Creates a storage network connecting multiple containers
        /// </summary>
        public void CreateStorageNetwork(string networkId, params string[] containerIds)
        {
            var validContainers = containerIds.Where(id => _containers.ContainsKey(id)).ToList();
            
            if (validContainers.Count < 2)
            {
                Logger.LogWarning("StorageManager", "Storage network requires at least 2 valid containers");
                return;
            }

            _storageNetworks[networkId] = validContainers;
            EmitSignal(SignalName.StorageNetworkCreated, networkId);
            
            Logger.LogInfo("StorageManager", $"Created storage network {networkId} with {validContainers.Count} containers");
        }

        /// <summary>
        /// Gets all containers in a storage network
        /// </summary>
        public List<StorageContainer> GetNetworkContainers(string networkId)
        {
            if (!_storageNetworks.TryGetValue(networkId, out var containerIds))
                return new List<StorageContainer>();

            return containerIds.Select(id => _containers.TryGetValue(id, out var container) ? container : null)
                              .Where(c => c != null)
                              .ToList();
        }

        /// <summary>
        /// Finds the best container to store an item based on filters and capacity
        /// </summary>
        public StorageContainer FindBestContainerForItem(string itemId, Vector2 nearPosition, float maxDistance = 100f)
        {
            var item = ItemDatabase.Instance?.GetItem(itemId);
            if (item == null) return null;

            var nearbyContainers = GetContainersInRadius(nearPosition, maxDistance)
                .Where(c => !c.IsLocked && !c.IsFull)
                .ToList();

            // Prefer specialized containers that accept this item type
            var specializedContainers = nearbyContainers
                .Where(c => c.Type == ContainerType.Specialized)
                .ToList();

            foreach (var container in specializedContainers)
            {
                // Check if container accepts this item type (simplified check)
                if (container.TryAddItem(itemId, 1)) // Test add
                {
                    container.TryRemoveItem(itemId, 1); // Remove test item
                    return container;
                }
            }

            // Fall back to any available container with space
            return nearbyContainers
                .OrderBy(c => {
                    Vector2 containerPosition = Vector2.Zero;
                    if (c.GetParent() is Structure structure)
                        containerPosition = structure.Position;
                    return containerPosition.DistanceTo(nearPosition);
                })
                .FirstOrDefault();
        }

        /// <summary>
        /// Auto-organizes items across multiple containers
        /// </summary>
        public void AutoOrganizeContainers(List<string> containerIds)
        {
            var containers = containerIds.Select(GetContainer).Where(c => c != null).ToList();
            if (containers.Count == 0) return;

            // Collect all items from containers
            var allItems = new List<(InventorySlot slot, StorageContainer source)>();
            
            foreach (var container in containers)
            {
                foreach (var slot in container.Items.Values)
                {
                    allItems.Add((slot, container));
                }
                
                // Clear containers temporarily
                foreach (var itemId in container.Items.Keys.ToList())
                {
                    container.TryRemoveItem(itemId, container.GetItemQuantity(itemId));
                }
            }

            // Redistribute items optimally
            foreach (var (slot, _) in allItems)
            {
                Vector2 firstContainerPosition = Vector2.Zero;
        if (containers[0].GetParent() is Structure firstStructure)
            firstContainerPosition = firstStructure.Position;
        var bestContainer = FindBestContainerForItem(slot.ItemId, firstContainerPosition, float.MaxValue);
                bestContainer?.TryAddItem(slot.ItemId, slot.Quantity, slot.Metadata);
            }

            Logger.LogInfo("StorageManager", $"Auto-organized {allItems.Count} items across {containers.Count} containers");
        }

        /// <summary>
        /// Gets storage statistics for all containers
        /// </summary>
        public StorageSystemStats GetSystemStats()
        {
            var stats = new StorageSystemStats
            {
                TotalContainers = _containers.Count,
                TotalCapacity = _containers.Values.Sum(c => c.MaxCapacity),
                TotalUsedCapacity = _containers.Values.Sum(c => c.CurrentCapacity),
                LockedContainers = _containers.Values.Count(c => c.IsLocked),
                StorageNetworks = _storageNetworks.Count
            };

            stats.UtilizationPercentage = stats.TotalCapacity > 0 
                ? (float)stats.TotalUsedCapacity / stats.TotalCapacity * 100f 
                : 0f;

            return stats;
        }

        /// <summary>
        /// Searches for items across all accessible containers
        /// </summary>
        public List<ItemLocation> SearchForItem(string itemId, string playerId, Vector2 searchCenter, float maxDistance = float.MaxValue)
        {
            var locations = new List<ItemLocation>();

            foreach (var container in _containers.Values)
            {
                Vector2 containerPosition = Vector2.Zero;
                if (container.GetParent() is Structure structure)
                    containerPosition = structure.Position;
                    
                if (containerPosition.DistanceTo(searchCenter) > maxDistance)
                    continue;

                if (container.IsLocked && !container.HasPermission(playerId, ContainerPermission.View))
                    continue;

                int quantity = container.GetItemQuantity(itemId);
                if (quantity > 0)
                {
                    locations.Add(new ItemLocation
                    {
                        ContainerId = container.ContainerId,
                        ContainerName = container.ContainerName,
                        Quantity = quantity,
                        Distance = containerPosition.DistanceTo(searchCenter)
                    });
                }
            }

            return locations.OrderBy(l => l.Distance).ToList();
        }
    }

    public class ContainerTemplate
    {
        public string Name { get; set; }
        public int BaseCapacity { get; set; }
        public ContainerType Type { get; set; }
        public HashSet<string> AllowedItemTypes { get; set; } = new();
        public ContainerSortMode DefaultSortMode { get; set; }
        public bool CanBeLocked { get; set; }
    }

    public class StorageSystemStats
    {
        public int TotalContainers { get; set; }
        public int TotalCapacity { get; set; }
        public int TotalUsedCapacity { get; set; }
        public float UtilizationPercentage { get; set; }
        public int LockedContainers { get; set; }
        public int StorageNetworks { get; set; }
    }

    public class ItemLocation
    {
        public string ContainerId { get; set; }
        public string ContainerName { get; set; }
        public int Quantity { get; set; }
        public float Distance { get; set; }
    }
}