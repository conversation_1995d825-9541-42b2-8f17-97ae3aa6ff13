using Godot;

namespace SurvivalLooterShooter
{
    public partial class PolishTestRunner : Control
{
    private Button _testAchievements;
    private Button _testAudio;
    private Button _testTutorial;
    private Button _testSettings;
    private Button _testStats;
    private Button _testProfiler;

    public override void _Ready()
    {
        GetNodes();
        ConnectSignals();
        
        // Initialize polish systems
        GamePolishManager.Instance?.InitializeGameSystems();
        
        GD.Print("Polish Test Scene loaded - all systems ready for testing");
    }

    private void GetNodes()
    {
        _testAchievements = GetNode<Button>("UI/TestButtons/TestAchievements");
        _testAudio = GetNode<Button>("UI/TestButtons/TestAudio");
        _testTutorial = GetNode<Button>("UI/TestButtons/TestTutorial");
        _testSettings = GetNode<Button>("UI/TestButtons/TestSettings");
        _testStats = GetNode<Button>("UI/TestButtons/TestStats");
        _testProfiler = GetNode<Button>("UI/TestButtons/TestProfiler");
    }

    private void ConnectSignals()
    {
        _testAchievements.Pressed += TestAchievementSystem;
        _testAudio.Pressed += TestAudioSystem;
        _testTutorial.Pressed += TestTutorialSystem;
        _testSettings.Pressed += TestSettingsSystem;
        _testStats.Pressed += TestStatisticsSystem;
        _testProfiler.Pressed += TestProfilerSystem;
    }

    private void TestAchievementSystem()
    {
        GD.Print("Testing Achievement System...");
        
        // Simulate some achievements
        EventBus.Instance?.EmitEnemyKilled("test_enemy", false);
        EventBus.Instance?.EmitItemCrafted("test_recipe", "bandage", 1);
        EventBus.Instance?.EmitPlayerDied();
        EventBus.Instance?.EmitItemPickedUp("test_item");
        
        // Show achievements menu
        GamePolishManager.Instance?.ShowAchievements();
        
        AudioManager.Instance?.PlaySFX("ui_click");
    }

    private void TestAudioSystem()
    {
        GD.Print("Testing Audio System...");
        
        // Test different audio types
        AudioManager.Instance?.PlaySFX("test_sound");
        AudioManager.Instance?.PlayMusic("test_music");
        AudioManager.Instance?.PlayAmbient("test_ambient");
        
        // Test UI sounds
        AudioManager.Instance?.PlayUISound("ui_click");
        AudioManager.Instance?.PlayWeaponSound("weapon_fire");
        AudioManager.Instance?.PlayFootstepSound("footstep");
        AudioManager.Instance?.PlayImpactSound("impact");
    }

    private void TestTutorialSystem()
    {
        GD.Print("Testing Tutorial System...");
        
        if (TutorialSystem.Instance != null)
        {
            if (TutorialSystem.Instance.IsTutorialActive())
            {
                GD.Print("Tutorial is already active");
            }
            else
            {
                TutorialSystem.Instance.StartTutorial();
            }
        }
    }

    private void TestSettingsSystem()
    {
        GD.Print("Testing Settings System...");
        GamePolishManager.Instance?.ShowSettings();
    }

    private void TestStatisticsSystem()
    {
        GD.Print("Testing Statistics System...");
        
        // Add some test statistics
        PlayerStatistics.Instance?.IncrementStat("enemies_killed", 5);
        PlayerStatistics.Instance?.IncrementStat("items_crafted", 10);
        PlayerStatistics.Instance?.IncrementStat("distance_traveled", 1000);
        PlayerStatistics.Instance?.SetStat("max_skill_level", 5);
        
        GamePolishManager.Instance?.ShowStatistics();
    }

    private void TestProfilerSystem()
    {
        GD.Print("Testing Performance Profiler...");
        
        // Test some performance profiling
        PerformanceProfiler.Instance?.StartProfile("TestOperation");
        
        // Simulate some work
        for (int i = 0; i < 1000; i++)
        {
            var dummy = Mathf.Sin(i) * Mathf.Cos(i);
        }
        
        PerformanceProfiler.Instance?.EndProfile("TestOperation");
        PerformanceProfiler.Instance?.ToggleDebugInfo();
    }

    public override void _Input(InputEvent @event)
    {
        if (@event is InputEventKey keyEvent && keyEvent.Pressed)
        {
            switch (keyEvent.Keycode)
            {
                case Key.Key1:
                    TestAchievementSystem();
                    break;
                case Key.Key2:
                    TestAudioSystem();
                    break;
                case Key.Key3:
                    TestTutorialSystem();
                    break;
                case Key.Key4:
                    TestSettingsSystem();
                    break;
                case Key.Key5:
                    TestStatisticsSystem();
                    break;
                case Key.Key6:
                    TestProfilerSystem();
                    break;
            }
        }
    }
}}
