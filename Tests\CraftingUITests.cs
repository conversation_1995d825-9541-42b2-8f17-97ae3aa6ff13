using Godot;
using System.Collections.Generic;

namespace SurvivalLooterShooter
{
	/// <summary>
	/// Integration tests for the CraftingUI class
	/// </summary>
	public partial class CraftingUITests : Node
	{
		private CraftingUI _craftingUI;
		private CraftingSystem _craftingSystem;
		private Inventory _inventory;
		private InventoryUI _inventoryUI;
		private PackedScene _craftingUIScene;
		private PackedScene _inventoryUIScene;

		public override void _Ready()
		{
			GD.Print("Starting CraftingUI integration tests...");
			
			// Initialize test environment
			SetupTestEnvironment();
			
			// Run all tests
			RunAllTests();
		}

		private void SetupTestEnvironment()
		{
			// Load UI scenes
			_craftingUIScene = GD.Load<PackedScene>("res://Scenes/CraftingUI.tscn");
			_inventoryUIScene = GD.Load<PackedScene>("res://Scenes/InventoryUI.tscn");
			
			// Create test instances
			_inventory = new Inventory();
			_craftingSystem = new CraftingSystem();
			_craftingSystem.SetInventory(_inventory);
			
			// Create UI instances
			_inventoryUI = _inventoryUIScene.Instantiate<InventoryUI>();
			_craftingUI = _craftingUIScene.Instantiate<CraftingUI>();
			
			// Add nodes to scene tree
			AddChild(_inventory);
			AddChild(_craftingSystem);
			AddChild(_inventoryUI);
			AddChild(_craftingUI);
			
			// Initialize UIs
			_inventoryUI.Initialize(_inventory);
			_craftingUI.Initialize(_craftingSystem, _inventory, _inventoryUI);
		}

		private void RunAllTests()
		{
			TestCraftingUIInitialization();
			TestShowHideCraftingUI();
			TestCraftingUIWithMaterials();
			TestCraftingUIWithoutMaterials();
			TestCraftingUIIntegration();
			
			GD.Print("All CraftingUI integration tests completed!");
		}

		private void TestCraftingUIInitialization()
		{
			GD.Print("Testing CraftingUI initialization...");
			
			if (_craftingUI != null && !_craftingUI.Visible)
			{
				GD.Print("✓ CraftingUI initialized correctly and is initially hidden");
			}
			else
			{
				GD.PrintErr("✗ CraftingUI initialization failed");
			}
		}

		private void TestShowHideCraftingUI()
		{
			GD.Print("Testing CraftingUI show/hide functionality...");
			
			// Test showing
			_craftingUI.ShowCrafting();
			if (_craftingUI.Visible)
			{
				GD.Print("✓ CraftingUI shows correctly");
			}
			else
			{
				GD.PrintErr("✗ CraftingUI failed to show");
			}
			
			// Test hiding
			_craftingUI.HideCrafting();
			if (!_craftingUI.Visible)
			{
				GD.Print("✓ CraftingUI hides correctly");
			}
			else
			{
				GD.PrintErr("✗ CraftingUI failed to hide");
			}
			
			// Test toggling
			_craftingUI.ToggleCrafting();
			bool visibleAfterToggle1 = _craftingUI.Visible;
			_craftingUI.ToggleCrafting();
			bool visibleAfterToggle2 = _craftingUI.Visible;
			
			if (visibleAfterToggle1 && !visibleAfterToggle2)
			{
				GD.Print("✓ CraftingUI toggles correctly");
			}
			else
			{
				GD.PrintErr($"✗ CraftingUI toggle failed: {visibleAfterToggle1} -> {visibleAfterToggle2}");
			}
		}

		private void TestCraftingUIWithMaterials()
		{
			GD.Print("Testing CraftingUI with sufficient materials...");
			
			// Add materials for crafting
			_inventory.AddItem("cloth", 10);
			_inventory.AddItem("alcohol", 5);
			
			// Show crafting UI
			_craftingUI.ShowCrafting();
			
			// Wait a frame for UI to update
			CallDeferred(nameof(CheckCraftingUIWithMaterials));
		}

		private void CheckCraftingUIWithMaterials()
		{
			GD.Print("✓ CraftingUI displayed with materials (visual verification required)");
			
			// Clean up
			_inventory.Clear();
			_craftingUI.HideCrafting();
			
			// Continue with next test
			CallDeferred(nameof(TestCraftingUIWithoutMaterials));
		}

		private void TestCraftingUIWithoutMaterials()
		{
			GD.Print("Testing CraftingUI without materials...");
			
			// Ensure inventory is empty
			_inventory.Clear();
			
			// Show crafting UI
			_craftingUI.ShowCrafting();
			
			// Wait a frame for UI to update
			CallDeferred(nameof(CheckCraftingUIWithoutMaterials));
		}

		private void CheckCraftingUIWithoutMaterials()
		{
			GD.Print("✓ CraftingUI displayed without materials (visual verification required)");
			
			// Clean up
			_craftingUI.HideCrafting();
			
			// Continue with next test
			CallDeferred(nameof(TestCraftingUIIntegration));
		}

		private void TestCraftingUIIntegration()
		{
			GD.Print("Testing CraftingUI integration with inventory...");
			
			// Add materials for crafting
			_inventory.AddItem("cloth", 4);
			_inventory.AddItem("alcohol", 2);
			
			int clothBefore = _inventory.GetItemQuantity("cloth");
			int alcoholBefore = _inventory.GetItemQuantity("alcohol");
			int bandageBefore = _inventory.GetItemQuantity("bandage");
			
			// Try to craft using the crafting system directly (simulating UI interaction)
			var recipes = _craftingSystem.GetAvailableRecipes();
			if (recipes.Count > 0)
			{
				var recipe = recipes[0]; // Use first available recipe
				bool craftSuccess = _craftingSystem.CraftItem(recipe);
				
				if (craftSuccess)
				{
					int clothAfter = _inventory.GetItemQuantity("cloth");
					int alcoholAfter = _inventory.GetItemQuantity("alcohol");
					int bandageAfter = _inventory.GetItemQuantity("bandage");
					
					GD.Print($"✓ Crafting integration successful: Cloth {clothBefore}->{clothAfter}, Alcohol {alcoholBefore}->{alcoholAfter}, Bandage {bandageBefore}->{bandageAfter}");
				}
				else
				{
					GD.PrintErr("✗ Crafting integration failed - crafting was unsuccessful");
				}
			}
			else
			{
				GD.Print("⚠ No recipes available for crafting integration test");
			}
			
			// Clean up
			_inventory.Clear();
		}
	}
}
