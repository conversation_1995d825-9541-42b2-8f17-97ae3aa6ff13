using Godot;
using System;
using System.Collections.Generic;
using System.Linq;

namespace SurvivalLooterShooter
{
    /// <summary>
    /// AI Controller with state machine for enemy behavior
    /// Handles patrol, chase, attack, flee states with different AI personalities
    /// </summary>
    public partial class AIController : Node2D
    {
        // AI States
        public enum AIState
        {
            Patrol,
            Chase,
            Attack,
            Flee,
            Idle,
            Dead
        }

        // AI Personalities
        public enum AIPersonality
        {
            Aggressive,    // Always attacks when player detected
            Defensive,     // Only attacks when attacked first
            Territorial,   // Attacks when player enters territory
            Pack,          // Coordinates with nearby allies
            Ambush         // Waits for player to get close before attacking
        }

        // State and configuration
        [Export] public AIState CurrentState { get; private set; } = AIState.Idle;
        [Export] public AIPersonality Personality { get; set; } = AIPersonality.Aggressive;
        [Export] public float StateChangeDelay { get; set; } = 0.5f;
        
        // Detection settings
        [Export] public float DetectionRange { get; set; } = 200f;
        [Export] public float LoseTargetRange { get; set; } = 300f;
        [Export] public float AttackRange { get; set; } = 50f;
        [Export] public float FleeRange { get; set; } = 100f;
        [Export] public float LineOfSightRange { get; set; } = 250f;
        
        // Patrol settings
        [Export] public float PatrolRadius { get; set; } = 150f;
        [Export] public float PatrolWaitTime { get; set; } = 2f;
        [Export] public float PatrolSpeed { get; set; } = 50f;
        
        // Territory settings (for territorial AI)
        [Export] public float TerritoryRadius { get; set; } = 200f;
        [Export] public Vector2 TerritoryCenter { get; set; }
        
        // Pack behavior settings
        [Export] public float PackCoordinationRange { get; set; } = 300f;
        [Export] public float PackSeparationDistance { get; set; } = 80f;
        
        // Health thresholds for behavior changes
        [Export] public float FleeHealthThreshold { get; set; } = 0.3f;
        [Export] public float DefensiveHealthThreshold { get; set; } = 0.5f;
        
        // References
        private Enemy _enemy;
        private Node2D _target;
        private NavigationAgent2D _navigationAgent;
        private Timer _stateTimer;
        private Timer _attackTimer;
        private Timer _patrolTimer;
        
        // State tracking
        private Vector2 _lastKnownTargetPosition;
        private Vector2 _patrolTarget;
        private Vector2 _spawnPosition;
        private float _lastStateChangeTime;
        private bool _hasBeenAttacked = false;
        private List<AIController> _packMembers = new List<AIController>();
        
        // Pathfinding
        private List<Vector2> _currentPath = new List<Vector2>();
        private int _currentPathIndex = 0;
        private float _pathUpdateInterval = 0.5f;
        private float _lastPathUpdate = 0f;
        
        // Events
        [Signal] public delegate void StateChangedEventHandler(AIState oldState, AIState newState);
        [Signal] public delegate void TargetDetectedEventHandler(Node2D target);
        [Signal] public delegate void TargetLostEventHandler();
        [Signal] public delegate void AttackInitiatedEventHandler(Node2D target);

        public override void _Ready()
        {
            SetupAIController();
        }

        /// <summary>
        /// Initializes the AI controller with necessary components
        /// </summary>
        private void SetupAIController()
        {
            // Get enemy reference
            _enemy = GetParent<Enemy>();
            if (_enemy == null)
            {
                Logger.LogError("AIController", "AIController must be child of Enemy node");
                return;
            }

            // Set up navigation agent
            _navigationAgent = new NavigationAgent2D();
            _navigationAgent.Name = "NavigationAgent2D";
            _navigationAgent.PathDesiredDistance = 10f;
            _navigationAgent.TargetDesiredDistance = 20f;
            _navigationAgent.PathMaxDistance = 500f;
            AddChild(_navigationAgent);

            // Set up timers
            _stateTimer = new Timer();
            _stateTimer.Name = "StateTimer";
            _stateTimer.OneShot = true;
            _stateTimer.Timeout += OnStateTimerTimeout;
            AddChild(_stateTimer);

            _attackTimer = new Timer();
            _attackTimer.Name = "AttackTimer";
            _attackTimer.WaitTime = 1.0f;
            _attackTimer.OneShot = true;
            _attackTimer.Timeout += OnAttackTimerTimeout;
            AddChild(_attackTimer);

            _patrolTimer = new Timer();
            _patrolTimer.Name = "PatrolTimer";
            _patrolTimer.WaitTime = PatrolWaitTime;
            _patrolTimer.OneShot = true;
            _patrolTimer.Timeout += OnPatrolTimerTimeout;
            AddChild(_patrolTimer);

            // Initialize state
            _spawnPosition = GlobalPosition;
            TerritoryCenter = _spawnPosition;
            _patrolTarget = _spawnPosition;
            
            // Connect to enemy events
            if (_enemy != null)
            {
                _enemy.EnemyTookDamage += OnEnemyTookDamage;
                _enemy.EnemyDied += OnEnemyDied;
            }

            // Start with appropriate initial state
            ChangeState(AIState.Patrol);
            
            Logger.LogInfo("AIController", $"AI Controller initialized for {_enemy?.EnemyName} with {Personality} personality");
        }

        private void OnEnemyDied()
        {
            throw new NotImplementedException();
        }

        private void OnEnemyTookDamage(float damage)
        {
            throw new NotImplementedException();
        }

        public override void _PhysicsProcess(double delta)
        {
            if (_enemy == null || _enemy.IsDead) return;

            UpdateAI((float)delta);
        }

        /// <summary>
        /// Main AI update loop
        /// </summary>
        private void UpdateAI(float delta)
        {
            // Update pack coordination
            UpdatePackBehavior();
            
            // Update pathfinding
            UpdatePathfinding(delta);
            
            // Update current state
            switch (CurrentState)
            {
                case AIState.Idle:
                    UpdateIdleState(delta);
                    break;
                case AIState.Patrol:
                    UpdatePatrolState(delta);
                    break;
                case AIState.Chase:
                    UpdateChaseState(delta);
                    break;
                case AIState.Attack:
                    UpdateAttackState(delta);
                    break;
                case AIState.Flee:
                    UpdateFleeState(delta);
                    break;
            }
            
            // Check for state transitions
            CheckStateTransitions();
        }

        #region State Updates

        /// <summary>
        /// Updates idle state behavior
        /// </summary>
        private void UpdateIdleState(float delta)
        {
            _enemy.StopMovement();
            
            // Look for targets
            var detectedTarget = DetectTarget();
            if (detectedTarget != null)
            {
                SetTarget(detectedTarget);
                
                // Determine next state based on personality
                switch (Personality)
                {
                    case AIPersonality.Aggressive:
                        ChangeState(AIState.Chase);
                        break;
                    case AIPersonality.Defensive:
                        if (_hasBeenAttacked)
                            ChangeState(AIState.Chase);
                        break;
                    case AIPersonality.Territorial:
                        if (IsTargetInTerritory(detectedTarget))
                            ChangeState(AIState.Chase);
                        break;
                    case AIPersonality.Pack:
                        if (ShouldPackAttack())
                            ChangeState(AIState.Chase);
                        break;
                    case AIPersonality.Ambush:
                        if (GetDistanceToTarget() <= AttackRange)
                            ChangeState(AIState.Attack);
                        break;
                }
            }
            else
            {
                // No target, start patrolling
                ChangeState(AIState.Patrol);
            }
        }

        /// <summary>
        /// Updates patrol state behavior
        /// </summary>
        private void UpdatePatrolState(float delta)
        {
            // Look for targets while patrolling
            var detectedTarget = DetectTarget();
            if (detectedTarget != null && ShouldEngageTarget(detectedTarget))
            {
                SetTarget(detectedTarget);
                ChangeState(AIState.Chase);
                return;
            }

            // Move towards patrol target
            if (GlobalPosition.DistanceTo(_patrolTarget) > 30f)
            {
                MoveTowardsTarget(_patrolTarget, PatrolSpeed, delta);
            }
            else
            {
                // Reached patrol point, wait and pick new target
                if (!_patrolTimer.IsStopped())
                {
                    _enemy.StopMovement();
                }
                else
                {
                    SetNewPatrolTarget();
                }
            }
        }

        /// <summary>
        /// Updates chase state behavior
        /// </summary>
        private void UpdateChaseState(float delta)
        {
            if (_target == null)
            {
                ChangeState(AIState.Patrol);
                return;
            }

            float distanceToTarget = GetDistanceToTarget();
            
            // Check if target is too far away
            if (distanceToTarget > LoseTargetRange)
            {
                LoseTarget();
                ChangeState(AIState.Patrol);
                return;
            }

            // Check if we should flee
            if (ShouldFlee())
            {
                ChangeState(AIState.Flee);
                return;
            }

            // Check if in attack range
            if (distanceToTarget <= AttackRange)
            {
                ChangeState(AIState.Attack);
                return;
            }

            // Move towards target
            _lastKnownTargetPosition = _target.GlobalPosition;
            MoveTowardsTarget(_target.GlobalPosition, _enemy.Speed, delta);
        }

        /// <summary>
        /// Updates attack state behavior
        /// </summary>
        private void UpdateAttackState(float delta)
        {
            if (_target == null)
            {
                ChangeState(AIState.Patrol);
                return;
            }

            float distanceToTarget = GetDistanceToTarget();
            
            // Check if target moved out of attack range
            if (distanceToTarget > AttackRange * 1.2f) // Add some hysteresis
            {
                ChangeState(AIState.Chase);
                return;
            }

            // Check if we should flee
            if (ShouldFlee())
            {
                ChangeState(AIState.Flee);
                return;
            }

            // Stop movement and attack
            _enemy.StopMovement();
            
            // Attack if timer allows
            if (_attackTimer.IsStopped())
            {
                PerformAttack();
                _attackTimer.Start();
            }
        }

        /// <summary>
        /// Updates flee state behavior
        /// </summary>
        private void UpdateFleeState(float delta)
        {
            if (_target == null)
            {
                ChangeState(AIState.Patrol);
                return;
            }

            float distanceToTarget = GetDistanceToTarget();
            
            // Check if we've fled far enough
            if (distanceToTarget > FleeRange * 2f)
            {
                // Decide next state based on personality and health
                if (GetHealthPercentage() > DefensiveHealthThreshold)
                {
                    ChangeState(AIState.Chase);
                }
                else
                {
                    ChangeState(AIState.Patrol);
                }
                return;
            }

            // Move away from target
            Vector2 fleeDirection = (GlobalPosition - _target.GlobalPosition).Normalized();
            Vector2 fleeTarget = GlobalPosition + fleeDirection * 200f;
            MoveTowardsTarget(fleeTarget, _enemy.Speed * 1.2f, delta);
        }

        #endregion

        #region State Transitions

        /// <summary>
        /// Checks for automatic state transitions
        /// </summary>
        private void CheckStateTransitions()
        {
            // Prevent rapid state changes
            if (Time.GetUnixTimeFromSystem() - _lastStateChangeTime < StateChangeDelay)
                return;

            // Check for flee conditions
            if (ShouldFlee() && CurrentState != AIState.Flee)
            {
                ChangeState(AIState.Flee);
                return;
            }

            // Check for line of sight loss during chase
            if (CurrentState == AIState.Chase && _target != null)
            {
                if (!HasLineOfSight(_target))
                {
                    // Move to last known position
                    if (GlobalPosition.DistanceTo(_lastKnownTargetPosition) > 50f)
                    {
                        MoveTowardsTarget(_lastKnownTargetPosition, _enemy.Speed, (float)GetPhysicsProcessDeltaTime());
                    }
                    else
                    {
                        // Lost target completely
                        LoseTarget();
                        ChangeState(AIState.Patrol);
                    }
                }
            }
        }

        /// <summary>
        /// Changes the AI state
        /// </summary>
        private void ChangeState(AIState newState)
        {
            if (newState == CurrentState) return;

            AIState oldState = CurrentState;
            CurrentState = newState;
            _lastStateChangeTime = (float)Time.GetUnixTimeFromSystem();

            // State entry actions
            switch (newState)
            {
                case AIState.Patrol:
                    SetNewPatrolTarget();
                    break;
                case AIState.Chase:
                    _navigationAgent.TargetDesiredDistance = 20f;
                    break;
                case AIState.Attack:
                    _navigationAgent.TargetDesiredDistance = AttackRange;
                    break;
                case AIState.Flee:
                    _navigationAgent.TargetDesiredDistance = 10f;
                    break;
            }

            EmitSignal(SignalName.StateChanged, (int)oldState, (int)newState);
            Logger.LogDebug("AIController", $"{_enemy?.EnemyName} changed state from {oldState} to {newState}");
        }

        #endregion

        #region Target Detection and Management

        /// <summary>
        /// Detects potential targets within detection range
        /// </summary>
        private Node2D DetectTarget()
        {
            // Look for player first
            var player = GetTree().GetFirstNodeInGroup("player") as Node2D;
            if (player != null && IsValidTarget(player))
            {
                float distance = GlobalPosition.DistanceTo(player.GlobalPosition);
                if (distance <= DetectionRange && HasLineOfSight(player))
                {
                    return player;
                }
            }

            return null;
        }

        /// <summary>
        /// Checks if a node is a valid target
        /// </summary>
        private bool IsValidTarget(Node2D target)
        {
            if (target == null) return false;
            
            // Check if target is alive (for player)
            if (target is PlayerController player)
            {
                return player.IsAlive;
            }

            return true;
        }

        /// <summary>
        /// Checks if there's line of sight to a target
        /// </summary>
        private bool HasLineOfSight(Node2D target)
        {
            if (target == null) return false;

            var spaceState = GetWorld2D().DirectSpaceState;
            var query = PhysicsRayQueryParameters2D.Create(
                GlobalPosition,
                target.GlobalPosition
            );
            
            // Exclude self and target from raycast
            var excludeArray = new Godot.Collections.Array<Rid>();
            if (_enemy is CollisionObject2D enemyCollision)
                excludeArray.Add(enemyCollision.GetRid());
            if (target is CollisionObject2D targetCollision)
                excludeArray.Add(targetCollision.GetRid());
            query.Exclude = excludeArray;
            query.CollisionMask = 1; // Only check walls/obstacles
            
            var result = spaceState.IntersectRay(query);
            
            // If no collision, we have line of sight
            return result.Count == 0;
        }

        /// <summary>
        /// Sets the current target
        /// </summary>
        private void SetTarget(Node2D target)
        {
            if (_target != target)
            {
                _target = target;
                _lastKnownTargetPosition = target?.GlobalPosition ?? Vector2.Zero;
                EmitSignal(SignalName.TargetDetected, target);
            }
        }

        /// <summary>
        /// Clears the current target
        /// </summary>
        private void LoseTarget()
        {
            if (_target != null)
            {
                _target = null;
                EmitSignal(SignalName.TargetLost);
            }
        }

        /// <summary>
        /// Gets distance to current target
        /// </summary>
        private float GetDistanceToTarget()
        {
            return _target?.GlobalPosition.DistanceTo(GlobalPosition) ?? float.MaxValue;
        }

        #endregion

        #region Movement and Pathfinding

        /// <summary>
        /// Updates pathfinding system
        /// </summary>
        private void UpdatePathfinding(float delta)
        {
            _lastPathUpdate += delta;
            
            if (_lastPathUpdate >= _pathUpdateInterval)
            {
                _lastPathUpdate = 0f;
                
                // Update navigation target based on current state
                Vector2 targetPosition = Vector2.Zero;
                
                switch (CurrentState)
                {
                    case AIState.Chase:
                        if (_target != null)
                            targetPosition = _target.GlobalPosition;
                        break;
                    case AIState.Patrol:
                        targetPosition = _patrolTarget;
                        break;
                    case AIState.Flee:
                        if (_target != null)
                        {
                            Vector2 fleeDirection = (GlobalPosition - _target.GlobalPosition).Normalized();
                            targetPosition = GlobalPosition + fleeDirection * 200f;
                        }
                        break;
                }
                
                if (targetPosition != Vector2.Zero)
                {
                    _navigationAgent.TargetPosition = targetPosition;
                }
            }
        }

        /// <summary>
        /// Moves towards a target position with pathfinding
        /// </summary>
        private void MoveTowardsTarget(Vector2 targetPosition, float speed, float delta)
        {
            if (_navigationAgent.IsNavigationFinished())
            {
                _enemy.StopMovement();
                return;
            }

            Vector2 nextPosition = _navigationAgent.GetNextPathPosition();
            Vector2 direction = (nextPosition - GlobalPosition).Normalized();
            
            // Apply pack separation if needed
            if (Personality == AIPersonality.Pack)
            {
                direction += GetPackSeparationForce();
                direction = direction.Normalized();
            }
            
            _enemy.Velocity = direction * speed;
            _enemy.MoveAndSlide();
        }

        #endregion

        #region Personality-Specific Behavior

        /// <summary>
        /// Determines if the AI should engage a target based on personality
        /// </summary>
        private bool ShouldEngageTarget(Node2D target)
        {
            switch (Personality)
            {
                case AIPersonality.Aggressive:
                    return true;
                    
                case AIPersonality.Defensive:
                    return _hasBeenAttacked;
                    
                case AIPersonality.Territorial:
                    return IsTargetInTerritory(target);
                    
                case AIPersonality.Pack:
                    return ShouldPackAttack();
                    
                case AIPersonality.Ambush:
                    return GlobalPosition.DistanceTo(target.GlobalPosition) <= AttackRange;
                    
                default:
                    return true;
            }
        }

        /// <summary>
        /// Checks if target is within territory (for territorial AI)
        /// </summary>
        private bool IsTargetInTerritory(Node2D target)
        {
            if (target == null) return false;
            return TerritoryCenter.DistanceTo(target.GlobalPosition) <= TerritoryRadius;
        }

        /// <summary>
        /// Determines if pack should attack (for pack AI)
        /// </summary>
        private bool ShouldPackAttack()
        {
            // Attack if any pack member is already attacking
            foreach (var packMember in _packMembers)
            {
                if (packMember != null && !packMember._enemy.IsDead && 
                    (packMember.CurrentState == AIState.Chase || packMember.CurrentState == AIState.Attack))
                {
                    return true;
                }
            }
            
            // Attack if we have numerical advantage
            int alivePackMembers = _packMembers.Count(p => p != null && !p._enemy.IsDead) + 1;
            return alivePackMembers >= 2;
        }

        /// <summary>
        /// Determines if the AI should flee
        /// </summary>
        private bool ShouldFlee()
        {
            float healthPercentage = GetHealthPercentage();
            
            switch (Personality)
            {
                case AIPersonality.Aggressive:
                    return healthPercentage <= FleeHealthThreshold * 0.5f; // Very low flee threshold
                    
                case AIPersonality.Defensive:
                    return healthPercentage <= FleeHealthThreshold;
                    
                case AIPersonality.Territorial:
                    // Only flee if outside territory and low health
                    return healthPercentage <= FleeHealthThreshold && 
                           GlobalPosition.DistanceTo(TerritoryCenter) > TerritoryRadius;
                    
                case AIPersonality.Pack:
                    // Flee if isolated or pack is mostly dead
                    int alivePackMembers = _packMembers.Count(p => p != null && !p._enemy.IsDead);
                    return healthPercentage <= FleeHealthThreshold || alivePackMembers == 0;
                    
                case AIPersonality.Ambush:
                    return healthPercentage <= FleeHealthThreshold;
                    
                default:
                    return healthPercentage <= FleeHealthThreshold;
            }
        }

        /// <summary>
        /// Gets current health as percentage
        /// </summary>
        private float GetHealthPercentage()
        {
            if (_enemy == null) return 0f;
            return _enemy.CurrentHealth / _enemy.MaxHealth;
        }

        #endregion

        #region Pack Behavior

        /// <summary>
        /// Updates pack coordination behavior
        /// </summary>
        private void UpdatePackBehavior()
        {
            if (Personality != AIPersonality.Pack) return;

            // Find nearby pack members
            _packMembers.Clear();
            var enemies = GetTree().GetNodesInGroup("enemies");
            
            foreach (Node enemy in enemies)
            {
                if (enemy is Enemy otherEnemy && otherEnemy != _enemy && !otherEnemy.IsDead)
                {
                    float distance = GlobalPosition.DistanceTo(otherEnemy.GlobalPosition);
                    if (distance <= PackCoordinationRange)
                    {
                        var aiController = otherEnemy.GetNode<AIController>("AIController");
                        if (aiController != null && aiController.Personality == AIPersonality.Pack)
                        {
                            _packMembers.Add(aiController);
                        }
                    }
                }
            }
        }

        /// <summary>
        /// Gets separation force to avoid crowding with pack members
        /// </summary>
        private Vector2 GetPackSeparationForce()
        {
            Vector2 separationForce = Vector2.Zero;
            int nearbyCount = 0;

            foreach (var packMember in _packMembers)
            {
                if (packMember == null || packMember._enemy.IsDead) continue;

                float distance = GlobalPosition.DistanceTo(packMember.GlobalPosition);
                if (distance < PackSeparationDistance && distance > 0)
                {
                    Vector2 awayFromPack = (GlobalPosition - packMember.GlobalPosition).Normalized();
                    separationForce += awayFromPack / distance; // Stronger force when closer
                    nearbyCount++;
                }
            }

            if (nearbyCount > 0)
            {
                separationForce /= nearbyCount;
                separationForce = separationForce.Normalized() * 0.5f; // Moderate influence
            }

            return separationForce;
        }

        #endregion

        #region Combat

        /// <summary>
        /// Performs an attack on the current target
        /// </summary>
        private void PerformAttack()
        {
            if (_target == null || _enemy == null) return;

            _enemy.Attack(_target);
            EmitSignal(SignalName.AttackInitiated, _target);
            
            Logger.LogDebug("AIController", $"{_enemy.EnemyName} attacked {_target.Name}");
        }

        #endregion

        #region Patrol Behavior

        /// <summary>
        /// Sets a new patrol target within patrol radius
        /// </summary>
        private void SetNewPatrolTarget()
        {
            var random = new Random();
            float angle = (float)(random.NextDouble() * Math.PI * 2);
            float distance = (float)(random.NextDouble() * PatrolRadius);
            
            Vector2 offset = new Vector2(
                Mathf.Cos(angle) * distance,
                Mathf.Sin(angle) * distance
            );
            
            _patrolTarget = _spawnPosition + offset;
            _patrolTimer.Start();
        }

        #endregion

        #region Event Handlers

        /// <summary>
        /// Handles enemy taking damage
        /// </summary>
        private void OnEnemyTookDamage(Enemy enemy, float damage, float remainingHealth)
        {
            _hasBeenAttacked = true;
            
            // Defensive AI becomes aggressive when attacked
            if (Personality == AIPersonality.Defensive && CurrentState == AIState.Patrol)
            {
                var attacker = DetectTarget();
                if (attacker != null)
                {
                    SetTarget(attacker);
                    ChangeState(AIState.Chase);
                }
            }
        }

        /// <summary>
        /// Handles enemy death
        /// </summary>
        private void OnEnemyDied(Enemy enemy)
        {
            ChangeState(AIState.Dead);
            SetProcessMode(ProcessModeEnum.Disabled);
        }

        /// <summary>
        /// Handles state timer timeout
        /// </summary>
        private void OnStateTimerTimeout()
        {
            // Used for timed state transitions
        }

        /// <summary>
        /// Handles attack timer timeout
        /// </summary>
        private void OnAttackTimerTimeout()
        {
            // Attack cooldown finished
        }

        /// <summary>
        /// Handles patrol timer timeout
        /// </summary>
        private void OnPatrolTimerTimeout()
        {
            // Patrol wait time finished, can move to next patrol point
        }

        #endregion

        #region Public Interface

        /// <summary>
        /// Initializes AI with specific settings
        /// </summary>
        public void Initialize(AIPersonality personality, float detectionRange, float attackRange)
        {
            Personality = personality;
            DetectionRange = detectionRange;
            AttackRange = attackRange;
            
            // Adjust settings based on personality
            switch (personality)
            {
                case AIPersonality.Aggressive:
                    FleeHealthThreshold = 0.2f;
                    break;
                case AIPersonality.Defensive:
                    FleeHealthThreshold = 0.4f;
                    DetectionRange *= 0.8f;
                    break;
                case AIPersonality.Territorial:
                    TerritoryRadius = detectionRange * 1.5f;
                    break;
                case AIPersonality.Pack:
                    PackCoordinationRange = detectionRange * 2f;
                    break;
                case AIPersonality.Ambush:
                    DetectionRange *= 0.6f;
                    AttackRange *= 1.2f;
                    break;
            }
        }

        /// <summary>
        /// Forces a state change (for external control)
        /// </summary>
        public void ForceStateChange(AIState newState)
        {
            ChangeState(newState);
        }

        /// <summary>
        /// Gets current target
        /// </summary>
        public Node2D GetCurrentTarget()
        {
            return _target;
        }

        /// <summary>
        /// Checks if AI is currently engaged in combat
        /// </summary>
        public bool IsInCombat()
        {
            return CurrentState == AIState.Chase || CurrentState == AIState.Attack;
        }

        /// <summary>
        /// Gets pack members (for pack AI)
        /// </summary>
        public List<AIController> GetPackMembers()
        {
            return new List<AIController>(_packMembers);
        }

        #endregion
    }
}