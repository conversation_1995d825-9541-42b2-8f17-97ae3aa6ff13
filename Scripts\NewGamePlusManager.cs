using Godot;
using System;
using System.Collections.Generic;
using System.Linq;

namespace SurvivalLooterShooter
{
    [System.Serializable]
    public class NewGamePlusData
{
    public int PlusLevel { get; set; } = 0;
    public Dictionary<string, float> RetainedSkills { get; set; } = new();
    public List<string> UnlockedRecipes { get; set; } = new();
    public List<string> UnlockedBlueprints { get; set; } = new();
    public int TotalPlaythroughs { get; set; } = 0;
    public float TotalPlayTime { get; set; } = 0f;
    public Dictionary<string, int> AchievementProgress { get; set; } = new();
    public List<string> PermanentUnlocks { get; set; } = new();
}

public partial class NewGamePlusManager : Node
{
    public static NewGamePlusManager Instance { get; private set; }
    
    public NewGamePlusData NGPlusData { get; set; } = new();
    [Export] public float DifficultyMultiplierPerLevel { get; set; } = 0.25f;
    [Export] public float SkillRetentionPercentage { get; set; } = 0.3f; // 30% of skills retained
    [Export] public int MaxNGPlusLevel { get; set; } = 10;
    
    private const string NGPlusSaveFile = "user://ngplus_data.save";

    public override void _Ready()
    {
        if (Instance == null)
        {
            Instance = this;
            LoadNGPlusData();
        }
        else
        {
            QueueFree();
        }
    }

    public void StartNewGamePlus()
    {
        if (!CanStartNewGamePlus())
        {
            GD.PrintErr("Cannot start New Game Plus - requirements not met");
            return;
        }

        // Prepare NG+ data
        PrepareNewGamePlusData();
        
        // Save current progress
        SaveNGPlusData();
        
        // Reset world and player state
        ResetGameWorld();
        
        // Apply NG+ bonuses and difficulty
        ApplyNewGamePlusModifiers();
        
        GD.Print($"Started New Game Plus Level {NGPlusData.PlusLevel}");
        EventBus.Instance?.EmitSignal(EventBus.SignalName.NewGamePlusStarted, NGPlusData.PlusLevel);
    }

    public bool CanStartNewGamePlus()
    {
        // Check if player has completed the game or reached certain milestones
        var playerLevel = SkillManager.Instance?.GetAverageSkillLevel() ?? 0f;
        var hasDefeatedBosses = PlayerStatistics.Instance?.GetStat("bosses_defeated") ?? 0;
        
        return playerLevel >= 50f || hasDefeatedBosses >= 5;
    }

    private void PrepareNewGamePlusData()
    {
        NGPlusData.PlusLevel++;
        NGPlusData.TotalPlaythroughs++;
        
        // Calculate total playtime
        var currentPlayTime = GameManager.Instance?.GetCurrentPlayTime() ?? 0f;
        NGPlusData.TotalPlayTime += currentPlayTime;
        
        // Retain percentage of skills
        if (SkillManager.Instance != null)
        {
            var currentSkills = SkillManager.Instance.GetAllSkillLevels();
            foreach (var skill in currentSkills)
            {
                var retainedLevel = skill.Value * SkillRetentionPercentage;
                NGPlusData.RetainedSkills[skill.Key] = Math.Max(
                    NGPlusData.RetainedSkills.GetValueOrDefault(skill.Key, 0f),
                    retainedLevel
                );
            }
        }
        
        // Retain unlocked recipes and blueprints
        if (CraftingSystem.Instance != null)
        {
            var unlockedRecipes = CraftingSystem.Instance.GetUnlockedRecipes();
            foreach (var recipe in unlockedRecipes)
            {
                if (!NGPlusData.UnlockedRecipes.Contains(recipe))
                {
                    NGPlusData.UnlockedRecipes.Add(recipe);
                }
            }
        }
        
        if (BuildingManager.Instance != null)
        {
            var unlockedBlueprints = BuildingManager.Instance.GetUnlockedBlueprints();
            foreach (var blueprint in unlockedBlueprints)
            {
                if (!NGPlusData.UnlockedBlueprints.Contains(blueprint))
                {
                    NGPlusData.UnlockedBlueprints.Add(blueprint);
                }
            }
        }
        
        // Add permanent unlocks based on achievements
        AddPermanentUnlocks();
    }

    private void AddPermanentUnlocks()
    {
        var achievements = AchievementSystem.Instance?.GetCompletedAchievements() ?? new List<string>();
        
        foreach (var achievement in achievements)
        {
            var unlock = GetPermanentUnlockForAchievement(achievement);
            if (!string.IsNullOrEmpty(unlock) && !NGPlusData.PermanentUnlocks.Contains(unlock))
            {
                NGPlusData.PermanentUnlocks.Add(unlock);
            }
        }
    }

    private string GetPermanentUnlockForAchievement(string achievement)
    {
        return achievement switch
        {
            "master_crafter" => "advanced_crafting_station",
            "boss_slayer" => "legendary_weapon_blueprints",
            "explorer" => "fast_travel_system",
            "survivor" => "enhanced_health_regen",
            "builder" => "mega_structures",
            "collector" => "expanded_inventory",
            _ => ""
        };
    }

    private void ResetGameWorld()
    {
        // Reset player stats but apply retained bonuses
        if (SurvivalStatsSystem.Instance != null)
        {
            SurvivalStatsSystem.Instance.ResetToDefaults();
            ApplyNGPlusStatBonuses();
        }
        
        // Reset inventory but give starting bonus items
        if (Inventory.Instance != null)
        {
            Inventory.Instance.ClearInventory();
            GiveNGPlusStartingItems();
        }
        
        // Reset world generation with new seed
        if (WorldManager.Instance != null)
        {
            var newSeed = new Random().Next();
            WorldManager.Instance.GenerateNewWorld(newSeed);
        }
        
        // Reset enemy difficulty
        if (EnemyManager.Instance != null)
        {
            EnemyManager.Instance.ResetDifficulty();
        }
    }

    private void ApplyNewGamePlusModifiers()
    {
        var difficultyMultiplier = 1f + (NGPlusData.PlusLevel * DifficultyMultiplierPerLevel);
        
        // Apply difficulty scaling to enemies
        if (EnemyManager.Instance != null)
        {
            EnemyManager.Instance.SetDifficultyMultiplier(difficultyMultiplier);
        }
        
        // Apply retained skills
        if (SkillManager.Instance != null)
        {
            foreach (var skill in NGPlusData.RetainedSkills)
            {
                SkillManager.Instance.SetSkillLevel(skill.Key, skill.Value);
            }
        }
        
        // Unlock retained recipes and blueprints
        if (CraftingSystem.Instance != null)
        {
            foreach (var recipe in NGPlusData.UnlockedRecipes)
            {
                CraftingSystem.Instance.UnlockRecipe(recipe);
            }
        }
        
        if (BuildingManager.Instance != null)
        {
            foreach (var blueprint in NGPlusData.UnlockedBlueprints)
            {
                BuildingManager.Instance.UnlockBlueprint(blueprint);
            }
        }
        
        // Apply permanent unlocks
        ApplyPermanentUnlocks();
        
        GD.Print($"Applied NG+ modifiers - Difficulty: {difficultyMultiplier:F2}x");
    }

    private void ApplyNGPlusStatBonuses()
    {
        // Give small stat bonuses based on NG+ level
        var bonusMultiplier = 1f + (NGPlusData.PlusLevel * 0.1f);
        
        if (SurvivalStatsSystem.Instance != null)
        {
            SurvivalStatsSystem.Instance.SetMaxHealthMultiplier(bonusMultiplier);
            SurvivalStatsSystem.Instance.SetMaxStaminaMultiplier(bonusMultiplier);
        }
    }

    private void GiveNGPlusStartingItems()
    {
        var startingItems = new Dictionary<string, int>
        {
            ["basic_tools"] = 1,
            ["survival_kit"] = 1,
            ["food_rations"] = 5,
            ["water_bottle"] = 3
        };
        
        // Add bonus items based on NG+ level
        var bonusItems = NGPlusData.PlusLevel switch
        {
            >= 5 => new Dictionary<string, int> { ["legendary_starter_weapon"] = 1, ["rare_materials"] = 10 },
            >= 3 => new Dictionary<string, int> { ["advanced_tools"] = 1, ["building_materials"] = 20 },
            >= 1 => new Dictionary<string, int> { ["improved_weapon"] = 1, ["extra_supplies"] = 5 },
            _ => new Dictionary<string, int>()
        };
        
        foreach (var item in startingItems.Concat(bonusItems))
        {
            Inventory.Instance?.AddItem(item.Key, item.Value);
        }
    }

    private void ApplyPermanentUnlocks()
    {
        foreach (var unlock in NGPlusData.PermanentUnlocks)
        {
            switch (unlock)
            {
                case "advanced_crafting_station":
                    CraftingSystem.Instance?.UnlockAdvancedCrafting();
                    break;
                case "legendary_weapon_blueprints":
                    BuildingManager.Instance?.UnlockLegendaryWeapons();
                    break;
                case "fast_travel_system":
                    WorldManager.Instance?.EnableFastTravel();
                    break;
                case "enhanced_health_regen":
                    SurvivalStatsSystem.Instance?.EnableEnhancedRegen();
                    break;
                case "mega_structures":
                    BuildingManager.Instance?.UnlockMegaStructures();
                    break;
                case "expanded_inventory":
                    Inventory.Instance?.ExpandInventorySlots(20);
                    break;
            }
        }
    }

    public float GetCurrentDifficultyMultiplier()
    {
        return 1f + (NGPlusData.PlusLevel * DifficultyMultiplierPerLevel);
    }

    public Dictionary<string, object> GetNGPlusStats()
    {
        return new Dictionary<string, object>
        {
            ["plus_level"] = NGPlusData.PlusLevel,
            ["total_playthroughs"] = NGPlusData.TotalPlaythroughs,
            ["total_playtime"] = NGPlusData.TotalPlayTime,
            ["difficulty_multiplier"] = GetCurrentDifficultyMultiplier(),
            ["retained_skills_count"] = NGPlusData.RetainedSkills.Count,
            ["unlocked_recipes_count"] = NGPlusData.UnlockedRecipes.Count,
            ["permanent_unlocks_count"] = NGPlusData.PermanentUnlocks.Count
        };
    }

    private void SaveNGPlusData()
    {
        try
        {
            using var saveFile = FileAccess.Open(NGPlusSaveFile, FileAccess.ModeFlags.Write);
            if (saveFile != null)
            {
                var dataDict = SerializeNGPlusData(NGPlusData);
                var jsonString = Json.Stringify(dataDict);
                saveFile.StoreString(jsonString);
                GD.Print("New Game Plus data saved successfully");
            }
        }
        catch (Exception ex)
        {
            GD.PrintErr($"Failed to save NG+ data: {ex.Message}");
        }
    }

    private void LoadNGPlusData()
    {
        try
        {
            if (FileAccess.FileExists(NGPlusSaveFile))
            {
                using var saveFile = FileAccess.Open(NGPlusSaveFile, FileAccess.ModeFlags.Read);
                if (saveFile != null)
                {
                    var jsonString = saveFile.GetAsText();
                    var json = new Json();
                    var parseResult = json.Parse(jsonString);
                    
                    if (parseResult == Error.Ok)
                    {
                        var data = json.Data.AsGodotDictionary();
                        NGPlusData = DeserializeNGPlusData(data);
                        GD.Print($"Loaded NG+ data - Level: {NGPlusData.PlusLevel}");
                    }
                }
            }
        }
        catch (Exception ex)
        {
            GD.PrintErr($"Failed to load NG+ data: {ex.Message}");
            NGPlusData = new NewGamePlusData();
        }
    }

    private NewGamePlusData DeserializeNGPlusData(Godot.Collections.Dictionary data)
    {
        var ngPlusData = new NewGamePlusData();
        
        if (data.ContainsKey("PlusLevel"))
            ngPlusData.PlusLevel = data["PlusLevel"].AsInt32();
        if (data.ContainsKey("TotalPlaythroughs"))
            ngPlusData.TotalPlaythroughs = data["TotalPlaythroughs"].AsInt32();
        if (data.ContainsKey("TotalPlayTime"))
            ngPlusData.TotalPlayTime = data["TotalPlayTime"].AsSingle();
        
        // Deserialize retained skills
        if (data.ContainsKey("RetainedSkills"))
        {
            var skillsDict = data["RetainedSkills"].AsGodotDictionary();
            foreach (var skill in skillsDict)
            {
                ngPlusData.RetainedSkills[skill.Key.AsString()] = skill.Value.AsSingle();
            }
        }
        
        // Deserialize unlocked recipes
        if (data.ContainsKey("UnlockedRecipes"))
        {
            var recipesArray = data["UnlockedRecipes"].AsGodotArray();
            foreach (var recipe in recipesArray)
            {
                ngPlusData.UnlockedRecipes.Add(recipe.AsString());
            }
        }
        
        // Deserialize unlocked blueprints
        if (data.ContainsKey("UnlockedBlueprints"))
        {
            var blueprintsArray = data["UnlockedBlueprints"].AsGodotArray();
            foreach (var blueprint in blueprintsArray)
            {
                ngPlusData.UnlockedBlueprints.Add(blueprint.AsString());
            }
        }
        
        // Deserialize permanent unlocks
        if (data.ContainsKey("PermanentUnlocks"))
        {
            var unlocksArray = data["PermanentUnlocks"].AsGodotArray();
            foreach (var unlock in unlocksArray)
            {
                ngPlusData.PermanentUnlocks.Add(unlock.AsString());
            }
        }
        
        return ngPlusData;
    }

    private Godot.Collections.Dictionary SerializeNGPlusData(NewGamePlusData data)
    {
        var dict = new Godot.Collections.Dictionary();
        dict["PlusLevel"] = data.PlusLevel;
        dict["TotalPlaythroughs"] = data.TotalPlaythroughs;
        dict["TotalPlayTime"] = data.TotalPlayTime;
        
        // Serialize retained skills
        var skillsDict = new Godot.Collections.Dictionary();
        foreach (var skill in data.RetainedSkills)
        {
            skillsDict[skill.Key] = skill.Value;
        }
        dict["RetainedSkills"] = skillsDict;
        
        // Serialize unlocked recipes
        var recipesArray = new Godot.Collections.Array();
        foreach (var recipe in data.UnlockedRecipes)
        {
            recipesArray.Add(recipe);
        }
        dict["UnlockedRecipes"] = recipesArray;
        
        // Serialize unlocked blueprints
        var blueprintsArray = new Godot.Collections.Array();
        foreach (var blueprint in data.UnlockedBlueprints)
        {
            blueprintsArray.Add(blueprint);
        }
        dict["UnlockedBlueprints"] = blueprintsArray;
        
        // Serialize permanent unlocks
        var unlocksArray = new Godot.Collections.Array();
        foreach (var unlock in data.PermanentUnlocks)
        {
            unlocksArray.Add(unlock);
        }
        dict["PermanentUnlocks"] = unlocksArray;
        
        return dict;
    }

    public void ResetNGPlusData()
    {
        NGPlusData = new NewGamePlusData();
        SaveNGPlusData();
        GD.Print("New Game Plus data reset");
    }
}}
