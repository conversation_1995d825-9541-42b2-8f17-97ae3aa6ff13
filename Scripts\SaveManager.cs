using System;
using System.Collections.Generic;
using System.Text.Json;
using Godot;

namespace SurvivalLooterShooter
{
    /// <summary>
    /// Manages saving and loading game state with error handling and validation
    /// </summary>
    public partial class SaveManager : Node
    {
        public static SaveManager Instance { get; private set; }
        
        private const string SAVE_DIRECTORY = "user://saves/";
        private const string SAVE_FILE_NAME = "game_save.json";
        private const string BACKUP_FILE_NAME = "game_save_backup.json";
        private const string SAVE_VERSION = "1.0.0";
        
        // Events for save/load operations
        [Signal]
        public delegate void GameSavedEventHandler(bool success, string message);
        
        [Signal]
        public delegate void GameLoadedEventHandler(bool success, string message);
        
        [Signal]
        public delegate void SaveCorruptedEventHandler(string errorMessage);

        private string _saveFilePath;
        private string _backupFilePath;
        
        // System references for data collection
        private GameManager _gameManager;
        private Inventory _inventory;
        private SurvivalStatsSystem _survivalStatsSystem;
        private WeaponController _weaponController;
        private PlayerController _playerController;

        public override void _Ready()
        {
            if (Instance == null)
            {
                Instance = this;
            }
            else
            {
                QueueFree();
                return;
            }
            
            // Ensure save directory exists
            if (!DirAccess.DirExistsAbsolute(SAVE_DIRECTORY))
            {
                DirAccess.MakeDirRecursiveAbsolute(SAVE_DIRECTORY);
            }
            
            _saveFilePath = SAVE_DIRECTORY + SAVE_FILE_NAME;
            _backupFilePath = SAVE_DIRECTORY + BACKUP_FILE_NAME;
            
            GD.Print($"SaveManager initialized. Save path: {_saveFilePath}");
        }

        /// <summary>
        /// Initializes the SaveManager with system references
        /// </summary>
        public void Initialize(GameManager gameManager, Inventory inventory, 
                             SurvivalStatsSystem survivalStatsSystem, WeaponController weaponController,
                             PlayerController playerController = null)
        {
            _gameManager = gameManager ?? throw new ArgumentNullException(nameof(gameManager));
            _inventory = inventory ?? throw new ArgumentNullException(nameof(inventory));
            _survivalStatsSystem = survivalStatsSystem ?? throw new ArgumentNullException(nameof(survivalStatsSystem));
            _weaponController = weaponController ?? throw new ArgumentNullException(nameof(weaponController));
            _playerController = playerController; // Optional for backward compatibility
            
            GD.Print("SaveManager system references initialized");
        }

        /// <summary>
        /// Saves the current game state to file with comprehensive error handling
        /// </summary>
        public bool SaveGame()
        {
            Logger.LogInfo("SaveManager", "Starting game save operation");
            
            try
            {
                // Validate initialization
                if (_inventory == null || _survivalStatsSystem == null)
                {
                    string error = "SaveManager not properly initialized";
                    Logger.LogError("SaveManager", error);
                    EmitSignal(SignalName.GameSaved, false, error);
                    return false;
                }

                // Collect all game data
                var saveData = CollectGameData();
                if (saveData == null)
                {
                    string error = "Failed to collect game data";
                    Logger.LogError("SaveManager", error);
                    EmitSignal(SignalName.GameSaved, false, error);
                    return false;
                }
                
                // Validate save data
                if (!DataValidator.ValidateSaveData(saveData, out List<string> validationErrors))
                {
                    Logger.LogWarning("SaveManager", $"Save data validation issues: {string.Join(", ", validationErrors)}");
                }
                
                // Serialize to JSON with error handling
                string jsonData;
                try
                {
                    var options = new JsonSerializerOptions
                    {
                        WriteIndented = true,
                        PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                        DefaultIgnoreCondition = System.Text.Json.Serialization.JsonIgnoreCondition.WhenWritingNull
                    };
                    
                    jsonData = JsonSerializer.Serialize(saveData, options);
                    
                    if (string.IsNullOrEmpty(jsonData))
                    {
                        string error = "Serialization produced empty JSON data";
                        Logger.LogError("SaveManager", error);
                        EmitSignal(SignalName.GameSaved, false, error);
                        return false;
                    }
                }
                catch (JsonException ex)
                {
                    string error = $"JSON serialization failed: {ex.Message}";
                    Logger.LogException("SaveManager", ex, "SaveGame serialization");
                    EmitSignal(SignalName.GameSaved, false, error);
                    return false;
                }
                
                // Create backup of existing save if it exists
                if (Godot.FileAccess.FileExists(_saveFilePath))
                {
                    if (!CreateBackup())
                    {
                        Logger.LogWarning("SaveManager", "Failed to create backup, continuing with save");
                    }
                }
                
                // Write new save data with atomic operation
                if (!WriteJsonToFile(jsonData, _saveFilePath))
                {
                    string error = "Failed to write save file";
                    Logger.LogError("SaveManager", error);
                    EmitSignal(SignalName.GameSaved, false, error);
                    return false;
                }
                
                // Verify the saved file
                if (!VerifySaveFile(_saveFilePath))
                {
                    Logger.LogError("SaveManager", "Save file verification failed");
                    // Try to restore from backup
                    if (Godot.FileAccess.FileExists(_backupFilePath))
                    {
                        RestoreFromBackup();
                    }
                    EmitSignal(SignalName.GameSaved, false, "Save file verification failed");
                    return false;
                }
                
                Logger.LogInfo("SaveManager", $"Game saved successfully at {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
                EmitSignal(SignalName.GameSaved, true, "Game saved successfully");
                return true;
            }
            catch (Exception ex)
            {
                string error = $"Unexpected error during save: {ex.Message}";
                Logger.LogException("SaveManager", ex, "SaveGame");
                EmitSignal(SignalName.GameSaved, false, error);
                return false;
            }
        }

        /// <summary>
        /// Loads game state from file and restores all systems with comprehensive error handling
        /// </summary>
        public bool LoadGame()
        {
            Logger.LogInfo("SaveManager", "Starting game load operation");
            
            try
            {
                // Validate initialization
                if (_inventory == null || _survivalStatsSystem == null)
                {
                    string error = "SaveManager not properly initialized";
                    Logger.LogError("SaveManager", error);
                    EmitSignal(SignalName.GameLoaded, false, error);
                    return false;
                }

                // Check if save file exists
                if (!Godot.FileAccess.FileExists(_saveFilePath))
                {
                    Logger.LogInfo("SaveManager", "No save file found, starting new game");
                    EmitSignal(SignalName.GameLoaded, false, "No save file found");
                    return false;
                }
                
                // Verify save file before loading
                if (!VerifySaveFile(_saveFilePath))
                {
                    Logger.LogWarning("SaveManager", "Main save file verification failed, trying backup");
                    return TryLoadBackup();
                }
                
                // Read save file
                string jsonData;
                try
                {
                    var saveFile = Godot.FileAccess.Open(_saveFilePath, Godot.FileAccess.ModeFlags.Read);
                    if (saveFile == null)
                    {
                        Logger.LogError("SaveManager", $"Failed to open save file: {_saveFilePath}");
                        return TryLoadBackup();
                    }
                    
                    jsonData = saveFile.GetAsText();
                    saveFile.Close();
                    
                    if (string.IsNullOrEmpty(jsonData))
                    {
                        Logger.LogError("SaveManager", "Save file is empty");
                        return TryLoadBackup();
                    }
                }
                catch (Exception ex)
                {
                    Logger.LogException("SaveManager", ex, "Reading save file");
                    return TryLoadBackup();
                }
                
                // Validate and deserialize save data
                var saveData = ValidateAndDeserialize(jsonData);
                if (saveData == null)
                {
                    Logger.LogError("SaveManager", "Failed to validate and deserialize save data");
                    return TryLoadBackup();
                }
                
                // Restore all systems from save data
                try
                {
                    RestoreGameData(saveData);
                }
                catch (Exception ex)
                {
                    Logger.LogException("SaveManager", ex, "Restoring game data");
                    EmitSignal(SignalName.GameLoaded, false, $"Failed to restore game data: {ex.Message}");
                    return false;
                }
                
                Logger.LogInfo("SaveManager", $"Game loaded successfully from save dated {saveData.LastSaveTime:yyyy-MM-dd HH:mm:ss}");
                EmitSignal(SignalName.GameLoaded, true, "Game loaded successfully");
                return true;
            }
            catch (Exception ex)
            {
                string error = $"Unexpected error during load: {ex.Message}";
                Logger.LogException("SaveManager", ex, "LoadGame");
                EmitSignal(SignalName.GameLoaded, false, error);
                return TryLoadBackup();
            }
        }

        /// <summary>
        /// Attempts to load from backup file if main save fails
        /// </summary>
        private bool TryLoadBackup()
        {
            try
            {
                if (!Godot.FileAccess.FileExists(_backupFilePath))
                {
                    GD.PrintErr("No backup save file available");
                    EmitSignal(SignalName.SaveCorrupted, "Both main and backup saves are unavailable");
                    return false;
                }
                
                GD.Print("Attempting to load from backup save...");
                
                var backupFile = Godot.FileAccess.Open(_backupFilePath, Godot.FileAccess.ModeFlags.Read);
                if (backupFile == null)
                {
                    GD.PrintErr("Failed to open backup save file");
                    return false;
                }
                
                string jsonData = backupFile.GetAsText();
                backupFile.Close();
                
                var saveData = ValidateAndDeserialize(jsonData);
                if (saveData == null)
                {
                    EmitSignal(SignalName.SaveCorrupted, "Backup save is also corrupted");
                    return false;
                }
                
                RestoreGameData(saveData);
                
                GD.Print("Successfully loaded from backup save");
                EmitSignal(SignalName.GameLoaded, true, "Loaded from backup save");
                return true;
            }
            catch (Exception ex)
            {
                GD.PrintErr($"Error loading backup: {ex.Message}");
                EmitSignal(SignalName.SaveCorrupted, $"Backup load failed: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Validates and deserializes JSON save data with comprehensive error handling
        /// </summary>
        private GameSaveData ValidateAndDeserialize(string jsonData)
        {
            try
            {
                // First validate JSON format
                if (!DataValidator.ValidateJsonContent(jsonData, out string jsonError))
                {
                    Logger.LogError("SaveManager", $"Invalid JSON in save file: {jsonError}");
                    EmitSignal(SignalName.SaveCorrupted, $"Invalid JSON: {jsonError}");
                    return null;
                }
                
                // Deserialize with error handling
                GameSaveData saveData;
                try
                {
                    var options = new JsonSerializerOptions
                    {
                        PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                        PropertyNameCaseInsensitive = true,
                        AllowTrailingCommas = true
                    };
                    
                    saveData = JsonSerializer.Deserialize<GameSaveData>(jsonData, options);
                }
                catch (JsonException ex)
                {
                    Logger.LogException("SaveManager", ex, "JSON deserialization");
                    EmitSignal(SignalName.SaveCorrupted, $"Deserialization failed: {ex.Message}");
                    return null;
                }
                
                if (saveData == null)
                {
                    Logger.LogError("SaveManager", "Deserialized save data is null");
                    EmitSignal(SignalName.SaveCorrupted, "Deserialized data is null");
                    return null;
                }
                
                // Validate save version compatibility
                if (string.IsNullOrEmpty(saveData.SaveVersion))
                {
                    Logger.LogWarning("SaveManager", "Save file has no version info, assuming compatible");
                    saveData.SaveVersion = SAVE_VERSION;
                }
                else if (saveData.SaveVersion != SAVE_VERSION)
                {
                    Logger.LogWarning("SaveManager", $"Save version mismatch: {saveData.SaveVersion} vs {SAVE_VERSION}");
                    // Here you could implement version migration logic
                    // For now, we'll try to load anyway
                }
                
                // Comprehensive validation with error recovery
                if (!DataValidator.ValidateSaveData(saveData, out List<string> validationErrors))
                {
                    Logger.LogWarning("SaveManager", $"Save data validation issues (recovered): {string.Join(", ", validationErrors)}");
                }
                
                // Additional validation for critical data
                ValidateCriticalSaveData(saveData);
                
                Logger.LogInfo("SaveManager", "Save data validated and deserialized successfully");
                return saveData;
            }
            catch (Exception ex)
            {
                Logger.LogException("SaveManager", ex, "ValidateAndDeserialize");
                EmitSignal(SignalName.SaveCorrupted, $"Validation failed: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Validates critical save data and applies fixes where possible
        /// </summary>
        private void ValidateCriticalSaveData(GameSaveData saveData)
        {
            // Validate timestamps
            if (saveData.LastSaveTime > DateTime.UtcNow.AddDays(1))
            {
                Logger.LogWarning("SaveManager", "Save timestamp is in the future, correcting");
                saveData.LastSaveTime = DateTime.UtcNow;
            }
            
            if (saveData.LastSaveTime < new DateTime(2020, 1, 1))
            {
                Logger.LogWarning("SaveManager", "Save timestamp is too old, correcting");
                saveData.LastSaveTime = DateTime.UtcNow;
            }
            
            // Validate game time
            if (saveData.GameTime < 0)
            {
                Logger.LogWarning("SaveManager", "Negative game time detected, correcting");
                saveData.GameTime = 0;
            }
            
            // Validate player position (basic bounds checking)
            if (float.IsNaN(saveData.PlayerPosition.X) || float.IsNaN(saveData.PlayerPosition.Y) ||
                float.IsInfinity(saveData.PlayerPosition.X) || float.IsInfinity(saveData.PlayerPosition.Y))
            {
                Logger.LogWarning("SaveManager", "Invalid player position detected, resetting to origin");
                saveData.PlayerPosition = Vector2.Zero;
            }
            
            // Validate player velocity
            if (float.IsNaN(saveData.PlayerVelocity.X) || float.IsNaN(saveData.PlayerVelocity.Y) ||
                float.IsInfinity(saveData.PlayerVelocity.X) || float.IsInfinity(saveData.PlayerVelocity.Y))
            {
                Logger.LogWarning("SaveManager", "Invalid player velocity detected, resetting to zero");
                saveData.PlayerVelocity = Vector2.Zero;
            }
            
            // Validate inventory items exist in database
            if (ItemDatabase.Instance != null)
            {
                var invalidItems = new List<string>();
                
                foreach (var kvp in saveData.InventoryItems)
                {
                    if (!ItemDatabase.Instance.HasItem(kvp.Value.ItemId))
                    {
                        invalidItems.Add(kvp.Key);
                        Logger.LogWarning("SaveManager", $"Inventory contains unknown item '{kvp.Value.ItemId}', removing");
                    }
                }
                
                foreach (var key in invalidItems)
                {
                    saveData.InventoryItems.Remove(key);
                }
                
                // Validate equipment items
                var invalidEquipment = new List<string>();
                
                foreach (var kvp in saveData.EquipmentSlots)
                {
                    if (!string.IsNullOrEmpty(kvp.Value.ItemId) && !ItemDatabase.Instance.HasItem(kvp.Value.ItemId))
                    {
                        invalidEquipment.Add(kvp.Key);
                        Logger.LogWarning("SaveManager", $"Equipment contains unknown item '{kvp.Value.ItemId}', removing");
                    }
                }
                
                foreach (var key in invalidEquipment)
                {
                    saveData.EquipmentSlots.Remove(key);
                }
            }
        }

        /// <summary>
        /// Collects current game data from all systems
        /// </summary>
        private GameSaveData CollectGameData()
        {
            var saveData = new GameSaveData
            {
                LastSaveTime = DateTime.UtcNow,
                SaveVersion = SAVE_VERSION,
                IsDead = _survivalStatsSystem.IsDead
            };
            
            // Collect inventory data
            var allItems = _inventory.GetAllItems();
            foreach (var kvp in allItems)
            {
                saveData.InventoryItems[kvp.Key] = new InventorySlotData(kvp.Value);
            }
            
            // Collect equipment data
            var equipmentSlots = new[] { "weapon", "armor", "tool" };
            foreach (var slotType in equipmentSlots)
            {
                var equippedItem = _inventory.GetEquippedItem(slotType);
                if (equippedItem != null && !equippedItem.IsEmpty)
                {
                    saveData.EquipmentSlots[slotType] = new InventorySlotData(equippedItem);
                }
            }
            
            // Collect survival stats data
            saveData.SurvivalStats = _survivalStatsSystem.GetAllStatsData();
            
            // Collect player data if player controller is available
            if (_playerController != null)
            {
                var playerData = _playerController.GetMovementData();
                saveData.PlayerPosition = playerData.Position;
                saveData.PlayerVelocity = playerData.Velocity;
                saveData.PlayerIsSprinting = playerData.IsSprinting;
                saveData.PlayerIsAlive = playerData.IsAlive;
            }
            
            // Collect day/night cycle data
            if (DayNightCycle.Instance != null)
            {
                saveData.CurrentTime = DayNightCycle.Instance.CurrentTime;
                saveData.DayLengthMinutes = DayNightCycle.Instance.DayLengthMinutes;
                saveData.DawnTime = DayNightCycle.Instance.DawnTime;
                saveData.DuskTime = DayNightCycle.Instance.DuskTime;
            }
            
            // Collect progression system data
            if (SkillManager.Instance != null)
            {
                saveData.SkillManagerData = SkillManager.Instance.GetSaveData();
            }
            
            if (ProgressionRewardSystem.Instance != null)
            {
                saveData.ProgressionRewardData = ProgressionRewardSystem.Instance.GetSaveData();
            }
            
            if (AbilitySystem.Instance != null)
            {
                saveData.AbilitySystemData = AbilitySystem.Instance.GetSaveData();
            }
            
            return saveData;
        }

        /// <summary>
        /// Restores all game systems from save data
        /// </summary>
        private void RestoreGameData(GameSaveData saveData)
        {
            // Clear current inventory
            _inventory.Clear();
            
            // Restore inventory items
            foreach (var kvp in saveData.InventoryItems)
            {
                var slotData = kvp.Value;
                if (!slotData.IsEmpty)
                {
                    _inventory.AddItem(slotData.ItemId, slotData.Quantity, slotData.Metadata);
                }
            }
            
            // Restore equipped items
            foreach (var kvp in saveData.EquipmentSlots)
            {
                var slotType = kvp.Key;
                var slotData = kvp.Value;
                if (!slotData.IsEmpty)
                {
                    // Make sure we have the item in inventory first
                    if (!_inventory.HasItem(slotData.ItemId))
                    {
                        _inventory.AddItem(slotData.ItemId, slotData.Quantity, slotData.Metadata);
                    }
                    _inventory.EquipItem(slotData.ItemId, slotType);
                }
            }
            
            // Restore survival stats
            _survivalStatsSystem.LoadAllStatsData(saveData.SurvivalStats);
            
            // Restore player data if player controller is available
            if (_playerController != null)
            {
                var playerData = new PlayerMovementData
                {
                    Position = saveData.PlayerPosition,
                    Velocity = saveData.PlayerVelocity,
                    IsSprinting = saveData.PlayerIsSprinting,
                    IsAlive = saveData.PlayerIsAlive
                };
                _playerController.LoadMovementData(playerData);
            }
            
            // Restore day/night cycle data
            if (DayNightCycle.Instance != null)
            {
                DayNightCycle.Instance.SetTime(saveData.CurrentTime);
                DayNightCycle.Instance.DayLengthMinutes = saveData.DayLengthMinutes;
                DayNightCycle.Instance.DawnTime = saveData.DawnTime;
                DayNightCycle.Instance.DuskTime = saveData.DuskTime;
            }
            
            // Restore progression system data
            if (SkillManager.Instance != null && saveData.SkillManagerData.Count > 0)
            {
                SkillManager.Instance.LoadSaveData(saveData.SkillManagerData);
            }
            
            if (ProgressionRewardSystem.Instance != null && saveData.ProgressionRewardData.Count > 0)
            {
                ProgressionRewardSystem.Instance.LoadSaveData(saveData.ProgressionRewardData);
            }
            
            if (AbilitySystem.Instance != null && saveData.AbilitySystemData.Count > 0)
            {
                AbilitySystem.Instance.LoadSaveData(saveData.AbilitySystemData);
            }
            
            GD.Print($"Game data restored from save dated {saveData.LastSaveTime:yyyy-MM-dd HH:mm:ss}");
        }

        /// <summary>
        /// Checks if a save file exists
        /// </summary>
        public bool SaveFileExists()
        {
            return Godot.FileAccess.FileExists(_saveFilePath);
        }

        /// <summary>
        /// Gets save file information
        /// </summary>
        public SaveFileInfo GetSaveFileInfo()
        {
            if (!SaveFileExists())
                return null;
                
            try
            {
                var saveFile = Godot.FileAccess.Open(_saveFilePath, Godot.FileAccess.ModeFlags.Read);
                if (saveFile == null)
                    return null;
                    
                string jsonData = saveFile.GetAsText();
                saveFile.Close();
                
                var saveData = ValidateAndDeserialize(jsonData);
                if (saveData == null)
                    return null;
                    
                return new SaveFileInfo
                {
                    LastSaveTime = saveData.LastSaveTime,
                    SaveVersion = saveData.SaveVersion,
                    GameTime = saveData.GameTime,
                    IsDead = saveData.IsDead
                };
            }
            catch (Exception ex)
            {
                GD.PrintErr($"Error reading save file info: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Creates a backup of the current save file
        /// </summary>
        private bool CreateBackup()
        {
            try
            {
                var existingFile = Godot.FileAccess.Open(_saveFilePath, Godot.FileAccess.ModeFlags.Read);
                if (existingFile == null)
                {
                    Logger.LogWarning("SaveManager", "Cannot create backup - main save file not accessible");
                    return false;
                }
                
                string existingData = existingFile.GetAsText();
                existingFile.Close();
                
                if (string.IsNullOrEmpty(existingData))
                {
                    Logger.LogWarning("SaveManager", "Cannot create backup - main save file is empty");
                    return false;
                }
                
                return WriteJsonToFile(existingData, _backupFilePath);
            }
            catch (Exception ex)
            {
                Logger.LogException("SaveManager", ex, "CreateBackup");
                return false;
            }
        }

        /// <summary>
        /// Writes JSON data to a file atomically
        /// </summary>
        private bool WriteJsonToFile(string jsonData, string filePath)
        {
            try
            {
                string tempFilePath = filePath + ".tmp";
                
                // Write to temporary file first
                var tempFile = Godot.FileAccess.Open(tempFilePath, Godot.FileAccess.ModeFlags.Write);
                if (tempFile == null)
                {
                    Logger.LogError("SaveManager", $"Failed to create temporary file: {tempFilePath}");
                    return false;
                }
                
                tempFile.StoreString(jsonData);
                tempFile.Close();
                
                // Verify temporary file
                if (!VerifySaveFile(tempFilePath))
                {
                    Logger.LogError("SaveManager", "Temporary file verification failed");
                    DirAccess.RemoveAbsolute(tempFilePath);
                    return false;
                }
                
                // Remove existing file if it exists
                if (Godot.FileAccess.FileExists(filePath))
                {
                    DirAccess.RemoveAbsolute(filePath);
                }
                
                // Move temporary file to final location
                var dir = DirAccess.Open("user://");
                if (dir == null || dir.Rename(tempFilePath, filePath) != Error.Ok)
                {
                    Logger.LogError("SaveManager", $"Failed to move temporary file to final location");
                    DirAccess.RemoveAbsolute(tempFilePath);
                    return false;
                }
                
                return true;
            }
            catch (Exception ex)
            {
                Logger.LogException("SaveManager", ex, "WriteJsonToFile");
                return false;
            }
        }

        /// <summary>
        /// Verifies that a save file is valid and readable
        /// </summary>
        private bool VerifySaveFile(string filePath)
        {
            try
            {
                if (!Godot.FileAccess.FileExists(filePath))
                {
                    return false;
                }
                
                var file = Godot.FileAccess.Open(filePath, Godot.FileAccess.ModeFlags.Read);
                if (file == null)
                {
                    return false;
                }
                
                string content = file.GetAsText();
                file.Close();
                
                if (string.IsNullOrEmpty(content))
                {
                    return false;
                }
                
                // Try to parse as JSON
                if (!DataValidator.ValidateJsonContent(content, out string error))
                {
                    Logger.LogWarning("SaveManager", $"Save file JSON validation failed: {error}");
                    return false;
                }
                
                // Try to deserialize as GameSaveData
                try
                {
                    var options = new JsonSerializerOptions
                    {
                        PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                    };
                    var saveData = JsonSerializer.Deserialize<GameSaveData>(content, options);
                    return saveData != null;
                }
                catch (JsonException)
                {
                    return false;
                }
            }
            catch (Exception ex)
            {
                Logger.LogException("SaveManager", ex, "VerifySaveFile");
                return false;
            }
        }

        /// <summary>
        /// Restores the main save file from backup
        /// </summary>
        private bool RestoreFromBackup()
        {
            try
            {
                if (!Godot.FileAccess.FileExists(_backupFilePath))
                {
                    Logger.LogError("SaveManager", "No backup file available for restoration");
                    return false;
                }
                
                var backupFile = Godot.FileAccess.Open(_backupFilePath, Godot.FileAccess.ModeFlags.Read);
                if (backupFile == null)
                {
                    Logger.LogError("SaveManager", "Cannot open backup file for restoration");
                    return false;
                }
                
                string backupData = backupFile.GetAsText();
                backupFile.Close();
                
                if (WriteJsonToFile(backupData, _saveFilePath))
                {
                    Logger.LogInfo("SaveManager", "Successfully restored save file from backup");
                    return true;
                }
                else
                {
                    Logger.LogError("SaveManager", "Failed to restore save file from backup");
                    return false;
                }
            }
            catch (Exception ex)
            {
                Logger.LogException("SaveManager", ex, "RestoreFromBackup");
                return false;
            }
        }

        /// <summary>
        /// Deletes the save file with error handling
        /// </summary>
        public bool DeleteSave()
        {
            try
            {
                bool success = true;
                
                if (Godot.FileAccess.FileExists(_saveFilePath))
                {
                    var result = DirAccess.RemoveAbsolute(_saveFilePath);
                    if (result != Error.Ok)
                    {
                        Logger.LogError("SaveManager", $"Failed to delete main save file: {result}");
                        success = false;
                    }
                }
                
                if (Godot.FileAccess.FileExists(_backupFilePath))
                {
                    var result = DirAccess.RemoveAbsolute(_backupFilePath);
                    if (result != Error.Ok)
                    {
                        Logger.LogError("SaveManager", $"Failed to delete backup save file: {result}");
                        success = false;
                    }
                }
                
                if (success)
                {
                    Logger.LogInfo("SaveManager", "Save files deleted successfully");
                }
                
                return success;
            }
            catch (Exception ex)
            {
                Logger.LogException("SaveManager", ex, "DeleteSave");
                return false;
            }
        }
    }

    /// <summary>
    /// Information about a save file
    /// </summary>
    public class SaveFileInfo
    {
        public DateTime LastSaveTime { get; set; }
        public string SaveVersion { get; set; }
        public float GameTime { get; set; }
        public bool IsDead { get; set; }
    }
}