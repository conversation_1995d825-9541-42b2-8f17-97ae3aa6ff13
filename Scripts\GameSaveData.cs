using System;
using System.Collections.Generic;
using Godot;

namespace SurvivalLooterShooter
{
    /// <summary>
    /// Serializable class containing all persistent game data
    /// </summary>
    [Serializable]
    public class GameSaveData
    {
        // Inventory data
        public Dictionary<string, InventorySlotData> InventoryItems { get; set; } = new();
        public Dictionary<string, InventorySlotData> EquipmentSlots { get; set; } = new();
        
        // Survival stats data
        public Dictionary<string, SurvivalStatData> SurvivalStats { get; set; } = new();
        
        // Player data
        public Vector2 PlayerPosition { get; set; } = Vector2.Zero;
        public Vector2 PlayerVelocity { get; set; } = Vector2.Zero;
        public bool PlayerIsSprinting { get; set; } = false;
        public bool PlayerIsAlive { get; set; } = true;
        
        // Game state data
        public DateTime LastSaveTime { get; set; } = DateTime.UtcNow;
        public float GameTime { get; set; } = 0f;
        public bool IsDead { get; set; } = false;
        
        // Day/Night cycle data
        public float CurrentTime { get; set; } = 12.0f; // Start at noon
        public float DayLengthMinutes { get; set; } = 20.0f;
        public float DawnTime { get; set; } = 6.0f;
        public float DuskTime { get; set; } = 18.0f;
        
        // Version for save compatibility
        public string SaveVersion { get; set; } = "1.0.0";
        
        // Progression system data
        public Dictionary<string, object> SkillManagerData { get; set; } = new();
        public Dictionary<string, object> ProgressionRewardData { get; set; } = new();
        public Dictionary<string, object> AbilitySystemData { get; set; } = new();
        
        public GameSaveData()
        {
            InventoryItems = new Dictionary<string, InventorySlotData>();
            EquipmentSlots = new Dictionary<string, InventorySlotData>();
            SurvivalStats = new Dictionary<string, SurvivalStatData>();
            SkillManagerData = new Dictionary<string, object>();
            ProgressionRewardData = new Dictionary<string, object>();
            AbilitySystemData = new Dictionary<string, object>();
        }
    }

    /// <summary>
    /// Serializable version of InventorySlot for save data
    /// </summary>
    [Serializable]
    public class InventorySlotData
    {
        public string ItemId { get; set; } = "";
        public int Quantity { get; set; } = 0;
        public Dictionary<string, object> Metadata { get; set; } = new();
        
        public InventorySlotData() { }
        
        public InventorySlotData(InventorySlot slot)
        {
            if (slot != null && !slot.IsEmpty)
            {
                ItemId = slot.ItemId;
                Quantity = slot.Quantity;
                Metadata = new Dictionary<string, object>(slot.Metadata);
            }
        }
        
        /// <summary>
        /// Converts this data back to an InventorySlot
        /// </summary>
        public InventorySlot ToInventorySlot()
        {
            if (string.IsNullOrEmpty(ItemId) || Quantity <= 0)
                return new InventorySlot();
                
            return new InventorySlot(ItemId, Quantity, Metadata);
        }
        
        public bool IsEmpty => Quantity <= 0 || string.IsNullOrEmpty(ItemId);
    }
}