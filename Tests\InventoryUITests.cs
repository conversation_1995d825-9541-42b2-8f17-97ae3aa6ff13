using Godot;
using System.Collections.Generic;

namespace SurvivalLooterShooter
{
    /// <summary>
    /// Test class for inventory UI functionality
    /// </summary>
    public partial class InventoryUITests : Node
    {
        private Inventory _testInventory;
        private InventoryUI _testInventoryUI;

        public override void _Ready()
        {
            // Run tests after a short delay to ensure ItemDatabase is loaded
            GetTree().CreateTimer(1.0f).Timeout += RunTests;
        }

        private void RunTests()
        {
            GD.Print("=== Starting Inventory UI Tests ===");
            
            TestInventoryUIInitialization();
            TestInventoryUIUpdates();
            TestTooltipGeneration();
            
            GD.Print("=== Inventory UI Tests Complete ===");
        }

        private void TestInventoryUIInitialization()
        {
            GD.Print("Testing inventory UI initialization...");
            
            // Create test inventory
            _testInventory = new Inventory();
            AddChild(_testInventory);
            
            // Load and create inventory UI
            var inventoryUIScene = GD.Load<PackedScene>("res://Scenes/InventoryUI.tscn");
            _testInventoryUI = inventoryUIScene.Instantiate<InventoryUI>();
            AddChild(_testInventoryUI);
            
            // Initialize UI with inventory
            _testInventoryUI.Initialize(_testInventory);
            
            GD.Print("✓ Inventory UI initialized successfully");
        }

        private void TestInventoryUIUpdates()
        {
            GD.Print("Testing inventory UI updates...");
            
            if (_testInventory == null || _testInventoryUI == null)
            {
                GD.PrintErr("✗ Test inventory or UI not initialized");
                return;
            }
            
            // Add items to inventory
            _testInventory.AddItem("bandage", 3);
            _testInventory.AddItem("cloth", 10);
            
            // Add weapon with metadata
            var weaponMetadata = new Dictionary<string, object>
            {
                ["durability"] = 75f
            };
            _testInventory.AddItem("assault_rifle", 1, weaponMetadata);
            
            GD.Print("✓ Items added to inventory, UI should update automatically");
        }

        private void TestTooltipGeneration()
        {
            GD.Print("Testing tooltip generation...");
            
            // This test verifies that the tooltip system can handle different item types
            var items = new[]
            {
                "bandage",      // consumable
                "assault_rifle", // weapon with durability
                "cloth",        // material
                "rifle_ammo"    // ammo
            };
            
            foreach (var itemId in items)
            {
                var item = ItemDatabase.Instance?.GetItem(itemId);
                if (item != null)
                {
                    GD.Print($"✓ Item {item.Name} found in database");
                }
                else
                {
                    GD.PrintErr($"✗ Item {itemId} not found in database");
                }
            }
            
            GD.Print("✓ Tooltip generation test complete");
        }
    }
}