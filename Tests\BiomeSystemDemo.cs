using Godot;
using System;
using System.Collections.Generic;

namespace SurvivalLooterShooter
{
    /// <summary>
    /// Demonstration of the enhanced biome system with resource distribution
    /// Shows biome transitions, resource clustering, and visual effects
    /// </summary>
    public partial class BiomeSystemDemo : Node2D
    {
        private WorldManager _worldManager;
        private Camera2D _camera;
        private Label _infoLabel;
        private Vector2 _playerPosition = Vector2.Zero;
        private float _moveSpeed = 200.0f;

        public override void _Ready()
        {
            GD.Print("Starting Biome System Demo...");
            SetupDemo();
        }

        private void SetupDemo()
        {
            // Create camera
            _camera = new Camera2D();
            _camera.Position = Vector2.Zero;
            _camera.Zoom = new Vector2(0.5f, 0.5f); // Zoom out to see more
            AddChild(_camera);

            // Create info label
            _infoLabel = new Label();
            _infoLabel.Position = new Vector2(10, 10);
            _infoLabel.Size = new Vector2(400, 200);
            _infoLabel.Text = "Loading biome system...";
            AddChild(_infoLabel);

            // Initialize world manager
            _worldManager = new WorldManager();
            AddChild(_worldManager);
            
            // Wait a frame for initialization
            CallDeferred(nameof(InitializeWorld));
        }

        private void InitializeWorld()
        {
            // Generate world with demo seed
            _worldManager.GenerateWorld(42);
            
            // Load initial chunks around spawn
            _worldManager.UpdateWorldLoading(_playerPosition);
            
            UpdateInfoDisplay();
            GD.Print("Biome system demo initialized!");
        }

        public override void _Process(double delta)
        {
            HandleInput((float)delta);
            UpdateCamera();
            UpdateInfoDisplay();
        }

        private void HandleInput(float delta)
        {
            Vector2 inputVector = Vector2.Zero;
            
            if (Input.IsActionPressed("ui_up"))
                inputVector.Y -= 1;
            if (Input.IsActionPressed("ui_down"))
                inputVector.Y += 1;
            if (Input.IsActionPressed("ui_left"))
                inputVector.X -= 1;
            if (Input.IsActionPressed("ui_right"))
                inputVector.X += 1;
                
            if (inputVector != Vector2.Zero)
            {
                _playerPosition += inputVector.Normalized() * _moveSpeed * delta;
                _worldManager.UpdateWorldLoading(_playerPosition);
            }
        }

        private void UpdateCamera()
        {
            _camera.Position = _playerPosition;
        }

        private void UpdateInfoDisplay()
        {
            if (_worldManager == null || !_worldManager.IsWorldGenerated)
            {
                _infoLabel.Text = "Initializing world...";
                return;
            }

            var biomeType = _worldManager.GetBiomeAt(_playerPosition);
            var elevation = _worldManager.GetElevationAt(_playerPosition);
            var chunk = _worldManager.GetChunkAt(_playerPosition);
            
            string biomeInfo = $"Position: ({_playerPosition.X:F0}, {_playerPosition.Y:F0})\n";
            biomeInfo += $"Biome: {biomeType}\n";
            biomeInfo += $"Elevation: {elevation:F2}\n";
            
            if (chunk != null)
            {
                var resources = chunk.GetResourcesNear(_playerPosition, 50.0f);
                biomeInfo += $"Nearby Resources: {resources.Count}\n";
                
                if (resources.Count > 0)
                {
                    var resourceCounts = new Dictionary<string, int>();
                    foreach (var resource in resources)
                    {
                        resourceCounts[resource.ItemId] = resourceCounts.GetValueOrDefault(resource.ItemId, 0) + 1;
                    }
                    
                    biomeInfo += "Resource Types:\n";
                    foreach (var kvp in resourceCounts)
                    {
                        biomeInfo += $"  {kvp.Key}: {kvp.Value}\n";
                    }
                }
            }
            
            biomeInfo += "\nControls: Arrow keys to move\nZoom out to see biome transitions";
            
            _infoLabel.Text = biomeInfo;
        }

        public override void _Draw()
        {
            // Draw player position
            DrawCircle(_playerPosition, 5.0f, Colors.Red);
            
            // Draw biome boundaries (simplified visualization)
            if (_worldManager != null && _worldManager.IsWorldGenerated)
            {
                DrawBiomeBoundaries();
            }
        }

        private void DrawBiomeBoundaries()
        {
            // Sample biomes in a grid around the player to show transitions
            int sampleRadius = 5;
            int sampleSize = 32;
            
            for (int x = -sampleRadius; x <= sampleRadius; x++)
            {
                for (int y = -sampleRadius; y <= sampleRadius; y++)
                {
                    Vector2 samplePos = _playerPosition + new Vector2(x * sampleSize, y * sampleSize);
                    BiomeType biome = _worldManager.GetBiomeAt(samplePos);
                    
                    Color biomeColor = GetBiomeColor(biome);
                    biomeColor.A = 0.3f; // Make it semi-transparent
                    
                    DrawRect(new Rect2(samplePos - Vector2.One * sampleSize * 0.5f, Vector2.One * sampleSize), biomeColor);
                }
            }
        }

        private Color GetBiomeColor(BiomeType biome)
        {
            return biome switch
            {
                BiomeType.Plains => Colors.LightGreen,
                BiomeType.Forest => Colors.DarkGreen,
                BiomeType.Desert => Colors.Yellow,
                BiomeType.Mountains => Colors.Gray,
                BiomeType.Swamp => Colors.DarkOliveGreen,
                BiomeType.Tundra => Colors.LightBlue,
                BiomeType.Ocean => Colors.Blue,
                _ => Colors.White
            };
        }

        public override void _Input(InputEvent @event)
        {
            if (@event is InputEventKey keyEvent && keyEvent.Pressed)
            {
                switch (keyEvent.Keycode)
                {
                    case Key.R:
                        // Regenerate world with new seed
                        var newSeed = new Random().Next();
                        _worldManager.GenerateWorld(newSeed);
                        GD.Print($"Regenerated world with seed: {newSeed}");
                        break;
                        
                    case Key.Escape:
                        GetTree().Quit();
                        break;
                }
            }
        }
    }
}